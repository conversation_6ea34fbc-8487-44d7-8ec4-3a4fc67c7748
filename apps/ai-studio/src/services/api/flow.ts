import type { IStudioSuccessListResponse } from '@/models';
import { axiosService } from '@resola-ai/services-shared';
import { executeRequest } from '../utils/helper';
import type { Flow, FlowCreatePayload } from '@/models/flow';
export interface FlowListSuccessResponse extends IStudioSuccessListResponse<Flow> {
  hasMore: boolean;
  hasPrev: boolean;
  currentPage: number;
}

export const FlowApi = {
  getList: async (wsId: string, limit: number, cursor: string, searchValue?: string) => {
    const response = await executeRequest<FlowListSuccessResponse>(() =>
      axiosService.instance.get(`${wsId}/flows`, {
        params: {
          limit,
          cursor,
          filter: JSON.stringify({
            name: { $regex: searchValue },
          }),
        },
      })
    );
    return response;
  },

  create: async (wsId: string, flow: FlowCreatePayload) => {
    return executeRequest<Flow>(() => axiosService.instance.post(`${wsId}/flows`, flow));
  },

  update: async (wsId: string, flow: Flow) => {
    return executeRequest<Flow>(() => axiosService.instance.put(`${wsId}/flows/${flow.id}`, flow));
  },

  delete: async (wsId: string, flowId: string) => {
    return executeRequest(() => axiosService.instance.delete(`${wsId}/flows/${flowId}`));
  },

  getById: async (wsId: string, flowId: string) => {
    return executeRequest<Flow>(() => axiosService.instance.get(`${wsId}/flows/${flowId}`));
  },
};
