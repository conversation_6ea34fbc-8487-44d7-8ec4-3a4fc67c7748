import { renderHook, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { FlowListContextProvider, useFlowListContext } from './index';
import { FlowApi, type FlowListSuccessResponse } from '@/services/api/flow';
import type { Flow } from '@/models/flow';
import { LayoutType } from '@/types';
import { MemoryRouter, useParams, useNavigate } from 'react-router-dom'; // Import necessary parts
import React from 'react';

// Mock router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual<typeof import('react-router-dom')>('react-router-dom');
  return {
    ...actual,
    useParams: vi.fn(() => ({ workspaceId: '123' })), // Mock useParams
    useNavigate: vi.fn(), // Mock useNavigate
  };
});

// Mock API
vi.mock('@/services/api/flow', () => ({
  FlowApi: {
    getList: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock ulid
vi.mock('ulid', () => ({
  ulid: vi.fn(),
}));

// Mock useTranslate
vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => ({
    t: (key: string) => key,
  })),
}));

// Mock useHandleApiError
vi.mock('@/hooks/useHandleApiError', () => ({
  useHandleApiError: () => ({
    handleApiError: vi.fn(),
  }),
}));

describe('FlowListContext', () => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <MemoryRouter>
      <FlowListContextProvider>{children}</FlowListContextProvider>
    </MemoryRouter>
  );

  const mockFlow: Flow = {
    id: '1',
    name: 'Test Flow',
    description: 'Test Description',
    nodes: {},
    status: 'enabled',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    triggers: {},
  };

  const mockFlowListResponse = {
    data: [mockFlow],
    nextCursor: null,
    prevCursor: null,
    hasMore: false,
    hasPrev: false,
    currentPage: 1,
  } as unknown as FlowListSuccessResponse;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(FlowApi.getList).mockResolvedValue(mockFlowListResponse);
    vi.mocked(useParams).mockReturnValue({ workspaceId: '123' }); // Ensure useParams is mocked correctly
    vi.mocked(useNavigate).mockReturnValue(vi.fn());
  });

  it('provides flow list context with default values', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    expect(result.current.flowList).toEqual([]);
    expect(result.current.limit).toBe(10);
    expect(result.current.search).toBe('');
    expect(result.current.filter).toBe('model');
    expect(result.current.layout).toBe(LayoutType.GRID);
    expect(result.current.isLoading).toBe(true);
    expect(result.current.openedCreateModal).toBe(false);
    expect(result.current.openedWaitingModal).toBe(false);
    expect(result.current.openedTemplateModal).toBe(false);
    expect(result.current.openedEditModal).toBe(false);
    expect(result.current.isCreatingFromTemplate).toBe(false);
    expect(result.current.cursorObject).toEqual({
      nextCursor: undefined,
      prevCursor: undefined,
      currentPage: 1,
    });
  });

  it('fetches flow list successfully', async () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    await act(async () => {
      await result.current.refetchList();
    });

    expect(FlowApi.getList).toHaveBeenCalledWith('123', 10, '', '');
    expect(result.current.flowList).toEqual([mockFlow]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.cursorObject).toEqual({
      nextCursor: null,
      prevCursor: null,
      currentPage: 1,
    });
  });

  it('handles getList error', async () => {
    const error = new Error('Failed to fetch flows');
    vi.mocked(FlowApi.getList).mockRejectedValueOnce(error);

    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    // we need to set flowList to [] before refetchList
    act(() => {
      result.current.setFlowList([]);
    });
    await act(async () => {
      try {
        await result.current.refetchList();
      } catch (e) {
        // Catch the error here to prevent it from being thrown globally
      }
    });

    expect(FlowApi.getList).toHaveBeenCalledWith('123', 10, '', '');
    expect(result.current.flowList).toEqual([]);
    expect(result.current.isLoading).toBe(false);
  });

  it('updates limit when setLimit is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setLimit(20);
    });

    expect(result.current.limit).toBe(20);
  });

  it('updates layout when setLayout is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setLayout(LayoutType.LIST);
    });

    expect(result.current.layout).toBe(LayoutType.LIST);
    expect(result.current.isFullWidth).toBe(true);
  });

  it('updates search when setSearch is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setSearch('test');
    });

    expect(result.current.search).toBe('test');
  });

  it('updates flowList when setFlowList is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setFlowList([mockFlow]);
    });

    expect(result.current.flowList).toEqual([mockFlow]);
  });

  it('updates cursor when handleSetCursor is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleSetCursor('next-cursor');
    });

    expect(result.current.cursor).toBe('next-cursor');
  });

  it('updates filter when handleFilter is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleFilter('newFilter');
    });

    expect(result.current.filter).toBe('newFilter');
  });

  it('updates search when handleOnChangeSearch is called', async () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    await act(async () => {
      result.current.handleOnChangeSearch('newSearch');
      await new Promise((resolve) => setTimeout(resolve, 150));
    });

    expect(result.current.search).toBe('newSearch');
  });

  it('updates openedCreateModal when setOpenedCreateModal is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setOpenedCreateModal(true);
    });

    expect(result.current.openedCreateModal).toBe(true);
  });

  it('updates openedWaitingModal when setOpenedWaitingModal is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setOpenedWaitingModal(true);
    });

    expect(result.current.openedWaitingModal).toBe(true);
  });

  it('updates openedTemplateModal when setOpenedTemplateModal is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setOpenedTemplateModal(true);
    });

    expect(result.current.openedTemplateModal).toBe(true);
  });

  it('updates openedEditModal when setOpenedEditModal is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setOpenedEditModal(true);
    });

    expect(result.current.openedEditModal).toBe(true);
  });

  it('handles create flow from scratch', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleCreateFromScratch();
    });

    expect(result.current.openedCreateModal).toBe(true);
    expect(result.current.isCreatingFromTemplate).toBe(false);
  });

  it('handles close create flow modal', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setOpenedCreateModal(true);
      result.current.handleCloseCreateFlowModal();
    });

    expect(result.current.openedCreateModal).toBe(false);
    expect(result.current.isCreatingFromTemplate).toBe(false);
  });

  it('handles import from file', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleImportFromFile();
    });
    // No state change, just console log
  });

  it('handles create from template', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleCreateFromTemplate();
    });

    expect(result.current.openedTemplateModal).toBe(true);
  });

  it('handles select template to create', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleSelectTemplateToCreate(mockFlow);
    });

    expect(result.current.openedCreateModal).toBe(true);
    expect(result.current.openedTemplateModal).toBe(false);
    expect(result.current.isCreatingFromTemplate).toBe(true);
  });

  // it('creates a flow successfully', async () => {
  //   const mockCreatedFlow = { ...mockFlow, id: 'new-flow-id' };
  //   vi.mocked(FlowApi.create).mockResolvedValueOnce(mockCreatedFlow);
  //   const { result } = renderHook(() => useFlowListContext(), { wrapper });

  //   await act(async () => {
  //     await result.current.handleCreateFlow({ title: 'New Flow', description: 'New Description' });
  //   });

  //   expect(FlowApi.create).toHaveBeenCalledWith('123', {
  //     name: 'New Flow',
  //     description: 'New Description',
  //     nodes: {
  //       'mocked-nanoid': {
  //         // Use the fixed value here
  //         id: 'mocked-nanoid', // Use the fixed value here
  //         type: FlowNodeType.WebhookTrigger,
  //         displayName: 'Webhook Trigger',
  //         description: 'Webhook Trigger',
  //         settings: {
  //           path: '',
  //           method: 'POST',
  //         },
  //       },
  //     },
  //     status: 'enabled',
  //   });
  // });

  it('handles createFlow error', async () => {
    vi.mocked(FlowApi.create).mockRejectedValueOnce(new Error('Failed to create flow'));
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    await act(async () => {
      await result.current.handleCreateFlow({ title: 'New Flow', description: 'New Description' });
    });

    expect(result.current.openedCreateModal).toBe(false);
    expect(result.current.openedWaitingModal).toBe(false);
    expect(result.current.isCreatingFromTemplate).toBe(false);
  });

  it('updates a flow successfully', async () => {
    vi.mocked(FlowApi.update).mockResolvedValueOnce(mockFlow);
    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    act(() => {
      result.current.setFlowList([mockFlow]);
    });
    await act(async () => {
      await result.current.handleEditFLow(mockFlow);
    });

    expect(FlowApi.update).toHaveBeenCalledWith('123', mockFlow);
    expect(result.current.flowList).toEqual([mockFlow]);
    expect(result.current.openedEditModal).toBe(false);
  });

  it('handles updateFlow error', async () => {
    vi.mocked(FlowApi.update).mockRejectedValueOnce(new Error('Failed to update flow'));
    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    act(() => {
      result.current.setFlowList([mockFlow]);
    });
    await act(async () => {
      await result.current.handleEditFLow(mockFlow);
    });

    expect(FlowApi.update).toHaveBeenCalledWith('123', mockFlow);
    expect(result.current.openedEditModal).toBe(false);
  });

  it('duplicates a flow successfully', async () => {
    const mockDuplicatedFlow = { ...mockFlow, id: 'new-flow-id', name: 'Test Flow (Copy)' };
    vi.mocked(FlowApi.create).mockResolvedValueOnce(mockDuplicatedFlow);
    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    act(() => {
      result.current.setFlowList([mockFlow]);
    });
    await act(async () => {
      await result.current.handleDuplicateFlow(mockFlow);
    });

    expect(FlowApi.create).toHaveBeenCalledWith('123', {
      name: 'Test Flow (Copy)',
      description: 'Test Description',
      nodes: {},
      triggers: {},
      status: 'enabled',
    });
    expect(result.current.flowList).toEqual([mockFlow, mockDuplicatedFlow]);
  });

  it('handles duplicateFlow error', async () => {
    vi.mocked(FlowApi.create).mockRejectedValueOnce(new Error('Failed to duplicate flow'));
    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    act(() => {
      result.current.setFlowList([mockFlow]);
    });
    await act(async () => {
      await result.current.handleDuplicateFlow(mockFlow);
    });

    expect(FlowApi.create).toHaveBeenCalledWith('123', {
      name: 'Test Flow (Copy)',
      description: 'Test Description',
      nodes: {},
      triggers: {},
      status: 'enabled',
    });
    expect(result.current.flowList).toEqual([mockFlow]);
  });

  it('deletes a flow successfully', async () => {
    vi.mocked(FlowApi.delete).mockResolvedValueOnce(undefined);
    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    act(() => {
      result.current.setFlowList([mockFlow]);
    });
    await act(async () => {
      await result.current.handleDeleteFlow(mockFlow);
    });

    expect(FlowApi.delete).toHaveBeenCalledWith('123', '1');
    expect(result.current.flowList).toEqual([]);
  });

  it('handles deleteFlow error', async () => {
    vi.mocked(FlowApi.delete).mockRejectedValueOnce(new Error('Failed to delete flow'));
    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    act(() => {
      result.current.setFlowList([mockFlow]);
    });
    await act(async () => {
      await result.current.handleDeleteFlow(mockFlow);
    });

    expect(FlowApi.delete).toHaveBeenCalledWith('123', '1');
    expect(result.current.flowList).toEqual([mockFlow]);
  });

  it('navigates after successful flow creation', async () => {
    const mockNavigate = vi.fn();
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);
    vi.mocked(FlowApi.create).mockResolvedValueOnce({ ...mockFlow, id: 'new-flow-id' });

    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    await act(async () => {
      await result.current.handleCreateFlow({ title: 'New Flow', description: 'New Description' });
    });

    expect(mockNavigate).toHaveBeenCalledWith('/studio/123/flows/new-flow-id');
  });

  it('throws error when useFlowListContext is used outside provider', () => {
    expect(() => {
      renderHook(() => useFlowListContext());
    }).toThrow('useFlowListContext must be used within a FlowListContextProvider');
  });

  it('handles filter with null value', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleFilter(null);
    });

    // Filter should not change when null is passed (stays at default 'model')
    expect(result.current.filter).toBe('model');
  });

  it('handles filter with empty string', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleFilter('');
    });

    // Filter should not change when empty string is passed (stays at default 'model')
    expect(result.current.filter).toBe('model');
  });
});
