import { useHandleApiError } from '@/hooks/useHandleApiError';
import { type Flow, type FlowCreatePayload, type FlowNodeData, FlowNodeType } from '@/models/flow';
import { FlowApi } from '@/services/api/flow';
import { LayoutType } from '@/types';
import { useTranslate } from '@tolgee/react';
import { throttle } from 'lodash';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import useSWR from 'swr';
import { ulid } from 'ulid';
export const useListOfFlows = (
  workspaceId: string,
  limit: number,
  cursor?: string,
  searchValue?: string
) => {
  return useSWR(
    workspaceId ? [workspaceId, 'flows/list', limit, cursor, searchValue] : null,
    () => {
      return FlowApi.getList(workspaceId, limit, cursor ?? '', searchValue ?? '');
    },
    {
      revalidateOnFocus: false,
    }
  );
};

export const useFlowList = () => {
  const navigate = useNavigate();
  const { t } = useTranslate('flow');
  const { workspaceId } = useParams();
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');
  const [cursor, setCursor] = useState('');
  const [filter, setFilter] = useState('model');
  const [layout, setLayout] = useState<LayoutType>(LayoutType.GRID);
  const [openedCreateModal, setOpenedCreateModal] = useState(false);
  const [openedWaitingModal, setOpenedWaitingModal] = useState(false);
  const [openedTemplateModal, setOpenedTemplateModal] = useState(false);
  const [isCreatingFromTemplate, setIsCreateFromTemplate] = useState(false);
  const [openedEditModal, setOpenedEditModal] = useState(false);
  const [flowList, setFlowList] = useState<Flow[]>([]);
  const {
    data: flowlistSuccessResponse,
    error,
    isLoading,
    mutate: refetchList,
  } = useListOfFlows(workspaceId ?? '', limit, cursor, search);
  const { handleApiError } = useHandleApiError();

  const isFullWidth = layout === LayoutType.LIST;

  const cursorObject = useMemo(() => {
    if (!flowlistSuccessResponse)
      return {
        nextCursor: undefined,
        prevCursor: undefined,
        currentPage: 1,
      };
    return {
      nextCursor: flowlistSuccessResponse?.nextCursor,
      prevCursor: flowlistSuccessResponse?.prevCursor,
      currentPage: flowlistSuccessResponse?.currentPage || 1,
    };
  }, [flowlistSuccessResponse]);

  const handleFilter = useCallback((value: string | null) => {
    console.log(value);
    value && setFilter(value);
  }, []);

  const handleOnChangeSearch = throttle((value) => {
    console.log(value);
    setSearch(value);
  }, 100);

  const handleCreateFromScratch = useCallback(() => {
    console.log('handleCreateFromScratch');
    setIsCreateFromTemplate(false);
    setOpenedCreateModal(true);
  }, []);

  const handleCloseCreateFlowModal = useCallback(() => {
    setOpenedCreateModal(false);
    isCreatingFromTemplate && setIsCreateFromTemplate(false);
  }, [isCreatingFromTemplate]);

  const handleImportFromFile = useCallback(() => {
    console.log('handleImportFromFile');
  }, []);

  const handleCreateFromTemplate = useCallback(() => {
    console.log('handleCreateFromTemplate');
    setOpenedTemplateModal(true);
  }, []);

  const handleSetCursor = useCallback((cursor?: string) => {
    cursor && setCursor(cursor);
  }, []);

  const handleCreateFlow = useCallback(
    async (data: { title: string; description: string }) => {
      const newId = ulid();
      const newFlowData: FlowCreatePayload = {
        name: data.title,
        description: data.description,
        triggers: {
          [newId]: {
            id: newId,
            name: FlowNodeType.NewTrigger,
            displayName: t('defaultNameTriggerNode'),
            description: '',
            settings: {},
            next: '',
          } as FlowNodeData,
        },
        nodes: {},
        status: 'enabled',
      };
      isCreatingFromTemplate && setOpenedWaitingModal(true);
      try {
        const res = await FlowApi.create(workspaceId ?? '', newFlowData);
        navigate(`/studio/${workspaceId}/flows/${res.id}`);
      } catch (error) {
        handleApiError(error);
        setOpenedCreateModal(false);
        setOpenedWaitingModal(false);
        setIsCreateFromTemplate(false);
      }
    },
    [isCreatingFromTemplate, navigate, workspaceId, t]
  );

  const handleSelectTemplateToCreate = useCallback((flow: Flow) => {
    console.log(flow);
    setOpenedCreateModal(true);
    setOpenedTemplateModal(false);
    setIsCreateFromTemplate(true);
  }, []);

  const handleEditFLow = useCallback(
    async (editedFlow: Flow) => {
      try {
        setFlowList((prev) => {
          const index = prev.findIndex((f) => f.id === editedFlow.id);
          const newFlowList = [...prev];
          newFlowList[index] = editedFlow;
          return newFlowList;
        });
        setOpenedEditModal(false);
        await FlowApi.update(workspaceId ?? '', editedFlow);
      } catch (error) {
        handleApiError(error);
      } finally {
        setOpenedEditModal(false);
      }
    },
    [workspaceId]
  );

  const handleDuplicateFlow = useCallback(
    async (flow: Flow) => {
      try {
        const { id, updatedAt, createdAt, status, ...originalData } = flow ?? {};
        const newFlowData = {
          ...originalData,
          name: `${flow.name} (Copy)`,
          status: 'enabled' as Flow['status'],
        };
        const newFlow = await FlowApi.create(workspaceId ?? '', newFlowData);
        setFlowList((prev) => {
          const index = prev.findIndex((f) => f.id === flow.id);
          const newFlowList = [...prev];
          newFlowList.splice(index + 1, 0, newFlow);
          return newFlowList;
        });
      } catch (error) {
        handleApiError(error);
      }
    },
    [workspaceId]
  );

  const handleDeleteFlow = useCallback(
    async (flow: Flow) => {
      try {
        await FlowApi.delete(workspaceId ?? '', flow.id);
        setFlowList((prev) => prev.filter((f) => f.id !== flow.id));
      } catch (error) {
        handleApiError(error);
      }
    },
    [workspaceId]
  );

  useEffect(() => {
    const flowList = flowlistSuccessResponse?.data;
    flowList && flowList?.length > 0 ? setFlowList(flowList) : setFlowList([]);
  }, [flowlistSuccessResponse]);

  return useMemo(
    () => ({
      limit,
      error,
      cursor,
      search,
      filter,
      layout,
      flowList,
      setLimit,
      setLayout,
      isLoading,
      setSearch,
      refetchList,
      isFullWidth,
      setFlowList,
      cursorObject,
      handleFilter,
      handleSetCursor,
      handleEditFLow,
      openedEditModal,
      handleCreateFlow,
      handleDeleteFlow,
      openedCreateModal,
      openedWaitingModal,
      setOpenedEditModal,
      handleDuplicateFlow,
      openedTemplateModal,
      handleOnChangeSearch,
      setOpenedCreateModal,
      handleImportFromFile,
      setOpenedWaitingModal,
      setOpenedTemplateModal,
      isCreatingFromTemplate,
      handleCreateFromScratch,
      handleCreateFromTemplate,
      handleCloseCreateFlowModal,
      handleSelectTemplateToCreate,
    }),
    [
      limit,
      error,
      search,
      filter,
      layout,
      cursor,
      flowList,
      isLoading,
      refetchList,
      isFullWidth,
      cursorObject,
      handleFilter,
      handleSetCursor,
      openedEditModal,
      handleCreateFlow,
      handleDeleteFlow,
      openedCreateModal,
      setOpenedEditModal,
      openedWaitingModal,
      handleDuplicateFlow,
      openedTemplateModal,
      handleOnChangeSearch,
      handleImportFromFile,
      isCreatingFromTemplate,
      handleCreateFromScratch,
      handleCreateFromTemplate,
      handleCloseCreateFlowModal,
      handleSelectTemplateToCreate,
    ]
  );
};

type FlowListContextType = ReturnType<typeof useFlowList>;

const FlowListContext = createContext<FlowListContextType | null>(null);
export const FlowListContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useFlowList();
  if (!value) {
    throw new Error('useFlowList is not defined');
  }
  return <FlowListContext.Provider value={value}>{children}</FlowListContext.Provider>;
};

export const useFlowListContext = () => {
  const context = useContext(FlowListContext);
  if (!context) {
    throw new Error('useFlowListContext must be used within a FlowListContextProvider');
  }
  return context;
};
