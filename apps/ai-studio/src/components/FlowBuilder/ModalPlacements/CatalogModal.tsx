import { forwardRef, useImperativeHandle, useState } from 'react';
import { Modal, rem } from '@mantine/core';
import { Catalog, CatalogConfig, TriggerNodes, ActionNodes, LoopingNodes } from '@resola-ai/ui';

type CatalogModalProps = {
  onClose: () => void;
  withCloseButton?: boolean;
  onSelect: (item: any) => void;
};

export type CatalogModalRef = {
  openWithDisabledOptions: (disabledOptions?: NodeNameTypes[]) => void;
  openWithEnabledOptions: (enabledOptions?: NodeNameTypes[], triggerContext?: boolean) => void;
};

const CatalogModal = forwardRef<CatalogModalRef, CatalogModalProps>(
  ({ onClose, onSelect, withCloseButton = false }, ref) => {
    const [opened, setOpened] = useState(false);
    const [isTriggerContext, setIsTriggerContext] = useState(false);
    const [disabledNodes, setDisabledNodes] = useState<NodeNameTypes[]>([]);

    useImperativeHandle(ref, () => ({
      openWithDisabledOptions: (disabledOptions?: NodeNameTypes[]) => {
        disabledOptions && setDisabledNodes(disabledOptions);
        setOpened(true);
      },
      openWithEnabledOptions: (enabledOptions?: NodeNameTypes[], triggerContext = false) => {
        enabledOptions &&
          setDisabledNodes(() => {
            const allNodes = Array.from(
              new Set([...TriggerNodes, ...LoopingNodes, ...ActionNodes] as NodeNameTypes[])
            );
            return allNodes.filter((node) => !enabledOptions.includes(node));
          });
        setIsTriggerContext(triggerContext);
        setOpened(true);
      },
    }));

    const handleClose = () => {
      setOpened(false);
      setDisabledNodes([]);
      setIsTriggerContext(false);
      onClose();
    };

    const handleOnSelectCatalog = (item: any) => {
      onSelect({ ...item, isTriggerContext });
      handleClose();
    };

    return (
      <Modal
        centered
        radius='md'
        opened={opened}
        size={rem(489)}
        padding={'1rem'}
        closeOnClickOutside
        onClose={handleClose}
        withCloseButton={withCloseButton}
      >
        <Catalog
          schema={CatalogConfig.schema}
          onSelect={handleOnSelectCatalog}
          disabledNodes={disabledNodes}
        />
      </Modal>
    );
  }
);

CatalogModal.displayName = 'CatalogModal';

export default CatalogModal;

export type NodeNameTypes =
  | 'chatbot'
  | 'code'
  | 'deca-ai-widgets'
  | 'deca-livechat'
  | 'deca-crm'
  | 'deca-kb'
  | 'deca-tables'
  | 'filter'
  | 'gmail'
  | 'google-calendar'
  | 'google-docs'
  | 'google-drive'
  | 'google-sheets'
  | 'google-slides'
  | 'hubspot'
  | 'http'
  | 'loop'
  | 'new-trigger'
  | 'openai'
  | 'pages'
  | 'path'
  | 'schedule'
  | 'slack'
  | 'wait'
  | 'formatter'
  | 'function'
  | 'zoom-meetings';

export { TriggerNodes, LoopingNodes, ActionNodes };
