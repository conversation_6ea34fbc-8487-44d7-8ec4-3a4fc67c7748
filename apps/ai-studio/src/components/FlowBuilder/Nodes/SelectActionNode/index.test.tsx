import { screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import userEvent from '@testing-library/user-event';
import { mockLibraries, renderWithMantine } from '@/utils/test';
import SelectActionNode from './index';
import { FlowBuilderProvider } from '@/contexts/FlowBuilderContext';
import { useTranslate } from '@tolgee/react';
import { FlowNodeType } from '@/models/flow';

mockLibraries();

const mockOpenCatalogForReplaceEmptyNode = vi.fn();
const mockHandleOpenFormPanel = vi.fn();

// Mock useTranslate
vi.mock('@tolgee/react', async () => {
  const actual = await vi.importActual('@tolgee/react');
  return {
    ...actual,
    useTranslate: vi.fn(),
  };
});

// Mock useFlowSchema hook
vi.mock('@/hooks/useFlowSchema', () => ({
  __esModule: true,
  default: vi.fn(() => ({
    schema: {
      displayName: 'Test Action',
      icon: 'test-icon',
    },
  })),
}));

// Mock FlowBuilderContext
vi.mock('@/contexts/FlowBuilderContext', () => ({
  useFlowBuilderContext: () => ({
    flowActionHandlers: {
      handleRemoveNode: vi.fn(),
    },
    handleOpenCatalogForReplaceEmptyNode: mockOpenCatalogForReplaceEmptyNode,
    handleOpenFormPanel: mockHandleOpenFormPanel,
  }),
  FlowBuilderProvider: ({ children }) => <>{children}</>,
}));

// Mock MenuActionNode component
vi.mock('../../MenuActionNode', () => ({
  __esModule: true,
  default: vi.fn().mockImplementation(({ onEdit, onDelete, dataTestId }) => {
    return (
      <div data-testid={dataTestId || 'menu-action-node'}>
        <button type='button' data-testid='edit-button' data-action='edit' onClick={onEdit}>
          Edit
        </button>
        <button type='button' data-testid='delete-button' data-action='delete' onClick={onDelete}>
          Delete
        </button>
      </div>
    );
  }),
}));

// Mock BaseSelectNode component
vi.mock('../BaseSelectNode', () => ({
  __esModule: true,
  default: vi.fn().mockImplementation(({ title, description, dataTestId, menuContent }) => (
    <div data-testid={dataTestId || 'base-select-node'}>
      <div data-testid='title'>{title}</div>
      <div data-testid='description'>{description}</div>
      <div data-testid='icon-bolt'>Icon Bolt</div>
      <div data-testid='menu-content'>{menuContent}</div>
    </div>
  )),
}));

describe('SelectActionNode', () => {
  const nodeId = 'test-node';
  const parentNodeId = 'parent-node';
  const mockOnEdit = vi.fn();
  const mockOnDelete = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock useTranslate
    (useTranslate as any).mockReturnValue({
      t: (key: string) => {
        const translations = {
          defaultTitleActionNode: 'Select an action',
          defaultNameActionNode: 'Select an action for your flow',
        };
        return translations[key] || key;
      },
    });
  });

  // Helper function to render with FlowBuilderProvider
  const renderWithProvider = (props: any) => {
    return renderWithMantine(
      <FlowBuilderProvider>
        <SelectActionNode {...props} />
      </FlowBuilderProvider>
    );
  };

  it('renders the node with correct text content', () => {
    renderWithProvider({ id: nodeId });
    expect(screen.getByText('0. Select an action for your flow')).toBeInTheDocument();
  });

  it('renders with correct data-testid', () => {
    renderWithProvider({ id: nodeId });
    expect(screen.getByTestId('text-action')).toBeInTheDocument();
  });

  it('renders icon', () => {
    renderWithProvider({ id: nodeId });
    expect(screen.getByTestId('icon-bolt')).toBeInTheDocument();
  });

  it('calls onEdit when edit button is clicked', async () => {
    const user = userEvent.setup();

    renderWithProvider({
      id: nodeId,
      data: { onEdit: mockOnEdit, orderedNumber: 1, actualParentNodeId: parentNodeId },
      type: FlowNodeType.EmptyNode,
    });

    const editButton = screen.getByTestId('edit-button');
    await user.click(editButton);

    expect(mockOpenCatalogForReplaceEmptyNode).toHaveBeenCalledTimes(1);
    expect(mockOpenCatalogForReplaceEmptyNode).toHaveBeenCalledWith({
      nodeId,
      triggerContext: false,
    });
  });

  it('calls onEdit when edit button is clicked', async () => {
    const user = userEvent.setup();

    renderWithProvider({
      id: nodeId,
      data: { onEdit: mockOnEdit, orderedNumber: 1, actualParentNodeId: parentNodeId },
      type: FlowNodeType.Code,
    });

    const editButton = screen.getByTestId('edit-button');
    await user.click(editButton);

    expect(mockHandleOpenFormPanel).toHaveBeenCalledTimes(1);
    expect(mockHandleOpenFormPanel).toHaveBeenCalledWith({
      nodeId,
      orderNumber: 1,
      parentNodeId,
      type: FlowNodeType.Code,
    });
  });

  it('calls onDelete when delete button is clicked', async () => {
    const user = userEvent.setup();

    renderWithProvider({ id: nodeId, data: { onDelete: mockOnDelete } });

    const deleteButton = screen.getByTestId('delete-button');
    await user.click(deleteButton);

    expect(mockOnDelete).toHaveBeenCalledTimes(0);
  });

  it('does not throw when callbacks are not provided', async () => {
    const user = userEvent.setup();

    renderWithProvider({ id: nodeId });

    const editButton = screen.getByTestId('edit-button');
    const deleteButton = screen.getByTestId('delete-button');

    // Should not throw errors when callbacks are not provided
    await expect(user.click(editButton)).resolves.not.toThrow();
    await expect(user.click(deleteButton)).resolves.not.toThrow();
  });
});
