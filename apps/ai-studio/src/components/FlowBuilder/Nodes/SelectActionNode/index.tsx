import { useCallback, useMemo } from 'react';
import BaseSelectNode from '../BaseSelectNode';
import MenuActionNode from '../../MenuActionNode';
import { useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import { FlowNodeType, FlowTypeNode } from '@/models/flow';
import useFlowSchema from '@/hooks/useFlowSchema';
import { useTranslate } from '@tolgee/react';
interface SelectActionNodeProps {
  id: string;
  data?: {
    displayName?: string;
    orderedNumber?: number;
    parentNodeId: string;
    actualParentNodeId: string;
    icon?: string;
    type: FlowNodeType;
    [key: string]: any;
    isTriggerNode?: boolean;
  };
  type: FlowNodeType;
}

export default function SelectActionNode(props: SelectActionNodeProps) {
  const { id, data, type } = props;
  const { t } = useTranslate('flow');
  const { schema } = useFlowSchema({ flowNodeType: type });
  const { flowActionHandlers, handleOpenCatalogForReplaceEmptyNode, handleOpenFormPanel } =
    useFlowBuilderContext();

  const orderedNumber = data?.orderedNumber ?? 0;

  const onDelete = useCallback(() => {
    flowActionHandlers?.handleRemoveNode({
      nodeId: id,
      typeRemove: data?.isTriggerNode ? FlowTypeNode.TriggerNode : FlowTypeNode.Node,
      parentId: data?.actualParentNodeId ?? '',
    });
  }, [data, id]);

  const handleNodeClick = useCallback(() => {
    if (type === FlowNodeType.EmptyNode || type === FlowNodeType.NewTrigger) {
      handleOpenCatalogForReplaceEmptyNode({
        nodeId: id,
        triggerContext: type === FlowNodeType.NewTrigger,
      });
    } else {
      handleOpenFormPanel({
        nodeId: id,
        orderNumber: orderedNumber,
        type: !!type
          ? type
          : data?.isTriggerNode
            ? FlowNodeType.NewTrigger
            : FlowNodeType.EmptyNode,
        parentNodeId: data?.actualParentNodeId ?? '',
      });
    }
  }, [type, handleOpenFormPanel, id, handleOpenCatalogForReplaceEmptyNode, orderedNumber, data]);

  const title = useMemo(() => {
    if (!type || type === FlowNodeType.NewTrigger) return t('defaultTitleTriggerNode');
    if (type === FlowNodeType.EmptyNode) return t('defaultTitleActionNode');
    return schema?.displayName;
  }, [type, schema?.displayName, t]);

  const displayName = useMemo(() => {
    const defaultName =
      type === FlowNodeType.NewTrigger ? t('defaultNameTriggerNode') : t('defaultNameActionNode');
    if (data?.displayName === schema?.displayName) {
      return defaultName;
    }
    if (data?.displayName) {
      return data?.displayName;
    }
    return defaultName;
  }, [data?.displayName, schema?.displayName, t]);

  const description = useMemo(() => {
    return `${orderedNumber}. ${displayName}`;
  }, [orderedNumber, displayName]);

  return (
    <BaseSelectNode
      id={id}
      title={title}
      customIcon={schema?.icon}
      dataTestId='text-action'
      description={description}
      openModalAction={handleNodeClick}
      menuContent={<MenuActionNode onDelete={onDelete} onEdit={handleNodeClick} />}
    />
  );
}
