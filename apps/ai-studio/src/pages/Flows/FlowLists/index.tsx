import { Container, Flex, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';

import { PageHeaderWithActions } from '@/components';
import AIPagination from '@/components/AIPagination';
import CreateFlowModal from '@/components/FlowList/CreateFlowModal';
import FlowTemplateModal from '@/components/FlowList/FlowTemplateModal';
import WaitingWhileCreatingModal from '@/components/FlowList/WaitingWhileCreatingFlowModal';
import { FlowListContextProvider, useFlowListContext } from '@/contexts/FlowListContext';
import FlowListComponent from '../../../components/FlowList/FlowListComponent';
import { useMemo } from 'react';
import AIEmpty from '@/components/AIEmpty';

const Flows = () => {
  const { t } = useTranslate('flow');
  const {
    search,
    layout,
    limit,
    flowList,
    setLimit,
    setLayout,
    isFullWidth,
    cursorObject,
    handleSetCursor,
    handleCreateFlow,
    openedCreateModal,
    openedWaitingModal,
    openedTemplateModal,
    handleOnChangeSearch,
    handleImportFromFile,
    setOpenedTemplateModal,
    handleCreateFromScratch,
    handleCreateFromTemplate,
    handleCloseCreateFlowModal,
    handleSelectTemplateToCreate,
  } = useFlowListContext();

  const hasFLow = useMemo(() => flowList?.length > 0, [flowList]);

  return (
    <>
      <Flex direction='column' w='100%' h='100%'>
        <Container fluid mb='md' px={0} w={'100%'}>
          <PageHeaderWithActions
            title={t('title')}
            layoutType={layout}
            searchValue={search}
            onLayoutChange={setLayout}
            description={t('description')}
            isUsingButtonMenuActions={true}
            btnMenuOptionPosition='bottom-end'
            onSearchChange={handleOnChangeSearch}
            searchPlaceholder={t('searchPlaceholder')}
            handleImportFromFile={handleImportFromFile}
            buttonActionLabel={t('buttonCreateFlowLabel')}
            handleCreateFromScratch={handleCreateFromScratch}
            handleCreateFromTemplate={handleCreateFromTemplate}
            hasData={hasFLow}
          />
        </Container>
        {hasFLow ? (
          <>
            <FlowListComponent isFullWidth={isFullWidth} />
            <Flex justify='center' align='center' gap={rem(16)} mt={rem(20)}>
              <AIPagination
                limit={limit}
                onChangeLimit={setLimit}
                nextCursor={cursorObject?.nextCursor}
                prevCursor={cursorObject?.prevCursor}
                onNext={() => handleSetCursor(cursorObject?.nextCursor)}
                onCursorChange={(cursor: string) => handleSetCursor(cursor)}
                onPrevious={() => handleSetCursor(cursorObject?.prevCursor)}
              />
            </Flex>
          </>
        ) : (
          <AIEmpty />
        )}
      </Flex>

      <CreateFlowModal
        opened={openedCreateModal}
        handleCreate={handleCreateFlow}
        onClose={handleCloseCreateFlowModal}
      />
      <FlowTemplateModal
        opened={openedTemplateModal}
        onClose={() => setOpenedTemplateModal(false)}
        onSelectTemplate={handleSelectTemplateToCreate}
      />
      <WaitingWhileCreatingModal opened={openedWaitingModal} />
    </>
  );
};

export default function FlowList() {
  return (
    <FlowListContextProvider>
      <Flows />
    </FlowListContextProvider>
  );
}
