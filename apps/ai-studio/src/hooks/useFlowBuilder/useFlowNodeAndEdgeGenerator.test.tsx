import { describe, it, expect, vi, beforeEach } from 'vitest';
import { FlowDiagram } from '@/helpers/flowDiagram';
import { FlowNodeType, type FlowNodeSetting, type FlowNodeData } from '@/models/flow';
import * as ReactFlow from '@xyflow/react';
import * as React from 'react';
import useFlowNodeAndEdgeGenerator, { useHideFlowAttribution } from './useFlowNodeAndEdgeGenerator';

// Import the internal function for testing
// We need to access the checkIsNodeIdToNumberChanges function for direct testing
const checkIsNodeIdToNumberChanges = (
  prevNodeIdToOrderNumber: Record<string, number>,
  nextNodeIdToOrderNumber: Record<string, number>
) => {
  if (Object.keys(prevNodeIdToOrderNumber).length !== Object.keys(nextNodeIdToOrderNumber).length) {
    return true;
  }
  return Object.keys(prevNodeIdToOrderNumber).some(
    (key) => prevNodeIdToOrderNumber?.[key] !== nextNodeIdToOrderNumber?.[key]
  );
};

// Mock the dependencies
vi.mock('@/helpers/flowDiagram', () => ({
  FlowDiagram: {
    getNodesAndEdges: vi.fn(),
    getLayoutCalculateElk: vi.fn(),
    getNodesForDiagram: vi.fn(),
    getEdgesForDiagram: vi.fn(),
    getBoundingRectOfNodes: vi.fn(),
    getCompoundNodeId: vi.fn().mockImplementation((loopId) => `${loopId}-looping-frame`),
  },
}));

vi.mock('@xyflow/react', () => ({
  useNodesState: vi.fn(() => [[], vi.fn()]),
  useEdgesState: vi.fn(() => [[], vi.fn()]),
}));

// Mock the React hooks
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual,
    useEffect: vi.fn((fn) => fn()),
    useState: vi.fn(() => [{}, vi.fn()]),
    useRef: vi.fn(() => ({ current: {} })),
  };
});

describe('checkIsNodeIdToNumberChanges', () => {
  it('should return true when objects have different lengths', () => {
    const prev = { 'node-1': 1 };
    const next = { 'node-1': 1, 'node-2': 2 };
    expect(checkIsNodeIdToNumberChanges(prev, next)).toBe(true);
  });

  it('should return true when objects have same length but different values', () => {
    const prev = { 'node-1': 1, 'node-2': 1 };
    const next = { 'node-1': 1, 'node-2': 2 };
    expect(checkIsNodeIdToNumberChanges(prev, next)).toBe(true);
  });

  it('should return false when objects are identical', () => {
    const prev = { 'node-1': 1, 'node-2': 2 };
    const next = { 'node-1': 1, 'node-2': 2 };
    expect(checkIsNodeIdToNumberChanges(prev, next)).toBe(false);
  });

  it('should return false when both objects are empty', () => {
    const prev = {};
    const next = {};
    expect(checkIsNodeIdToNumberChanges(prev, next)).toBe(false);
  });

  it('should handle undefined values correctly', () => {
    const prev = { 'node-1': 1, 'node-2': undefined as any };
    const next = { 'node-1': 1, 'node-2': 2 };
    expect(checkIsNodeIdToNumberChanges(prev, next)).toBe(true);
  });
});

describe('useFlowNodeAndEdgeGenerator', () => {
  // Mock data
  const mockTriggers = {
    'trigger-1': {
      id: 'trigger-1',
      name: FlowNodeType.EmptyNode,
      displayName: 'Trigger 1',
      description: 'Test trigger',
      settings: {} as FlowNodeSetting,
      next: 'node-1',
    },
  };

  const mockNodesConfig = {
    'node-1': {
      id: 'node-1',
      name: FlowNodeType.EmptyNode,
      displayName: 'Node 1',
      description: 'Test node',
      settings: {} as FlowNodeSetting,
      parent: 'trigger-1',
    },
  };

  const mockNodesAndEdges = {
    nodes: [{ id: 'node-1', data: {} }],
    edges: [{ id: 'edge-1', source: 'trigger-1', target: 'node-1' }],
    loopings: [],
    nodeIdToOrderNumber: {},
  };

  const mockLayoutData = {
    nodes: [{ id: 'node-1', position: { x: 100, y: 100 } }],
    edges: [{ id: 'edge-1', source: 'trigger-1', target: 'node-1' }],
    compoundNodes: [],
  };

  const mockNodesWithLoopings = {
    nodes: [
      { id: 'node-1', data: {} },
      { id: 'looping-1', data: {} },
      { id: 'child-1', data: {} },
    ],
    edges: [
      { id: 'edge-1', source: 'trigger-1', target: 'node-1' },
      { id: 'edge-2', source: 'looping-1', target: 'child-1' },
    ],
    loopings: [
      {
        loopId: 'looping-1',
        loopSubNodes: [
          {
            id: 'child-1',
            name: FlowNodeType.EmptyNode,
            displayName: 'Child 1',
            description: 'Child node',
            settings: {} as FlowNodeSetting,
            parent: 'looping-1',
          } as FlowNodeData,
        ],
      },
    ],
    nodeIdToOrderNumber: {},
  };

  const mockLayoutDataWithLoopings = {
    nodes: [
      { id: 'node-1', position: { x: 100, y: 100 } },
      { id: 'looping-1', position: { x: 200, y: 200 } },
      { id: 'child-1', position: { x: 250, y: 250 } },
    ],
    edges: [
      { id: 'edge-1', source: 'trigger-1', target: 'node-1' },
      { id: 'edge-2', source: 'looping-1', target: 'child-1' },
    ],
    compoundNodes: [],
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock console.log to prevent test output pollution
    vi.spyOn(console, 'log').mockImplementation(() => {});

    // Setup mocks for FlowDiagram functions
    vi.mocked(FlowDiagram.getNodesAndEdges).mockReturnValue(mockNodesAndEdges);
    vi.mocked(FlowDiagram.getLayoutCalculateElk).mockResolvedValue(mockLayoutData);
    vi.mocked(FlowDiagram.getNodesForDiagram).mockReturnValue([
      { id: 'node-1', type: 'default', position: { x: 100, y: 100 }, data: {} },
    ]);
    vi.mocked(FlowDiagram.getEdgesForDiagram).mockReturnValue([
      { id: 'edge-1', source: 'trigger-1', target: 'node-1' },
    ]);
    vi.mocked(FlowDiagram.getBoundingRectOfNodes).mockReturnValue({
      top: 100,
      bottom: 200,
      left: 100,
      right: 200,
    });
  });

  it('should call FlowDiagram.getNodesAndEdges with the correct arguments', () => {
    // Mock the React hooks
    const mockSetNodes = vi.fn();
    const mockSetEdges = vi.fn();
    const mockOnNodesChange = vi.fn();
    const mockOnEdgesChange = vi.fn();

    vi.mocked(ReactFlow.useNodesState).mockReturnValue([
      [] as any,
      mockSetNodes,
      mockOnNodesChange,
    ]);
    vi.mocked(ReactFlow.useEdgesState).mockReturnValue([
      [] as any,
      mockSetEdges,
      mockOnEdgesChange,
    ]);

    // Call the hook
    useFlowNodeAndEdgeGenerator(mockTriggers, mockNodesConfig);

    // Verify that getNodesAndEdges was called with the correct arguments
    expect(FlowDiagram.getNodesAndEdges).toHaveBeenCalledWith(mockTriggers, mockNodesConfig);
  });

  it('should use empty object as default when triggers are not provided', () => {
    // Mock the React hooks
    const mockSetNodes = vi.fn();
    const mockSetEdges = vi.fn();
    const mockOnNodesChange = vi.fn();
    const mockOnEdgesChange = vi.fn();

    vi.mocked(ReactFlow.useNodesState).mockReturnValue([
      [] as any,
      mockSetNodes,
      mockOnNodesChange,
    ]);
    vi.mocked(ReactFlow.useEdgesState).mockReturnValue([
      [] as any,
      mockSetEdges,
      mockOnEdgesChange,
    ]);

    // Call the hook without triggers
    useFlowNodeAndEdgeGenerator(undefined, mockNodesConfig);

    // Verify that getNodesAndEdges was called with an empty object for triggers
    expect(FlowDiagram.getNodesAndEdges).toHaveBeenCalledWith({}, mockNodesConfig);
  });

  it('should handle errors in getNodesAndEdges', () => {
    // Mock the React hooks
    const mockSetNodes = vi.fn();
    const mockSetEdges = vi.fn();
    const mockOnNodesChange = vi.fn();
    const mockOnEdgesChange = vi.fn();

    vi.mocked(ReactFlow.useNodesState).mockReturnValue([
      [] as any,
      mockSetNodes,
      mockOnNodesChange,
    ]);
    vi.mocked(ReactFlow.useEdgesState).mockReturnValue([
      [] as any,
      mockSetEdges,
      mockOnEdgesChange,
    ]);

    // Mock getNodesAndEdges to throw an error
    vi.mocked(FlowDiagram.getNodesAndEdges).mockImplementation(() => {
      throw new Error('Test error');
    });

    // Call the hook
    useFlowNodeAndEdgeGenerator(mockTriggers, mockNodesConfig);

    // Verify that the error was logged
    expect(console.log).toHaveBeenCalled();
  });

  it('should handle errors in getLayoutCalculateElk', async () => {
    // Mock the React hooks
    const mockSetNodes = vi.fn();
    const mockSetEdges = vi.fn();
    const mockOnNodesChange = vi.fn();
    const mockOnEdgesChange = vi.fn();

    vi.mocked(ReactFlow.useNodesState).mockReturnValue([
      [] as any,
      mockSetNodes,
      mockOnNodesChange,
    ]);
    vi.mocked(ReactFlow.useEdgesState).mockReturnValue([
      [] as any,
      mockSetEdges,
      mockOnEdgesChange,
    ]);

    // Mock getLayoutCalculateElk to reject
    vi.mocked(FlowDiagram.getLayoutCalculateElk).mockRejectedValue(new Error('Layout error'));

    // Call the hook
    useFlowNodeAndEdgeGenerator(mockTriggers, mockNodesConfig);

    // Wait for the promise to be rejected
    await new Promise(process.nextTick);

    // Verify that the error was logged
    expect(console.log).toHaveBeenCalled();
  });

  it('should process flow with looping nodes', () => {
    // Mock the React hooks
    const mockSetNodes = vi.fn();
    const mockSetEdges = vi.fn();
    const mockOnNodesChange = vi.fn();
    const mockOnEdgesChange = vi.fn();

    vi.mocked(ReactFlow.useNodesState).mockReturnValue([
      [] as any,
      mockSetNodes,
      mockOnNodesChange,
    ]);
    vi.mocked(ReactFlow.useEdgesState).mockReturnValue([
      [] as any,
      mockSetEdges,
      mockOnEdgesChange,
    ]);

    // Setup mocks for looping nodes
    vi.mocked(FlowDiagram.getNodesAndEdges).mockReturnValue(mockNodesWithLoopings);

    // Mock the layout calculation to immediately return data
    vi.mocked(FlowDiagram.getLayoutCalculateElk).mockImplementation(() => {
      return Promise.resolve(mockLayoutDataWithLoopings);
    });

    vi.mocked(FlowDiagram.getNodesForDiagram).mockReturnValue([
      { id: 'node-1', type: 'default', position: { x: 100, y: 100 }, data: {} },
      { id: 'looping-1', type: 'default', position: { x: 200, y: 200 }, data: {} },
      { id: 'child-1', type: 'default', position: { x: 250, y: 250 }, data: {} },
    ]);
    vi.mocked(FlowDiagram.getEdgesForDiagram).mockReturnValue([
      { id: 'edge-1', source: 'trigger-1', target: 'node-1' },
      { id: 'edge-2', source: 'looping-1', target: 'child-1' },
    ]);

    // Mock getBoundingRectOfNodes to return specific values
    vi.mocked(FlowDiagram.getBoundingRectOfNodes).mockReturnValue({
      top: 100,
      bottom: 300,
      left: 100,
      right: 300,
    });

    // Mock useEffect to immediately call the callback
    vi.mocked(React.useEffect).mockImplementation((cb) => {
      cb();
      return () => {};
    });

    // Call the hook
    useFlowNodeAndEdgeGenerator(mockTriggers, mockNodesConfig);

    // Verify that the functions were called
    expect(FlowDiagram.getNodesAndEdges).toHaveBeenCalled();
    expect(FlowDiagram.getLayoutCalculateElk).toHaveBeenCalled();
  });

  it('should handle case when looping node is not found', async () => {
    // Mock the React hooks
    const mockSetNodes = vi.fn();
    const mockSetEdges = vi.fn();
    const mockOnNodesChange = vi.fn();
    const mockOnEdgesChange = vi.fn();

    vi.mocked(ReactFlow.useNodesState).mockReturnValue([
      [] as any,
      mockSetNodes,
      mockOnNodesChange,
    ]);
    vi.mocked(ReactFlow.useEdgesState).mockReturnValue([
      [] as any,
      mockSetEdges,
      mockOnEdgesChange,
    ]);

    // Setup mocks for looping nodes with a non-existent looping node
    const mockNodesWithMissingLooping = {
      ...mockNodesWithLoopings,
      loopings: [
        {
          loopId: 'non-existent-looping',
          loopSubNodes: [
            {
              id: 'child-1',
              name: FlowNodeType.EmptyNode,
              displayName: 'Child 1',
              description: 'Child node',
              settings: {} as FlowNodeSetting,
              parent: 'non-existent-looping',
            } as FlowNodeData,
          ],
        },
      ],
    };

    vi.mocked(FlowDiagram.getNodesAndEdges).mockReturnValue(mockNodesWithMissingLooping);
    vi.mocked(FlowDiagram.getLayoutCalculateElk).mockResolvedValue(mockLayoutDataWithLoopings);

    // Call the hook
    useFlowNodeAndEdgeGenerator(mockTriggers, mockNodesConfig);

    // Wait for the promise to resolve
    await new Promise(process.nextTick);

    // Verify that the nodes and edges were set without error
    expect(mockSetNodes).toHaveBeenCalled();
    expect(mockSetEdges).toHaveBeenCalled();
  });

  it('should handle nodeIdToOrderNumber changes correctly', () => {
    // Mock the React hooks
    const mockSetNodes = vi.fn();
    const mockSetEdges = vi.fn();
    const mockOnNodesChange = vi.fn();
    const mockOnEdgesChange = vi.fn();
    const mockSetNodeIdToOrderNumber = vi.fn();

    vi.mocked(ReactFlow.useNodesState).mockReturnValue([
      [] as any,
      mockSetNodes,
      mockOnNodesChange,
    ]);
    vi.mocked(ReactFlow.useEdgesState).mockReturnValue([
      [] as any,
      mockSetEdges,
      mockOnEdgesChange,
    ]);

    // Mock useState to capture the setter function
    vi.mocked(React.useState).mockReturnValue([{}, mockSetNodeIdToOrderNumber]);

    // Setup mock data with nodeIdToOrderNumber
    const mockNodesAndEdgesWithOrder = {
      ...mockNodesAndEdges,
      nodeIdToOrderNumber: { 'node-1': 1, 'node-2': 2 },
    };

    vi.mocked(FlowDiagram.getNodesAndEdges).mockReturnValue(mockNodesAndEdgesWithOrder);

    // Call the hook
    useFlowNodeAndEdgeGenerator(mockTriggers, mockNodesConfig);

    // Verify that setNodeIdToOrderNumber was called
    expect(mockSetNodeIdToOrderNumber).toHaveBeenCalled();

    // Get the callback function passed to setNodeIdToOrderNumber
    const setterCallback = mockSetNodeIdToOrderNumber.mock.calls[0][0];

    // Test the callback with different scenarios
    // Scenario 1: Different lengths - should return new nodeIdToOrderNumber
    const prevState1 = { 'node-1': 1 };
    const newState1 = { 'node-1': 1, 'node-2': 2 };
    const result1 = setterCallback(prevState1);
    expect(result1).toEqual(newState1);

    // Scenario 2: Same length but different values - should return new nodeIdToOrderNumber
    const prevState2 = { 'node-1': 1, 'node-2': 1 };
    const result2 = setterCallback(prevState2);
    expect(result2).toEqual(newState1);

    // Scenario 3: Same values - should return previous state
    const prevState3 = { 'node-1': 1, 'node-2': 2 };
    const result3 = setterCallback(prevState3);
    expect(result3).toEqual(prevState3);
  });

  it('should handle empty nodeIdToOrderNumber objects', () => {
    // Mock the React hooks
    const mockSetNodes = vi.fn();
    const mockSetEdges = vi.fn();
    const mockOnNodesChange = vi.fn();
    const mockOnEdgesChange = vi.fn();
    const mockSetNodeIdToOrderNumber = vi.fn();

    vi.mocked(ReactFlow.useNodesState).mockReturnValue([
      [] as any,
      mockSetNodes,
      mockOnNodesChange,
    ]);
    vi.mocked(ReactFlow.useEdgesState).mockReturnValue([
      [] as any,
      mockSetEdges,
      mockOnEdgesChange,
    ]);

    // Mock useState to capture the setter function
    vi.mocked(React.useState).mockReturnValue([{}, mockSetNodeIdToOrderNumber]);

    // Setup mock data with empty nodeIdToOrderNumber
    const mockNodesAndEdgesEmpty = {
      ...mockNodesAndEdges,
      nodeIdToOrderNumber: {},
    };

    vi.mocked(FlowDiagram.getNodesAndEdges).mockReturnValue(mockNodesAndEdgesEmpty);

    // Call the hook
    useFlowNodeAndEdgeGenerator(mockTriggers, mockNodesConfig);

    // Verify that setNodeIdToOrderNumber was called
    expect(mockSetNodeIdToOrderNumber).toHaveBeenCalled();

    // Get the callback function passed to setNodeIdToOrderNumber
    const setterCallback = mockSetNodeIdToOrderNumber.mock.calls[0][0];

    // Test with empty objects
    const prevState = {};
    const result = setterCallback(prevState);
    expect(result).toEqual(prevState);
  });

  it('should handle undefined nodesConfig parameter', () => {
    // Mock the React hooks
    const mockSetNodes = vi.fn();
    const mockSetEdges = vi.fn();
    const mockOnNodesChange = vi.fn();
    const mockOnEdgesChange = vi.fn();

    vi.mocked(ReactFlow.useNodesState).mockReturnValue([
      [] as any,
      mockSetNodes,
      mockOnNodesChange,
    ]);
    vi.mocked(ReactFlow.useEdgesState).mockReturnValue([
      [] as any,
      mockSetEdges,
      mockOnEdgesChange,
    ]);

    // Call the hook with undefined nodesConfig
    useFlowNodeAndEdgeGenerator(mockTriggers, undefined);

    // Verify that getNodesAndEdges was called with undefined for nodesConfig
    expect(FlowDiagram.getNodesAndEdges).toHaveBeenCalledWith(mockTriggers, undefined);
  });

  it('should handle successful layout calculation with data', async () => {
    // Mock the React hooks
    const mockSetNodes = vi.fn();
    const mockSetEdges = vi.fn();
    const mockOnNodesChange = vi.fn();
    const mockOnEdgesChange = vi.fn();

    vi.mocked(ReactFlow.useNodesState).mockReturnValue([
      [] as any,
      mockSetNodes,
      mockOnNodesChange,
    ]);
    vi.mocked(ReactFlow.useEdgesState).mockReturnValue([
      [] as any,
      mockSetEdges,
      mockOnEdgesChange,
    ]);

    // Mock successful layout calculation
    vi.mocked(FlowDiagram.getLayoutCalculateElk).mockResolvedValue(mockLayoutData);

    // Call the hook
    useFlowNodeAndEdgeGenerator(mockTriggers, mockNodesConfig);

    // Wait for the promise to resolve
    await new Promise(process.nextTick);

    // Verify that layout functions were called
    expect(FlowDiagram.getLayoutCalculateElk).toHaveBeenCalled();
    expect(FlowDiagram.getNodesForDiagram).toHaveBeenCalled();
    expect(FlowDiagram.getEdgesForDiagram).toHaveBeenCalled();
    expect(mockSetNodes).toHaveBeenCalled();
    expect(mockSetEdges).toHaveBeenCalled();
  });

  it('should handle layout calculation returning null data', async () => {
    // Mock the React hooks
    const mockSetNodes = vi.fn();
    const mockSetEdges = vi.fn();
    const mockOnNodesChange = vi.fn();
    const mockOnEdgesChange = vi.fn();

    vi.mocked(ReactFlow.useNodesState).mockReturnValue([
      [] as any,
      mockSetNodes,
      mockOnNodesChange,
    ]);
    vi.mocked(ReactFlow.useEdgesState).mockReturnValue([
      [] as any,
      mockSetEdges,
      mockOnEdgesChange,
    ]);

    // Mock layout calculation returning null
    vi.mocked(FlowDiagram.getLayoutCalculateElk).mockResolvedValue(null);

    // Call the hook
    useFlowNodeAndEdgeGenerator(mockTriggers, mockNodesConfig);

    // Wait for the promise to resolve
    await new Promise(process.nextTick);

    // Verify that setNodes and setEdges were not called when data is null
    expect(FlowDiagram.getLayoutCalculateElk).toHaveBeenCalled();
    // Since data is null, the nodes and edges should not be set
    expect(mockSetNodes).not.toHaveBeenCalled();
    expect(mockSetEdges).not.toHaveBeenCalled();
  });

  it('should handle empty loopings array', async () => {
    // Mock the React hooks
    const mockSetNodes = vi.fn();
    const mockSetEdges = vi.fn();
    const mockOnNodesChange = vi.fn();
    const mockOnEdgesChange = vi.fn();

    vi.mocked(ReactFlow.useNodesState).mockReturnValue([
      [] as any,
      mockSetNodes,
      mockOnNodesChange,
    ]);
    vi.mocked(ReactFlow.useEdgesState).mockReturnValue([
      [] as any,
      mockSetEdges,
      mockOnEdgesChange,
    ]);

    // Setup mock data with empty loopings
    const mockNodesAndEdgesEmptyLoopings = {
      ...mockNodesAndEdges,
      loopings: [],
    };

    vi.mocked(FlowDiagram.getNodesAndEdges).mockReturnValue(mockNodesAndEdgesEmptyLoopings);
    vi.mocked(FlowDiagram.getLayoutCalculateElk).mockResolvedValue(mockLayoutData);

    // Call the hook
    useFlowNodeAndEdgeGenerator(mockTriggers, mockNodesConfig);

    // Wait for the promise to resolve
    await new Promise(process.nextTick);

    // Verify that nodes and edges were set without looping frame processing
    expect(mockSetNodes).toHaveBeenCalled();
    expect(mockSetEdges).toHaveBeenCalled();
    expect(FlowDiagram.getBoundingRectOfNodes).not.toHaveBeenCalled();
  });
});

describe('useHideFlowAttribution', () => {
  it('should hide the React Flow attribution', () => {
    // Mock the window and document
    const mockElement = { style: { display: '' } };
    const mockQuerySelector = vi.fn().mockReturnValue(mockElement);
    const mockSetInterval = vi.fn().mockReturnValue(123);
    const mockClearInterval = vi.fn();

    // Setup the mocks
    Object.defineProperty(global, 'window', {
      value: {
        document: {
          querySelector: mockQuerySelector,
        },
      },
      writable: true,
    });

    // Mock setInterval and clearInterval
    global.setInterval = mockSetInterval as any;
    global.clearInterval = mockClearInterval as any;

    // Call the hook
    useHideFlowAttribution();

    // Get the interval callback
    const intervalCallback = mockSetInterval.mock.calls[0][0];

    // Call the interval callback
    intervalCallback();

    // Verify that querySelector was called with the correct selector
    expect(mockQuerySelector).toHaveBeenCalledWith('.react-flow__attribution');

    // Verify that the element's display style was set to 'none'
    expect(mockElement.style.display).toBe('none');
  });

  it('should handle case when element is not found', () => {
    // Mock the window and document with no element found
    const mockQuerySelector = vi.fn().mockReturnValue(null);
    const mockSetInterval = vi.fn().mockReturnValue(123);
    const mockClearInterval = vi.fn();

    // Setup the mocks
    Object.defineProperty(global, 'window', {
      value: {
        document: {
          querySelector: mockQuerySelector,
        },
      },
      writable: true,
    });

    // Mock setInterval and clearInterval
    global.setInterval = mockSetInterval as any;
    global.clearInterval = mockClearInterval as any;

    // Call the hook
    useHideFlowAttribution();

    // Get the interval callback
    const intervalCallback = mockSetInterval.mock.calls[0][0];

    // Call the interval callback
    intervalCallback();

    // Verify that querySelector was called with the correct selector
    expect(mockQuerySelector).toHaveBeenCalledWith('.react-flow__attribution');
    // No error should be thrown
  });

  it('should clear interval on cleanup', () => {
    // Mock the window and document
    const mockQuerySelector = vi.fn();
    const mockSetInterval = vi.fn().mockReturnValue(123);
    const mockClearInterval = vi.fn();

    // Setup the mocks
    Object.defineProperty(global, 'window', {
      value: {
        document: {
          querySelector: mockQuerySelector,
        },
      },
      writable: true,
    });

    // Mock setInterval and clearInterval
    global.setInterval = mockSetInterval as any;
    global.clearInterval = mockClearInterval as any;

    // Mock useEffect to capture and call the cleanup function
    let cleanupFn: () => void = () => {};

    // Override the useEffect implementation just for this test
    const originalUseEffect = vi.spyOn(React, 'useEffect');
    const mockUseEffect = vi.fn().mockImplementation((fn) => {
      cleanupFn = fn() as () => void;
      return undefined;
    });
    originalUseEffect.mockImplementation(mockUseEffect);

    // Call the hook
    useHideFlowAttribution();

    // Call the cleanup function
    cleanupFn();

    // Verify that clearInterval was called with the correct ID
    expect(mockClearInterval).toHaveBeenCalledWith(123);
  });
});
