import { COMMON_HEIGHT_NODE } from '@/components/FlowBuilder/constants';
import { FlowDiagram } from '@/helpers/flowDiagram';
import { useEffect, useRef, useState } from 'react';
import { type FlowNodes, FlowNodeType } from '@/models/flow';
import { type Edge, type Node, useEdgesState, useNodesState } from '@xyflow/react';

const INITIAL_TRIGGER_NODE = {} as FlowNodes;

/**
 * Optimized function to check if node ID to order number mapping has changed
 * Uses early exit strategy and minimizes object key iterations
 */
const checkIsNodeIdToNumberChanges = (
  prevNodeIdToOrderNumber: Record<string, number>,
  nextNodeIdToOrderNumber: Record<string, number>
): boolean => {
  // Get keys once and cache them
  const prevKeys = Object.keys(prevNodeIdToOrderNumber);
  const nextKeys = Object.keys(nextNodeIdToOrderNumber);

  // Quick length check first
  if (prevKeys.length !== nextKeys.length) {
    return true;
  }

  // Early exit if both are empty
  if (prevKeys.length === 0) {
    return false;
  }

  // Check each key with early exit on first difference
  for (const key of prevKeys) {
    if (prevNodeIdToOrderNumber[key] !== nextNodeIdToOrderNumber[key]) {
      return true;
    }
  }

  return false;
};

/**
 * Optimized comparison function for FlowNodes objects
 * Uses early exit strategy and minimizes object key iterations
 */
const areFlowNodesEqual = (oldNodes: FlowNodes, newNodes: FlowNodes): boolean => {
  // Get keys once and cache them
  const oldKeys = Object.keys(oldNodes);
  const newKeys = Object.keys(newNodes);

  // Quick length check first
  if (oldKeys.length !== newKeys.length) {
    return false;
  }

  // Early exit if both are empty
  if (oldKeys.length === 0) {
    return true;
  }

  // Check each node with early exit on first difference
  for (const key of oldKeys) {
    const oldNode = oldNodes[key];
    const newNode = newNodes[key];

    // If new node doesn't exist, they're different
    if (!newNode) {
      return false;
    }

    // Compare critical properties with early exit
    if (
      oldNode?.name !== newNode?.name ||
      oldNode?.displayName !== newNode?.displayName ||
      oldNode?.icon !== newNode?.icon
    ) {
      return false;
    }
  }

  return true;
};

function useFlowNodeAndEdgeGenerator(triggers?: FlowNodes, nodesConfig?: FlowNodes) {
  const [nodes, setNodes] = useNodesState<Node>([]);
  const [edges, setEdges] = useEdgesState<Edge>([]);
  const [nodeIdToOrderNumber, setNodeIdToOrderNumber] = useState<Record<string, number>>({});
  // a ref to save old triggers and nodesConfig
  const oldTriggersRef = useRef<FlowNodes>({});
  const oldNodesConfigRef = useRef<FlowNodes>({});

  useHideFlowAttribution();

  useEffect(() => {
    const triggerNodes = triggers ?? INITIAL_TRIGGER_NODE;
    // compare old triggers and nodesConfig with new one to check if we need to update the diagram
    // by comparing the number of triggers, nodesConfig
    // by comparing the type, displayName, icon of each node
    if (
      oldTriggersRef.current &&
      oldNodesConfigRef.current &&
      areFlowNodesEqual(oldTriggersRef.current, triggerNodes) &&
      areFlowNodesEqual(oldNodesConfigRef.current, nodesConfig ?? {})
    ) {
      return;
    }

    oldTriggersRef.current = triggerNodes;
    oldNodesConfigRef.current = nodesConfig ?? {};

    try {
      const { nodes, edges, loopings, nodeIdToOrderNumber } = FlowDiagram.getNodesAndEdges(
        triggerNodes,
        nodesConfig
      );

      setNodeIdToOrderNumber((pre) => {
        if (checkIsNodeIdToNumberChanges(pre, nodeIdToOrderNumber)) {
          return nodeIdToOrderNumber;
        }
        return pre;
      });

      const layoutCalculatedData = async () =>
        await FlowDiagram.getLayoutCalculateElk(nodes, edges, loopings);
      layoutCalculatedData()
        .then((data) => {
          if (data) {
            const nodesForDiagram = FlowDiagram.getNodesForDiagram(
              nodes,
              data.nodes
            ) as Node<any>[];
            const edgesForDiagram = FlowDiagram.getEdgesForDiagram(
              edges,
              data.edges
            ) as Edge<any>[];

            if (loopings?.length > 0) {
              // Add some looping frame node to cover looping node and its children
              loopings.forEach((looping) => {
                const loopNode = nodesForDiagram.find((node) => node.id === looping.loopId);
                const nodeIds = looping.loopSubNodes.map((node) => node.id);
                const nodeData = nodesForDiagram.filter((node) => nodeIds?.includes(node.id));
                if (!loopNode) return;
                const { top, bottom, left, right } = FlowDiagram.getBoundingRectOfNodes([
                  loopNode,
                  ...nodeData,
                ]);

                // Add a frame to cover looping node and its children
                nodesForDiagram.push({
                  id: FlowDiagram.getCompoundNodeId(looping.loopId),
                  type: FlowNodeType.LoopingFrame,
                  position: { x: left - 10, y: top + COMMON_HEIGHT_NODE / 2 },
                  data: {
                    width: right - left + 20,
                    height: bottom - top,
                  },
                  width: right - left + 20,
                  height: bottom - top + 20,
                  zIndex: -1,
                  draggable: true,
                });
              });
            }
            setNodes(nodesForDiagram);
            setEdges(edgesForDiagram);
          }
        })
        .catch((error) => {
          console.log('PARSING ERROR', { error });
        });
    } catch (error) {
      console.log({ error });
    }
  }, [triggers, nodesConfig]);
  return {
    nodes,
    edges,
    nodeIdToOrderNumber,
    setNodes,
  };
}

export const useHideFlowAttribution = () => {
  // Hide React Flow attribution
  useEffect(() => {
    const timeout = setInterval(() => {
      if (window) {
        const element = window.document.querySelector('.react-flow__attribution') as HTMLDivElement;
        if (element) {
          element.style.display = 'none';
        }
      }
    }, 1000);
    return () => clearInterval(timeout);
  }, []);
};

export default useFlowNodeAndEdgeGenerator;
