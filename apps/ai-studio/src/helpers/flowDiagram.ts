import { COMMON_HEIGHT_NODE, COMMON_WIDTH_NODE } from '@/components/FlowBuilder/constants';
import {
  type FlowNodes,
  TypeEdge,
  FlowNodeType,
  type FlowRouteByIntentSetting,
  type FlowNodeData,
} from '@/models/flow';
import { type Edge, MarkerType, type Node } from '@xyflow/react';
import ELK, { type ElkExtendedEdge, type ElkNode } from 'elkjs';
import { ulid } from 'ulid';

class Stack<T> {
  stack: T[];

  constructor() {
    this.stack = [];
  }

  push(item) {
    this.stack.push(item);
  }

  pop() {
    return this.stack.pop();
  }

  peek() {
    return this.stack[this.stack.length - 1];
  }

  isEmpty() {
    return this.stack.length === 0;
  }

  size() {
    return this.stack.length;
  }

  clear() {
    this.stack = [];
  }

  print() {
    return [...this.stack];
  }
}

type LoopingsData = {
  currentLoopingId: string; // for checking looping node when generating node.
  loopingData: {
    loopId: string; // for storing looping id
    loopSubNodes: FlowNodeData[];
  }[];
  callStackNode: Stack<any>;
};

const position = { x: 0, y: 0 };

const elk = new ELK();

const getDimensionNodeByType = (type: FlowNodeType) => {
  if (type === FlowNodeType.AddNode)
    return {
      width: 260,
      height: 24,
    };
  return {
    width: 260, // Normal/common node width
    height: 62, // Normal/common node height
  };
};

const getCompoundNodeId = (loopId: string) => `${loopId}-${FlowNodeType.LoopingFrame}`;
const getCompoundExitNodeId = (loopId: string) => `${loopId}-compound-exit-id`;

function calculateLayoutElk(nodes: Node[], edges: ElkExtendedEdge[]) {
  const nodesForElk = nodes.map((node) => {
    if (node.type === FlowNodeType.LoopingFrame) {
      const compoundNode = node as any;
      return {
        id: compoundNode.id,
        edges: compoundNode.edges,
        layoutOptions: {
          'elk.spacing.nodeNode': '75',
          'elk.spacing.edgeNode': '75',
          hierarchyHandling: 'INCLUDE_CHILDREN',
          'elk.layered.spacing.edgeNodeBetweenLayers': '30',
          'elk.layered.spacing.nodeNodeBetweenLayers': '40',
          'elk.layered.nodePlacement.bk.fixedAlignment': 'BALANCED',
          'elk.layered.considerModelOrder.strategy': 'NODES_AND_EDGES',
        },
        children: compoundNode.children.map((child) => {
          const { width, height } = getDimensionNodeByType(child.type as FlowNodeType);
          return {
            id: child.id,
            width: width,
            height: height,
          };
        }),
      };
    }
    const { width, height } = getDimensionNodeByType(node.type as FlowNodeType);
    return {
      id: node.id,
      width: width,
      height: height,
    };
  });

  const graph: ElkNode = {
    id: 'root',
    layoutOptions: {
      'elk.direction': 'DOWN',
      'elk.algorithm': 'layered',
      'elk.spacing.nodeNode': '75',
      'elk.spacing.edgeNode': '75',
      'elk.radial.centerOnRoot': 'true',
      'elk.spacing.componentComponent': '75',
      'elk.hierarchyHandling': 'INCLUDE_CHILDREN',
      'elk.layered.spacing.edgeNodeBetweenLayers': '30',
      'elk.layered.spacing.nodeNodeBetweenLayers': '40',
      'elk.layered.nodePlacement.bk.fixedAlignment': 'BALANCED',
      'elk.layered.considerModelOrder.strategy': 'NODES_AND_EDGES',
    },

    children: nodesForElk,
    edges: edges,
  };
  return elk.layout(graph);
}

const getLayoutCalculateElk = async (nodes, edges, loopings: LoopingsData['loopingData']) => {
  let newNodes = [...nodes];
  let newEdges = [...edges];
  const compoundNodeIds: string[] = [];
  const compoundExitNodeIds: string[] = [];
  if (loopings?.length) {
    loopings.forEach((looping) => {
      const loopNode = nodes.find((node) => node.id === looping.loopId);
      const nodesAndLoopNodeIds = [loopNode.id, ...looping.loopSubNodes.map((node) => node.id)];
      const loopAndItsSubNodes = [loopNode, ...looping.loopSubNodes];
      const relatedAddNodes = nodes.filter(
        (node) =>
          nodesAndLoopNodeIds.includes(node.data?.parentNodeId) &&
          !node?.data?.directNextNodeLooping
      );
      const compoundExitIdNode = getCompoundExitNodeId(looping.loopId);
      compoundExitNodeIds.push(compoundExitIdNode);
      const nodesInsideLoop = [
        ...loopAndItsSubNodes,
        ...relatedAddNodes,
        {
          id: compoundExitIdNode,
          width: 0,
          height: 0,
        },
      ];
      const leafNodes = relatedAddNodes.filter((node) => {
        return !node.nextNodeId && node.type !== FlowNodeType.AddPathNode;
      });

      const nodesInsideLoopIds = nodesInsideLoop.map((node) => node.id);
      const edgesInsideLoop = edges.filter(
        (edge) =>
          nodesInsideLoopIds.includes(edge.source) && nodesInsideLoopIds.includes(edge.target)
      );

      // create edges from leaf nodes to compound exit node
      edgesInsideLoop.push(
        ...leafNodes.map((leafNode) => {
          const edgeFromLeafNodeToCompoundExit = {
            id: `${leafNode.id}-${compoundExitIdNode}`,
            source: leafNode.id,
            target: compoundExitIdNode,
            type: 'step',
            data: { actualParentNodeId: leafNode.id },
            parentId: leafNode.id,
            selectable: false,
            focusable: false,
          };
          return edgeFromLeafNodeToCompoundExit;
        })
      );

      // remove all nodes/edges belong to looping node in the newNodes/newEdges.
      newNodes = newNodes.filter((node) => !nodesInsideLoopIds.includes(node.id));
      newEdges = newEdges.filter(
        (edge) =>
          !(nodesInsideLoopIds.includes(edge.source) && nodesInsideLoopIds.includes(edge.target))
      );

      // Create compound node
      const newCompoundNodeId = getCompoundNodeId(looping.loopId);
      compoundNodeIds.push(newCompoundNodeId);
      const loopCompoundNode = {
        id: newCompoundNodeId,
        type: FlowNodeType.LoopingFrame,
        children: nodesInsideLoop,
        edges: edgesInsideLoop,
      };
      newNodes.push(loopCompoundNode);
      const addNodeNextOfLoop = newNodes.find(
        (node) => node.parentNodeId === loopNode.id && node.data?.directNextNodeLooping
      );

      if (addNodeNextOfLoop) {
        newNodes = newNodes.filter((node) => node.id !== addNodeNextOfLoop.id);
        addNodeNextOfLoop.parentNodeId = newCompoundNodeId;
        newNodes.push(addNodeNextOfLoop);

        const edgeFromLoopNodeToAddNodeNextOfLoop = newEdges.find(
          (edge) => edge.source === loopNode.id && edge.target === addNodeNextOfLoop.id
        );
        edgeFromLoopNodeToAddNodeNextOfLoop.container = 'root';
        edgeFromLoopNodeToAddNodeNextOfLoop.source = compoundExitIdNode;

        newEdges = newEdges.filter((edge) => edge.id !== edgeFromLoopNodeToAddNodeNextOfLoop.id);
        newEdges.push(edgeFromLoopNodeToAddNodeNextOfLoop);
      }
    });
  }

  try {
    const data = await calculateLayoutElk(newNodes, newEdges);
    if (!data) return null;
    const compoundNodes = data?.children!.filter((node) => compoundNodeIds.includes(node.id));
    let nodes: any[] = [];
    let edges: any[] = data.edges ?? [];
    // update children compound's nodes position
    compoundNodes.forEach((compoundNode) => {
      compoundNode?.children?.forEach((child) => {
        child.x = (child?.x ?? 0) + (compoundNode?.x ?? 0);
        child.y = (child?.y ?? 0) + (compoundNode?.y ?? 0);
      });
    });
    data?.children?.forEach((node) => {
      if (compoundNodes.find((compoundNode) => compoundNode.id === node.id)) {
        nodes.push(...(node?.children ?? []));
        edges.push(...(node?.edges ?? []));
      } else {
        nodes.push(node);
      }
    });

    // remove all nodes which are compound exit nodes
    nodes = nodes.filter((node) => !compoundExitNodeIds.includes(node.id));
    // remove all edges which are leaf nodes connected to compound exit nodes
    edges = edges.filter((edge) => !compoundExitNodeIds.includes(edge.target));
    // update all edges which are connected to compound exit nodes to be connected to the looping frame node
    edges.forEach((edge) => {
      if (compoundExitNodeIds.includes(edge.source)) {
        const loopId = edge.source.split('-')[0];
        edge.source = getCompoundNodeId(loopId);
        const targetNodeOfEdge = nodes.find((node) => node.id === edge.target);
        if (targetNodeOfEdge) {
          targetNodeOfEdge.y = targetNodeOfEdge.y - 60;
        }
      }
    });
    return {
      nodes,
      edges,
      compoundNodes,
    };
  } catch (e) {
    console.log(e);
    return null;
  }
};

const getTriggerNodeData = (
  triggerId: string,
  configData: FlowNodeData,
  globalCount: { count: number }
) => {
  return {
    id: triggerId,
    type: configData.name || FlowNodeType.NewTrigger,
    data: {
      ...configData,
      orderedNumber: ++globalCount.count,
      isTriggerNode: true, // determine this is trigger node
    },
    position,
    parentNodeId: 'root',
    actualParentNodeId: 'root',
  };
};

const getSubPathNode = (
  id: string,
  parentId: string,
  actualParentNodeId: string,
  nextNodeId: string,
  globalCount: { count: number }
) => {
  return {
    id,
    type: FlowNodeType.SubPathNode,
    data: {
      nextNodeId,
      parentNodeId: parentId,
      orderedNumber: ++globalCount.count,
      actualParentNodeId: actualParentNodeId,
    },
    position,
    parentNodeId: parentId,
    actualParentNodeId: actualParentNodeId,
  } as Node;
};

const getNodeData = (
  node: FlowNodeData,
  parentId: string,
  actualParentNodeId: string,
  globalCount: { count: number }
) => {
  return {
    id: node.id,
    type: node.name,
    data: {
      ...node.settings,
      icon: node.icon,
      description: node.description,
      displayName: node.displayName,
      orderedNumber: ++globalCount.count,
      parentNodeId: parentId,
      actualParentNodeId: actualParentNodeId,
      action: node.action,
    },
    position,
    parentNodeId: parentId,
    actualParentNodeId: actualParentNodeId,
  };
};

const getAddNewStepNode = (
  parentId: string,
  config?: {
    name?: FlowNodeType;
    parentNodeId?: string;
    nextNodeId?: string;
    directSubNodeLooping?: boolean;
    directNextNodeLooping?: boolean;
    directAddNodeFromTrigger?: boolean;
    [key: string]: any;
  }
) => {
  const type = config?.name ?? FlowNodeType.AddNode;
  const nextNodeId = config?.nextNodeId ?? '';
  const parentNodeId = config?.parentNodeId ?? '';
  const directSubNodeLooping = config?.directSubNodeLooping ?? false;
  const directNextNodeLooping = config?.directNextNodeLooping ?? false;
  return {
    id: ulid(),
    type: type,
    data: { ...config, nextNodeId, parentNodeId, directSubNodeLooping, directNextNodeLooping },
    position,
    parentNodeId: parentId,
    actualParentNodeId: parentId,
    nextNodeId: nextNodeId,
  } as Node;
};

const getEdgeData = (
  sourceId: string,
  targetId: string,
  actualParentNodeId: string,
  typeEdge: TypeEdge
) => {
  const edge: Edge = {
    id: `${sourceId}___${targetId}`,
    source: sourceId,
    target: targetId,
    type: 'step',
    data: { actualParentNodeId },
    selectable: false,
    focusable: false,
    style: {
      strokeWidth: 2,
      color: '#C6C7D7',
    },
    markerEnd: undefined,
  };
  if (typeEdge === TypeEdge.Arrow) {
    edge.markerEnd = {
      type: typeEdge === TypeEdge.Arrow ? MarkerType.Arrow : MarkerType.ArrowClosed,
    };
  }

  return edge;
};

const getNodesAndEdgesBehindNode = (
  currNode: FlowNodeData,
  originalFlowNodes: FlowNodes,
  parentId: string,
  actualParentNodeId: string,
  checkedNodeIds: Set<string>,
  globalCount: { count: number },
  loopings: LoopingsData
) => {
  const nodes: any[] = [];
  const edges: any[] = [];
  checkedNodeIds.add(currNode.id);

  const nodeData = getNodeData(currNode, parentId, actualParentNodeId, globalCount);
  const edgeFromTopToNode = getEdgeData(parentId, nodeData.id, actualParentNodeId, TypeEdge.Arrow);

  nodes.push(nodeData);
  edges.push(edgeFromTopToNode);

  if (loopings.currentLoopingId) {
    loopings.loopingData
      .find((item) => item.loopId === loopings.currentLoopingId)
      ?.loopSubNodes.push(currNode);
  }

  if (currNode.name === FlowNodeType.Branch || currNode.name === FlowNodeType.Path) {
    const currentAddNode = getAddNewStepNode(nodeData.id, {
      name: FlowNodeType.AddPathNode,
      parentNodeId: nodeData.id,
    });
    const edgeFromNodeToAddNode = getEdgeData(
      nodeData.id,
      currentAddNode.id,
      currNode.id,
      TypeEdge.OnlyLine
    );
    nodes.push(currentAddNode);
    edges.push(edgeFromNodeToAddNode);

    const branches = (currNode.settings as FlowRouteByIntentSetting).paths;
    branches.forEach((branch) => {
      const { nodes: nodesFromBranch, edges: edgesFromBranch } = getNodesAndEdgesFromBranch(
        branch.id,
        branch?.next,
        originalFlowNodes,
        currentAddNode.id,
        nodeData.id,
        checkedNodeIds,
        globalCount,
        loopings
      );
      nodes.push(...nodesFromBranch);
      edges.push(...edgesFromBranch);
    });
  } else if (currNode.name === FlowNodeType.Looping) {
    const next = currNode?.next ?? '';
    const nextLoopNodeId = currNode?.settings?.nextChildID ?? '';
    const nextNode = originalFlowNodes?.[next];
    const nextNodeInsideLoop = originalFlowNodes?.[nextLoopNodeId];

    const addStepNodeInsideLoop = getAddNewStepNode(nodeData.id, {
      parentNodeId: nodeData.id,
      nextNodeId: nextLoopNodeId,
      directSubNodeLooping: true,
    });
    const edgeFromNodeToAddNodeInsideLoop = getEdgeData(
      nodeData.id,
      addStepNodeInsideLoop.id,
      nodeData.id,
      TypeEdge.OnlyLine
    );

    const addStepNodeNextOfLoop = getAddNewStepNode(nodeData.id, {
      nextNodeId: next,
      parentNodeId: nodeData.id, // this gonna change to looping frame's id after post-calculating
      directNextNodeLooping: true,
      actualParentNodeId: nodeData.id,
    });

    const edgeFromNodeToAddNodeNextOfLoop = getEdgeData(
      nodeData.id,
      addStepNodeNextOfLoop.id,
      nodeData.id,
      TypeEdge.OnlyLine
    );

    nodes.push(...[addStepNodeInsideLoop, addStepNodeNextOfLoop]);
    edges.push(...[edgeFromNodeToAddNodeInsideLoop, edgeFromNodeToAddNodeNextOfLoop]);

    loopings.currentLoopingId = currNode.id;

    loopings.callStackNode.clear();
    loopings.callStackNode.push(currNode.id);

    loopings.loopingData.push({
      loopId: currNode.id,
      loopSubNodes: [],
    });

    if (nextNodeInsideLoop) {
      loopings.callStackNode.push(nextNodeInsideLoop.id);
      const { nodes: nodesBehindNode, edges: edgesFromBehindNode } = getNodesAndEdgesBehindNode(
        nextNodeInsideLoop,
        originalFlowNodes,
        addStepNodeInsideLoop.id,
        currNode.id,
        checkedNodeIds,
        globalCount,
        loopings
      );
      nodes.push(...nodesBehindNode);
      edges.push(...edgesFromBehindNode);
      loopings.callStackNode.pop();
    }
    if (nextNode) {
      loopings.currentLoopingId = ''; // we just set it to ''  to next call to check the looping node.
      loopings.callStackNode.clear();
      const { nodes: nodesBehindNode, edges: edgesFromBehindNode } = getNodesAndEdgesBehindNode(
        nextNode,
        originalFlowNodes,
        addStepNodeNextOfLoop.id,
        currNode.id,
        checkedNodeIds,
        globalCount,
        loopings
      );
      nodes.push(...nodesBehindNode);
      edges.push(...edgesFromBehindNode);
    }

    loopings.callStackNode.pop();
    if (loopings.callStackNode.isEmpty()) {
      loopings.currentLoopingId = '';
    }
  } else {
    const next = currNode?.next ?? '';
    const addStepNode = getAddNewStepNode(nodeData.id, {
      parentNodeId: nodeData.id,
      nextNodeId: next,
    });
    const edgeFromNodeToAddNode = getEdgeData(
      nodeData.id,
      addStepNode.id,
      nodeData.id,
      TypeEdge.OnlyLine
    );
    nodes.push(addStepNode);
    edges.push(edgeFromNodeToAddNode);
    const nextNode = originalFlowNodes?.[next];
    if (nextNode) {
      loopings.callStackNode.push(nextNode.id);
      const { nodes: nodesBehindNode, edges: edgesFromBehindNode } = getNodesAndEdgesBehindNode(
        nextNode,
        originalFlowNodes,
        addStepNode.id,
        currNode.id,
        checkedNodeIds,
        globalCount,
        loopings
      );
      loopings.callStackNode.pop();
      nodes.push(...nodesBehindNode);
      edges.push(...edgesFromBehindNode);
    }
  }
  return {
    nodes,
    edges,
  };
};

const getNodesAndEdgesFromBranch = (
  id: string,
  nextId: string,
  originalFlowNodes: FlowNodes,
  parentId: string,
  actualParentNodeId: string,
  checkedNodeIds: Set<string>,
  globalCount: { count: number },
  loopings: LoopingsData
) => {
  const nodes: any[] = [];
  const edges: any[] = [];

  const subPathNode = getSubPathNode(id, parentId, actualParentNodeId, nextId, globalCount);
  const edgeFromPathToSubPathNode = getEdgeData(
    parentId,
    subPathNode.id,
    actualParentNodeId,
    TypeEdge.Arrow
  );
  nodes.push(subPathNode);
  edges.push(edgeFromPathToSubPathNode);

  const addStepNode = getAddNewStepNode(subPathNode.id, {
    parentNodeId: actualParentNodeId,
    nextNodeId: nextId,
  });
  const edgeFromNodeToAddNode = getEdgeData(
    subPathNode.id,
    addStepNode.id,
    subPathNode.id,
    TypeEdge.OnlyLine
  );

  nodes.push(addStepNode);
  edges.push(edgeFromNodeToAddNode);
  if (loopings.currentLoopingId) {
    loopings.loopingData
      .find((item) => item.loopId === loopings.currentLoopingId)
      ?.loopSubNodes.push(subPathNode as unknown as FlowNodeData);
  }

  if (!nextId || !originalFlowNodes || !originalFlowNodes?.[nextId]) {
    return {
      nodes,
      edges,
    };
  }
  const node = originalFlowNodes[nextId];
  loopings.callStackNode.push(node.id);
  const { nodes: nodesBehindNode, edges: edgesBehindNode } = getNodesAndEdgesBehindNode(
    node,
    originalFlowNodes,
    addStepNode.id,
    actualParentNodeId,
    checkedNodeIds,
    globalCount,
    loopings
  );
  loopings.callStackNode.pop();
  nodes.push(...nodesBehindNode);
  edges.push(...edgesBehindNode);

  return {
    nodes,
    edges,
  };
};

function getNodesAndEdges(triggers?: FlowNodes, originalFlowNodes?: FlowNodes) {
  const nodes: any[] = [];
  const edges: any[] = [];
  const checkedNodeIds: Set<string> = new Set();
  const globalCount = { count: 0 };
  const loopings: LoopingsData = {
    currentLoopingId: '',
    loopingData: [],
    callStackNode: new Stack<string>(),
  };

  if (triggers && Object.keys(triggers).length >= 1) {
    const triggerKeys = Object.keys(triggers);

    const triggerNodes = triggerKeys.map((key) => {
      return getTriggerNodeData(key, triggers[key], globalCount);
    });
    const addStepNode = getAddNewStepNode(triggerNodes[0].id, {
      nextNodeId: triggers[triggerKeys[0]]?.next,
      parentNodeId: triggerNodes[0].id,
      directAddNodeFromTrigger: true,
    });
    nodes.push(...triggerNodes, addStepNode);
    edges.push(
      ...triggerNodes.map((item) => {
        return getEdgeData(item.id, addStepNode.id, item.id, TypeEdge.OnlyLine);
      })
    );

    const nodeToStartId = triggers[triggerKeys[0]]?.next ?? '';

    if (originalFlowNodes && Object.keys(originalFlowNodes).length >= 1) {
      const currentParentId: string = triggerNodes[0].id;
      const nodeToStart = originalFlowNodes?.[nodeToStartId];
      if (nodeToStart) {
        const { nodes: nodesBehindNode, edges: edgesBehindNode } = getNodesAndEdgesBehindNode(
          nodeToStart,
          originalFlowNodes,
          addStepNode.id,
          currentParentId,
          checkedNodeIds,
          globalCount,
          loopings
        );
        nodes.push(...nodesBehindNode);
        edges.push(...edgesBehindNode);
      }
    }
  }

  const nodeIdToOrderNumber = nodes.reduce<Record<string, number>>((acc, node) => {
    if (!node.data?.orderedNumber) return acc;
    acc[node.id] = node.data.orderedNumber;
    return acc;
  }, {});

  return {
    nodes,
    edges,
    loopings: loopings.loopingData,
    nodeIdToOrderNumber,
  };
}

function getNodesForDiagram(originalNodes: Node[] = [], positionCalculatedNodes: ElkNode[] = []) {
  if (originalNodes.length && positionCalculatedNodes.length) {
    return [
      ...positionCalculatedNodes.map((node) => {
        const originalNode = originalNodes.find((n) => n.id === node.id);
        return {
          type: 'vitual-node-add',
          id: node.id,
          ...originalNode,
          position: {
            x: node.x,
            y: node.y,
          },
        };
      }),
    ];
  }
  return [];
}

function getEdgesForDiagram(
  originalEdges: Edge[] = [],
  positionCalculatedEdges: ElkExtendedEdge[] = []
) {
  if (originalEdges.length && positionCalculatedEdges.length) {
    return [
      ...positionCalculatedEdges.map((edge) => {
        return {
          ...originalEdges.find((n) => n.id === edge.id),
        };
      }),
    ];
  }
  return [];
}

function getBoundingRectOfNodes(nodes: Node[]) {
  let top = nodes?.[0].position?.y ?? Number.NEGATIVE_INFINITY;
  let bottom = Number.NEGATIVE_INFINITY;
  let left = nodes?.[0].position?.x ?? Number.NEGATIVE_INFINITY;
  let right = Number.NEGATIVE_INFINITY;
  nodes.forEach((node) => {
    const { x, y } = node.position;
    if (x < left) {
      left = x;
    }
    if (x > right) {
      right = x;
    }

    if (y < top) {
      top = y;
    }
    if (y > bottom) {
      bottom = y;
    }
  });
  right += COMMON_WIDTH_NODE; // to be equal to the width of the node
  bottom += COMMON_HEIGHT_NODE + COMMON_HEIGHT_NODE / 2; // Add some padding to the bottom
  return {
    top,
    bottom,
    left,
    right,
  };
}

function getPreviousNodes(nodeId: string, nodes: any[], edges: any[]): Node[] {
  const previousNodes: Node[] = [];
  let currentNodeId = nodeId;
  while (currentNodeId) {
    const node = nodes.find((node) => node.id === currentNodeId);

    if (node?.type === FlowNodeType.LoopingFrame) {
      const edge = edges.find((edge) => edge.source === currentNodeId);
      if (edge) {
        currentNodeId = edge.data.actualParentNodeId;
        continue;
      }
    }

    if (node?.type === FlowNodeType.Looping) {
      previousNodes.push(node);
      currentNodeId = node.parentNodeId;

      let currentNextNodeId = node.data.nextChildID;
      while (currentNextNodeId) {
        const nextNode = nodes.find((node) => node.id === currentNextNodeId);
        previousNodes.push(nextNode);
        const edge = edges.find((edge) => edge.source === currentNextNodeId);
        if (edge) {
          currentNextNodeId = edge.target;
        } else {
          currentNextNodeId = '';
        }
      }
    }

    const parentNodeId = node?.parentNodeId ?? '';
    if (!parentNodeId) {
      break;
    }

    const parentNode = nodes.find((node) => node.id === parentNodeId);
    if (!parentNode) {
      break;
    }

    if (!parentNode?.data?.paths?.find((path) => path.id === nodeId)) {
      previousNodes.push(parentNode);
    }

    currentNodeId = parentNodeId;
  }

  return previousNodes;
}

const shouldUpdateNodeName = (node: FlowNodeData, schema: Record<string, any>) => {
  if (node.displayName === schema.displayName) {
    return true;
  }

  if (schema?.actions) {
    const actions = Object.values(schema.actions);
    if (actions.some((action: any) => action.displayName === node.displayName)) {
      return true;
    }
  }

  if (schema?.triggers) {
    const triggers = Object.values(schema.triggers);
    if (triggers.some((trigger: any) => trigger.displayName === node.displayName)) {
      return true;
    }
  }

  return false;
};

export const FlowDiagram = {
  getPreviousNodes,
  getNodesAndEdges,
  getCompoundNodeId,
  getNodesForDiagram,
  getEdgesForDiagram,
  shouldUpdateNodeName,
  getLayoutCalculateElk,
  getBoundingRectOfNodes,
};
