{"addPathLabel": "Add Path", "addStepLabel": "Add Step", "buttonCreateFlowLabel": "Create a Flow", "copyPostFix": "(Copy)", "createModalTitle": "Create Flow", "defaultNameActionNode": "Select an event for your flow to run", "defaultNameTriggerNode": "Select a trigger that starts your flow", "defaultPathName": "Split into paths", "defaultSubPathName": "Path conditions", "defaultTitleActionNode": "Select an action", "defaultTitleTriggerNode": "Select a trigger", "delete": "Delete", "deleteModalDescription": "Your Flow will be permanently deleted with no chance of recovery. Type <strong>{name}</strong> in the input below to confirm.", "deleteModalError": "Please Enter Flow Name : {name}", "deleteModalTitle": "Delete Flow", "deleteModelPlaceholder": "Enter Flow name", "description": "Automate complex processes by integrating multiple apps and tools to streamline your operations efficiently.", "edit": "Edit", "editModalFlowDescription": "Description", "editModalFlowTitle": "Flow Name", "editModalTitle": "Edit Name & Description", "filterSelectPlaceholder": "Filter", "loopingLabel": "Looping", "noTemplateFound": "No Template Found", "pathLabel": "Paths", "pleaseWaitText": "Please wait a moment", "reallyDelete": "Really delete?", "requiredInputField": "Please input this field", "searchPlaceholder": "Search a flow...", "searchTemplate": "Search Template", "settingFlowTextGradient": "Setting up your flow now...", "templateModelTitle": "Browse Flow Templates", "title": "Flows", "updateAtLabel": "Updated {time}"}