import { useAppContext } from '@/contexts/AppContext';
import {
  useData,
  useObject,
  useObjects,
  useRecordsCount,
  useTags,
  useView,
  useViews,
} from '@/hooks';
import { FieldAPI, RecordAPI, ViewAPI } from '@/services/api';
import { useDisclosure } from '@mantine/hooks';
import { usePathParams } from '@resola-ai/ui/hooks';
import { render, screen, waitFor } from '@testing-library/react';
import { renderHook } from '@testing-library/react';
import { fireEvent } from '@testing-library/react';
import { renderWithMantine, MantineWrapper } from '@/tests/utils/testUtils';
import type React from 'react';
import { useParams } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { WorkspaceContextProvider, useWorkspaceContext } from './WorkspaceContext';

// Mock hooks
const mockNavigateFunc = vi.fn();

vi.mock('react-router-dom', () => ({
  useParams: vi.fn(),
  useNavigate: () => mockNavigateFunc,
}));

vi.mock('@mantine/hooks', () => ({
  useDisclosure: vi.fn(),
}));

vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: vi.fn(),
}));

vi.mock('@resola-ai/utils', () => ({
  defaultFontName: 'sans-serif',
}));

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: vi.fn(),
}));

vi.mock('@/hooks', () => ({
  useData: vi.fn(),
  useObject: vi.fn(),
  useTags: vi.fn(),
  useObjects: vi.fn(),
  useView: vi.fn(),
  useViews: vi.fn(),
  useRecordsCount: vi.fn(),
}));

// Mock API services
vi.mock('@/services/api', () => ({
  FieldAPI: {
    update: vi.fn(),
    save: vi.fn(),
    delete: vi.fn(),
  },
  RecordAPI: {
    save: vi.fn(),
    updateByField: vi.fn(),
    delete: vi.fn(),
  },
  ViewAPI: {
    update: vi.fn(),
    save: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock configs
vi.mock('@/configs', () => ({
  default: {
    BASE_PATH: '/app/',
  },
}));

const TestComponent = () => {
  const context = useWorkspaceContext();
  return (
    <div>
      <h1>Workspace Test Component</h1>
      <div data-testid='data-count'>{context.data?.length || 0}</div>
      <div data-testid='columns-count'>{context.columns?.length || 0}</div>
      <div data-testid='row-selection'>{Object.keys(context.rowSelection || {}).length}</div>
      <button onClick={() => context.handleAddColumn({ name: 'New Column', type: 'text' })}>
        Add Column
      </button>
      <button onClick={() => context.handleActionRow('DELETE_ROW', ['row-1'])}>Delete Row</button>
      <button onClick={() => context.handleActionRow('DELETE_COL', ['column-1'])}>
        Delete Column
      </button>
      <button onClick={() => context.openProfile('record-1')}>Open Profile</button>
    </div>
  );
};

describe('WorkspaceContext', () => {
  const mockCreatePathWithLngParam = vi.fn((path) => path);
  const mockOpen = vi.fn();
  const mockClose = vi.fn();

  const mockObject = {
    id: 'object-1',
    fields: [
      { id: 'column-1', name: 'Column 1', type: 'text' },
      { id: 'column-2', name: 'Column 2', type: 'number' },
    ],
    views: [
      {
        id: 'view-1',
        name: 'View 1',
        active: true,
        type: 'grid',
        icon: 'table',
        createdBy: 'user-1',
        rowHeight: 'medium',
        fields: [
          { fieldMetaId: 'column-1', isVisible: true },
          { fieldMetaId: 'column-2', isVisible: true },
        ],
        fieldOrder: ['column-1', 'column-2'],
      },
      {
        id: 'view-2',
        name: 'View 2',
        active: false,
        type: 'grid',
        icon: 'table',
        createdBy: 'user-1',
        rowHeight: 'medium',
        fields: [{ fieldMetaId: 'column-1', isVisible: true }],
        fieldOrder: ['column-1'],
      },
    ],
  };

  const mockRecords = [
    { id: 'row-1', 'column-1': 'Value 1', 'column-2': 100 },
    { id: 'row-2', 'column-1': 'Value 2', 'column-2': 200 },
  ];

  const mockTags = [
    { id: 'tag-1', name: 'Tag 1' },
    { id: 'tag-2', name: 'Tag 2' },
  ];

  const mockObjects = [
    { id: 'object-1', name: 'Object 1' },
    { id: 'object-2', name: 'Object 2' },
  ];

  beforeEach(() => {
    vi.clearAllMocks();

    // Mock useParams
    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace-1',
      id: 'object-1',
      viewId: 'view-1',
    });

    // Mock useDisclosure
    (useDisclosure as unknown as ReturnType<typeof vi.fn>).mockReturnValue([
      false,
      { open: mockOpen, close: mockClose, toggle: vi.fn() },
    ]);

    // Mock usePathParams
    (usePathParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      createPathWithLngParam: mockCreatePathWithLngParam,
    });

    // Mock useAppContext
    (useAppContext as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      reloadObject: null,
      isManager: true,
    });

    // Mock useObject
    (useObject as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      object: mockObject,
      mutate: vi.fn(),
      objectLoading: false,
    });

    // Mock useTags
    (useTags as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      tags: mockTags,
      mutate: vi.fn(),
    });

    // Mock useObjects
    (useObjects as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      objects: mockObjects,
    });

    // Mock useData
    (useData as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      records: mockRecords,
      isLoading: false,
      mutate: vi.fn(),
      size: 1,
      setSize: vi.fn(),
      totalRecords: 2,
      isValidating: false,
    });

    // Mock useView
    (useView as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      view: mockObject.views.find((view) => view.active),
      isLoading: false,
      mutateView: vi.fn(),
    });

    // Mock useViews
    (useViews as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      views: mockObject.views,
      isLoading: false,
    });

    // Mock useRecordsCount
    (useRecordsCount as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      recordsCount: { 'view-1': 15, 'view-2': 13 },
    });

    // Mock API calls
    (FieldAPI.update as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({});
    (FieldAPI.save as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({});
    (FieldAPI.delete as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({});
    (RecordAPI.save as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ id: 'new-row' });
    (RecordAPI.updateByField as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({
      record: { id: 'row-1', 'column-1': 'Updated Value' },
    });
    (RecordAPI.delete as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({});
    (ViewAPI.update as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ id: 'view-1' });
    (ViewAPI.save as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ id: 'new-view' });
    (ViewAPI.delete as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({});
  });

  it('provides workspace context to children', async () => {
    renderWithMantine(
      <WorkspaceContextProvider>
        <TestComponent />
      </WorkspaceContextProvider>
    );

    expect(screen.getByText('Workspace Test Component')).toBeInTheDocument();
    expect(screen.getByTestId('data-count').textContent).toBe('2');
    expect(screen.getByTestId('columns-count').textContent).toBe('2');
    expect(screen.getByTestId('row-selection').textContent).toBe('0');
  });

  it('provides access to RecordAPI for adding rows', async () => {
    // Test that RecordAPI.save is properly mocked and can be called
    const mockNewRow = { id: 'new-row', 'column-1': 'New Value' };
    await RecordAPI.save('workspace-1', 'object-1', mockNewRow);

    expect(RecordAPI.save).toHaveBeenCalledWith('workspace-1', 'object-1', mockNewRow);
  });

  it('handles adding a column', async () => {
    renderWithMantine(
      <WorkspaceContextProvider>
        <TestComponent />
      </WorkspaceContextProvider>
    );

    fireEvent.click(screen.getByText('Add Column'));

    expect(FieldAPI.save).toHaveBeenCalledWith(
      'workspace-1',
      'object-1',
      expect.objectContaining({
        field: expect.objectContaining({
          name: 'New Column',
          type: 'text',
        }),
      })
    );
  });

  it.skip('handles deleting a row', async () => {
    // Set up any additional mocks needed for this test
    (RecordAPI.delete as unknown as ReturnType<typeof vi.fn>).mockImplementation(() =>
      Promise.resolve({})
    );

    render(
      <WorkspaceContextProvider>
        <TestComponent />
      </WorkspaceContextProvider>
    );

    fireEvent.click(screen.getByText('Delete Row'));

    // Wait for the async deleteRow function to complete
    await waitFor(() => {
      expect(RecordAPI.delete).toHaveBeenCalled();
    });
  });

  it.skip('handles deleting a column', async () => {
    // Set up any additional mocks needed for this test
    (FieldAPI.delete as unknown as ReturnType<typeof vi.fn>).mockImplementation(() =>
      Promise.resolve({})
    );

    render(
      <WorkspaceContextProvider>
        <TestComponent />
      </WorkspaceContextProvider>
    );

    fireEvent.click(screen.getByText('Delete Column'));

    // Wait for the delete operation to complete
    await waitFor(() => {
      expect(FieldAPI.delete).toHaveBeenCalled();
    });
  });

  it('handles opening a profile', async () => {
    renderWithMantine(
      <WorkspaceContextProvider>
        <TestComponent />
      </WorkspaceContextProvider>
    );

    fireEvent.click(screen.getByText('Open Profile'));

    // Test that the navigation functions are called correctly
    expect(mockCreatePathWithLngParam).toHaveBeenCalledWith(
      '/app/workspace/workspace-1/objects/object-1/views/view-1/record-1'
    );
    expect(mockNavigateFunc).toHaveBeenCalledWith(
      '/app/workspace/workspace-1/objects/object-1/views/view-1/record-1'
    );
  });

  it('loads the active view from url or object userconfig', async () => {
    // Case 1: viewId is in URL parameters
    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace-1',
      id: 'object-1',
      viewId: 'view-2',
    });

    renderWithMantine(
      <WorkspaceContextProvider>
        <TestComponent />
      </WorkspaceContextProvider>
    );

    // Clear mocks for the second test
    vi.clearAllMocks();

    // Case 2: No viewId in URL parameters, should fall back to object.userconfig.viewId
    (useParams as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      wsId: 'workspace-1',
      id: 'object-1',
    });

    // Prepare mock with userconfig
    (useObject as unknown as ReturnType<typeof vi.fn>).mockReturnValue({
      object: {
        ...mockObject,
        userconfig: {
          viewId: 'view-1',
        },
      },
      mutate: vi.fn(),
      objectLoading: false,
    });

    renderWithMantine(
      <WorkspaceContextProvider>
        <TestComponent />
      </WorkspaceContextProvider>
    );

    // Optional: verify that the view from userconfig is used
    expect(useView).toHaveBeenCalledWith('workspace-1', 'object-1', expect.anything());
  });

  it.skip('handles view changes (create, switch, delete)', async () => {
    // Mock implementations for view API calls
    (ViewAPI.save as unknown as ReturnType<typeof vi.fn>).mockImplementation(() =>
      Promise.resolve({ id: 'new-view', name: 'New View' })
    );
    (ViewAPI.update as unknown as ReturnType<typeof vi.fn>).mockImplementation(() =>
      Promise.resolve({ id: 'view-1' })
    );
    (ViewAPI.delete as unknown as ReturnType<typeof vi.fn>).mockImplementation(() =>
      Promise.resolve({})
    );

    // Render the hook
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <WorkspaceContextProvider>{children}</WorkspaceContextProvider>
    );
    const { result } = renderHook(() => useWorkspaceContext(), { wrapper });

    // Type assertion to bypass TypeScript errors
    const context = result.current as any;

    // Test creating a view
    const newView = {
      id: 'new-view',
      name: 'New View',
      active: true,
      type: 'grid',
      icon: 'table',
      createdBy: 'user-1',
      rowHeight: 'medium',
      fields: [],
      fieldOrder: [],
    };

    await context.handleViewChange('new-view', newView, 'CREATE_VIEW');
    expect(ViewAPI.save).toHaveBeenCalled();

    // Test switching to another view
    await context.handleViewChange('view-2', mockObject.views[1], 'SWITCH_VIEW');
    expect(mockCreatePathWithLngParam).toHaveBeenCalled();

    // Test deleting a view
    await context.handleViewChange('view-2', mockObject.views[1], 'DELETE_VIEW');
    expect(ViewAPI.delete).toHaveBeenCalled();
  });

  it('handles saving cell data', async () => {
    const { result } = renderHook(() => useWorkspaceContext(), {
      wrapper: ({ children }) => (
        <MantineWrapper>
          <WorkspaceContextProvider>{children}</WorkspaceContextProvider>
        </MantineWrapper>
      ),
    });

    await result.current.onSaveData('Updated Value', 0, 'column-1');

    expect(RecordAPI.updateByField).toHaveBeenCalledWith(
      'workspace-1',
      'object-1',
      'row-1',
      'column-1',
      {
        id: 'row-1',
        'column-1': 'Updated Value',
      }
    );
  });

  it('handles updating a column', async () => {
    const { result } = renderHook(() => useWorkspaceContext(), {
      wrapper: ({ children }) => (
        <MantineWrapper>
          <WorkspaceContextProvider>{children}</WorkspaceContextProvider>
        </MantineWrapper>
      ),
    });

    await result.current.handleUpdateColumn('column-1', {
      name: 'Updated Column',
      options: { required: true },
    });

    expect(FieldAPI.update).toHaveBeenCalled();
  });

  it('throws error when used outside of WorkspaceContextProvider', () => {
    // Expect the useWorkspaceContext hook to throw when used outside of the provider
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    expect(() => {
      renderHook(() => useWorkspaceContext());
    }).toThrow('useWorkspaceContext must be used inside WorkspaceContextProvider');

    consoleSpy.mockRestore();
  });

  it.skip('handles column operations correctly', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <WorkspaceContextProvider>{children}</WorkspaceContextProvider>
    );
    const { result } = renderHook(() => useWorkspaceContext(), { wrapper });

    // Cast the context to any to avoid TypeScript errors with missing methods
    const context = result.current as any;

    // Test updating column
    await context.handleUpdateColumn('field-1', {
      name: 'Updated Field',
      options: { some: 'option' },
    });

    expect(FieldAPI.update).toHaveBeenCalledWith(
      'workspace-1',
      'object-1',
      'field-1',
      expect.objectContaining({
        field: expect.objectContaining({
          name: 'Updated Field',
          options: { some: 'option' },
        }),
      })
    );

    // Set up specific implementation for this test
    (FieldAPI.delete as unknown as ReturnType<typeof vi.fn>).mockImplementation(() =>
      Promise.resolve({})
    );

    // Test deleting column
    await context.handleActionRow('DELETE_COL', ['field-1']);

    await waitFor(() => {
      expect(FieldAPI.delete).toHaveBeenCalled();
    });

    const mockMutate = (useObject as unknown as ReturnType<typeof vi.fn>).mock.results[0].value
      .mutate;
    expect(mockMutate).toHaveBeenCalled();
  });
});
