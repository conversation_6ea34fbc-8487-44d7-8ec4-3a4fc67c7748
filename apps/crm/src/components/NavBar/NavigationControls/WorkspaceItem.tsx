import AppConfig from '@/configs';
import { NAVBAR_MIN_WIDTH } from '@/constants';
import { IconList } from '@/constants/workspace';
import { useAppContext } from '@/contexts/AppContext';
import { useNavbarContext } from '@/contexts/NavbarContext';
import type { WSObject } from '@/models';
import { useDndContext } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ActionIcon, Box, Flex, Menu, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaTooltip } from '@resola-ai/ui/components/DecaTooltip';
import { usePathParams } from '@resola-ai/ui/hooks';
import { IconDotsVertical, IconFile } from '@tabler/icons-react';
import { type CSSProperties, useEffect, useState } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import MenuDropdown from './MenuDropdown';

const BASE_PATH = AppConfig.BASE_PATH;
const linearCommon = {
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
};

const useStyles = createStyles((theme) => ({
  link: {
    '&:hover': {
      borderRadius: rem(8),
      backgroundColor: theme.colors.decaNavy[0],

      '& .mantine-Text-root': {
        color: theme.colors.decaNavy[5],
      },
      '& svg': {
        color: theme.colors.decaNavy[5],
      },
      '& .menu-button': {
        opacity: 1,
        visibility: 'visible',
      },
    },
  },
  rootLink: {
    marginBottom: rem(4),
    alignItems: 'center',

    '& .mantine-Text-root': {
      color: theme.colors.decaGrey[8],
    },
    '& svg': {
      color: theme.colors.decaGrey[8],
    },
  },
  navLink: {
    width: '100%',
    textDecoration: 'none',
    display: 'flex',
    alignItems: 'center',
    fontSize: rem(14),
    gap: rem(6),
    whiteSpace: 'normal',
    background: `linear-gradient(90deg, ${theme.colors.decaGrey[9]} 40%, ${theme.colors.decaGrey[4]} 80%, white 100%)`,
    ...linearCommon,

    '&:hover': {
      background: `linear-gradient(90deg, ${theme.colors.decaNavy[5]} 40%, ${theme.colors.decaNavy[2]} 80%, white 100%)`,
      ...linearCommon,
    },
  },
  navLinkActive: {
    background: `linear-gradient(90deg, ${theme.colors.decaNavy[5]} 40%, ${theme.colors.decaNavy[2]} 80%, white 100%)`,
    ...linearCommon,
  },
  activeLink: {
    borderRadius: rem(8),
    backgroundColor: theme.colors.decaNavy[0],
    '& .mantine-Text-root': {
      color: theme.colors.decaNavy[5],
    },
    '& svg': {
      color: theme.colors.decaNavy[5],
    },
  },
  text: {
    lineHeight: 1,
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
  },
  dropTarget: {
    position: 'relative',
    '&::after': {
      content: '""',
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: rem(2),
      backgroundColor: theme.colors.decaBlue[5],
    },
  },
  menuButton: {
    opacity: 0,
    visibility: 'hidden',
    transition: 'opacity 0.2s ease, visibility 0.2s ease',
  },
  highlighted: {
    backgroundColor: theme.colors.decaBlue[0],
    animation: 'pulse 1.5s ease-in-out',
    borderRadius: rem(8),
  },
}));

const WorkspaceItem = ({
  item,
  isHighlighted = false,
}: { item: WSObject; isHighlighted?: boolean }) => {
  const { classes, cx } = useStyles();
  const [activePath, setActivePath] = useState<string>(`${BASE_PATH}/workspace`);
  const { createPathWithLngParam } = usePathParams();
  const { currWorkspace } = useNavbarContext();
  const { sidebarWidth } = useAppContext();
  const wsPath = `${BASE_PATH}workspace/${currWorkspace}/objects`;
  const IconComp = IconList.find((icon) => icon.value === item.icon)?.Icon as any;
  const [menuOpened, setMenuOpened] = useState(false);
  const [selectedItemId, setSelectedItemId] = useState<string>();
  const [isHovered, setIsHovered] = useState(false);
  const pathName = location.pathname;
  const isCollapsed = sidebarWidth <= NAVBAR_MIN_WIDTH;

  const { attributes, isDragging, listeners, setNodeRef, transform, transition } = useSortable({
    id: item.id,
  });

  const { over } = useDndContext();

  const navigate = useNavigate();

  const style: CSSProperties = {
    opacity: isDragging ? 0.4 : undefined,
    transform: CSS.Translate.toString(transform),
    transition,
  };

  useEffect(() => {
    setActivePath(pathName);
  }, [pathName]);

  const isActive = activePath.includes(`${wsPath}/${item.id}/`);

  const isOver = over?.id === item.id;

  return (
    <>
      {!isCollapsed ? (
        <Menu
          data-testid={`workspace-item-${item.id}`}
          position='right-start'
          opened={menuOpened}
          onClose={() => {
            setMenuOpened(false);
            setSelectedItemId(undefined);
          }}
          closeOnItemClick={false}
        >
          <Flex
            ref={setNodeRef}
            {...attributes}
            {...listeners}
            style={{
              ...style,
            }}
            w={'100%'}
            pos={'relative'}
            align={'center'}
            pr={isHovered ? rem(20) : rem(10)}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
            className={cx(classes.rootLink, classes.link, {
              [classes.activeLink]: isActive,
              [classes.dropTarget]: isOver,
              [classes.highlighted]: isHighlighted,
            })}
          >
            <Box
              data-testid={`workspace-item-link-${item.id}`}
              role='link'
              onClick={() => {
                if (!isDragging) {
                  navigate(createPathWithLngParam(`${wsPath}/${item.id}/`));
                }
              }}
              p={rem(10)}
              pr={isHovered ? rem(10) : rem(0)}
              className={cx(classes.navLink, {
                [classes.navLinkActive]: isActive,
              })}
              style={{ cursor: isDragging ? 'grabbing' : 'pointer' }}
            >
              <ActionIcon variant='transparent' c={'decaGrey.4'} size={20}>
                {IconComp ? <IconComp /> : <IconFile />}
              </ActionIcon>
              <DecaTooltip
                withArrow
                arrowPosition='center'
                position='right'
                label={item.name?.singular}
              >
                <Text className={classes.text} data-object-id={item.id} fw={500}>
                  {item.name?.singular}
                </Text>
              </DecaTooltip>
            </Box>
            {sidebarWidth > 110 && (
              <Menu.Target>
                <ActionIcon
                  className={cx(classes.menuButton, 'menu-button')}
                  style={{ position: 'absolute', right: rem(10) }}
                  variant='subtle'
                  c={'decaGrey.4'}
                  onClick={() => {
                    setSelectedItemId(item.id);
                    setMenuOpened(true);
                  }}
                >
                  <IconDotsVertical size={14} />
                </ActionIcon>
              </Menu.Target>
            )}
          </Flex>
          <MenuDropdown id={selectedItemId} setMenuOpened={setMenuOpened} />
        </Menu>
      ) : (
        <Flex
          className={cx(classes.rootLink, classes.link, {
            [classes.activeLink]: isActive,
            [classes.highlighted]: isHighlighted,
          })}
        >
          <NavLink
            to={createPathWithLngParam(`${wsPath}/${item.id}/`)}
            data-testid={`workspace-item-link-${item.id}`}
            style={{ padding: rem(10) }}
            className={cx(classes.navLink, {
              [classes.navLinkActive]: isActive,
            })}
          >
            <DecaTooltip
              data-testid={`workspace-item-tooltip-${item.id}`}
              withArrow
              arrowPosition='center'
              position='right'
              label={item.name?.singular}
            >
              <ActionIcon variant='transparent' c={'decaGrey.4'} size={20}>
                {IconComp ? <IconComp size={20} /> : <IconFile size={20} />}
              </ActionIcon>
            </DecaTooltip>
          </NavLink>
        </Flex>
      )}
    </>
  );
};

export default WorkspaceItem;
