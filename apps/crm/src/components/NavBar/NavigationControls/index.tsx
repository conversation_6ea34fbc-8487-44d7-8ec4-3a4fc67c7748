import { NAVBAR_MIN_WIDTH, NAVBAR_WIDTH } from '@/constants';
import { useAppContext } from '@/contexts/AppContext';
import { NavbarContextProvider, useNavbarContext } from '@/contexts/NavbarContext';
import type { WSObject } from '@/models';
import { DndContext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { SortableContext } from '@dnd-kit/sortable';
import { ActionIcon, Box, Divider, Flex, ScrollArea, Stack, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { Modal } from '@resola-ai/ui';
import { IconChevronsLeft, IconMenu2, IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React, { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { NameForm } from './NameForm';
import { ObjectSettings } from './ObjectSettings';
import WorkspaceItem from './WorkspaceItem';
import { useNavigateObject } from './useNavigateObject';
import { ObjectAPI } from '@/services/api/object';

const COLLAPSED_HEIGHT = 170;

const useStyles = createStyles((theme) => ({
  container: {
    color: theme.colors.decaGrey[8],
    fontWeight: 500,
    width: '100%',
    height: '100%',
    justifyContent: 'space-between',
    flexDirection: 'column',
  },
  navLink: {
    textDecoration: 'none',
    display: 'flex',
    alignItems: 'center',
    fontSize: rem(14),
    gap: rem(12),
    color: theme.colors.decaGrey[8],
    padding: rem(8),

    '&:hover': {
      borderRadius: rem(8),
      backgroundColor: theme.colors.decaNavy[0],
      color: theme.colors.decaNavy[5],

      '& svg': {
        color: theme.colors.decaNavy[5],
      },
    },
  },
  activeLink: {
    borderRadius: rem(8),
    backgroundColor: theme.colors.decaNavy[0],
    color: theme.colors.decaNavy[5],

    '& svg': {
      color: theme.colors.decaNavy[5],
    },
  },
  iconStyle: {
    '&:hover': {
      color: theme.colors.decaNavy[5],
    },
  },
  text: {
    overflow: 'hidden',
    wordBreak: 'break-all',
  },
  objects: {
    '.mantine-ScrollArea-viewport': {
      '& > div:first-of-type': {
        display: 'block !important',
      },
    },
  },
  modal: {
    '.mantine-Modal-content': {
      height: '80vh',
      display: 'flex',
      flexDirection: 'column',
      overflow: 'hidden',
      '.mantine-Modal-body': {
        height: `calc(100% - ${rem(60)})`,
        '> div': {
          marginRight: rem(-8),
          marginLeft: rem(-8),
          height: '100%',
        },
      },
    },
  },
}));

const Navigation: React.FC<any> = () => {
  const { classes } = useStyles();
  const { t } = useTranslate('common');
  const {
    objects,
    onToggleSidebar,
    formOpened,
    setFormOpened,
    setSidebarWidth,
    sidebarWidth,
    mutateObjects,
  } = useAppContext();
  const { settingOpened, onCloseSetting } = useNavbarContext();
  const { handleNavigateObject } = useNavigateObject();

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    })
  );

  const { id, wsId } = useParams();
  const itemRefs = useRef({});
  const hasScrolledRef = useRef<string | null>(null);
  const [highlightedItemId, setHighlightedItemId] = useState<string | null>(null);

  useEffect(() => {
    if (id && itemRefs.current[id] && hasScrolledRef.current !== id) {
      itemRefs.current[id].scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
      hasScrolledRef.current = id;
    }
  }, [id, objects]);

  const handleSave = (obj: WSObject) => {
    setFormOpened(false);
    handleNavigateObject(obj.id);
  };

  const handleDragEnd = async (active: any, over: any) => {
    if (over && active.id !== over.id) {
      const oldIndex = objects.findIndex((obj) => obj.id === active.id);
      const newIndex = objects.findIndex((obj) => obj.id === over.id);
      if (oldIndex === -1 || newIndex === -1) return;
      const updatedObjects = [...objects];
      const [movedItem] = updatedObjects.splice(oldIndex, 1);
      updatedObjects.splice(newIndex, 0, movedItem);

      // Get previous and next object IDs of the moved item
      const prevId = newIndex > 0 ? updatedObjects[newIndex - 1].id : '';
      const nextId = newIndex < updatedObjects.length - 1 ? updatedObjects[newIndex + 1].id : '';

      await ObjectAPI.reorder(wsId ?? '', movedItem.id, {
        prevId,
        nextId,
      });
      await mutateObjects();

      setHighlightedItemId(active.id);
      setTimeout(() => {
        setHighlightedItemId(null);
      }, 1500);
    }
  };

  const isCollapsed = sidebarWidth <= NAVBAR_MIN_WIDTH;

  return (
    <Flex
      w={'100%'}
      h={'100%'}
      className={classes.container}
      data-testid='navigation-container'
      py={rem(12)}
    >
      {formOpened ? (
        <Stack px={rem(4)} h={'100%'}>
          <NameForm onCancel={() => setFormOpened(false)} onSave={handleSave} />
        </Stack>
      ) : (
        <React.Fragment>
          <Box data-testid='navigation-container-box' px={rem(12)} pb={rem(4)}>
            <Box w={'100%'}>
              <Flex justify={!isCollapsed ? 'space-between' : 'center'} p={rem(8)} align={'center'}>
                <Text fw={500} size={rem(12)} c={'decaGrey.9'} w={rem(80)} truncate>
                  {t('objects')}
                </Text>
                <ActionIcon
                  data-testid='toggle-sidebar-button'
                  variant='subtle'
                  c={'decaGrey.4'}
                  onClick={() => {
                    onToggleSidebar();
                  }}
                >
                  {!isCollapsed ? <IconChevronsLeft size={14} /> : <IconMenu2 size={14} />}
                </ActionIcon>
              </Flex>

              <ScrollArea
                h={`calc(100vh - ${rem(COLLAPSED_HEIGHT)})`}
                type='never'
                className={classes.objects}
              >
                <DndContext
                  sensors={sensors}
                  onDragEnd={({ active, over }) => {
                    if (over && active.id !== over?.id) {
                      handleDragEnd(active, over);
                    }
                  }}
                >
                  <SortableContext items={objects || []}>
                    <Flex mah={rem(280)} direction={'column'} mt={objects?.length ? rem(10) : 0}>
                      {objects.map((item, index) => (
                        <Box ref={(el) => (itemRefs.current[item.id] = el)} key={index}>
                          <WorkspaceItem
                            item={item}
                            isHighlighted={highlightedItemId === item.id}
                          />
                        </Box>
                      ))}
                    </Flex>
                  </SortableContext>
                </DndContext>
              </ScrollArea>
            </Box>
          </Box>
          <Box w={'100%'} c={'decaGrey.9'} p={rem(12)} pt={rem(0)}>
            <Divider w={'100%'} />
            <Flex
              justify={!isCollapsed ? 'space-between' : 'center'}
              px={rem(8)}
              pt={rem(8)}
              align={'center'}
            >
              <Text size={rem(12)} c={'decaGrey.9'} w={rem(80)} truncate>
                {t('createNew').toUpperCase()}
              </Text>
              <ActionIcon
                data-testid='create-new-button'
                variant='white'
                c={'decaGrey.4'}
                onClick={() => {
                  if (isCollapsed) {
                    onToggleSidebar();
                  }
                  setSidebarWidth(NAVBAR_WIDTH);
                  setFormOpened(true);
                }}
                sx={{ borderRadius: '100%' }}
              >
                <IconPlus size={14} />
              </ActionIcon>
            </Flex>
          </Box>
          <Modal
            size={rem(720)}
            opened={settingOpened}
            onClose={onCloseSetting}
            title={t('objectSetting')}
            centered
            className={classes.modal}
            closeOnClickOutside
          >
            <ObjectSettings />
          </Modal>
        </React.Fragment>
      )}
    </Flex>
  );
};

const NavigationControls = () => {
  return (
    <NavbarContextProvider>
      <Navigation />
    </NavbarContextProvider>
  );
};

export default NavigationControls;
