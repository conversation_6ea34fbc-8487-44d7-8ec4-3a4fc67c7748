import type { MessageType } from '@/constants/workspace';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import { WorkspaceAPI } from '@/services/api';
import { useShowTemplateSavedNotification } from '@/utils';
import { Box, Button, Divider, Flex, Paper, ScrollArea, Text, TextInput, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDebouncedState } from '@mantine/hooks';
import { RichTextEditor } from '@mantine/tiptap';
import { Modal as DecaModal } from '@resola-ai/ui';
import { Modal } from '@resola-ai/ui';
import { IconPlus } from '@tabler/icons-react';
import { useEditor } from '@tiptap/react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  type EditorInstance,
  EditorToolbar,
  type Template,
  availableFields,
  editorStyles,
  extensions,
} from './EditorConfig';

const useStyles = createStyles(() => ({
  modal: {
    ['.mantine-Modal-content']: {
      overflow: 'hidden',
      borderRadius: rem(8),
    },
  },
}));
const TemplateModal: React.FC<{
  opened: boolean;
  onClose: () => void;
  editorType: MessageType;
  mutateTemplates: () => void;
  templates: Template[];
}> = ({ opened, onClose, editorType, mutateTemplates, templates }) => {
  const { wsId, objId } = useParams();
  const { classes } = useStyles();
  const { classes: editorClasses } = editorStyles();
  const { t } = useTranslate('workspace');
  const showTemplateSavedNotification = useShowTemplateSavedNotification();
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [newTemplate, setNewTemplate] = useDebouncedState<Template>(
    {
      id: '',
      name: '',
      description: '',
      content: '',
    },
    200
  );
  const [nameError, setNameError] = useState(false);
  const [subjectError, setSubjectError] = useState(false);
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [contentError, setContentError] = useState(false);
  const [confirmModalOpened, setConfirmModalOpened] = useState(false);
  const nameRef = useRef<HTMLInputElement>(null);
  const desRef = useRef<HTMLInputElement>(null);
  const { object } = useWorkspaceContext();
  const editor: EditorInstance = useEditor({
    extensions: extensions(
      editorType,
      t(editorType === 'email' ? 'inputEmailContent' : 'inputMessageContent'),
      object?.fields,
      editorType === 'email' && object ? availableFields(object) : undefined,
      true
    ) as any,
    onUpdate: () => {
      setContentError(false);
    },
  });

  useEffect(() => {
    if (selectedTemplate) {
      if (nameRef.current) nameRef.current.value = selectedTemplate.name;
      if (desRef.current) desRef.current.value = selectedTemplate.description;
      editor?.commands.setContent(selectedTemplate.content);
    } else {
      if (nameRef.current) nameRef.current.value = '';
      if (desRef.current) desRef.current.value = '';
      editor?.commands.setContent('');
    }
    setContentError(false);
  }, [selectedTemplate, editor]);

  const handleSave = async () => {
    setSaving(true);
    setNameError(false);
    setSubjectError(false);
    setContentError(false);

    if (!newTemplate.name && !selectedTemplate?.name) {
      setNameError(true);
      setSaving(false);
      return;
    }

    if (editorType === 'email' && !newTemplate.description && !selectedTemplate?.description) {
      setSubjectError(true);
      setSaving(false);
      return;
    }

    const content = editor?.getText();
    if (!content || content.trim() === '') {
      setContentError(true);
      setSaving(false);
      return;
    }

    let res;
    if (selectedTemplate && wsId) {
      res = await WorkspaceAPI.updateTemplate(wsId, selectedTemplate.id, {
        ...selectedTemplate,
        content: editor?.getHTML(),
        type: editorType === 'email' ? 'email' : 'sms',
        objectId: objId || object?.id,
      });
    } else if (newTemplate.name && wsId) {
      res = await WorkspaceAPI.createTemplate(wsId, {
        ...newTemplate,
        content: editor?.getHTML(),
        type: editorType === 'email' ? 'email' : 'sms',
        objectId: objId || object?.id,
      });
    }
    if (res && 'id' in res) {
      showTemplateSavedNotification({ editorType });
      mutateTemplates();
    }
    setSaving(false);
  };

  const handleDelete = async () => {
    if (selectedTemplate && wsId) {
      setDeleting(true);
      const res = await WorkspaceAPI.deleteTemplate(wsId, selectedTemplate.id);

      if (res?.message) {
        mutateTemplates();
        addNewTemplate();
        setConfirmModalOpened(false);
      }
      setDeleting(false);
    }
  };

  const addNewTemplate = useCallback(() => {
    setSelectedTemplate(null);
    setNewTemplate({ id: '', name: '', description: '', content: '' });
    setNameError(false);
    setSubjectError(false);
    setContentError(false);
    editor?.commands.setContent('');
  }, [editor, setNewTemplate]);

  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNameError(false);
    const newValue = e.target.value;
    if (selectedTemplate) {
      setSelectedTemplate({ ...selectedTemplate, name: newValue });
    } else {
      setNewTemplate({ ...newTemplate, name: newValue });
    }
  };

  const handleSubjectChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSubjectError(false);
    const newValue = e.target.value;
    if (selectedTemplate) {
      setSelectedTemplate({ ...selectedTemplate, description: newValue });
    } else {
      setNewTemplate({ ...newTemplate, description: newValue });
    }
  };

  // Add data-template attribute to ProseMirror element when component is mounted
  useEffect(() => {
    if (editor) {
      const editorElement = document.querySelector('.ProseMirror');
      if (editorElement) {
        editorElement.setAttribute('data-template', 'true');
      }
    }
  }, [editor]);

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      size='70%'
      title={editorType === 'email' ? t('manageEmailTemplates') : t('manageSMSTemplates')}
      centered
      bodyPadding={0}
      className={classes.modal}
    >
      <Flex direction='row'>
        <Paper w='25%' p='md' bg='decaLight.0'>
          <Button
            color='decaBlue.5'
            variant='subtle'
            leftSection={<IconPlus size={16} />}
            fullWidth
            mb='md'
            onClick={addNewTemplate}
            bg={!selectedTemplate ? 'white' : 'transparent'}
          >
            {t('newTemplate')}
          </Button>
          <ScrollArea h={400}>
            {templates.map((template) => (
              <Flex
                key={template.id}
                onClick={() => {
                  setSelectedTemplate(template);
                  editor?.commands.setContent(template.content);
                }}
                bg={selectedTemplate?.id === template.id ? 'white' : 'transparent'}
                p='md'
                direction='column'
              >
                <Text fz={rem(14)} c='decaGrey.9'>
                  {template.name}
                </Text>
                <Text fz={rem(12)} c='decaGrey.5' lineClamp={2}>
                  {template.description}
                </Text>
              </Flex>
            ))}
          </ScrollArea>
        </Paper>
        <Divider p={0} orientation='vertical' />
        <Paper p='md' w='75%'>
          <ScrollArea h={`calc(80vh - ${rem(130)})`}>
            <TextInput
              ref={nameRef}
              label={
                <Text fw={500} fz={rem(14)} mb={rem(6)}>
                  <span>{t('templateName')}&nbsp;</span>
                  <span style={{ color: '#fa5252' }}>*</span>
                </Text>
              }
              onChange={handleNameChange}
              error={nameError && t('templateNameRequired')}
              mb='md'
              placeholder={t('inputName')}
              size='md'
            />
            {editorType === 'email' && (
              <TextInput
                ref={desRef}
                label={
                  <Text fw={500} fz={rem(14)} mb={rem(6)}>
                    <span>{t('emailSubject')}&nbsp;</span>
                    <span style={{ color: '#fa5252' }}>*</span>
                  </Text>
                }
                onChange={handleSubjectChange}
                error={subjectError && t('emailSubjectRequired')}
                mb='md'
                placeholder={t('inputSubject')}
                size='md'
              />
            )}
            <Text fw={500} fz={rem(14)} mb={rem(6)}>
              <span>
                {editorType === 'email' ? t('emailContent') : t('smsContent')}
                &nbsp;
              </span>
              <span style={{ color: '#fa5252' }}>*</span>
            </Text>
            <RichTextEditor editor={editor} className={editorClasses.editor}>
              <ScrollArea h={300}>
                <RichTextEditor.Content autoFocus mih={rem(300)} mah={rem(600)} />
              </ScrollArea>
              {editor && editorType === 'email' && (
                <EditorToolbar editor={editor} withAttachment={false} />
              )}
            </RichTextEditor>
            {contentError && (
              <Text c='#fa5252' fz={rem(10)}>
                {t('templateContentRequired')}
              </Text>
            )}
          </ScrollArea>
          <Flex justify='space-between'>
            <Button
              color='red'
              variant='white'
              mt='md'
              onClick={() => setConfirmModalOpened(true)}
              loading={deleting}
              disabled={!selectedTemplate || saving}
            >
              {t('deleteTemplate')}
            </Button>
            <Button mt='md' onClick={handleSave} loading={saving} disabled={deleting}>
              {t('save')}
            </Button>
          </Flex>
        </Paper>
      </Flex>
      <Divider />
      <Box py={rem(16)}>
        <Button variant='white' onClick={onClose} color='decaGrey.6' fw={500}>
          {t('close')}
        </Button>
      </Box>
      <DecaModal
        centered
        opened={confirmModalOpened}
        onClose={() => setConfirmModalOpened(false)}
        onCancel={() => setConfirmModalOpened(false)}
        onOk={handleDelete}
        title={t('deleteTemplate')}
        okText={t('delete')}
        cancelText={t('cancel')}
        okButtonProps={{ variant: 'negative' }}
        footerDivider={false}
      >
        <Text>
          {t('deleteTemplateWarning', {
            template: selectedTemplate?.name || '',
          })}
        </Text>
      </DecaModal>
    </Modal>
  );
};

export default TemplateModal;
