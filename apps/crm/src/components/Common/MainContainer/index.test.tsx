import { AppContextProvider } from '@/contexts/AppContext';
import { MantineProvider, createTheme } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import { render, screen } from '@testing-library/react';
import { vi } from 'vitest';
import MainContainer from '.';

// Mock router dependencies
vi.mock('react-router-dom', () => {
  const mockLocation = {
    pathname: '/',
    search: '?lang=en',
    hash: '',
    state: null,
  };

  const mockNavigate = vi.fn();
  const mockSearchParams = new URLSearchParams('?lang=en');
  const mockSetSearchParams = vi.fn();

  return {
    useNavigate: () => mockNavigate,
    useLocation: () => mockLocation,
    useParams: () => ({}),
    useSearchParams: () => [mockSearchParams, mockSetSearchParams],
  };
});

// Mock useQueryParams hook
vi.mock('@resola-ai/ui/hooks/useQueryParams', () => ({
  useQueryParams: () => ({
    queryParams: {
      getLanguageQueryParam: () => 'en',
      setLanguageQueryParam: vi.fn(),
      removeLanguageQueryParam: vi.fn(),
    },
    setQueryParams: vi.fn(),
    removeQueryParams: vi.fn(),
  }),
}));

// Mock useSetDefaultLang hook
vi.mock('@resola-ai/ui/hooks/useSetDefaultLang', () => ({
  useSetDefaultLang: () => 'en',
  useGetLang: () => 'en',
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn().mockReturnValue('en'),
  setItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

vi.mock('@/tolgee', () => ({
  tolgee: {
    changeLanguage: vi.fn(),
  },
}));

// Mock the hooks that AppContext depends on
vi.mock('@/hooks', () => ({
  useObjects: vi.fn(() => ({
    objects: [],
    mutate: vi.fn(),
  })),
}));

vi.mock('@/services/api', () => ({
  WorkspaceAPI: {
    getFirst: vi.fn().mockResolvedValue({ id: 'workspace-123' }),
    getById: vi.fn().mockResolvedValue({ id: 'workspace-123' }),
  },
}));

vi.mock('@auth0/auth0-react', () => ({
  useAuth0: vi.fn(() => ({
    isAuthenticated: true,
    getAccessTokenSilently: vi.fn().mockResolvedValue('mock-token'),
  })),
}));

vi.mock('swr', () => ({
  default: vi.fn(() => ({
    data: 'workspace-123',
    error: null,
    isLoading: false,
  })),
}));

// Mock react-hook-form-mantine
vi.mock('react-hook-form-mantine', () => ({
  TextInput: ({ control, name }: any) => (
    <div data-testid={`mock-text-input-${name}`}>
      <input type='text' {...control.register(name)} />
    </div>
  ),
}));

// Mock @resola-ai/ui components
vi.mock('@resola-ai/ui', () => ({
  Modal: ({ children }: any) => <div data-testid='mock-modal'>{children}</div>,
  HFCheckbox: ({ label, control, name }: any) => (
    <div data-testid={`mock-checkbox-${name}`}>
      <label>{label}</label>
      <input type='checkbox' {...control.register(name)} />
    </div>
  ),
  HFSwitch: ({ control, name }: any) => (
    <div data-testid={`mock-switch-${name}`}>
      <input type='checkbox' {...control.register(name)} />
    </div>
  ),
  DecaButton: ({ children, onClick, variant }: any) => (
    <button data-testid={`mock-button-${variant}`} onClick={onClick}>
      {children}
    </button>
  ),
}));

// Mock NavbarContext
vi.mock('@/contexts/NavbarContext', () => ({
  NavbarContextProvider: ({ children }: any) => (
    <div data-testid='mock-navbar-context'>{children}</div>
  ),
  useNavbarContext: () => ({
    settingOpened: false,
    onCloseSetting: vi.fn(),
  }),
}));

// Mock useNavigateObject hook
vi.mock('./useNavigateObject', () => ({
  useNavigateObject: () => ({
    handleNavigateObject: vi.fn(),
  }),
}));

// Mock WorkspaceItem component
vi.mock('./WorkspaceItem', () => ({
  default: ({ item }: any) => <div data-testid={`workspace-item-${item.id}`}>{item.name}</div>,
}));

// Mock NavbarContainer component
vi.mock('@/components/NavBar', () => ({
  default: () => <div data-testid='navbar-container'>Navbar Container</div>,
}));

// Mock Mantine Container component
vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal() as any;
  return {
    ...actual,
    Container: ({ children, className, ...props }: any) => (
      <div data-testid='main-container' className={className} {...props}>
        {children}
      </div>
    ),
  };
});

// Mock AppContext
vi.mock('@/contexts/AppContext', () => ({
  AppContextProvider: ({ children }: any) => <>{children}</>,
  useAppContext: () => ({
    showSidebar: true,
    sidebarWidth: 240,
    setSidebarWidth: vi.fn(),
    onToggleSidebar: vi.fn(),
    objects: [],
    mutateObjects: vi.fn(),
    formOpened: false,
    setFormOpened: vi.fn(),
    wsDefault: 'workspace-123',
    reloadObject: '',
    setReloadObject: vi.fn(),
    accessToken: 'mock-token',
  }),
}));

// Create a mock theme
const mockTheme = createTheme({
  colors: {
    decaGrey: [
      '#F8F9FA',
      '#F1F3F5',
      '#E9ECEF',
      '#DEE2E6',
      '#CED4DA',
      '#ADB5BD',
      '#868E96',
      '#495057',
      '#343A40',
      '#212529',
    ],
    decaLight: [
      '#FFFFFF',
      '#F8F9FA',
      '#F1F3F5',
      '#E9ECEF',
      '#DEE2E6',
      '#CED4DA',
      '#ADB5BD',
      '#868E96',
      '#495057',
      '#212529',
    ],
    decaNavy: [
      '#E7F5FF',
      '#D0EBFF',
      '#A5D8FF',
      '#74C0FC',
      '#4DABF7',
      '#339AF0',
      '#228BE6',
      '#1C7ED6',
      '#1971C2',
      '#1864AB',
    ],
    decaBlue: [
      '#E7F5FF',
      '#D0EBFF',
      '#A5D8FF',
      '#74C0FC',
      '#4DABF7',
      '#339AF0',
      '#228BE6',
      '#1C7ED6',
      '#1971C2',
      '#1864AB',
    ],
  },
  white: '#FFFFFF',
});

describe('MainContainer', () => {
  const renderMainContainer = () => {
    return render(
      <MantineEmotionProvider>
        <MantineProvider theme={mockTheme}>
          <AppContextProvider>
            <MainContainer>Test Content</MainContainer>
          </AppContextProvider>
        </MantineProvider>
      </MantineEmotionProvider>
    );
  };

  const renderMainContainerWithProps = (props: any = {}) => {
    return render(
      <MantineEmotionProvider>
        <MantineProvider theme={mockTheme}>
          <AppContextProvider>
            <MainContainer {...props}>Test Content</MainContainer>
          </AppContextProvider>
        </MantineProvider>
      </MantineEmotionProvider>
    );
  };

  it('renders without crashing', () => {
    renderMainContainer();
    expect(screen.getByTestId('main-container')).toBeInTheDocument();
  });

  it('renders children correctly', () => {
    renderMainContainer();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('renders with custom className', () => {
    const customClass = 'custom-container';
    renderMainContainerWithProps({ className: customClass });
    expect(screen.getByTestId('main-container')).toHaveClass(customClass);
  });

  it('renders NavbarContainer component', () => {
    renderMainContainer();
    expect(screen.getByTestId('navbar-container')).toBeInTheDocument();
  });

  it('renders complex children structure', () => {
    render(
      <MantineEmotionProvider>
        <MantineProvider theme={mockTheme}>
          <AppContextProvider>
            <MainContainer>
              <div data-testid='child-1'>Child 1</div>
              <div data-testid='child-2'>
                <span>Nested Child</span>
              </div>
            </MainContainer>
          </AppContextProvider>
        </MantineProvider>
      </MantineEmotionProvider>
    );

    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
    expect(screen.getByText('Nested Child')).toBeInTheDocument();
  });

  it('maintains layout when content changes', () => {
    const { rerender } = render(
      <MantineEmotionProvider>
        <MantineProvider theme={mockTheme}>
          <AppContextProvider>
            <MainContainer>Initial Content</MainContainer>
          </AppContextProvider>
        </MantineProvider>
      </MantineEmotionProvider>
    );

    const initialContainer = screen.getByTestId('main-container');
    const initialStyles = window.getComputedStyle(initialContainer);

    rerender(
      <MantineEmotionProvider>
        <MantineProvider theme={mockTheme}>
          <AppContextProvider>
            <MainContainer>Updated Content</MainContainer>
          </AppContextProvider>
        </MantineProvider>
      </MantineEmotionProvider>
    );

    const updatedContainer = screen.getByTestId('main-container');
    expect(updatedContainer).toHaveStyle({
      width: initialStyles.width,
      overflow: initialStyles.overflow,
      padding: initialStyles.padding,
      margin: initialStyles.margin,
    });
    expect(screen.getByText('Updated Content')).toBeInTheDocument();
  });
});
