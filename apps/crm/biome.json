{"extends": ["@resola-ai/biome-config/biome"], "files": {"ignoreUnknown": true, "ignore": ["**/*.html", "**/*.md", "**/*.yml", "**/*.yaml", ".env*", ".to<PERSON><PERSON><PERSON>", "**/*.svg"]}, "linter": {"rules": {"complexity": {"useLiteralKeys": "off", "useOptionalChain": "warn"}, "style": {"noUnusedTemplateLiteral": "off", "useNodejsImportProtocol": "warn", "noUselessElse": "warn", "useDefaultParameterLast": "warn"}, "a11y": {"noSvgWithoutTitle": "warn", "noLabelWithoutControl": "warn", "useButtonType": "warn", "useSemanticElements": "warn", "useKeyWithClickEvents": "warn"}, "suspicious": {"noArrayIndexKey": "warn", "noImplicitAnyLet": "warn", "noAssignInExpressions": "warn"}, "security": {"noDangerouslySetInnerHtml": "warn"}, "correctness": {"noConstructorReturn": "warn"}, "performance": {"noAccumulatingSpread": "warn"}}}}