{"name": "deca-pages-chef", "version": "1.0.0", "main": "index.js", "scripts": {"build": "tsc"}, "author": "", "license": "ISC", "description": "DECA Page Builder", "dependencies": {"@aws-lambda-powertools/commons": "^2.14.0", "@aws-lambda-powertools/logger": "^2.14.0", "@aws-lambda-powertools/metrics": "^2.14.0", "@aws-lambda-powertools/tracer": "^2.14.0", "@aws-sdk/client-s3": "^3.744.0", "@aws-sdk/client-secrets-manager": "^3.0.0", "@aws-sdk/client-sqs": "^3.744.0", "@middy/core": "^6.0.0", "fernet-nodejs": "^1.0.6", "mime-types": "^2.1.35", "ulid": "^2.3.0", "zod": "^3.24.2"}, "devDependencies": {"@types/aws-lambda": "^8.10.147", "@types/node": "^22.10.9", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}