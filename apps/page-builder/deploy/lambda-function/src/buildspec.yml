---
version: 0.2

env:
  variables:
    BASE_DIR: "./"
    FUNCTION_DIR: "./apps/page-builder/deploy/lambda-function/src"
    DOCKER_IMAGE_URI: $DOCKER_IMAGE_URI
    BUILDER_LAMBDA_ARN: $BUILDER_LAMBDA_ARN
    SNS_WEBHOOK_URL: $SNS_WEBHOOK_URL

phases:
  install:
    runtime-versions:
      nodejs: 20
    commands:
      - npm install -g pnpm@9.15
      - npm install -g terser@5.43.1

  build:
    commands:
      - echo Build started on `date`, at `pwd`
      - pnpm install -C $BASE_DIR
      - export DOCKER_IMAGE_VERSION="${CODEBUILD_RESOLVED_SOURCE_VERSION}"
      - export DOCKER_IMAGE_TAG="${DOCKER_IMAGE_URI}:${DOCKER_IMAGE_VERSION}"
      - docker build $BASE_DIR -f "${FUNCTION_DIR}/Dockerfile" -t $DOCKER_IMAGE_TAG

  post_build:
    commands:
      - docker images
      - export ECR_LOGIN_URL="${ACCOUNT}.dkr.ecr.${REGION}.amazonaws.com"
      - aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $ECR_LOGIN_URL
      - docker push $DOCKER_IMAGE_TAG
      - |
        if [[ "$BUILDER_LAMBDA_ARN" != "" ]]; then
            echo "Attempt to update $BUILDER_LAMBDA_ARN"
            aws lambda update-function-code \
              --function-name $BUILDER_LAMBDA_ARN \
              --image-uri $DOCKER_IMAGE_TAG
            if [[ "$SNS_WEBHOOK_URL" != "" ]]; then
              MESSAGE="Lambda has been deployed to the commit ${DOCKER_IMAGE_VERSION}. Full image tag: ${DOCKER_IMAGE_TAG}"
              curl -XPOST $SNS_WEBHOOK_URL \
                -H "Content-Type: application/json" \
                -d"{\"text\": \"${MESSAGE}\"}"
            fi
        fi

cache:
  paths:
  - "./apps/page-builder/deploy/lambda-function/src/node_modules"
  - "./node_modules"
