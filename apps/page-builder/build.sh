#!/bin/bash

SCRIPTDIR=$(cd `dirname ${0}` && pwd)
BUILDMODE=${1:-"undefined_build_mode"}
# clean the build first
rm -rf $SCRIPTDIR/out $SCRIPTDIR/.next

echo "Enter $BUILDMODE build mode"
case $BUILDMODE in
  static)
    mv $SCRIPTDIR/src/pages/preview $SCRIPTDIR/src/.preview
    cd $SCRIPTDIR && pnpm build-static
    mv $SCRIPTDIR/src/.preview $SCRIPTDIR/src/pages/preview
    ;;
  ssr)
    cd $SCRIPTDIR && pnpm build-ssr
    ;;
  *)
    echo "Invalid or empty build mode: $BUILDMODE"
    exit 1
    ;;
esac

echo "Build succeeds."

exit 0
