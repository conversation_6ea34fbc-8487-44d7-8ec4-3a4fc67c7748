import { getPages, getPage } from '@/api/page';
import { getSite, getSiteData, getSiteSetting } from '@/api/site';
import { removeNullProps, snakeToCamel } from '@/utils/data-transform';
import {
  TextElement,
  ButtonElement,
  MediaElement,
  DividerElement,
  SearchElement,
  PictureCategoryElement,
  IllustrationCategoryElement,
  MapElement,
} from '@resola-ai/ui/components/PageBuilder';
import {
  ContainerWrapper,
  ContainerElement,
  CategoryListElement,
  CategoryTreeElement,
  ArticleDetailElement,
  BreadcrumbElement,
} from '@/components';
import { getCategories } from '@/api/site';
import SearchResultElement from '@/components/SearchResultElement';
import Section from '@/components/Section';
import { LinkDestinationType, SiteDataType } from '@/types/enum';
import { Link } from '@/types/site';
import dynamic from 'next/dynamic';
import { ButtonVariantType } from '@resola-ai/ui/types/pageBuilder';

const BoxyArticlesElement = dynamic(() => import('@/components/BoxyArticlesElement'), {
  ssr: false,
});
const FlatArticlesElement = dynamic(() => import('@/components/FlatArticlesElement'), {
  ssr: false,
});

const buttonVariantMap = {
  [ButtonVariantType.Primary]: 'primary',
  [ButtonVariantType.Secondary]: 'secondary',
  [ButtonVariantType.Text]: 'text',
};

export const componentResolver = {
  TextElement,
  ButtonElement,
  MediaEmbed: MediaElement,
  DividerElement,
  SearchElement,
  ContainerElement,
  ContainerWrapper,
  ArticleDetailEmbed: ArticleDetailElement,
  PictureCategoryElement,
  IllustrationCategoryElement,
  MapEmbed: MapElement,
  CategoryTreeElement,
  CategoryListElement,
  SearchResultElement,
  Section,
  BreadcrumbElement,
  FlatArticlesElement,
  BoxyArticlesElement,
};

const applyBoxColors = (box: any, themeColorsMap: Record<string, string>) => {
  if (!box) return null;

  return {
    ...box,
    backgroundColor: themeColorsMap[box?.backgroundColor ?? 'background'],
    textColor: themeColorsMap[box?.textColor ?? 'foreground'],
  };
};

const PROPS_USING_THEME_COLOR = [
  'color',
  'borderColor',
  'buttonColor',
  'textColor',
  'backgroundColor',
  'accentColor',
  'iconColor',
  'illustrationBackgroundColor',
  'dividerColor',
];

const applyThemeColors = (themeColorsMap: Record<string, string>, props: Record<string, any>) => {
  Object.keys(props).forEach(key => {
    if (props[key] && PROPS_USING_THEME_COLOR.includes(key)) {
      if (typeof props[key] === 'object' && 'desktop' in props[key]) {
        props[key] = {
          desktop: themeColorsMap[props[key].desktop] ?? props[key].desktop,
          tablet: themeColorsMap[props[key].tablet] ?? props[key].tablet,
          mobile: themeColorsMap[props[key].mobile] ?? props[key].mobile,
        };
      } else {
        props[key] = themeColorsMap[props[key]] ?? props[key];
      }
    }
  });

  return props;
};

const getLink = (linkData: Link, pages: Record<string, any>[]) => {
  if (linkData.destination === LinkDestinationType.Page) {
    const _pageUrl = pages.find(p => p.logical_id === linkData.targetId)?.url;
    return { ...linkData, url: getSlug(_pageUrl) };
  }
  return linkData;
};

const getSlug = (slug: string) => slug;

export const enhanceComponentProps = ({
  key,
  component,
  pages,
  categories,
  siteData,
  siteSetting,
  siteId,
}: {
  key: string;
  component: Record<string, any>;
  pages: Record<string, any>[];
  categories: Record<string, any>[];
  siteData: Record<string, any>;
  siteSetting: Record<string, any>;
  siteId: string;
}) => {
  const themeColorsMap = siteSetting?.theme?.content?.colors?.reduce((acc, color) => {
    acc[color.name] = color.color;
    return acc;
  }, {});
  component.props = applyThemeColors(themeColorsMap, component.props);

  switch (component.type?.resolvedName) {
    case 'SearchElement':
      return {
        ...component,
        props: {
          ...component.props,
          elementId: key,
          searchResultPageSlug: getSlug(pages.find(p => p.type === 'faq_search_result')?.url),
          articleDetailSlug: getSlug(pages.find(p => p.type === 'faq_article_detail')?.url),
          borderColor: component.props.borderColor || themeColorsMap.border,
          backgroundColor: component.props.backgroundColor || themeColorsMap.background,
          textColor: component.props.textColor || themeColorsMap.foreground,
          buttonColor: component.props.buttonColor || themeColorsMap.accent,
          placeholderColor: themeColorsMap.text,
        },
      };
    case 'SearchResultElement':
      return {
        ...component,
        props: {
          ...component.props,
          elementId: key,
          siteData: siteData,
          siteId: siteId,
          articleDetailSlug: getSlug(pages.find(p => p.type === 'faq_article_detail')?.url),
          borderColor: component.props.borderColor || themeColorsMap.border,
          backgroundColor: component.props.backgroundColor || themeColorsMap.background,
          iconColor: component.props.iconColor || themeColorsMap.foreground,
          iconBgColor: component.props.iconBgColor || themeColorsMap.background,
          categoryType: component.props.categoryType || 'box',
          illustrationType: component.props.illustrationType || 'icon',
          textColor: themeColorsMap.foreground,
          itemBackgroundColor: themeColorsMap.background,
        },
      };
    case 'PictureCategoryElement':
    case 'IllustrationCategoryElement':
      return {
        ...component,
        props: {
          ...component.props,
          elementId: key,
          url: getSlug(pages.find(p => p.type === 'faq_category_list')?.url),
        },
      };
    case 'ArticleDetailEmbed':
      return {
        ...component,
        props: {
          ...component.props,
          elementId: key,
          textColor: themeColorsMap.foreground,
          dividerColor: themeColorsMap.border,
          contentBackgroundColor: themeColorsMap.background,
          borderColor: themeColorsMap.border,
        },
      };
    case 'CategoryTreeElement':
      return {
        ...component,
        props: {
          ...component.props,
          categories: categories,
          articleDetailSlug: getSlug(pages.find(p => p.type === 'faq_article_detail')?.url),
          categoryListSlug: getSlug(pages.find(p => p.type === 'faq_category_list')?.url),
          styles: {
            root: {
              backgroundColor: component.props.backgroundColor || themeColorsMap.background,
            },
            item: {
              borderColor: `${component.props.dividerColor || themeColorsMap.border} !important`,
            },
            chevron: {
              color: themeColorsMap.text,
            },
            label: {
              color: themeColorsMap.foreground,
            },
            control: {
              '&:hover': {
                backgroundColor: `${themeColorsMap.secondary} !important`,
              },
            },
          },
        },
      };
    case 'CategoryListElement':
      return {
        ...component,
        props: {
          ...component.props,
          categories: categories,
          articleDetailSlug: getSlug(pages.find(p => p.type === 'faq_article_detail')?.url),
          textColor: themeColorsMap.foreground,
          iconColor: themeColorsMap.text,
          containerBorderColor: themeColorsMap.border,
        },
      };
    case 'BreadcrumbElement':
      return {
        ...component,
        props: {
          ...component.props,
          categories: categories,
          articleDetailSlug: getSlug(pages.find(p => p.type === 'faq_article_detail')?.url),
          categoryListSlug: getSlug(pages.find(p => p.type === 'faq_category_list')?.url),
          borderColor: themeColorsMap?.border,
        },
      };
    case 'ButtonElement':
      const buttonSettings = siteSetting?.theme?.content?.button;
      const buttonColors = buttonSettings[buttonVariantMap[component.props.variant]];
      return {
        ...component,
        props: {
          ...component.props,
          link: component.props.link ? getLink(component.props.link, pages) : null,
          color: themeColorsMap[component.props.color || buttonColors?.background_color],
          textColor: themeColorsMap[component.props.textColor || buttonColors?.text_color],
          borderColor: themeColorsMap[buttonColors?.border_color],
          radius: buttonSettings.border_radius,
        },
      };
    case 'TextElement':
      return {
        ...component,
        props: {
          ...component.props,
          link: component.props.link ? getLink(component.props.link, pages) : null,
          typographySettings: siteSetting?.theme?.content?.typography,
        },
      };
    case 'MediaEmbed':
      return {
        ...component,
        props: {
          ...component.props,
          link: component.props.link ? getLink(component.props.link, pages) : null,
        },
      };
    case 'BoxyArticlesElement':
    case 'FlatArticlesElement':
      return {
        ...component,
        props: {
          ...component.props,
          answerBox: applyBoxColors(component.props.answerBox, themeColorsMap),
          questionBox: applyBoxColors(component.props.questionBox, themeColorsMap),
        },
      };
    case 'DividerElement':
      return {
        ...component,
        props: {
          ...component.props,
          color: component.props.color ?? themeColorsMap['border'],
        },
      };
    default:
      return component;
  }
};

export const processPageData = async (siteId: string, slug: string) => {
  const [page, pages, categories, siteData, site, siteSetting] = await Promise.all([
    getPage(siteId, slug),
    getPages(siteId),
    getCategories(siteId),
    getSiteData(siteId, SiteDataType.FaqArticles),
    getSite(siteId),
    getSiteSetting(siteId),
  ]);

  if (!page) return null;

  return {
    site,
    siteSetting,
    page: {
      ...page,
      content: removeNullProps(
        Object.fromEntries(
          Object.entries(page.content).map(([key, value]) => {
            const camelValue = snakeToCamel(value);
            return [
              key,
              enhanceComponentProps({
                key,
                component: camelValue,
                pages,
                categories,
                siteData,
                siteSetting,
                siteId,
              }),
            ];
          })
        )
      ),
    },
  };
};
