import { useThemeChange } from '@/contexts/ThemeChangeContext';
import { useUserInfor } from '@/hooks';
import { useCurrentTheme } from '@/hooks/theme/useCurrentTheme';
import { useThemes } from '@/hooks/theme/useThemes';
import { ThemeAPI } from '@/services/api';
import { showNotificationToast } from '@/utils/notification';
import { Box, Divider, Flex, Stack, Text, Tooltip } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useState } from 'react';
import ColorsBar from './ColorsBar';
import DesignPreviewCard from './DesignCardPreview';
import PublishThemeButton from './PublishThemeButton';
import classes from './StylingMenu.module.css';

const MAX_THEMES = 5;

interface DesignTabProps {
  setShowColorSettings: (show: boolean) => void;
  setShowTypographySettings: (show: boolean) => void;
  setShowButtonSettings: (show: boolean) => void;
}

const DesignTab = ({
  setShowColorSettings,
  setShowTypographySettings,
  setShowButtonSettings,
}: DesignTabProps) => {
  const { t } = useTranslate('builder');
  const { isStudioUser } = useUserInfor();
  const { themes, mutate: mutateThemes } = useThemes();
  const { theme: selectedTheme, colorsMap } = useCurrentTheme();
  const { isThemeChanged, setInitialTheme } = useThemeChange();
  const [isSaving, setIsSaving] = useState(false);
  const reachedMaxThemes = themes?.length >= MAX_THEMES;
  const disabledSaveButton = reachedMaxThemes || !isThemeChanged(selectedTheme);

  const saveButtonTooltip = reachedMaxThemes
    ? t('saveAsThemeTooltipMessage', {
        maxThemes: 5,
      })
    : t('youHaveSavedThisTheme');

  const handleSaveAsTheme = async () => {
    try {
      const { button, typography, colors } = selectedTheme;
      const newTheme = { button, typography, colors };
      setIsSaving(true);
      await ThemeAPI.create({
        content: newTheme,
      });
      mutateThemes();
      setInitialTheme(newTheme);
      showNotificationToast({
        message: t('saveAsThemeSuccessMessage', {
          themeName: 'My Theme',
        }),
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Flex direction='column' justify='space-between' h='100%'>
      <Stack p='sm' gap='lg' className={classes.tabContent}>
        <Text c='decaNavy.5' fz='sm' fw={700} tt='uppercase'>
          {t('styles')}
        </Text>
        <DesignPreviewCard title={t('color')} onClick={() => setShowColorSettings(true)}>
          <ColorsBar colors={colorsMap || {}} />
        </DesignPreviewCard>

        <DesignPreviewCard title={t('typography')} onClick={() => setShowTypographySettings(true)}>
          <Stack gap='xs'>
            <Text
              size='xl'
              fw={700}
              style={{
                fontFamily: selectedTheme?.typography?.heading?.font_family,
                fontWeight: selectedTheme?.typography?.heading?.font_weight,
              }}
            >
              {selectedTheme?.typography?.heading?.font_family}
            </Text>
            <Text
              size='xs'
              c='dimmed'
              style={{
                fontFamily: selectedTheme?.typography?.caption?.font_family,
                fontWeight: selectedTheme?.typography?.caption?.font_weight,
              }}
            >
              {selectedTheme?.typography?.caption?.font_family}
            </Text>
          </Stack>
        </DesignPreviewCard>

        <DesignPreviewCard title={t('button')} onClick={() => setShowButtonSettings(true)}>
          <DecaButton
            radius={selectedTheme?.button?.border_radius}
            bg={
              colorsMap?.[selectedTheme?.button?.primary?.background_color] ??
              selectedTheme?.button?.primary?.background_color
            }
            c={
              colorsMap?.[selectedTheme?.button?.primary?.text_color] ??
              selectedTheme?.button?.primary?.text_color
            }
            style={{
              border: `1px solid ${colorsMap?.[selectedTheme?.button?.primary?.border_color] || selectedTheme?.button?.primary?.border_color}`,
            }}
          >
            {t('buttonText')}
          </DecaButton>
        </DesignPreviewCard>
      </Stack>
      <Divider />
      <Box p='md'>
        {isStudioUser ? (
          <PublishThemeButton />
        ) : disabledSaveButton ? (
          <Tooltip label={saveButtonTooltip}>
            <DecaButton
              w='100%'
              variant='neutral'
              size='sm'
              loading={isSaving}
              disabled={disabledSaveButton}
              onClick={handleSaveAsTheme}
            >
              {t('saveAsTheme')}
            </DecaButton>
          </Tooltip>
        ) : (
          <DecaButton
            w='100%'
            variant='neutral'
            size='sm'
            loading={isSaving}
            disabled={disabledSaveButton}
            onClick={handleSaveAsTheme}
          >
            {t('saveAsTheme')}
          </DecaButton>
        )}
      </Box>
    </Flex>
  );
};

export default DesignTab;
