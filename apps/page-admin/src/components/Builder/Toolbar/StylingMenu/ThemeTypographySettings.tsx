import { fontFamilies, fontWeights } from '@/constants/theme';
import { useCurrentTheme } from '@/hooks/theme/useCurrentTheme';
import { Divider, NativeSelect, Stack, Text } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import BackButton from './BackButton';
import classes from './StylingMenu.module.css';

interface ThemeTypographySettingsProps {
  onBack: () => void;
}

const ThemeTypographySettings = ({ onBack }: ThemeTypographySettingsProps) => {
  const { t } = useTranslate('builder');
  const { theme, updateTheme } = useCurrentTheme();

  const handleFontFamilyChange =
    (type: 'heading' | 'caption' | 'body') => async (e: React.ChangeEvent<HTMLSelectElement>) => {
      e.stopPropagation();
      const value = e.target.value;
      if (!value) return;

      const updatedTheme = {
        ...theme,
        typography: {
          ...theme.typography,
          [type]: {
            ...theme.typography[type],
            font_family: value,
          },
        },
      };
      await updateTheme(updatedTheme);
    };

  const handleFontWeightChange =
    (type: 'heading' | 'caption') => async (e: React.ChangeEvent<HTMLSelectElement>) => {
      e.stopPropagation();
      const value = e.target.value;
      if (!value) return;

      const updatedTheme = {
        ...theme,
        typography: {
          ...theme.typography,
          [type]: {
            ...theme.typography[type],
            font_weight: value.toLowerCase(),
          },
        },
      };
      await updateTheme(updatedTheme);
    };

  return (
    <Stack gap='0' className={classes.settingsContainer}>
      <BackButton onBack={onBack} />
      <Text tt='uppercase' size='sm' c='decaNavy.4' fw={600}>
        {t('typography')}
      </Text>
      <Stack gap='lg' mt='md'>
        <Stack gap='xs'>
          <Text fw={500}>{t('heading')}</Text>
          <NativeSelect
            data={fontFamilies}
            value={theme.typography?.heading?.font_family}
            onChange={handleFontFamilyChange('heading')}
          />
          <Text>{t('fontWeight')}</Text>
          <NativeSelect
            data={fontWeights}
            value={
              theme.typography?.heading?.font_weight.charAt(0).toUpperCase() +
              theme.typography?.heading?.font_weight.slice(1)
            }
            onChange={handleFontWeightChange('heading')}
          />
        </Stack>
        <Divider />
        <Stack gap='xs'>
          <Text fw={500}>{t('body')}</Text>
          <NativeSelect
            data={fontFamilies}
            value={theme.typography?.body?.font_family}
            onChange={handleFontFamilyChange('body')}
          />
        </Stack>
        <Divider />
        <Stack gap='xs'>
          <Text fw={500}>{t('paragraphCaption')}</Text>
          <NativeSelect
            data={fontFamilies}
            value={theme.typography?.caption?.font_family}
            onChange={handleFontFamilyChange('caption')}
          />
          <Text>{t('fontWeight')}</Text>
          <NativeSelect
            data={fontWeights}
            value={
              theme.typography?.caption?.font_weight.charAt(0).toUpperCase() +
              theme.typography?.caption?.font_weight.slice(1)
            }
            onChange={handleFontWeightChange('caption')}
          />
        </Stack>
      </Stack>
    </Stack>
  );
};

export default ThemeTypographySettings;
