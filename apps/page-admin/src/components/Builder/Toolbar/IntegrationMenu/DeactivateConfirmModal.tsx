import { useToolbar } from '@/contexts/ToolbarContext';
import { Button, Group, Modal, Stack, Text, ThemeIcon, rem } from '@mantine/core';
import { IconAlertCircle } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { forwardRef, useEffect, useRef } from 'react';

interface DeactivateConfirmModalProps {
  opened: boolean;
  onClose: () => void;
  onConfirm: () => void;
  integrationName: string;
}

const DeactivateConfirmModal = forwardRef<HTMLDivElement, DeactivateConfirmModalProps>(
  ({ opened, onClose, onConfirm, integrationName }) => {
    const { t } = useTranslate('integrations');
    const modalRef = useRef<HTMLDivElement>(null);
    const { addOutsideRef, removeOutsideRef } = useToolbar();

    useEffect(() => {
      if (opened && modalRef.current) {
        addOutsideRef(modalRef.current);
      }
      return () => {
        if (modalRef.current) {
          removeOutsideRef(modalRef.current);
        }
      };
    }, [opened, addOutsideRef, removeOutsideRef]);

    const handleDeactivate = () => {
      onConfirm();
      onClose();
    };

    return (
      <Modal
        opened={opened}
        onClose={onClose}
        size='md'
        centered
        padding={`${rem(24)}`}
        ref={modalRef}
      >
        <Stack gap='md'>
          <ThemeIcon color='red.1' size={60} radius={60}>
            <IconAlertCircle size={34} stroke={1.5} color='red' />
          </ThemeIcon>

          <Text fw={500} size='lg'>
            {t('deactivateIntegrationTitle')}
          </Text>

          <Text size='md' c='dimmed'>
            {t('deactivateIntegrationContent', { integration: integrationName })}
          </Text>

          <Group justify='right' mt={rem(20)} w='100%'>
            <Button variant='default' onClick={onClose}>
              <Text size='md'>{t('cancelLabel')}</Text>
            </Button>
            <Button color='red' onClick={handleDeactivate}>
              <Text size='md'>{t('deactivateLabel')}</Text>
            </Button>
          </Group>
        </Stack>
      </Modal>
    );
  }
);

DeactivateConfirmModal.displayName = 'DeactivateConfirmModal';

export default DeactivateConfirmModal;
