import ChattingWindowImage from '@/assets/images/chatting-window-connect.svg';
import ImageMessage from '@/assets/images/img-messges.svg';
import { Image, Text } from '@mantine/core';
import { useTranslate } from '@tolgee/react';

const NoChatwindowContent = () => {
  const { t } = useTranslate('integrations');

  return (
    <>
      <Image src={ChattingWindowImage} width={238} />

      <Image src={ImageMessage} style={{ width: 60, height: 60 }} />

      <Text size='md' c='dimmed'>
        {t('noChatwindowFound')}
      </Text>

      <Text size='md' c='dimmed'>
        {t('noChatwindowDescription')}
      </Text>
    </>
  );
};

export default NoChatwindowContent;
