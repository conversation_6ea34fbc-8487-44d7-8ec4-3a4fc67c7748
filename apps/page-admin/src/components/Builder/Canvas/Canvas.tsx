import { useCurrentTheme } from '@/hooks/theme/useCurrentTheme';
import { useAppSelector } from '@/store/hooks';
import type { Page } from '@/types/site';
import { Element, Frame, useEditor } from '@craftjs/core';
import { Box, Flex } from '@mantine/core';
import {
  BUILDER_CONTAINER_ELEMENT_ID,
  PREVIEW_BUILDER_CONTAINER_ELEMENT_ID,
} from '@resola-ai/ui/constants/page-builder';
import { useEffect, useMemo } from 'react';
import styles from './Canvas.module.css';

interface BuilderCanvasProps {
  content: Page['content'] | undefined;
  preview?: boolean;
  customWidth?: string;
}

const removeEmptyProps = (obj: Record<string, any> | undefined) => {
  if (!obj) return obj;

  const clean = (obj) => {
    if (obj?.props) {
      Object.keys(obj.props).forEach((key) => {
        const val = obj.props[key];
        if (
          val === null ||
          (Array.isArray(val) && val.length === 0) ||
          (typeof val === 'object' && !Array.isArray(val) && Object.keys(val).length === 0)
        ) {
          delete obj.props[key];
        }
      });
    }

    Object.values(obj).forEach((val) => {
      if (typeof val === 'object' && val !== null) {
        clean(val);
      }
    });
  };

  clean(obj);
  return obj;
};

const getEmptyContent = () => {
  return {
    ROOT: {
      type: 'div',
      isCanvas: true,
      props: {
        style: { height: '100%' },
      },
      displayName: 'div',
      custom: {},
      parent: null,
      hidden: false,
      nodes: [],
      linkedNodes: {},
    },
  };
};

const BuilderCanvas = ({ content, preview = false, customWidth }: BuilderCanvasProps) => {
  const { actions } = useEditor();
  const { theme } = useCurrentTheme();
  const builderWidth = useAppSelector((state) => state.headerNavigation.builderWidth) || '1200px';
  const zoom = useAppSelector((state) => state.headerNavigation.builderZoom) || '100%';
  const _content = useMemo(() => removeEmptyProps(content), [content]);
  const jsonContent = JSON.stringify(_content);

  useEffect(() => {
    if (_content?.ROOT?.nodes && _content?.ROOT?.nodes?.length > 0) {
      actions.deserialize(_content);
    } else {
      actions.deserialize(getEmptyContent());
    }
  }, [JSON.stringify(_content)]);

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    if (preview) return;
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (preview) return;
  };

  return (
    <Flex className={styles.container} mih='100%' justify='center'>
      <Box
        className={styles.canvas}
        onDragEnter={preview ? undefined : handleDragEnter}
        onDragOver={preview ? undefined : handleDragOver}
        onDrop={preview ? undefined : handleDrop}
        maw={customWidth || builderWidth}
        mx={customWidth ? 'auto' : ''}
        style={{
          transform: `scale(${zoom})`,
          fontFamily: `'${theme.typography?.body?.font_family || ''}'`,
        }}
        id={preview ? PREVIEW_BUILDER_CONTAINER_ELEMENT_ID : BUILDER_CONTAINER_ELEMENT_ID}
      >
        <Frame data={jsonContent}>
          <Element is={'div'} canvas />
        </Frame>
      </Box>
    </Flex>
  );
};

export default BuilderCanvas;
