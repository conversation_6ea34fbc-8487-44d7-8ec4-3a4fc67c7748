import { DefaultElementProps } from '@/constants/craftjs';
import { useResponsiveNode } from '@/hooks';
import { useCurrentTheme } from '@/hooks/theme/useCurrentTheme';
import { PageBuilderElement } from '@/types/enum';
import { useEditor } from '@craftjs/core';
import { rem } from '@mantine/core';
import { SearchElement as SearchElementUI } from '@resola-ai/ui/components/PageBuilder';
import SearchSettings from '../Settings/SearchSettings/SearchSettings';
import { ElementWrapper } from './ElementWrapper';

const SearchElement = ({ type }: { type: string }) => {
  const { getThemeColor } = useCurrentTheme();

  const {
    size,
    placeholder,
    showSearchButton,
    borderColor,
    backgroundColor,
    textColor,
    buttonColor,
    quickResultsPanel,
    selectedArticles,
    isKbEnabled,
    width,
  } = useResponsiveNode((node) => ({
    type: node.data.props.type || 'round',
    size: node.data.props.size || 's',
    borderColor: node.data.props.borderColor,
    backgroundColor: node.data.props.backgroundColor,
    textColor: node.data.props.textColor,
    buttonColor: node.data.props.buttonColor,
    placeholder: node.data.props.placeholder,
    showSearchButton: node.data.props.showSearchButton,
    quickResultsPanel: node.data.props.quickResultsPanel,
    selectedArticles: node.data.props.selectedArticles,
    isKbEnabled: node.data.props.isKbEnabled,
    width: node.data.props.width,
  }));

  const { query } = useEditor();
  const editorOptions = query.getOptions();
  return (
    <ElementWrapper style={{ width: rem(width) }}>
      <SearchElementUI
        placeholder={placeholder}
        type={type}
        backgroundColor={getThemeColor(backgroundColor, 'background')}
        borderColor={getThemeColor(borderColor, 'border')}
        textColor={getThemeColor(textColor, 'foreground')}
        buttonColor={getThemeColor(buttonColor, 'accent')}
        placeholderColor={getThemeColor('text')}
        showSearchButton={showSearchButton}
        quickResultsPanel={!editorOptions.enabled && quickResultsPanel}
        isKbEnabled={isKbEnabled}
        selectedArticles={selectedArticles}
        size={size}
      />
    </ElementWrapper>
  );
};

SearchElement.craft = {
  props: DefaultElementProps[PageBuilderElement.SearchElement],
  related: {
    settings: SearchSettings,
  },
};

export default SearchElement;
