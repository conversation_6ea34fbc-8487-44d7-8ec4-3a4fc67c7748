import { useCurrentTheme } from '@/hooks/theme/useCurrentTheme';
import type { ThemeColor } from '@/types';
import { Box, Button, Flex, Group, Popover, Switch, Text, useMantineTheme } from '@mantine/core';
import { IconChevronDown } from '@tabler/icons-react';
import { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { ColorList } from './ColorList';
import { CustomColorPicker } from './CustomColorPicker';
import { MAX_COLORS } from './constants';

interface ColorSettingsProps {
  label: string;
  showEnableSwitch?: boolean;
  defaultEnable?: boolean;
  onChange: (color: string) => void;
  defaultValue?: string;
  onEnableChange?: (enable: boolean) => void;
}

const ColorSettings = ({
  label,
  showEnableSwitch,
  defaultEnable = true,
  onChange,
  onEnableChange,
  defaultValue = '',
}: ColorSettingsProps) => {
  const theme = useMantineTheme();
  const { siteId } = useParams();
  const {
    getThemeColor,
    theme: { colors },
  } = useCurrentTheme();

  const [showPickColor, setShowPickColor] = useState(false);
  const [pickColor, setPickColor] = useState('');
  const [enable, setEnable] = useState(defaultEnable);
  const [opened, setOpened] = useState(false);
  const [newColor, setNewColor] = useState<string | null>(null);
  const [themeColors, setThemeColors] = useState<ThemeColor[]>(colors ?? []);
  const { updateTheme } = useCurrentTheme();

  const handlePickColor = useCallback(
    (color: string) => {
      setPickColor(color);
      onChange(color);
    },
    [onChange]
  );

  useEffect(() => {
    setPickColor(defaultValue);
  }, [defaultValue]);

  const handleAddNewColor = async (color: string | null) => {
    if (!siteId || !color) return;

    const numberOfCustomColors = colors.filter((c) => c.name.startsWith('custom_')).length;
    const newColorName = `custom_${numberOfCustomColors + 1}`;
    const newThemeColors = [...colors, { name: newColorName, color: color }];

    updateTheme((prevTheme) => ({
      ...prevTheme,
      colors: newThemeColors,
    }));
    setNewColor(null);
    setThemeColors(newThemeColors);
    setShowPickColor(false);
    handlePickColor(newColorName);
  };

  const handleColorPickerClose = useCallback(async () => {
    setShowPickColor(false);
    await handleAddNewColor(newColor);
  }, [newColor]);

  const handlePopoverClose = useCallback(async () => {
    setOpened(false);
    await handleAddNewColor(newColor);
  }, [newColor]);

  const handlePickColorChange = useCallback((color: string) => {
    handlePickColor(color);
    setNewColor(color);
  }, []);

  return (
    <Flex align={'center'} justify={'space-between'} gap={'xl'}>
      <Group gap='xs' style={{ flexShrink: 0 }}>
        <Text fw={500} c='decaGrey.9'>
          {label}
        </Text>
        {showEnableSwitch && (
          <Switch
            size='xs'
            color='green'
            defaultChecked={enable}
            onChange={() => {
              setEnable(!enable);
              onEnableChange?.(!enable);
            }}
          />
        )}
      </Group>
      <Popover
        onClose={handlePopoverClose}
        onChange={setOpened}
        opened={opened}
        width={230}
        position='bottom-end'
        shadow='md'
      >
        <Popover.Target component='div'>
          <Button
            variant='default'
            w={85}
            size='xs'
            onClick={() => {
              if (!enable) return;
              setOpened(!opened);
            }}
            style={{ pointerEvents: enable ? 'auto' : 'none' }}
            leftSection={
              <Box
                w={16}
                h={16}
                bg={getThemeColor(pickColor)}
                style={{ borderRadius: 2, border: `1px solid ${theme.colors.decaLight[2]}` }}
              />
            }
            rightSection={<IconChevronDown size={14} />}
          />
        </Popover.Target>
        <Popover.Dropdown>
          {showPickColor ? (
            <CustomColorPicker
              value={pickColor}
              onChange={handlePickColorChange}
              onBack={handleColorPickerClose}
            />
          ) : (
            <ColorList
              colors={themeColors}
              selectedColor={pickColor}
              onAddNewColor={() => setShowPickColor(true)}
              allowAddNewColor={themeColors.length < MAX_COLORS}
              onSelectColor={handlePickColor}
            />
          )}
        </Popover.Dropdown>
      </Popover>
    </Flex>
  );
};

export default ColorSettings;
