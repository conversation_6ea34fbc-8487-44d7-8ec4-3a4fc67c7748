import ScreenSizeSelector from '@/components/Builder/ScreenSizeSelector';
import { AppConfig } from '@/configs/app';
import { useUserInfor } from '@/hooks';
import usePages from '@/hooks/usePages';
import type { ScreenSizeType } from '@/hooks/useScreenSize';
import {
  setBuilderCanvasPreviewMode,
  setHeaderNavigationShowingPreview,
} from '@/store/action/headerNavigation';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { Device, type Page } from '@/types';
import { Box, Flex, Modal, Select, rem, useMantineTheme } from '@mantine/core';
import { LoadingOverlay } from '@mantine/core';
import { useAuthentication } from '@resola-ai/ui/hooks';
import { IconArrowLeft } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Label } from '../Label';

type Props = {
  isOpen: boolean;
  onClose: () => void;
  id?: string;
  pageId?: string;
};

const PREVIEW_COOKIES = {
  SITE_ID: 'preview_site_id',
  ACCESS_TOKEN: 'preview_access_token',
  ORG_ID: 'preview_org_id',
};

export const PreviewModal = ({ isOpen, onClose, id, pageId }: Props) => {
  const { t } = useTranslate('site_settings');
  const theme = useMantineTheme();
  const dispatch = useAppDispatch();
  const { builderPreviewMode } = useAppSelector((state) => state.headerNavigation);
  const [pages, setPages] = useState<Page[]>([]);
  const [selectedPageIndex, setSelectedPageIndex] = useState<number>(0);
  const [customWidth, setCustomWidth] = useState<string>('1200px');
  const [isIframeLoading, setIsIframeLoading] = useState<boolean>(false);
  const { organizationId } = useUserInfor();
  const { accessToken } = useAuthentication();

  const { siteId: resourceId } = useParams();
  const { isLoading: isLoadingPages, mutate } = usePages(
    {
      siteId: resourceId as string,
      versionId: id || '',
    },
    {
      onSuccess: (data: Page[]) => {
        setPages(data);
        if (data.length > 0) {
          let index = 0;
          if (pageId) {
            index = data.findIndex((page: Page) => page.id === pageId);
          }
          setSelectedPageIndex(index);
        }
      },
    }
  );

  useEffect(() => {
    if (pageId) {
      mutate();
    }
  }, [pageId]);

  useEffect(() => {
    if (isOpen) {
      mutate();
      dispatch(setHeaderNavigationShowingPreview(true));
    }
  }, [isOpen]);

  useEffect(() => {
    if (isOpen && pages.length > 0 && accessToken) {
      const setCookiesForPreview = () => {
        const url = new URL(window.location.href);
        const domain = url.hostname.split('.').slice(-2).join('.');

        // Only add SameSite and Secure flags for non-localhost environments
        const isLocalhost = url.hostname === 'localhost';
        const cookieFlags = isLocalhost ? '' : '; Secure; SameSite=Lax';

        document.cookie = `${PREVIEW_COOKIES.SITE_ID}=${resourceId}; domain=${domain}; path=/${cookieFlags}`;
        document.cookie = `${PREVIEW_COOKIES.ACCESS_TOKEN}=${accessToken}; domain=${domain}; path=/${cookieFlags}`;
        document.cookie = `${PREVIEW_COOKIES.ORG_ID}=${organizationId}; domain=${domain}; path=/${cookieFlags}`;
      };

      setCookiesForPreview();
    }
  }, [isOpen, pages, accessToken, resourceId, organizationId]);

  const pageOptions = pages.map((page, index) => ({
    value: index.toString(),
    label: page.name || `Page ${index + 1}`,
  }));

  const handleWidthChange = (width: string) => {
    setCustomWidth(width);
    const widthNum = Number.parseInt((width || '1200px').replace('px', ''));
    dispatch(
      setBuilderCanvasPreviewMode(
        widthNum >= 1200 ? Device.Desktop : widthNum >= 768 ? Device.Tablet : Device.Mobile
      )
    );
  };

  const onCloseModal = () => {
    onClose();
    dispatch(setBuilderCanvasPreviewMode(Device.Desktop));
    dispatch(setHeaderNavigationShowingPreview(false));
  };
  const handlePageChange = (value: string | null) => {
    if (value !== null) {
      setSelectedPageIndex(Number(value));
      setIsIframeLoading(true);
    }
  };

  const handleIframeLoad = () => {
    setIsIframeLoading(false);
  };

  const currentPage = pages[selectedPageIndex];
  const previewUrl = currentPage ? `${AppConfig.PREVIEW_URL}/${currentPage.url}` : '';

  useEffect(() => {
    if (previewUrl) {
      setIsIframeLoading(true);
    }
  }, [previewUrl]);

  return (
    <Modal
      opened={isOpen}
      onClose={onCloseModal}
      withCloseButton={false}
      size='100%'
      styles={{
        body: {
          padding: rem(16),
          paddingTop: 0,
          paddingBottom: 0,
        },
      }}
      fullScreen
    >
      <Box>
        <Flex align='center' gap={rem(16)} justify='space-between' m={rem(16)}>
          <Flex
            onClick={onCloseModal}
            sx={{ cursor: 'pointer' }}
            align='center'
            justify='center'
            gap={rem(8)}
          >
            <IconArrowLeft color={theme.colors.blue[6]} size={20} />
            <Label text={t('backButton')} fw={500} size={rem(16)} mb={0} color='blue.6' />
          </Flex>
          <ScreenSizeSelector
            initialSizeType={builderPreviewMode.toUpperCase() as ScreenSizeType}
            onWidthChange={handleWidthChange}
          />
          <Flex align='center' gap={rem(8)}>
            <Select
              data={pageOptions}
              value={selectedPageIndex.toString()}
              onChange={handlePageChange}
              w={rem(150)}
              disabled={isLoadingPages || pages.length === 0}
            />
          </Flex>
        </Flex>
      </Box>
      <Box sx={{ position: 'relative', height: 'calc(100vh - 70px)' }}>
        <LoadingOverlay visible={isLoadingPages || isIframeLoading} />
        {!isLoadingPages && pages.length > 0 && previewUrl && (
          <Flex sx={{ flex: 1, height: '100%' }} justify='center' bg={'silverFox.2'}>
            <Box sx={{ width: customWidth, height: '100%' }}>
              <iframe
                title={t('preview')}
                src={previewUrl}
                width='100%'
                height='100%'
                onLoad={handleIframeLoad}
                style={{
                  border: 'none',
                  opacity: isIframeLoading ? 0 : 1,
                  transition: 'opacity 0.3s ease',
                }}
              />
            </Box>
          </Flex>
        )}
      </Box>
    </Modal>
  );
};

export default PreviewModal;
