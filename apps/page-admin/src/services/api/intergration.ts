import type { IntegrationPayload } from '@/types/Intergration';
import { axiosService } from '@resola-ai/services-shared';

export const ChatbotAPI = {
  getListChatwindow: async () => {
    const res = await axiosService.instance.get('/integrations/chatwindows');
    return res?.data?.response;
  },
  getIntegration: async (filter: { site_id: string; type: string }) => {
    const res = await axiosService.instance.get('/integrations', {
      params: {
        filter: JSON.stringify(filter),
      },
    });
    return res.data;
  },
  connectChatwindow: async (data: IntegrationPayload) => {
    const res = await axiosService.instance.post('/integrations', data);
    return res.data;
  },
  updateIntegration: async (siteId: string, data: IntegrationPayload) => {
    const res = await axiosService.instance.patch(`/integrations/${siteId}`, data);
    return res.data;
  },
};
