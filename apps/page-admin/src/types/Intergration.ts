type ChatWindow = {
  id: string;
  status: string;
  title: string;
  description: string;
  created_at: string;
  updated_at: string;
};

type IntegrationPayload = {
  site_id: string;
  type: string;
  resource_id: string;
  is_enabled?: boolean;
  options: {
    page_scope: 'all_pages' | 'specific_pages';
    page_ids: string[];
  };
};

type Integration = {
  id: string;
  title: string;
  site_id: string;
  type: string;
  resource_id: string;
  is_enabled: boolean;
  options: {
    page_scope: 'all_pages' | 'specific_pages';
    page_ids: string[];
  };
  status: 'connected' | 'disconnected';
  created_at: string;
  updated_at: string;
};

export type { ChatWindow, IntegrationPayload, Integration };
