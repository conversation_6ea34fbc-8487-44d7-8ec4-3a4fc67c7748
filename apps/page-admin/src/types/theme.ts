export interface ThemeColors {
  primary: string;
  background: string;
  secondary: string;
  foreground: string;
  text: string;
  border: string;
  accent: string;
}

export interface Typography {
  body: {
    font_family: string;
  };
  caption: {
    font_family: string;
    font_weight: string;
  };
  heading: {
    font_family: string;
    font_weight: 'bold' | 'regular';
  };
}

export interface ButtonStyles {
  border_radius: number;
  primary: {
    background_color: string;
    border_color: string;
    text_color: string;
  };
  secondary: {
    background_color: string;
    border_color: string;
    text_color: string;
  };
  text: {
    text_color: string;
  };
}

export interface Theme {
  colors: ThemeColor[];
  typography: Typography;
  button: ButtonStyles;
}

export interface ThemeItem {
  id: string;
  name: string;
  content: Theme;
  cat_id: string;
  cat_name: string;
  published_by: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface ThemeCategory {
  id: string;
  name: string;
  type: string;
  index: number;
  variants: {
    number: number;
    name: string;
  }[];
}

export interface PublishThemePayload {
  cat_id: string;
  content: Record<string, any>;
}

export enum ThemeSource {
  STUDIO = 'studio',
  WORKSPACE = 'workspace',
}

export interface ThemeColor {
  name: string;
  color: string;
}
