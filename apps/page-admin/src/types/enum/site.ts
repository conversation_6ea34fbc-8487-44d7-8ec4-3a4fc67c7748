export enum SiteStatus {
  Draft = 'draft',
  Published = 'published',
}

export enum SitePath {
  Builder = 'builder',
  Settings = 'settings',
}

export enum SiteResourceType {
  Page = 'page',
  Site = 'site',
}

export enum PageType {
  Custom = 'custom',
  FaqSearchResult = 'faq_search_result',
  FaqCategoryList = 'faq_category_list',
  FaqArticleDetail = 'faq_article_detail',
  FaqAnswer = 'faq_answer',
  QuestionList = 'question_list',
}

export enum ToolbarMenuKey {
  AddElementMenu = 'add-element',
  LayerMenu = 'layer',
  PagesMenu = 'pages',
  StylingMenu = 'style',
  StudioThemeMenu = 'studio-theme',
  IntegrationMenu = 'integration',
}
