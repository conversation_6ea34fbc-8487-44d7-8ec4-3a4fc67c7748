import { AppConfig } from '@/configs/app';
import { useAuth0 } from '@auth0/auth0-react';

export const useUserInfor = () => {
  const { user } = useAuth0();
  const userNameSpace = user?.namespace;
  const organizationId = userNameSpace && user?.[`${userNameSpace}/organization`]?.organization_id;
  const isStudioUser = AppConfig.STUDIO_OIDS?.split(',').includes(organizationId);

  return {
    user,
    isStudioUser,
    organizationId,
  };
};
