import { ChatbotAP<PERSON> } from '@/services/api/intergration';
import type { Integration } from '@/types/Intergration';
import useSWR from 'swr';

const useDecaIntergrations = (siteId: string) => {
  const { data: integrations, mutate } = useSWR<{ response: Integration[] }>(
    ['/integrations', siteId],
    () => ChatbotAPI.getIntegration({ site_id: siteId, type: 'deca_chatwindow' })
  );

  return { integrations: integrations?.response || [], mutate };
};

export default useDecaIntergrations;
