import type { IBaseAppConfig } from '@resola-ai/models';
import { createBaseConfig } from '@resola-ai/services-shared';

interface PageAdminConfig extends IBaseAppConfig {
  STUDIO_OIDS: string;
  POINTING_IP: string;
  PREVIEW_URL: string;
}

export const AppConfig: PageAdminConfig = {
  ...createBaseConfig({
    envType: 'vite',
    viteEnv: import.meta.env,
  }),
  STUDIO_OIDS: import.meta.env.VITE_STUDIO_OIDS ?? '',
  POINTING_IP: import.meta.env.VITE_POINTING_IP ?? '',
  PREVIEW_URL: import.meta.env.VITE_PREVIEW_URL ?? '',
};
