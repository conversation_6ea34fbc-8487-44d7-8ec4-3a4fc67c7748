import { <PERSON><PERSON><PERSON>Wrapper } from '@/components';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { ModalsProvider } from '@mantine/modals';
import { Notifications } from '@mantine/notifications';
import {
  axiosService,
  datadogService,
  logger,
  shareAppConfigService,
} from '@resola-ai/services-shared';
import { AuthenticationLayer, AxiosNavigation } from '@resola-ai/ui';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { Auth0ProviderConfiguration, BrowserNavigationProvider } from '@resola-ai/ui/providers';
import { TolgeeProvider } from '@tolgee/react';
import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import { AppConfig } from './configs';
import {
  AppContextProvider,
  DocumentContextProvider,
  ExplorerContextProvider,
  FolderContextProvider,
  KbContextProvider,
  KnowledgeBaseContextProvider,
  UploaderContextProvider,
} from './contexts';
import { tolgee } from './tolgee';

import '@mantine/core/styles.css';
import '@mantine/dates/styles.css';
import '@mantine/notifications/styles.css';
import '@mantine/core/styles.layer.css';
import 'mantine-contextmenu/styles.layer.css';
import './index.scss';
import { version } from '../package.json';
axiosService.init(AppConfig.API_SERVER_URL);
logger.init(AppConfig.IS_PRODUCTION);
shareAppConfigService.init(AppConfig);

// Initialize Datadog service
datadogService.init({
  applicationId: import.meta.env.VITE_DATADOG_APPLICATION_ID ?? '',
  clientToken: import.meta.env.VITE_DATADOG_CLIENT_TOKEN ?? '',
  site: import.meta.env.VITE_DATADOG_SITE ?? '',
  service: import.meta.env.VITE_DATADOG_SERVICE ?? '',
  env: import.meta.env.VITE_DATADOG_ENV ?? '',
  version: version,
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
      <MantineEmotionProvider>
        <BrowserRouter>
          <BrowserNavigationProvider>
            <Auth0ProviderConfiguration>
              <TolgeeProvider tolgee={tolgee} fallback='Loading...'>
                <AuthenticationLayer>
                  <AppContextProvider>
                    <KnowledgeBaseContextProvider>
                      <KbContextProvider>
                        <DocumentContextProvider>
                          <FolderContextProvider>
                            <ExplorerContextProvider>
                              <ModalsProvider>
                                <Notifications position='top-right' limit={6} zIndex={99999} />
                                <UploaderContextProvider>
                                  <KBAppWrapper />
                                </UploaderContextProvider>
                                <AxiosNavigation appPath={AppConfig.BASE_PATH} />
                              </ModalsProvider>
                            </ExplorerContextProvider>
                          </FolderContextProvider>
                        </DocumentContextProvider>
                      </KbContextProvider>
                    </KnowledgeBaseContextProvider>
                  </AppContextProvider>
                </AuthenticationLayer>
              </TolgeeProvider>
            </Auth0ProviderConfiguration>
          </BrowserNavigationProvider>
        </BrowserRouter>
      </MantineEmotionProvider>
    </MantineProvider>
  </React.StrictMode>
);
