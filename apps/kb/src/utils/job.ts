import type { Job } from '@/types/job';
import { formatDateTime } from '@resola-ai/ui/utils';
import camelCase from 'lodash/camelCase';

type TranslateFunction = (key: string, options?: any) => string;

export const formatJobTitle = (job: Job, translator: TranslateFunction) => {
  if (!job) return '';

  const jobType = translator(`jobType.${camelCase(job.jobType)}`);
  const atText = translator('at', { ns: 'common' });
  const timestamp = formatDateTime(job.startedAt || job.createdAt);

  return `${jobType} ${atText} ${timestamp}`;
};
