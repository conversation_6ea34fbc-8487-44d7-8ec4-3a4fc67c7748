import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

export const sortByCreatedAt = (list: Array<any>) => {
  return list.sort((a, b) => {
    return dayjs(b.createdAt).unix() - dayjs(a.createdAt).unix();
  });
};

export const sortByDate = (list: Array<any>, key: string, direction: 'asc' | 'desc' = 'desc') => {
  return list.sort((a, b) => {
    if (direction === 'asc') {
      if (!a[key]) {
        return 1;
      }
      if (!b[key]) {
        return -1;
      }
      return dayjs(a[key]).unix() - dayjs(b[key]).unix();
    }

    if (!a[key]) {
      return -1;
    }
    if (!b[key]) {
      return 1;
    }

    return dayjs(b[key]).unix() - dayjs(a[key]).unix();
  });
};

export const formatDuration = (start: Date, end: Date) => {
  const startDate = dayjs(start);
  const endDate = dayjs(end);

  const duration = endDate.diff(startDate, 'second');

  const months = Math.floor(duration / 2592000);
  const days = Math.floor((duration % 2592000) / 86400);
  const hours = Math.floor(duration / 3600);
  const minutes = Math.floor((duration % 3600) / 60);
  const seconds = duration % 60;

  if (months > 0) {
    return {
      key: 'duration.month',
      value: months,
    };
  }
  if (days > 0) {
    return {
      key: 'duration.day',
      value: days,
    };
  }
  if (hours > 0) {
    return {
      key: 'duration.hour',
      value: hours,
    };
  }
  if (minutes > 0) {
    return {
      key: 'duration.minute',
      value: minutes,
    };
  }
  return {
    key: 'duration.second',
    value: seconds,
  };
};
