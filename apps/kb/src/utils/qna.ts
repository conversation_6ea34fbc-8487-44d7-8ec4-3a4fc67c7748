import { QNA_JOBS_STATUS, QnAGenerateStep, type QnAJobStatus } from '@/types/qna';

export const getQnAJobStatusMapping = (status: QnAJobStatus | string) => {
  switch (status) {
    case QNA_JOBS_STATUS.in_progress:
      return {
        step: QnAGenerateStep.GENERATING,
        actionColor: 'orange',
        actionText: 'jobStatus.generating',
      };
    case QNA_JOBS_STATUS.succeed:
      return {
        step: QnAGenerateStep.QNA_SELECTION,
        actionColor: 'green',
        actionText: 'jobStatus.readyToReview',
      };
    case QNA_JOBS_STATUS.failed:
    case QNA_JOBS_STATUS.idle:
    default:
      return {
        step: QnAGenerateStep.DOCUMENT_SELECTION,
        actionColor: 'blue',
      };
  }
};
