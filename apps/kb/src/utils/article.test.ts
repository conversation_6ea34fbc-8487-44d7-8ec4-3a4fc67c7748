import { describe, expect, it } from 'vitest';
import { cleanBadCharacters } from './article';

describe('cleanBadCharacters', () => {
  it('should remove zero-width characters from KB name', () => {
    // Test data with various zero-width characters
    const kbNameWithZeroWidthChars = 'Test\u200BKB\u200CName\u200D\uFEFF';
    const expectedCleanName = 'TestKBName';

    const result = cleanBadCharacters(kbNameWithZeroWidthChars);

    expect(result).toBe(expectedCleanName);
  });

  it('should handle KB name with only zero-width space', () => {
    const kbNameWithZeroWidthSpace = 'Test\u200BKB';
    const expectedCleanName = 'TestKB';

    const result = cleanBadCharacters(kbNameWithZeroWidthSpace);

    expect(result).toBe(expectedCleanName);
  });

  it('should handle KB name with only zero-width non-joiner', () => {
    const kbNameWithZeroWidthNonJoiner = 'Test\u200CKB';
    const expectedCleanName = 'TestKB';

    const result = cleanBadCharacters(kbNameWithZeroWidthNonJoiner);

    expect(result).toBe(expectedCleanName);
  });

  it('should handle KB name with only zero-width joiner', () => {
    const kbNameWithZeroWidthJoiner = 'Test\u200DKB';
    const expectedCleanName = 'TestKB';

    const result = cleanBadCharacters(kbNameWithZeroWidthJoiner);

    expect(result).toBe(expectedCleanName);
  });

  it('should handle KB name with only BOM (zero-width no-break space)', () => {
    const kbNameWithBOM = 'Test\uFEFFKB';
    const expectedCleanName = 'TestKB';

    const result = cleanBadCharacters(kbNameWithBOM);

    expect(result).toBe(expectedCleanName);
  });

  it('should return the same string if no zero-width characters are present', () => {
    const normalKbName = 'Normal KB Name';

    const result = cleanBadCharacters(normalKbName);

    expect(result).toBe(normalKbName);
  });

  it('should return empty string when input is null', () => {
    const result = cleanBadCharacters(null);

    expect(result).toBe('');
  });

  it('should return empty string when input is undefined', () => {
    const result = cleanBadCharacters(undefined);

    expect(result).toBe('');
  });

  it('should return empty string when input is empty string', () => {
    const result = cleanBadCharacters('');

    expect(result).toBe('');
  });

  it('should handle KB name with multiple occurrences of zero-width characters', () => {
    const kbNameWithMultipleZeroWidthChars = '\u200BTest\u200C\u200DKB\uFEFF\u200BName\u200C';
    const expectedCleanName = 'TestKBName';

    const result = cleanBadCharacters(kbNameWithMultipleZeroWidthChars);

    expect(result).toBe(expectedCleanName);
  });

  it('should handle KB name with only zero-width characters', () => {
    const kbNameOnlyZeroWidthChars = '\u200B\u200C\u200D\uFEFF';
    const expectedCleanName = '';

    const result = cleanBadCharacters(kbNameOnlyZeroWidthChars);

    expect(result).toBe(expectedCleanName);
  });

  it('should preserve regular whitespace characters', () => {
    const kbNameWithSpaces = 'Test KB Name';

    const result = cleanBadCharacters(kbNameWithSpaces);

    expect(result).toBe(kbNameWithSpaces);
  });

  it('should handle KB name with mixed zero-width and regular characters', () => {
    const kbNameMixed = 'Test\u200B KB \u200CName\u200D With\uFEFF Spaces';
    const expectedCleanName = 'Test KB Name With Spaces';

    const result = cleanBadCharacters(kbNameMixed);

    expect(result).toBe(expectedCleanName);
  });
});
