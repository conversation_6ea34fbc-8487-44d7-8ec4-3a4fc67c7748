import { UploaderContextProvider } from '@/contexts';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { BrowserNavigationProvider } from '@resola-ai/ui/providers';
import { getKeyFromText } from '@resola-ai/ui/utils';
import { render } from '@testing-library/react';
import { TolgeeProvider } from '@tolgee/react';
import type React from 'react';
import { BrowserRouter } from 'react-router-dom';
import { vi } from 'vitest';

// Common mock configurations
const MOCK_LOCATION = {
  pathname: '/test-path',
  search: '',
  hash: '',
  state: null,
};

const MOCK_NAVIGATION_DEFAULTS = {
  navigateReturnValue: vi.fn(),
  pathname: '/test-path',
  search: '?query=test',
};

// Initialize global mocks
Object.defineProperty(window, 'location', {
  value: {
    origin: 'http://localhost',
    protocol: 'http:',
    host: 'localhost',
    hostname: 'localhost',
    port: '',
    pathname: '/',
    search: '',
    hash: '',
    href: 'http://localhost/',
  },
  writable: true,
});

if (typeof global.ResizeObserver === 'undefined') {
  global.ResizeObserver = class MockResizeObserver {
    observe = vi.fn();
    unobserve = vi.fn();
    disconnect = vi.fn();
  };
}

// Mock requestAnimationFrame and cancelAnimationFrame
if (typeof global.requestAnimationFrame === 'undefined') {
  global.requestAnimationFrame = vi.fn(
    (cb: FrameRequestCallback) => setTimeout(cb, 16) as unknown as number
  );
  global.cancelAnimationFrame = vi.fn((id: number) => clearTimeout(id));
}

// Mock utilities - must be defined before mocks
export const mockNavigate = vi.fn();
export const mockLocation = {
  pathname: '/test-path',
  search: '',
  hash: '',
  state: null,
};

export const mockParams = {
  entityType: 'document',
};

export let mockSearchParams = new URLSearchParams();
export const mockSetSearchParams = vi.fn();

// Function to update the mock search params
export const updateMockSearchParams = (params: URLSearchParams) => {
  mockSearchParams = params;
};

// Mock tolgee instance
const mockTolgee = {
  t: (key: string, options?: any) => {
    let translationKey = key;
    if (key.includes(':')) {
      translationKey = key.split(':')[1];
    }

    if (options && typeof options === 'object') {
      let result = translationKey;
      Object.entries(options).forEach(([optionKey, value]) => {
        result = result.replace(`{{${optionKey}}}`, String(value));
      });
      return result;
    }
    return translationKey;
  },
  addPlugin: vi.fn(),
  use: vi.fn(),
  init: vi.fn(),
  changeLanguage: vi.fn(),
  getLanguage: vi.fn(() => 'en'),
  isLoaded: vi.fn(() => true),
  isLoading: vi.fn(() => false),
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  addActiveNs: vi.fn(),
};

// Mock of react-router-dom

// Setup mocks
vi.mock('@resola-ai/ui/providers', () => {
  const Auth0ProviderConfiguration = ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  );
  const BrowserNavigationProvider = ({ children }: { children: React.ReactNode }) => (
    <>{children}</>
  );

  Auth0ProviderConfiguration.displayName = 'Auth0ProviderConfiguration';
  BrowserNavigationProvider.displayName = 'BrowserNavigationProvider';

  return { Auth0ProviderConfiguration, BrowserNavigationProvider };
});

vi.mock('@tolgee/react', () => {
  const TolgeeProvider = ({ children }: { children: React.ReactNode }) => <>{children}</>;
  const T = ({ keyName, params }: { keyName: string; params?: any }) => {
    if (params && typeof params === 'object') {
      return (
        <span>
          {keyName}
          {Object.entries(params).map(([key, value]) => (
            <span key={key}>{String(value)}</span>
          ))}
        </span>
      );
    }
    return <span>{keyName}</span>;
  };

  TolgeeProvider.displayName = 'TolgeeProvider';
  T.displayName = 'T';

  return {
    TolgeeProvider,
    useTranslate: () => ({
      t: (key: string, options?: any) => {
        // Handle specific translation cases
        if (key === 'table.selectionInfo' && options && typeof options === 'object') {
          const { selected, total } = options;
          return `${selected} items selected / ${total} items`;
        }

        if (key === 'warnings.maxSelectionReached' && options && typeof options === 'object') {
          const { maxSelection, max } = options;
          return `Maximum ${maxSelection || max} items can be selected`;
        }

        let translationKey = key;
        if (key.includes(':')) {
          translationKey = key.split(':')[1];
        }

        if (options && typeof options === 'object') {
          let result = translationKey;
          Object.entries(options).forEach(([optionKey, value]) => {
            result = result.replace(`{{${optionKey}}}`, String(value));
          });
          return result;
        }
        return translationKey;
      },
      i18n: { language: 'en' },
    }),
    useTolgee: () => mockTolgee,
    useTolgeeContext: () => mockTolgee,
    T,
  };
});

vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');

  const MockAnchor = ({ children, onClick, ...props }: any) => (
    <button onClick={onClick} data-testid='anchor' {...props}>
      {children}
    </button>
  );
  MockAnchor.displayName = 'MockAnchor';

  const MockTooltip = ({ children, label, ...props }: any) => (
    <div data-testid='mantine-tooltip' data-tooltip-label={label} {...props}>
      {children}
    </div>
  );
  MockTooltip.displayName = 'MockTooltip';

  const MockTextInput = ({ value, onChange, onBlur, className, placeholder, ...props }) => (
    <input
      data-testid='text-input'
      value={value}
      onChange={onChange}
      onBlur={onBlur}
      className={className}
      placeholder={placeholder}
      {...props}
    />
  );
  MockTextInput.displayName = 'MockTextInput';
  MockTextInput.extend = vi.fn((props) => ({ ...props, displayName: 'MockTextInputExtended' }));

  return {
    ...actual,
    InputBase: { extend: vi.fn((props) => ({ ...props })) },
    TextInput: MockTextInput,
    Anchor: MockAnchor,
    Tooltip: MockTooltip,
  };
});

vi.mock('@resola-ai/ui', () => {
  const DecaButton = ({
    children,
    onClick,
    leftSection,
    variant,
    loading,
    disabled,
    size,
    radius,
    className,
    ...domProps
  }: any) => (
    <button
      onClick={onClick}
      data-variant={variant}
      data-loading={loading}
      disabled={disabled || loading}
      data-size={size}
      data-radius={radius}
      data-testid='button'
      className={`mantine-Button-root ${className || ''}`}
      {...domProps}
    >
      {leftSection}
      {children}
    </button>
  );
  DecaButton.displayName = 'DecaButton';

  const ThreeDotsMenu = ({ items }: { items: any[] }) => (
    <div data-testid='three-dots-menu'>
      {items.map((item, index) => (
        <button
          key={`${getKeyFromText(item.label)}-${index}`}
          type='button'
          onClick={item.onClick}
          data-testid={`menu-item-${getKeyFromText(item.label)}`}
        >
          {item.label}
        </button>
      ))}
    </div>
  );
  ThreeDotsMenu.displayName = 'ThreeDotsMenu';

  const RoundedButton = ({ children, onClick, variant, ...props }: any) => (
    <button onClick={onClick} data-variant={variant} data-testid='rounded-button' {...props}>
      {children}
    </button>
  );
  RoundedButton.displayName = 'RoundedButton';

  const DecaBadge = ({ text, className, badgeProps, ...props }: any) => (
    <span
      className={className}
      data-testid='mocked-deca-badge'
      data-variant={badgeProps?.variant}
      data-color={badgeProps?.color}
      data-radius={badgeProps?.radius}
      badgeProps={badgeProps}
      {...props}
    >
      {text}
    </span>
  );
  DecaBadge.displayName = 'DecaBadge';

  const TextEllipsis = ({ children, lines, className, ...props }: any) => (
    <div className={className} data-testid='text-ellipsis' {...props}>
      {children}
    </div>
  );
  TextEllipsis.displayName = 'TextEllipsis';

  const ErrorMessage = ({ message, className, ...props }: any) => (
    <div className={className} data-testid='error-message' role='alert' {...props}>
      {message}
    </div>
  );
  ErrorMessage.displayName = 'ErrorMessage';

  const DynamicDrawer = ({
    children,
    opened,
    onClose,
    className,
    rightAction,
    onResize,
    ...props
  }: any) => (
    <div className={className} data-testid='dynamic-drawer' data-opened={opened} {...props}>
      {rightAction && <div data-testid='drawer-right-action'>{rightAction}</div>}
      {opened && (
        <div>
          <button type='button' onClick={onClose} data-testid='drawer-close'>
            Close
          </button>
          <button type='button' data-testid='drawer-resize-button' onClick={() => onResize?.(true)}>
            Resize
          </button>
          {children}
        </div>
      )}
    </div>
  );
  DynamicDrawer.displayName = 'DynamicDrawer';

  const NotFound = ({ title, description, ...props }: any) => (
    <div data-testid='not-found' {...props}>
      <h1>{title}</h1>
      <p>{description}</p>
    </div>
  );
  NotFound.displayName = 'NotFound';

  return {
    DecaButton,
    ThreeDotsMenu,
    RoundedButton,
    DecaBadge,
    TextEllipsis,
    ErrorMessage,
    DynamicDrawer,
    NotFound,
  };
});

vi.mock('@tabler/icons-react', async () => {
  const actual = await vi.importActual('@tabler/icons-react');
  const createIcon = (name: string) => {
    const IconComponent = (props: any) => {
      const { size = 24, ...rest } = props;
      return (
        <span data-testid={name} aria-hidden='true' width={size} height={size} {...rest}>
          {name}
        </span>
      );
    };
    IconComponent.displayName = name;
    return IconComponent;
  };

  return {
    ...actual,
    IconEdit: createIcon('EditIcon'),
    IconEye: createIcon('EyeIcon'),
    IconLoader: createIcon('LoaderIcon'),
    IconUpload: createIcon('IconUpload'),
  };
});

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual<typeof import('react-router-dom')>('react-router-dom');

  const Link = ({
    to,
    children,
    ...props
  }: {
    to: string;
    children: React.ReactNode;
    [key: string]: any;
  }) => (
    <a href={to} data-testid='mock-link' {...props}>
      {children}
    </a>
  );
  Link.displayName = 'Link';

  // Use actual routers to provide proper context
  const MemoryRouter = actual.MemoryRouter;
  const BrowserRouter = actual.BrowserRouter;

  return {
    ...actual,
    useLocation: () => mockLocation,
    useNavigate: () => mockNavigate,
    useParams: () => mockParams,
    useSearchParams: () => [mockSearchParams, mockSetSearchParams],
    MemoryRouter,
    BrowserRouter,
    Link,
  };
});

// Provider wrapper configurations
type ProviderConfig = {
  includeMantine?: boolean;
  includeTolgee?: boolean;
  includeRouter?: boolean;
  includeNavigation?: boolean;
  includeUploader?: boolean;
  includeAppContext?: boolean;
};

const createProviderWrapper = (config: ProviderConfig = {}) => {
  const {
    includeMantine = true,
    includeTolgee = false,
    includeRouter = false,
    includeNavigation = false,
    includeUploader = false,
    includeAppContext: _includeAppContext = false,
  } = config;

  const ProviderWrapper = ({ children }: { children: React.ReactNode }) => {
    let content = children;

    if (includeUploader) {
      content = <UploaderContextProvider>{content}</UploaderContextProvider>;
    }

    if (includeNavigation) {
      content = <BrowserNavigationProvider>{content}</BrowserNavigationProvider>;
    }

    if (includeRouter) {
      content = <BrowserRouter>{content}</BrowserRouter>;
    }

    if (includeTolgee) {
      content = <TolgeeProvider tolgee={mockTolgee as any}>{content}</TolgeeProvider>;
    }

    // Skip AppContext provider for now since it's mocked globally
    // if (includeAppContext) {
    //   // Use the mocked AppContextProvider from the mock
    //   const { AppContextProvider } = require('@/contexts/AppContext');
    //   content = <AppContextProvider>{content}</AppContextProvider>;
    // }

    if (includeMantine) {
      content = (
        <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
          <MantineEmotionProvider>{content}</MantineEmotionProvider>
        </MantineProvider>
      );
    }

    return <>{content}</>;
  };

  ProviderWrapper.displayName = 'ProviderWrapper';
  return ProviderWrapper;
};

// Specific provider wrappers
export const AllTheProviders = createProviderWrapper({
  includeMantine: true,
  includeTolgee: true,
  includeRouter: true,
  includeNavigation: true,
  includeUploader: true,
  includeAppContext: true,
});

export const MantineWrapper = createProviderWrapper({
  includeMantine: true,
});

export const ComponentTestWrapper = createProviderWrapper({
  includeMantine: true,
});

// Render utilities
const createRenderFunction =
  (wrapper: React.ComponentType<{ children: React.ReactNode }>) =>
  (ui: React.ReactNode, options = {}) =>
    render(ui, { wrapper, ...options });

export const renderWithProviders = createRenderFunction(AllTheProviders);
export const renderWithMantine = createRenderFunction(MantineWrapper);

export const mockReactRouter = (customLocation: Partial<typeof MOCK_LOCATION> = {}) => {
  // This function is deprecated - the global mock handles react-router-dom
  // Individual tests should modify mockLocation and mockNavigate directly if needed
  const location = { ...MOCK_LOCATION, ...customLocation };
  Object.assign(mockLocation, location);
};

export const mockLibraries = (options: Partial<typeof MOCK_NAVIGATION_DEFAULTS> = {}) => {
  // This function is deprecated - the global mock handles react-router-dom
  // Individual tests should modify mockLocation and mockNavigate directly if needed
  const { navigateReturnValue, pathname, search } = { ...MOCK_NAVIGATION_DEFAULTS, ...options };

  // Update the global mock references
  Object.assign(mockLocation, { pathname, search });
  mockNavigate.mockImplementation(navigateReturnValue);

  return { mockNavigate: navigateReturnValue };
};

export const setupCommonComponentRenderWithMocks = () => ({
  renderCommonComponent: createRenderFunction(ComponentTestWrapper),
});

// Export constants for reuse
export { mockTolgee };
