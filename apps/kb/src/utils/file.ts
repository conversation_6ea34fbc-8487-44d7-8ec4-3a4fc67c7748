import { SPECIAL_FILE_TYPES, STORAGE_UNITS } from '@/constants/file';

export const fileSize = (size: number) => {
  const storageUnitIndex = Math.floor(Math.log(size) / Math.log(1024));
  if (Number.isNaN(storageUnitIndex) || storageUnitIndex === 0) return `${size} Bytes`;

  return `${(size / 1024 ** storageUnitIndex).toFixed(2)} ${STORAGE_UNITS[storageUnitIndex]}`;
};

export const fileType = (type: string) => {
  if (SPECIAL_FILE_TYPES[type]) {
    return SPECIAL_FILE_TYPES[type].toUpperCase();
  }

  return type.split('/')[1].toUpperCase();
};

export const getUploaderId = (fileId: string) => `uploader-${fileId}`;

/**
 * Downloads a file from a URL and saves it as a blob
 * @param {string} url - The URL of the file to download
 * @param {string} filename - The filename to save the file as
 */
export const downloadFile = async (url: string, filename: string) => {
  const response = await fetch(url);
  const blob = await response.blob();

  downloadBlob(blob, filename);
};

/**
 * Downloads a blob and revokes the object URL
 * @param {Blob} blob - The blob to download
 * @param {string} filename - The filename to save the blob as
 */
export const downloadBlob = async (blob: Blob, filename: string) => {
  const newUrl = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = newUrl;
  a.download = filename;
  a.click();

  window.URL.revokeObjectURL(newUrl);
  a.remove();
};
