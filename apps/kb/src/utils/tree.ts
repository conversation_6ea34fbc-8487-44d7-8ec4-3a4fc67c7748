import { ROOT_PATH } from '@/constants/folder';
import type { Folder, TreeItem, Trees } from '@/types';

export const convertListToTree = (folders: Folder[]): Trees => {
  const map = new Map<string, TreeItem>();
  const root: TreeItem = {
    id: ROOT_PATH,
    name: 'tree.root',
    path: ROOT_PATH,
    count: 0,
    childFolderCount: 0,
    childKbCount: 0,
    children: [],
  };

  map.set(ROOT_PATH, root);

  folders.forEach((folder) => {
    map.set(folder.id, {
      ...folder,
      children: [],
    });
  });

  folders.forEach((folder) => {
    const node = map.get(folder.id)!;
    const parent = folder.parentDirId && map.get(folder.parentDirId);

    if (parent) {
      parent.children = [...(parent.children || []), node];
    }
  });

  root.count = root.children?.length || 0;
  root.childFolderCount = root.children?.length || 0;

  return [root];
};

export const isFolder = (item: any) => {
  return 'path' in item;
};

export const isDocument = (item: any) => {
  return 'metadata' in item;
};
