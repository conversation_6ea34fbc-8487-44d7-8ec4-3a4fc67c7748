import { render, screen } from '@testing-library/react';
import type React from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  AllTheProviders,
  ComponentTestWrapper,
  MantineWrapper,
  mockLibraries,
  mockLocation,
  mockNavigate,
  mockReactRouter,
  mockTolgee,
  renderWithMantine,
  renderWithProviders,
  setupCommonComponentRenderWithMocks,
} from '../unitTest';

// Test constants
const MOCK_COMPONENT_TEXT = 'Test Component';
const MOCK_TOLGEE_KEY = 'test.key';
const MOCK_TOLGEE_KEY_WITH_NAMESPACE = 'namespace:test.key';
const MOCK_TOLGEE_OPTIONS = { name: 'John', count: 5 };

// Simple test component
const TestComponent = () => <div>{MOCK_COMPONENT_TEXT}</div>;

describe('unitTest utility', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    vi.resetModules();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Mock Objects and Functions', () => {
    describe('mockTolgee', () => {
      it('should provide correct translation function', () => {
        const result = mockTolgee.t(MOCK_TOLGEE_KEY);
        expect(result).toBe(MOCK_TOLGEE_KEY);
      });

      it('should handle namespaced keys correctly', () => {
        const result = mockTolgee.t(MOCK_TOLGEE_KEY_WITH_NAMESPACE);
        expect(result).toBe('test.key');
      });

      it('should handle options correctly', () => {
        const result = mockTolgee.t('hello.{{name}}', MOCK_TOLGEE_OPTIONS);
        expect(result).toBe('hello.John');
      });

      it('should provide required tolgee methods', () => {
        expect(mockTolgee.addPlugin).toBeDefined();
        expect(mockTolgee.use).toBeDefined();
        expect(mockTolgee.init).toBeDefined();
        expect(mockTolgee.changeLanguage).toBeDefined();
        expect(mockTolgee.getLanguage).toBeDefined();
        expect(mockTolgee.isLoaded).toBeDefined();
        expect(mockTolgee.isLoading).toBeDefined();
        expect(mockTolgee.on).toBeDefined();
        expect(mockTolgee.off).toBeDefined();
        expect(mockTolgee.emit).toBeDefined();
        expect(mockTolgee.addActiveNs).toBeDefined();
      });

      it('should return correct default values', () => {
        expect(mockTolgee.getLanguage).toBeDefined();
        expect(mockTolgee.isLoaded).toBeDefined();
        expect(mockTolgee.isLoading).toBeDefined();

        // Test that the functions can be called
        expect(typeof mockTolgee.getLanguage).toBe('function');
        expect(typeof mockTolgee.isLoaded).toBe('function');
        expect(typeof mockTolgee.isLoading).toBe('function');
      });
    });

    describe('mockLocation and mockNavigate', () => {
      it('should provide default location object', () => {
        expect(mockLocation.pathname).toBe('/test-path');
        expect(mockLocation.search).toBe('');
        expect(mockLocation.hash).toBe('');
        expect(mockLocation.state).toBeNull();
      });

      it('should provide navigate function', () => {
        expect(mockNavigate).toBeDefined();
        expect(vi.isMockFunction(mockNavigate)).toBe(true);
      });
    });
  });

  describe('Provider Wrappers', () => {
    describe('AllTheProviders', () => {
      it('should render component with all providers', () => {
        render(<TestComponent />, { wrapper: AllTheProviders });

        expect(screen.getByText(MOCK_COMPONENT_TEXT)).toBeInTheDocument();
      });
    });

    describe('MantineWrapper', () => {
      it('should render component with Mantine provider', () => {
        render(<TestComponent />, { wrapper: MantineWrapper });

        expect(screen.getByText(MOCK_COMPONENT_TEXT)).toBeInTheDocument();
      });
    });

    describe('ComponentTestWrapper', () => {
      it('should render component with basic providers', () => {
        render(<TestComponent />, { wrapper: ComponentTestWrapper });

        expect(screen.getByText(MOCK_COMPONENT_TEXT)).toBeInTheDocument();
      });
    });
  });

  describe('Render Utilities', () => {
    describe('renderWithProviders', () => {
      it('should render component with all providers', () => {
        renderWithProviders(<TestComponent />);

        expect(screen.getByText(MOCK_COMPONENT_TEXT)).toBeInTheDocument();
      });

      it('should accept additional options', () => {
        const customContainer = document.createElement('div');
        const customOptions = { container: customContainer };
        const result = renderWithProviders(<TestComponent />, customOptions);

        expect(result.container).toBeDefined();
        expect(result.container).toBe(customContainer);
        expect(result.container.textContent).toContain(MOCK_COMPONENT_TEXT);
      });
    });

    describe('renderWithMantine', () => {
      it('should render component with Mantine provider', () => {
        renderWithMantine(<TestComponent />);

        expect(screen.getByText(MOCK_COMPONENT_TEXT)).toBeInTheDocument();
      });
    });
  });

  describe('Utility Functions', () => {
    describe('mockReactRouter', () => {
      it('should update mockLocation with custom values', () => {
        const originalPathname = mockLocation.pathname;
        const customLocation = { pathname: '/custom-path', search: '?test=1' };

        mockReactRouter(customLocation);

        expect(mockLocation.pathname).toBe('/custom-path');
        expect(mockLocation.search).toBe('?test=1');

        // Reset for other tests
        mockLocation.pathname = originalPathname;
      });

      it('should preserve existing location properties when not overridden', () => {
        const originalHash = mockLocation.hash;
        const originalState = mockLocation.state;

        mockReactRouter({ pathname: '/new-path' });

        expect(mockLocation.pathname).toBe('/new-path');
        expect(mockLocation.hash).toBe(originalHash);
        expect(mockLocation.state).toBe(originalState);

        // Reset for other tests
        mockLocation.pathname = '/test-path';
      });
    });

    describe('mockLibraries', () => {
      it('should update mockLocation and mockNavigate with custom values', () => {
        const originalPathname = mockLocation.pathname;
        const customNavigate = vi.fn();
        const options = {
          navigateReturnValue: customNavigate,
          pathname: '/mock-path',
          search: '?mock=true',
        };

        const result = mockLibraries(options);

        expect(mockLocation.pathname).toBe('/mock-path');
        expect(mockLocation.search).toBe('?mock=true');
        expect(result.mockNavigate).toBe(customNavigate);

        // Reset for other tests
        mockLocation.pathname = originalPathname;
        mockLocation.search = '';
      });

      it('should use default values when no options provided', () => {
        const result = mockLibraries();

        expect(result.mockNavigate).toBeDefined();
      });
    });

    describe('setupCommonComponentRenderWithMocks', () => {
      it('should return render function', () => {
        const { renderCommonComponent } = setupCommonComponentRenderWithMocks();

        expect(renderCommonComponent).toBeDefined();
        expect(typeof renderCommonComponent).toBe('function');
      });

      it('should render component with ComponentTestWrapper', () => {
        const { renderCommonComponent } = setupCommonComponentRenderWithMocks();

        renderCommonComponent(<TestComponent />);

        expect(screen.getByText(MOCK_COMPONENT_TEXT)).toBeInTheDocument();
      });
    });
  });

  describe('Global Mock Setup', () => {
    it('should setup window.location mock', () => {
      expect(window.location.origin).toBe('http://localhost');
    });

    it('should setup ResizeObserver mock', () => {
      expect(global.ResizeObserver).toBeDefined();

      const observer = new global.ResizeObserver(() => {});
      expect(observer.observe).toBeDefined();
      expect(observer.unobserve).toBeDefined();
      expect(observer.disconnect).toBeDefined();
    });

    it('should setup animation frame mocks', () => {
      expect(global.requestAnimationFrame).toBeDefined();
      expect(global.cancelAnimationFrame).toBeDefined();

      const callback = vi.fn();
      const id = global.requestAnimationFrame(callback);
      expect(typeof id).toBe('number');

      global.cancelAnimationFrame(id);
    });
  });

  describe('Translation Function Edge Cases', () => {
    it('should handle tolgee translation with empty options', () => {
      const result = mockTolgee.t('test.key', {});
      expect(result).toBe('test.key');
    });

    it('should handle tolgee translation with null options', () => {
      const result = mockTolgee.t('test.key', null);
      expect(result).toBe('test.key');
    });

    it('should handle tolgee translation with undefined options', () => {
      const result = mockTolgee.t('test.key');
      expect(result).toBe('test.key');
    });

    it('should handle empty key for tolgee translation', () => {
      const result = mockTolgee.t('');
      expect(result).toBe('');
    });

    it('should handle complex namespace keys', () => {
      const result = mockTolgee.t('namespace:sub.namespace:final.key');
      expect(result).toBe('sub.namespace');
    });

    it('should handle multiple template variables', () => {
      const result = mockTolgee.t('Hello {{name}}, you have {{count}} messages', {
        name: 'John',
        count: 5,
      });
      expect(result).toBe('Hello John, you have 5 messages');
    });
  });

  describe('Mock Function Behavior', () => {
    it('should allow calling mock functions', () => {
      expect(() => mockTolgee.addPlugin()).not.toThrow();
      expect(() => mockTolgee.use()).not.toThrow();
      expect(() => mockTolgee.init()).not.toThrow();
      expect(() => mockTolgee.changeLanguage('en')).not.toThrow();
      expect(() => mockTolgee.on('event', vi.fn())).not.toThrow();
      expect(() => mockTolgee.off('event', vi.fn())).not.toThrow();
      expect(() => mockTolgee.emit('event')).not.toThrow();
      expect(() => mockTolgee.addActiveNs('namespace')).not.toThrow();
    });

    it('should allow mocking navigate function', () => {
      mockNavigate('/test-path');
      expect(mockNavigate).toHaveBeenCalledWith('/test-path');
    });

    it('should allow modifying location object', () => {
      const originalPathname = mockLocation.pathname;
      mockLocation.pathname = '/new-path';

      expect(mockLocation.pathname).toBe('/new-path');

      // Reset for other tests
      mockLocation.pathname = originalPathname;
    });
  });

  describe('Provider Wrapper Functionality', () => {
    it('should render nested components in AllTheProviders', () => {
      const NestedComponent = () => (
        <div>
          <TestComponent />
          <div data-testid='nested-content'>Nested</div>
        </div>
      );

      render(<NestedComponent />, { wrapper: AllTheProviders });

      expect(screen.getByText(MOCK_COMPONENT_TEXT)).toBeInTheDocument();
      expect(screen.getByTestId('nested-content')).toBeInTheDocument();
    });

    it('should handle components with props in wrappers', () => {
      const PropsComponent = ({ title }: { title: string }) => (
        <div data-testid='props-component'>{title}</div>
      );

      render(<PropsComponent title='Test Title' />, { wrapper: MantineWrapper });

      expect(screen.getByTestId('props-component')).toHaveTextContent('Test Title');
    });
  });

  describe('Render Utility Edge Cases', () => {
    it('should handle empty components', () => {
      const EmptyComponent = () => null;

      expect(() => renderWithProviders(<EmptyComponent />)).not.toThrow();
    });

    it('should handle components with children', () => {
      const ParentComponent = ({ children }: { children: React.ReactNode }) => (
        <div data-testid='parent'>{children}</div>
      );

      renderWithMantine(
        <ParentComponent>
          <TestComponent />
        </ParentComponent>
      );

      expect(screen.getByTestId('parent')).toBeInTheDocument();
      expect(screen.getByText(MOCK_COMPONENT_TEXT)).toBeInTheDocument();
    });
  });
});
