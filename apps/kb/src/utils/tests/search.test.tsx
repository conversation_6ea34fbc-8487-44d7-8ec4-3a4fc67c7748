import {
  type ArticleSearchData,
  type BaseSearchData,
  type DocumentSearchData,
  SearchTypeEnums,
} from '@/types';
import { normalizeSearchResult } from '../search';

describe('search utils', () => {
  const mockTranslator = (key: string) => key;

  describe('normalizeSearchResult', () => {
    it('should handle folder type search results', () => {
      const mockFolderData = {
        type: SearchTypeEnums.folder,
        data: {
          name: 'Folder 17/03/2025',
          breadcrumbArray: ['/root', 'Parent', 'Current'],
          path: '/root/1/2/3',
        },
      };

      const result = normalizeSearchResult(mockFolderData, mockTranslator);

      expect(result.title).toBe('Folder 17/03/2025');
      expect(result.breadcrumbs).toHaveLength(3);
      expect(result.breadcrumbs[0].title).toBe('tree.root');
      expect(result.breadcrumbs[1].title).toBe('Parent');
      expect(result.breadcrumbs[2].title).toBe('Current');
      expect(result.description).toBe('');
    });

    it('should handle base type search results', () => {
      const mockBaseData = {
        type: SearchTypeEnums.base,
        data: {
          id: '123',
          name: 'Base 17/03/2025',
          description: 'Test\nDescription',
          parentDirBreadcrumbArray: ['/root', 'Parent'],
          parentDirPath: '/root/1',
        } as BaseSearchData,
      };

      const result = normalizeSearchResult(mockBaseData, mockTranslator);

      expect(result.title).toBe('Base 17/03/2025');
      expect(result.description).toBe('Test Description');
      expect(result.breadcrumbs).toHaveLength(3);
      expect(result.breadcrumbs[0].title).toBe('tree.root');
      expect(result.breadcrumbs[1].title).toBe('Parent');
    });

    it('should handle article type search results', () => {
      const mockArticleData = {
        type: SearchTypeEnums.article,
        data: {
          id: 'article-123',
          title: 'Article 17/03/2025',
          content: 'Test\nContent',
          base: {
            id: 'base-123',
            name: 'Article 17/03/2025',
            parentDirBreadcrumbArray: ['/root', 'Parent'],
            parentDirPath: '/root/1',
          },
        } as ArticleSearchData,
      };

      const result = normalizeSearchResult(mockArticleData, mockTranslator);

      expect(result.title).toBe('Article 17/03/2025');
      expect(result.description).toBe('Test Content');
      expect(result.breadcrumbs.length).toBeGreaterThan(2);
      expect(result.breadcrumbs[0].title).toBe('tree.root');
      expect(result.breadcrumbs[1].title).toBe('Parent');
    });

    it('should handle document type search results', () => {
      const mockDocumentData = {
        type: SearchTypeEnums.document,
        data: {
          id: 'doc-123',
          metadata: {
            name: 'Document 17/03/2025.pdf',
            contentType: 'application/pdf',
          },
          previewText: 'Test\nPreview',
          parentDirBreadcrumbArray: ['/root', 'Parent'],
          parentDirPath: '/root/1',
        } as DocumentSearchData,
      };

      const result = normalizeSearchResult(mockDocumentData, mockTranslator);

      expect(result.title).toBe('Document 17/03/2025');
      expect(result.description).toBe('Test Preview');
      expect(result.breadcrumbs.length).toBeGreaterThan(1);
      expect(result.breadcrumbs[0].title).toBe('tree.root');
      expect(result.breadcrumbs[1].title).toBe('Parent');
    });

    it('should handle unknown type search results', () => {
      const mockUnknownData = {
        type: 'unknown' as any,
        data: {},
      };

      const result = normalizeSearchResult(mockUnknownData, mockTranslator);

      expect(result.title).toBe('');
      expect(result.description).toBe('');
      expect(result.breadcrumbs).toHaveLength(1);
    });

    it('should handle empty or null values', () => {
      const mockEmptyData = {
        type: SearchTypeEnums.base,
        data: {
          id: '123',
        } as BaseSearchData,
      };

      const result = normalizeSearchResult(mockEmptyData, mockTranslator);

      expect(result.title).toBe('');
      expect(result.description).toBe('');
      expect(result.breadcrumbs).toHaveLength(2);
    });

    it('should handle article without base data', () => {
      const mockArticleWithoutBase = {
        type: SearchTypeEnums.article,
        data: {
          id: 'article-123',
          title: 'Article 17/03/2025',
          content: 'Test Content',
        } as ArticleSearchData,
      };

      const result = normalizeSearchResult(mockArticleWithoutBase, mockTranslator);

      expect(result.title).toBe('Article 17/03/2025');
      expect(result.description).toBe('Test Content');
      expect(result.breadcrumbs).toHaveLength(2);
    });
  });
});
