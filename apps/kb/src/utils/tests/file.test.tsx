import { SPECIAL_FILE_TYPES } from '@/constants/file';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { downloadBlob, downloadFile, fileSize, fileType, getUploaderId } from '../file';

// Test constants
const TEST_VALUES = {
  BYTES: 100,
  KB: 2048,
  MB: 2097152,
  GB: 2147483648,
  FILE_ID: '123456',
  DOWNLOAD_URL: 'https://example.com/file.pdf',
  FILENAME: 'test-file.pdf',
  TEXT_FILENAME: 'test-file.txt',
  BLOB_URL: 'blob:test-url',
  BLOB_CONTENT: 'test content',
  MIME_TYPES: {
    PDF: 'application/pdf',
    JPEG: 'image/jpeg',
    TEXT: 'text/plain',
  },
};

describe('file utils', () => {
  describe('fileSize', () => {
    it('should return size in bytes when size is small', () => {
      expect(fileSize(TEST_VALUES.BYTES)).toBe(`${TEST_VALUES.BYTES} Bytes`);
    });

    it('should return size in KB when appropriate', () => {
      expect(fileSize(TEST_VALUES.KB)).toBe('2.00 KB');
    });

    it('should return size in MB when appropriate', () => {
      expect(fileSize(TEST_VALUES.MB)).toBe('2.00 MB');
    });

    it('should return size in GB when appropriate', () => {
      expect(fileSize(TEST_VALUES.GB)).toBe('2.00 GB');
    });

    it('should handle zero values', () => {
      const result = fileSize(0);
      expect(result).toBe('NaN undefined');
    });

    it('should handle NaN values', () => {
      const result = fileSize(Number.NaN);
      expect(result).toBe('NaN Bytes');
    });
  });

  describe('fileType', () => {
    it('should return special file type in uppercase when available', () => {
      const specialType = Object.keys(SPECIAL_FILE_TYPES)[0];
      expect(fileType(specialType)).toBe(SPECIAL_FILE_TYPES[specialType].toUpperCase());
    });

    it('should return the second part of MIME type in uppercase when not a special type', () => {
      expect(fileType(TEST_VALUES.MIME_TYPES.PDF)).toBe('PDF');
      expect(fileType(TEST_VALUES.MIME_TYPES.JPEG)).toBe('JPEG');
      expect(fileType(TEST_VALUES.MIME_TYPES.TEXT)).toBe('PLAIN');
    });
  });

  describe('getUploaderId', () => {
    it('should return uploader ID with the correct prefix', () => {
      expect(getUploaderId(TEST_VALUES.FILE_ID)).toBe(`uploader-${TEST_VALUES.FILE_ID}`);
    });
  });

  describe('downloadFile', () => {
    let fetchMock: any;
    let blobMock: any;
    let mockCreateObjectURL: any;
    let mockRevokeObjectURL: any;

    beforeEach(() => {
      // Mock Blob
      blobMock = new Blob([TEST_VALUES.BLOB_CONTENT], { type: TEST_VALUES.MIME_TYPES.TEXT });

      // Mock fetch
      fetchMock = vi.fn().mockResolvedValue({
        blob: vi.fn().mockResolvedValue(blobMock),
      });
      global.fetch = fetchMock;

      // Create mocks for URL methods
      mockCreateObjectURL = vi.fn().mockReturnValue(TEST_VALUES.BLOB_URL);
      mockRevokeObjectURL = vi.fn();

      // Define window.URL globally for the test environment
      global.URL = {
        createObjectURL: mockCreateObjectURL,
        revokeObjectURL: mockRevokeObjectURL,
      } as any;
    });

    it('should fetch the file and call downloadBlob', async () => {
      const mockAnchor = {
        href: '',
        download: '',
        click: vi.fn(),
        remove: vi.fn(),
      };

      vi.spyOn(document, 'createElement').mockImplementation(() => {
        return mockAnchor as unknown as HTMLAnchorElement;
      });

      await downloadFile(TEST_VALUES.DOWNLOAD_URL, TEST_VALUES.FILENAME);

      expect(fetchMock).toHaveBeenCalledWith(TEST_VALUES.DOWNLOAD_URL);
      expect(mockCreateObjectURL).toHaveBeenCalled();
      expect(mockAnchor.download).toBe(TEST_VALUES.FILENAME);
      expect(mockAnchor.click).toHaveBeenCalled();
      expect(mockRevokeObjectURL).toHaveBeenCalled();
    });
  });

  describe('downloadBlob', () => {
    let anchorMock: any;
    let mockCreateObjectURL: any;
    let mockRevokeObjectURL: any;

    beforeEach(() => {
      anchorMock = {
        href: '',
        download: '',
        click: vi.fn(),
        remove: vi.fn(),
      };

      // Create mocks for URL methods
      mockCreateObjectURL = vi.fn().mockReturnValue(TEST_VALUES.BLOB_URL);
      mockRevokeObjectURL = vi.fn();

      // Define window.URL globally for the test environment
      global.URL = {
        createObjectURL: mockCreateObjectURL,
        revokeObjectURL: mockRevokeObjectURL,
      } as any;

      vi.spyOn(document, 'createElement').mockImplementation(
        () => anchorMock as unknown as HTMLAnchorElement
      );
    });

    it('should create an object URL and trigger download', async () => {
      const blob = new Blob([TEST_VALUES.BLOB_CONTENT], { type: TEST_VALUES.MIME_TYPES.TEXT });

      await downloadBlob(blob, TEST_VALUES.TEXT_FILENAME);

      expect(mockCreateObjectURL).toHaveBeenCalledWith(blob);
      expect(anchorMock.href).toBe(TEST_VALUES.BLOB_URL);
      expect(anchorMock.download).toBe(TEST_VALUES.TEXT_FILENAME);
      expect(anchorMock.click).toHaveBeenCalled();
      expect(mockRevokeObjectURL).toHaveBeenCalledWith(TEST_VALUES.BLOB_URL);
      expect(anchorMock.remove).toHaveBeenCalled();
    });
  });
});
