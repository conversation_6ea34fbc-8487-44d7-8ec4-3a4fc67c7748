import { differenceWith, filter, find, map } from 'lodash';
import { ulid } from 'ulid';

/**
 * <PERSON><PERSON> create custom field with key value pair from list by ID
 * @param {any[]} list - The list of custom fields
 * @returns {Object} - The custom fields map
 */
export const createFieldsMapFromList = (list: any[]) => {
  return list.reduce((acc, field) => {
    acc[field.id] = field;
    return acc;
  }, {});
};

/**
 * Check if the value is an array
 * @param {any} value - The value
 * @returns {any[]} - The array value
 */
export const checkListValue = (value: any) => {
  return Array.isArray(value) ? value : [];
};

/**
 * Merge default custom fields with custom fields data by ID
 * @param {any[]} defaultCustomFields - The default custom fields
 * @param {any[]} customFieldsData - The custom fields data
 * @returns {any[]} - The merged custom fields
 */
export const mergeCustomFields = (defaultCustomFields: any[], customFieldsData: any[]) => {
  return defaultCustomFields.map((field) => {
    const customFieldData = customFieldsData.find((data) => data.id === field.id);
    return {
      ...field,
      ...customFieldData,
    };
  });
};

/**
 * Generate random field ID
 * @returns {string} - The random field ID
 */
export const randomFieldId = () => ulid();

/**
 * Order custom fields by template orders
 * @param {any[]} fields - The custom fields
 * @param {string[]} orders - The order of the custom fields
 */
export const orderCustomFieldsByTemplateOrders = (fields: any[], orders: string[]) => {
  const fieldIds = fields.map((field) => field.id);
  const avaiableOrders = orders.filter((id) => fieldIds.includes(id));

  // Find missmatched orders
  const missmatchedOrders = differenceWith(
    fieldIds,
    avaiableOrders,
    (fieldId, orderId) => fieldId === orderId
  );

  const finalOrders = [...missmatchedOrders, ...avaiableOrders];

  return filter(
    map(finalOrders, (id) => find(fields, { id }) ?? null),
    (field) => field !== null
  );
};
