import { KBDirectionQueryEnum, type KnowledgeBaseDirectionQuery } from '@/types';
import type { IPaginationNextPrevious } from '@resola-ai/models';
import { logger } from '@resola-ai/services-shared';
import get from 'lodash/get';

export const handleResponse = async <T>(promise: Promise<T>, dataPath?: string) => {
  try {
    const response = await promise;
    return dataPath ? get(response, dataPath, response) : get(response, 'data', response);
  } catch (error) {
    logger.error(error);
    return undefined;
  }
};

export const handleResponseWithError = async <T>(promise: Promise<T>, dataPath?: string) => {
  try {
    const response = await promise;
    return dataPath ? get(response, dataPath, response) : get(response, 'data', response);
  } catch (error) {
    logger.error(error);
    throw error;
  }
};

/**
 * Check next page available and get pagination param information with direction
 * @param pagination - IPaginationNextPrevious
 * @param direction - KnowledgeBaseDirectionQuery
 * @returns {
 *  cursor: string;
 *  direction: KnowledgeBaseDirectionQuery;
 *  hasMoreData: boolean;
 * }
 */
export const getPaginationWithDirection = (
  pagination: IPaginationNextPrevious,
  direction: KnowledgeBaseDirectionQuery
) => {
  const isBackwardDirection = direction === KBDirectionQueryEnum.Backward;

  const cursor = isBackwardDirection ? pagination.first : pagination.last;

  const hasMorePages = isBackwardDirection ? pagination.hasPreviousPage : pagination.hasNextPage;
  const hasMoreData = hasMorePages && cursor;

  return {
    ...pagination,
    cursor,
    direction,
    hasMoreData,
  };
};
