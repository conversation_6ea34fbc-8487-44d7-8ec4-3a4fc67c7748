import {
  type ArticleAnalyticData,
  ArticleAnalyticPeriod,
  type ArticleAnalyticSummary,
  type ArticleDateRangeValue,
} from '@/types/article';
import type { User } from '@/types/user';
import dayjs from 'dayjs';
import camelCase from 'lodash/camelCase';
import mapKeys from 'lodash/mapKeys';
import reduce from 'lodash/reduce';

/**
 * Get and format user name by locale
 * @param user - User information
 * @param locale - Locale to format name
 * @returns Formatted user name
 */
export const getUserName = (user: User, locale = 'ja'): string => {
  if (!user) return '';
  const { displayName = '', email = '', familyName = '', givenName = '' } = user;

  // If display name is available, return it with top priority
  if (displayName) return displayName;

  // If display name is not available, return name based on locale
  const name = locale === 'ja' ? `${familyName} ${givenName}` : `${givenName} ${familyName}`;

  return givenName || familyName ? name : email;
};

/**
 * Convert keys to camel case
 * @param data - Data to convert
 * @returns Data with camel case keys
 */
export const convertKeysToCamelCase = (data: any) => {
  return mapKeys(data, (_, key) => camelCase(key));
};
/**
 * Get date range by period
 * @param period - Period to get date range
 * @returns Date range tuple [startDate, endDate]
 */
export const getDateRangeByPeriod = (period: ArticleAnalyticPeriod): ArticleDateRangeValue => {
  // Cache the current time to avoid multiple instantiations
  const now = dayjs();
  const endDate = now.endOf('day').toDate();

  const dateRanges: Record<ArticleAnalyticPeriod, ArticleDateRangeValue> = {
    [ArticleAnalyticPeriod.LAST_7_DAYS]: [now.subtract(7, 'day').startOf('day').toDate(), endDate],
    [ArticleAnalyticPeriod.LAST_28_DAYS]: [
      now.subtract(28, 'day').startOf('day').toDate(),
      endDate,
    ],
    [ArticleAnalyticPeriod.LAST_90_DAYS]: [
      now.subtract(90, 'day').startOf('day').toDate(),
      endDate,
    ],
    [ArticleAnalyticPeriod.ALL_TIME]: [new Date(0), endDate],
  };

  return dateRanges[period] ?? [null, null];
};

/**
 * Determines the analytics period based on a date range
 * @param startDate - Start date of the range
 * @param endDate - End date of the range
 * @returns The corresponding ArticleAnalyticPeriod
 */
export const getPeriodByDateRange = (
  startDate: Date | null,
  endDate: Date | null
): ArticleAnalyticPeriod => {
  // Early return for invalid dates
  if (!startDate || !endDate) {
    return ArticleAnalyticPeriod.ALL_TIME;
  }

  // Create dayjs instances once
  const start = dayjs(startDate);
  const end = dayjs(endDate);
  const now = dayjs();

  // Check if end date is today
  if (!end.isSame(now, 'day')) {
    return ArticleAnalyticPeriod.ALL_TIME;
  }

  // Create a map of periods and their corresponding day differences
  const periodMap: [number, ArticleAnalyticPeriod][] = [
    [7, ArticleAnalyticPeriod.LAST_7_DAYS],
    [28, ArticleAnalyticPeriod.LAST_28_DAYS],
    [90, ArticleAnalyticPeriod.LAST_90_DAYS],
  ];

  // Find matching period
  for (const [days, period] of periodMap) {
    if (start.isSame(end.subtract(days, 'day'), 'day')) {
      return period;
    }
  }

  return ArticleAnalyticPeriod.ALL_TIME;
};

/**
 * Summarize analytics data based on product items, per data item was cottected by hour.
 * @param data - ArticleAnalyticData
 * @returns summary - ArticleAnalyticSummary
 */
export const summarizeAnalyticsData = (data: ArticleAnalyticData): ArticleAnalyticSummary => {
  // Initialize with default values
  const analyticsSummary: ArticleAnalyticSummary = {
    totalViewCount: 0,
    totalFeedbackCount: 0,
    totalLikeCount: 0,
    totalDislikeCount: 0,
    products: {},
  };

  return reduce(
    data,
    (summary, item) => {
      const { product, viewCount, feedbackCount, likeCount, dislikeCount } = item;

      // Update totals
      summary.totalViewCount += viewCount;
      summary.totalFeedbackCount += feedbackCount;
      summary.totalLikeCount += likeCount;
      summary.totalDislikeCount += dislikeCount;

      // Update product-specific metrics using object spread
      summary.products[product] = {
        product,
        viewCount: (summary.products[product]?.viewCount || 0) + viewCount,
        feedbackCount: (summary.products[product]?.feedbackCount || 0) + feedbackCount,
        likeCount: (summary.products[product]?.likeCount || 0) + likeCount,
        dislikeCount: (summary.products[product]?.dislikeCount || 0) + dislikeCount,
      };

      return summary;
    },
    analyticsSummary
  );
};

/**
 * Clean text by removing zero-width characters and BOM
 * @param text - Text to clean (can be undefined/null)
 * @returns Cleaned text or empty string if text is falsy
 */
export const cleanBadCharacters = (text?: string | null): string => {
  if (!text) return '';

  // Remove zero-width characters: zero-width space, zero-width non-joiner,
  // zero-width joiner, and zero-width no-break space (BOM)
  return text.replace(/\u200B|\u200C|\u200D|\uFEFF/g, '');
};
