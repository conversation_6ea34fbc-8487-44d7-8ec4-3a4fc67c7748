import FileIcon from '@/components/common/FileIcon';
import {
  type ArticleSearchData,
  type BaseSearchData,
  type DocumentSearchData,
  type SearchResult,
  SearchTypeEnums,
} from '@/types';
import { rem } from '@mantine/core';
import type { BreadcrumbItem } from '@resola-ai/ui';
import { IconFileText, IconFolder, IconLicense } from '@tabler/icons-react';
import get from 'lodash/get';
import split from 'lodash/split';

// Constants
const ROOT_PATH = '/kb/';
const FOLDER_PATH = `${ROOT_PATH}folder/`;
const ICON_SIZE = 24;

interface BreadcrumbConfig {
  array: string[];
  path: string;
}

/**
 * Removes line breaks from text and replaces them with spaces
 * @param text - The input text to process
 * @returns Processed text with line breaks replaced by spaces
 */
const normalizeText = (text: string): string => {
  if (!text) return '';
  return text.replace(/\n/g, ' ');
};

/**
 * Creates breadcrumb items from an array of titles and their corresponding path
 * @param config - Configuration object containing breadcrumb array and path
 * @param skipRoot - Whether to skip the root breadcrumb
 * @returns Array of BreadcrumbItem objects
 */
const createBreadcrumbFromArray = (config: BreadcrumbConfig, skipRoot = true): BreadcrumbItem[] => {
  const breadcrumbIds = split(config.path, '/').filter(Boolean);

  // Skip the first item ('/root') and map the rest
  if (skipRoot) {
    const skipRootIds = breadcrumbIds.slice(1);
    return config.array.slice(1).map((title: string, index: number) => ({
      title,
      href: `${FOLDER_PATH}${skipRootIds[index]}`,
    }));
  }

  return config.array.map((title: string, index: number) => ({
    title,
    href: `${FOLDER_PATH}${breadcrumbIds[index]}`,
  }));
};

/**
 * Generates base breadcrumb items for a given base data
 * @param base - Base search data
 * @returns Array of BreadcrumbItem objects
 */
const createBaseBreadcrumb = (base: BaseSearchData | null): BreadcrumbItem[] => {
  if (!base) return [];

  const parentBreadcrumbs = createBreadcrumbFromArray({
    array: base.parentDirBreadcrumbArray || [],
    path: base.parentDirPath || '',
  });

  return [...parentBreadcrumbs, { title: base.name || '', href: `${ROOT_PATH}${base.id}` }];
};

/**
 * Generates article breadcrumb items
 * @param articleData - Article search data
 * @returns Array of BreadcrumbItem objects
 */
const createArticleBreadcrumb = (articleData: ArticleSearchData): BreadcrumbItem[] => [
  ...(articleData.base ? createBaseBreadcrumb(articleData.base) : []),
  {
    title: articleData.title || '',
    href: `${ROOT_PATH}${articleData.base?.name || ''}?articleId=${articleData.id}`,
  },
];

/**
 * Generates document breadcrumb items
 * @param documentData - Document search data
 * @returns Array of BreadcrumbItem objects
 */
const createDocumentBreadcrumb = (documentData: DocumentSearchData): BreadcrumbItem[] => {
  const parentBreadcrumbs = createBreadcrumbFromArray({
    array: documentData?.parentDirBreadcrumbArray || [],
    path: documentData?.parentDirPath || '',
  });

  const documentName = get(documentData, 'metadata.name', '').split('.')[0];
  return [...parentBreadcrumbs, { title: documentName, href: `${ROOT_PATH}${documentData.id}` }];
};

export interface NormalizedSearchResult {
  breadcrumbs: BreadcrumbItem[];
  icon: React.ReactNode;
  title: string;
  description: string;
}

/**
 * Normalizes search results into a consistent format
 * @param searchResult - The search result to normalize
 * @param translator - Translation function
 * @returns Normalized search result information
 */
export const normalizeSearchResult = (
  searchResult: SearchResult,
  translator: (key: string) => string
): NormalizedSearchResult => {
  const { type, data } = searchResult;
  const iconStyles = { width: rem(ICON_SIZE), height: rem(ICON_SIZE) };
  const rootBreadcrumb = { title: translator('tree.root'), href: ROOT_PATH };

  const defaultResult: NormalizedSearchResult = {
    breadcrumbs: [rootBreadcrumb],
    icon: <IconFolder style={iconStyles} />,
    title: '',
    description: '',
  };

  switch (type) {
    case SearchTypeEnums.folder: {
      const folderBreadcrumbs = createBreadcrumbFromArray({
        array: get(data, 'breadcrumbArray', []),
        path: get(data, 'path', ''),
      });

      return {
        ...defaultResult,
        breadcrumbs: [...defaultResult.breadcrumbs, ...folderBreadcrumbs],
        title: get(data, 'name', ''),
      };
    }

    case SearchTypeEnums.base: {
      const baseData = data as BaseSearchData;
      return {
        ...defaultResult,
        breadcrumbs: [...defaultResult.breadcrumbs, ...createBaseBreadcrumb(baseData)],
        icon: <IconLicense size={ICON_SIZE} />,
        title: baseData.name || '',
        description: normalizeText(baseData.description || ''),
      };
    }

    case SearchTypeEnums.article: {
      const articleData = data as ArticleSearchData;
      return {
        ...defaultResult,
        breadcrumbs: [...defaultResult.breadcrumbs, ...createArticleBreadcrumb(articleData)],
        icon: <IconFileText size={ICON_SIZE} />,
        title: articleData.title || '',
        description: normalizeText(articleData.content || ''),
      };
    }

    case SearchTypeEnums.document: {
      const documentData = data as DocumentSearchData;
      const documentName = get(documentData, 'metadata.name', '').split('.')[0];

      return {
        ...defaultResult,
        breadcrumbs: [...defaultResult.breadcrumbs, ...createDocumentBreadcrumb(documentData)],
        icon: (
          <FileIcon
            contentType={get(documentData, 'metadata.contentType', '')}
            style={iconStyles}
          />
        ),
        title: documentName,
        description: normalizeText(get(documentData, 'previewText', '')),
      };
    }

    default:
      return defaultResult;
  }
};
