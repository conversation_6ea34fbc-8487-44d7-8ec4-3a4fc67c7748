import type { StepperStepProps } from '@mantine/core';

type TranslateFunction = (key: string, options?: any) => string;

export const generateStep = (t: TranslateFunction): StepperStepProps[] => [
  {
    label: t('qnaGenerateStep1', { ns: 'kb' }),
    title: 'Select a  Document KB & input prompt',
  },
  {
    label: t('qnaGenerateStep2', { ns: 'kb' }),
    title: 'Generate QnA',
  },
  {
    label: t('qnaGenerateStep3', { ns: 'kb' }),
    title: 'Select QnA from generated list',
  },
  {
    label: t('qnaGenerateStep4', { ns: 'kb' }),
    title: 'Complete',
  },
];
