import { useSetDefaultLang } from '@resola-ai/ui/hooks';
import { ErrorPage } from '@resola-ai/ui/pages';
import { useTolgee, useTranslate } from '@tolgee/react';
import { useEffect } from 'react';
import { Route, Routes } from 'react-router-dom';

import {
  DetailPage,
  DocumentPage,
  FolderPage,
  GeneratePage,
  HomePage,
  JobDetailPage,
  JobResultPage,
  JobsPage,
  NotFoundPage,
  QnADetailPage,
  QnAPage,
  TemplateDetailPage,
  TemplatesPage,
} from './pages';

const App = () => {
  const { t } = useTranslate('home');
  const lang = useSetDefaultLang();
  const tolgee = useTolgee();

  useEffect(() => {
    if (tolgee.getLanguage() !== lang) {
      tolgee.changeLanguage(lang);
    }
  }, [lang, tolgee]);

  return (
    <Routes>
      <Route path='/kb'>
        <Route path='' element={<HomePage />} />
        <Route path=':kbId' element={<DetailPage />} />
        <Route path='folder/:folderId' element={<FolderPage />} />
        <Route path='jobs' element={<JobsPage />} />
        <Route path='jobs/:jobId' element={<JobDetailPage />} />
        <Route path='jobs/:jobId/result' element={<JobResultPage />} />
        <Route path='qna' element={<QnAPage />} />
        <Route path='qna/:kbId' element={<QnADetailPage />} />
        <Route path='qna/:kbId/generate' element={<GeneratePage />} />
        <Route path='document/:documentId' element={<DocumentPage />} />
        <Route path='settings/templates' element={<TemplatesPage />} />
        <Route path='settings/templates/:templateId' element={<TemplateDetailPage />} />
        <Route path='unauthorized' element={<ErrorPage appName={t('kb')} />} />
        <Route path='404' element={<NotFoundPage />} />
        <Route path='404/:entityType' element={<NotFoundPage />} />
      </Route>
    </Routes>
  );
};

export default App;
