import { JobAPI } from '@/services/api/v2';
import {
  type IJobsRequestParams,
  type Job,
  JobType,
  KBDirectionQueryEnum,
  type KnowledgeBaseDirectionQuery,
} from '@/types';
import { getPaginationWithDirection } from '@/utils/api';
import { sortByCreatedAt } from '@/utils/dateTime';
import type { IPaginationNextPrevious } from '@resola-ai/models';
import { createContextHook } from '@resola-ai/utils';
import { useCallback, useState } from 'react';

interface IJobContextState {
  jobsLoading: boolean;
  jobs: Job[] | undefined;
  capturedPagination: IPaginationNextPrevious;
  capturedDirection: KnowledgeBaseDirectionQuery;
}

const useJob = () => {
  const [jobsState, setJobsState] = useState<IJobContextState>({
    jobsLoading: false,
    jobs: undefined,
    capturedPagination: {
      first: '',
      last: '',
      hasNextPage: false,
      hasPreviousPage: false,
    },
    capturedDirection: KBDirectionQueryEnum.Backward,
  });

  /**
   * Fetch jobs with default params in the first time
   * @param {IJobsRequestParams} params
   * @returns {Promise<any>}
   */
  const fetchJobs = useCallback(
    async (
      params: IJobsRequestParams = {
        jobType: [JobType.ContentGeneration, JobType.ArticleExport],
        direction: KBDirectionQueryEnum.Backward,
        cursor: '',
      }
    ) => {
      setJobsState((prev) => ({ ...prev, jobsLoading: true }));
      const res = await JobAPI.getJobs(params);

      if (res?.status === 'success') {
        setJobsState((prev) => ({
          ...prev,
          jobs: sortByCreatedAt(res?.data ?? []),
          capturedPagination: res?.pagination ?? {},
          capturedDirection: params.direction ?? KBDirectionQueryEnum.Backward,
          jobsLoading: false,
        }));
      } else {
        setJobsState((prev) => ({ ...prev, jobsLoading: false }));
      }

      return res;
    },
    []
  );

  /**
   * Fetch more jobs with direction and cursor
   * @returns void
   */
  const fetchMoreJobs = useCallback(
    async (
      params: IJobsRequestParams = {
        jobType: JobType.ContentGeneration,
      }
    ) => {
      const { cursor, direction, hasMoreData } = getPaginationWithDirection(
        jobsState.capturedPagination,
        jobsState.capturedDirection
      );

      if (!hasMoreData) return;

      setJobsState((prev) => ({ ...prev, jobsLoading: true }));
      const res = await JobAPI.getJobs({ ...params, cursor, direction });

      if (res?.status === 'success') {
        setJobsState((prev) => ({
          ...prev,
          jobs: [...(prev.jobs ?? []), ...sortByCreatedAt(res?.data ?? [])],
          capturedPagination: res?.pagination ?? {},
          capturedDirection: direction,
          jobsLoading: false,
        }));

        return res;
      }

      setJobsState((prev) => ({ ...prev, jobsLoading: false }));
      return res;
    },
    [jobsState.capturedPagination, jobsState.capturedDirection]
  );

  /**
   * Delete a job
   * @param {string} jobId
   * @returns {Promise<any>}
   */
  const deleteJob = useCallback(async (jobId: string) => {
    const res = await JobAPI.deleteJob(jobId);
    if (res?.status === 'success') {
      setJobsState((prev) => ({ ...prev, jobs: prev.jobs?.filter((job) => job.id !== jobId) }));
    }
  }, []);

  return {
    fetchJobs,
    fetchMoreJobs,
    deleteJob,
    jobs: jobsState.jobs,
    jobsLoading: jobsState.jobsLoading,
    capturedPagination: jobsState.capturedPagination,
  };
};

export const [JobContextProvider, useJobContext, JobContext] = createContextHook(
  useJob,
  'JobContext'
);
