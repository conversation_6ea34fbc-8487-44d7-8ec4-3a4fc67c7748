import { act, renderHook } from '@testing-library/react';
import type { AxiosResponse } from 'axios';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock dependencies with explicit typing
vi.mock('@/services/api/v2', () => ({
  JobAPI: {
    getJobDetail: vi.fn(),
    getJobResult: vi.fn(),
    retryJob: vi.fn(),
    deleteJob: vi.fn(),
  },
  GenArticleAPI: {
    download: vi.fn(),
  },
}));

vi.mock('@/utils/file', () => ({
  downloadBlob: vi.fn(),
}));

// Mock useApiHandler to prevent React Router hook calls
vi.mock('@/hooks/useApiHandler', () => ({
  useApiHandler: () => ({
    handleApiRequest: async (promise: Promise<AxiosResponse>) => {
      const response = await promise;
      return response.data;
    },
  }),
}));

// Mock useNotifications
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
    i18n: {
      language: 'en',
    },
  }),
}));

// Import after all vi.mock calls
import { JobDetailProvider, useJobDetailContext } from '@/contexts/JobDetailContext';
import { GenArticleAPI, JobAPI } from '@/services/api/v2';
import { downloadBlob } from '@/utils/file';

// Test constants
const TEST_VALUES = {
  JOB_ID: 'job-123',
  ARTICLE_ID_1: 'article-123',
  ARTICLE_ID_2: 'article-456',
  FILENAME: 'test-job',
  JOB_DETAIL: {
    id: 'job-123',
    name: 'Test Job',
    status: 'completed',
  },
  JOB_ARTICLES: [
    { id: 'article-123', title: 'Article 1' },
    { id: 'article-456', title: 'Article 2' },
  ],
  CSV_DATA: 'title,content\nArticle 1,Content 1\nArticle 2,Content 2',
};

describe('JobDetailContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  // Wrap the provider with BrowserRouter to support React Router hooks
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <BrowserRouter>
      <JobDetailProvider>{children}</JobDetailProvider>
    </BrowserRouter>
  );

  describe('getJobDetail', () => {
    it('should fetch job detail and results', async () => {
      // Mock API responses
      (JobAPI.getJobDetail as any).mockResolvedValue(TEST_VALUES.JOB_DETAIL);
      (JobAPI.getJobResult as any).mockResolvedValue(TEST_VALUES.JOB_ARTICLES);

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.getJobDetail(TEST_VALUES.JOB_ID);
      });

      expect(JobAPI.getJobDetail).toHaveBeenCalledWith(TEST_VALUES.JOB_ID);
      expect(JobAPI.getJobResult).toHaveBeenCalledWith(TEST_VALUES.JOB_ID);
      expect(result.current.jobDetail).toEqual(TEST_VALUES.JOB_DETAIL);
      expect(result.current.articles).toEqual(TEST_VALUES.JOB_ARTICLES);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isEmpty).toBe(false);
    });

    it('should handle empty results', async () => {
      (JobAPI.getJobDetail as any).mockResolvedValue(TEST_VALUES.JOB_DETAIL);
      (JobAPI.getJobResult as any).mockResolvedValue([]);

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.getJobDetail(TEST_VALUES.JOB_ID);
      });

      expect(result.current.isEmpty).toBe(true);
    });

    it('should handle errors', async () => {
      // Mock console.error to prevent stderr output
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      (JobAPI.getJobDetail as any).mockRejectedValue(new Error('API error'));

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.getJobDetail(TEST_VALUES.JOB_ID);
      });

      expect(result.current.isLoading).toBe(false);
      expect(result.current.isEmpty).toBe(false);

      // Restore console.error
      consoleErrorSpy.mockRestore();
    });
  });

  describe('getJobResult', () => {
    it('should fetch job results', async () => {
      // Mock API response
      (JobAPI.getJobResult as any).mockResolvedValue(TEST_VALUES.JOB_ARTICLES);

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.getJobResult(TEST_VALUES.JOB_ID);
      });

      expect(JobAPI.getJobResult).toHaveBeenCalledWith(TEST_VALUES.JOB_ID);
      expect(result.current.articles).toEqual(TEST_VALUES.JOB_ARTICLES);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.isEmpty).toBe(false);
    });

    it('should handle empty results', async () => {
      (JobAPI.getJobResult as any).mockResolvedValue([]);

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.getJobResult(TEST_VALUES.JOB_ID);
      });

      expect(result.current.isEmpty).toBe(true);
    });

    it('should handle errors', async () => {
      // Mock console.error to prevent stderr output
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      (JobAPI.getJobResult as any).mockRejectedValue(new Error('API error'));

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.getJobResult(TEST_VALUES.JOB_ID);
      });

      expect(result.current.isLoading).toBe(false);

      // Restore console.error
      consoleErrorSpy.mockRestore();
    });

    it('should do nothing when jobId is empty', async () => {
      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.getJobResult('');
      });

      expect(JobAPI.getJobResult).not.toHaveBeenCalled();
    });
  });

  describe('article selection', () => {
    it('should select an article', async () => {
      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      act(() => {
        result.current.selectArticle(TEST_VALUES.ARTICLE_ID_1);
      });

      expect(result.current.selectedArticleIds).toContain(TEST_VALUES.ARTICLE_ID_1);
    });

    it('should deselect an article', async () => {
      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      act(() => {
        result.current.selectArticle(TEST_VALUES.ARTICLE_ID_1);
        result.current.deselectArticle(TEST_VALUES.ARTICLE_ID_1);
      });

      expect(result.current.selectedArticleIds).not.toContain(TEST_VALUES.ARTICLE_ID_1);
    });

    it('should select all articles', async () => {
      (JobAPI.getJobDetail as any).mockResolvedValue(TEST_VALUES.JOB_DETAIL);
      (JobAPI.getJobResult as any).mockResolvedValue(TEST_VALUES.JOB_ARTICLES);

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.getJobDetail(TEST_VALUES.JOB_ID);
      });

      act(() => {
        result.current.selectAllArticles();
      });

      expect(result.current.selectedArticleIds).toHaveLength(2);
      expect(result.current.selectedArticleIds).toContain(TEST_VALUES.ARTICLE_ID_1);
      expect(result.current.selectedArticleIds).toContain(TEST_VALUES.ARTICLE_ID_2);
    });

    it('should deselect all articles', async () => {
      (JobAPI.getJobDetail as any).mockResolvedValue(TEST_VALUES.JOB_DETAIL);
      (JobAPI.getJobResult as any).mockResolvedValue(TEST_VALUES.JOB_ARTICLES);

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.getJobDetail(TEST_VALUES.JOB_ID);
        result.current.selectAllArticles();
        result.current.deselectAllArticles();
      });

      expect(result.current.selectedArticleIds).toHaveLength(0);
    });
  });

  describe('retryJob', () => {
    it('should retry a job', async () => {
      (JobAPI.retryJob as any).mockResolvedValue({ id: 'new-job-123' });

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.retryJob(TEST_VALUES.JOB_ID);
      });

      expect(JobAPI.retryJob).toHaveBeenCalledWith(TEST_VALUES.JOB_ID);
      expect(result.current.isLoading).toBe(true);
    });
  });

  describe('deleteJob', () => {
    it('should delete a job', async () => {
      (JobAPI.deleteJob as any).mockResolvedValue({ data: { success: true } });

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      let response: any;
      await act(async () => {
        response = await result.current.deleteJob(TEST_VALUES.JOB_ID);
      });

      expect(JobAPI.deleteJob).toHaveBeenCalledWith(TEST_VALUES.JOB_ID);
      expect(response).toEqual({ success: true });
    });

    it('should throw error when delete fails', async () => {
      (JobAPI.deleteJob as any).mockResolvedValue(null);

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await expect(result.current.deleteJob(TEST_VALUES.JOB_ID)).rejects.toThrow(
        'Failed to delete job'
      );
    });
  });

  describe('downloadGenArticles', () => {
    it('should download generated articles', async () => {
      (GenArticleAPI.download as any).mockResolvedValue({ data: TEST_VALUES.CSV_DATA });

      global.Blob = vi.fn().mockImplementation((content, options) => {
        return {
          content,
          options,
          size: TEST_VALUES.CSV_DATA.length,
          type: options.type,
        };
      }) as any;

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.downloadGenArticles(TEST_VALUES.JOB_ID, TEST_VALUES.FILENAME);
      });

      expect(GenArticleAPI.download).toHaveBeenCalledWith(TEST_VALUES.JOB_ID, 'csv', undefined);
      expect(downloadBlob).toHaveBeenCalledWith(
        expect.objectContaining({ size: TEST_VALUES.CSV_DATA.length }),
        `${TEST_VALUES.FILENAME}.csv`
      );
    });

    it('should use job ID as filename when no filename provided', async () => {
      (GenArticleAPI.download as any).mockResolvedValue({ data: TEST_VALUES.CSV_DATA });

      global.Blob = vi.fn().mockImplementation((content, options) => {
        return {
          content,
          options,
          size: TEST_VALUES.CSV_DATA.length,
          type: options.type,
        };
      }) as any;

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.downloadGenArticles(TEST_VALUES.JOB_ID);
      });

      expect(downloadBlob).toHaveBeenCalledWith(
        expect.objectContaining({ size: TEST_VALUES.CSV_DATA.length }),
        `${TEST_VALUES.JOB_ID}.csv`
      );
    });

    it('should handle download errors', async () => {
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      (GenArticleAPI.download as any).mockRejectedValue(new Error('Download failed'));

      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.downloadGenArticles(TEST_VALUES.JOB_ID);
      });

      expect(consoleErrorSpy).toHaveBeenCalled();
      consoleErrorSpy.mockRestore();
    });

    it('should do nothing when jobId is empty', async () => {
      const { result } = renderHook(() => useJobDetailContext(), { wrapper });

      await act(async () => {
        await result.current.downloadGenArticles('');
      });

      expect(GenArticleAPI.download).not.toHaveBeenCalled();
    });
  });
});
