import {
  ArticleDetailContextProvider,
  useArticleDetailContext,
} from '@/contexts/ArticleDetailContext';
import { COMPLETE_MOCK_ARTICLE, TEST_IDS } from '@/mocks/articleDetailContextMock';
import { mockAxiosConfig } from '@/mocks/axiosMock';
import { ArticleAPI } from '@/services/api/v2/article';
import { VoteAPI } from '@/services/api/v2/vote';
import { VoteType } from '@/types';
import { AllTheProviders, mockReactRouter } from '@/utils/unitTest';
import { renderHook } from '@testing-library/react';
import type { AxiosResponse } from 'axios';
import { act } from 'react-dom/test-utils';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock declarations
vi.mock('@/services/api/v2/article', () => ({
  ArticleAPI: {
    get: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    uploadFile: vi.fn(),
    uploadFileWhenCreating: vi.fn(),
    createShortcut: vi.fn(),
  },
}));

vi.mock('@/services/api/v2/vote', () => ({
  VoteAPI: {
    getArticleVote: vi.fn(),
    updateArticleVote: vi.fn(),
  },
}));

vi.mock('@/hooks', () => ({
  useFileUpload: () => ({
    uploadFiles: vi.fn(),
    uploaders: [],
    validateFiles: vi.fn(),
  }),
  useApiHandler: () => ({
    handleError: vi.fn(),
    handleApiRequest: vi.fn((promise) => promise),
    API_RESPONSE_STATUS: {
      SUCCESS: '200',
    },
  }),
}));

vi.mock('@/hooks/useArticleDeleteConfirmation', () => ({
  useArticleDeleteConfirmation: () => ({
    confirmThenDeleteArticle: vi.fn((article, onDeleted, setIsLoading) => {
      // Mock implementation - don't throw errors but simulate success/failure flow
      if (article?.baseId && article.id) {
        setIsLoading?.(true);

        // Call the API and handle both success and error paths without throwing
        ArticleAPI.delete(article.baseId, article.id)
          .then(() => {
            // Success case
            onDeleted?.();
            setIsLoading?.(false);
          })
          .catch(() => {
            // Error case - just set loading to false without calling onDeleted
            setIsLoading?.(false);
          });
      }
    }),
  }),
}));

vi.mock('@/contexts/UploaderContext', () => ({
  useUploader: () => ({
    uploadFiles: vi.fn(),
    uploaders: [],
    validateFiles: vi.fn(),
    openedUploadStatus: false,
    openUploadStatus: vi.fn(),
    closeUploadStatus: vi.fn(),
  }),
  UploaderContextProvider: ({ children }) => children,
}));

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: () => ({
    openConfirmModal: vi.fn(),
    closeConfirmModal: vi.fn(),
  }),
  AppContextProvider: ({ children }) => children,
}));

vi.mock('react-router-dom', () => ({
  useNavigate: () => vi.fn(),
  useLocation: () => ({ pathname: '/', search: '', hash: '', state: null }),
  useParams: () => ({ articleId: 'test-article-id' }),
}));

const wrapper = ({ children }) => (
  <AllTheProviders>
    <ArticleDetailContextProvider>{children}</ArticleDetailContextProvider>
  </AllTheProviders>
);

describe('ArticleDetailContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockReactRouter();
  });

  describe('getArticleById', () => {
    it('should have required functions', () => {
      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });
      expect(result.current.getArticleById).toBeDefined();
      expect(typeof result.current.getArticleById).toBe('function');
    });

    it('should fetch article successfully', async () => {
      // Create a proper Axios response
      const axiosResponse: AxiosResponse = {
        data: COMPLETE_MOCK_ARTICLE,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: mockAxiosConfig,
      };

      vi.mocked(ArticleAPI.get).mockResolvedValue(axiosResponse);

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      await result.current.getArticleById(TEST_IDS.ARTICLE_ID);

      expect(ArticleAPI.get).toHaveBeenCalledWith(TEST_IDS.ARTICLE_ID);

      consoleErrorSpy.mockRestore();
    });

    it('should handle error when fetching article', async () => {
      const error = new Error('Failed to fetch article');
      vi.mocked(ArticleAPI.get).mockRejectedValue(error);

      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      try {
        await result.current.getArticleById(TEST_IDS.ARTICLE_ID);
      } catch (e) {
        expect((e as Error).message).toBe('Failed to fetch article');
      }

      expect(ArticleAPI.get).toHaveBeenCalledWith(TEST_IDS.ARTICLE_ID);

      consoleErrorSpy.mockRestore();
    });
  });

  describe('updateArticle', () => {
    const updatePayload = {
      title: 'Updated Title',
      content: 'Updated Content',
      status: 'published',
    };

    it('should update article successfully', async () => {
      const updatedArticle = {
        ...COMPLETE_MOCK_ARTICLE,
        ...updatePayload,
      };

      // For the update API, we need to use the expected ISuccessResponse format
      vi.mocked(ArticleAPI.update).mockResolvedValue({
        status: '200',
        data: updatedArticle,
      } as any);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      await act(async () => {
        await result.current.updateArticle(TEST_IDS.ARTICLE_ID, updatePayload);
      });

      expect(ArticleAPI.update).toHaveBeenCalledWith(
        TEST_IDS.ARTICLE_ID,
        expect.objectContaining(updatePayload)
      );
    });

    it('should handle error when updating article', async () => {
      const error = new Error('Failed to update article');
      vi.mocked(ArticleAPI.update).mockRejectedValue(error);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      let updatePromiseError: any;
      await act(async () => {
        try {
          await result.current.updateArticle(TEST_IDS.ARTICLE_ID, updatePayload);
        } catch (e) {
          updatePromiseError = e;
        }
      });

      expect(updatePromiseError).toBeDefined();
      expect(updatePromiseError.message).toBe('Failed to update article');
    });
  });

  describe('deleteArticle', () => {
    it('should delete article successfully', async () => {
      // Setup the mock to resolve
      const axiosResponse: AxiosResponse = {
        data: { success: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: mockAxiosConfig,
      };
      vi.mocked(ArticleAPI.delete).mockResolvedValue(axiosResponse);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });
      const onDeletedMock = vi.fn();

      // Initial state should be not loading
      expect(result.current.isDetailLoading).toBe(false);

      await act(async () => {
        result.current.deleteArticle(COMPLETE_MOCK_ARTICLE, onDeletedMock);

        // Allow time for all promises to resolve
        await new Promise((resolve) => setTimeout(resolve, 100));
      });

      // Verify the API was called with correct parameters
      expect(ArticleAPI.delete).toHaveBeenCalledWith(
        COMPLETE_MOCK_ARTICLE.baseId,
        COMPLETE_MOCK_ARTICLE.id
      );

      // After completion, it should not be loading
      expect(result.current.isDetailLoading).toBe(false);

      // Most importantly, verify the onDeleted callback was called
      expect(onDeletedMock).toHaveBeenCalled();
    });

    it('should handle error when deleting article', async () => {
      // Setup the mock to reject
      const error = new Error('Failed to delete article');
      vi.mocked(ArticleAPI.delete).mockRejectedValue(error);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });
      const onDeletedMock = vi.fn();

      // Initial state should be not loading
      expect(result.current.isDetailLoading).toBe(false);

      await act(async () => {
        // Call the deleteArticle method
        result.current.deleteArticle(COMPLETE_MOCK_ARTICLE, onDeletedMock);

        // Allow time for all promises to resolve
        await new Promise((resolve) => setTimeout(resolve, 100));
      });

      // Verify the API was called with correct parameters
      expect(ArticleAPI.delete).toHaveBeenCalledWith(
        COMPLETE_MOCK_ARTICLE.baseId,
        COMPLETE_MOCK_ARTICLE.id
      );

      // After error, it should not be loading
      expect(result.current.isDetailLoading).toBe(false);

      // Most importantly, verify the onDeleted callback was not called
      expect(onDeletedMock).not.toHaveBeenCalled();
    });
  });

  describe('fileUpload', () => {
    beforeEach(() => {
      vi.clearAllMocks();

      // Create a proper Axios response
      const axiosResponse: AxiosResponse = {
        data: COMPLETE_MOCK_ARTICLE,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: mockAxiosConfig,
      };

      vi.mocked(ArticleAPI.get).mockResolvedValue(axiosResponse);
    });

    it('should upload file to existing article', async () => {
      const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' });

      // For the upload API, we need to use the expected format
      vi.mocked(ArticleAPI.uploadFile).mockResolvedValue({
        status: '200',
        data: {
          ...COMPLETE_MOCK_ARTICLE,
          imageUrl: 'https://example.com/image.jpg',
        },
      } as any);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      await act(async () => {
        await result.current.uploadFileInArticle(TEST_IDS.BASE_ID, TEST_IDS.ARTICLE_ID, mockFile);
      });

      expect(ArticleAPI.uploadFile).toHaveBeenCalledWith(
        TEST_IDS.BASE_ID,
        TEST_IDS.ARTICLE_ID,
        mockFile
      );
    });

    it('should upload file when creating article', async () => {
      const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' });

      // Create a proper Axios response
      const axiosResponse: AxiosResponse = {
        data: {
          ...COMPLETE_MOCK_ARTICLE,
          imageUrl: 'https://example.com/image.jpg',
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: mockAxiosConfig,
      };

      vi.mocked(ArticleAPI.uploadFileWhenCreating).mockResolvedValue(axiosResponse);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      await expect(
        result.current.uploadFileWhenCreatingArticle(TEST_IDS.BASE_ID, mockFile)
      ).resolves.not.toThrow();

      expect(ArticleAPI.uploadFileWhenCreating).toHaveBeenCalledTimes(1);
      expect(ArticleAPI.uploadFileWhenCreating).toHaveBeenCalledWith(TEST_IDS.BASE_ID, mockFile);
    });

    it('should handle error when uploading file', async () => {
      const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' });
      const error = new Error('Failed to upload file');

      vi.mocked(ArticleAPI.uploadFile).mockRejectedValue(error);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      let uploadError: any;
      await act(async () => {
        try {
          await result.current.uploadFileInArticle(TEST_IDS.BASE_ID, TEST_IDS.ARTICLE_ID, mockFile);
        } catch (e) {
          uploadError = e;
        }
      });

      expect(uploadError).toBeDefined();
      expect(uploadError.message).toBe('Failed to upload file');
      expect(ArticleAPI.uploadFile).toHaveBeenCalledWith(
        TEST_IDS.BASE_ID,
        TEST_IDS.ARTICLE_ID,
        mockFile
      );
    });

    it('should handle file size limit', async () => {
      const largeFile = new File(['large content'], 'large.txt', { type: 'text/plain' });
      Object.defineProperty(largeFile, 'size', { value: 10 * 1024 * 1024 });

      const error = new Error('Failed to upload file');
      vi.mocked(ArticleAPI.uploadFile).mockRejectedValue(error);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      await expect(
        result.current.uploadFileInArticle(TEST_IDS.BASE_ID, TEST_IDS.ARTICLE_ID, largeFile)
      ).rejects.toThrow('Failed to upload file');
    });

    it('should update upload status correctly', async () => {
      const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' });

      // For the upload API, we need to use the expected format
      vi.mocked(ArticleAPI.uploadFile).mockResolvedValue({
        status: '200',
        data: {
          ...COMPLETE_MOCK_ARTICLE,
          imageUrl: 'https://example.com/image.jpg',
        },
      } as any);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      await act(async () => {
        await result.current.uploadFileInArticle(TEST_IDS.BASE_ID, TEST_IDS.ARTICLE_ID, mockFile);
      });

      expect(ArticleAPI.uploadFile).toHaveBeenCalled();
    });
  });

  describe('context state', () => {
    it('should initialize with default values', () => {
      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      expect(result.current.getArticleById).toBeDefined();
      expect(result.current.updateArticle).toBeDefined();
      expect(result.current.deleteArticle).toBeDefined();
    });

    it('should handle loading states correctly', async () => {
      let resolvePromise: (value: any) => void;
      const mockPromise = new Promise((resolve) => {
        resolvePromise = resolve;
      });

      // Setup the mock with implementation
      vi.mocked(ArticleAPI.get).mockImplementation(() => mockPromise as Promise<any>);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      let getArticlePromise: Promise<any>;
      act(() => {
        getArticlePromise = result.current.getArticleById(TEST_IDS.ARTICLE_ID);
      });

      // Create a proper Axios response
      const axiosResponse: AxiosResponse = {
        data: COMPLETE_MOCK_ARTICLE,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: mockAxiosConfig,
      };

      // Resolve with proper Axios-compatible response
      await act(async () => {
        resolvePromise(axiosResponse);
        await getArticlePromise;
      });

      expect(ArticleAPI.get).toHaveBeenCalled();
    });
  });

  describe('createShortcut', () => {
    it('should create a shortcut successfully', async () => {
      // Create a proper Axios response
      const axiosResponse: AxiosResponse = {
        data: { id: 'shortcut-123' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: mockAxiosConfig,
      };

      vi.mocked(ArticleAPI.createShortcut).mockResolvedValue(axiosResponse);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      await act(async () => {
        await result.current.createShortcut(TEST_IDS.ARTICLE_ID, TEST_IDS.BASE_ID);
      });

      expect(ArticleAPI.createShortcut).toHaveBeenCalledWith(TEST_IDS.ARTICLE_ID, TEST_IDS.BASE_ID);
    });

    it('should handle error when creating shortcut', async () => {
      const error = new Error('Failed to create shortcut');
      vi.mocked(ArticleAPI.createShortcut).mockRejectedValue(error);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      let shortcutPromiseError: any;
      await act(async () => {
        try {
          await result.current.createShortcut(TEST_IDS.ARTICLE_ID, TEST_IDS.BASE_ID);
        } catch (e) {
          shortcutPromiseError = e;
        }
      });

      expect(shortcutPromiseError).toBeDefined();
      expect(shortcutPromiseError.message).toBe('Failed to create shortcut');
    });
  });

  describe('articleVote', () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    it('should update article vote successfully', async () => {
      const voteType = VoteType.LIKE;

      // Create a proper Axios response
      const axiosResponse: AxiosResponse = {
        data: { id: 'vote-123', type: voteType },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: mockAxiosConfig,
      };

      // Use the imported VoteAPI
      vi.mocked(VoteAPI.updateArticleVote).mockResolvedValue(axiosResponse);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      await act(async () => {
        await result.current.updateArticleVote(TEST_IDS.ARTICLE_ID, voteType);
      });

      expect(VoteAPI.updateArticleVote).toHaveBeenCalledWith(TEST_IDS.ARTICLE_ID, voteType);
    });

    it('should handle error when updating article vote', async () => {
      const voteType = VoteType.LIKE;
      const error = new Error('Failed to update vote');

      // Use the imported VoteAPI
      vi.mocked(VoteAPI.updateArticleVote).mockRejectedValue(error);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      let votePromiseError: any;
      await act(async () => {
        try {
          await result.current.updateArticleVote(TEST_IDS.ARTICLE_ID, voteType);
        } catch (e) {
          votePromiseError = e;
        }
      });

      expect(votePromiseError).toBeDefined();
      expect(votePromiseError.message).toBe('Failed to update vote');
    });

    it('should fetch article vote correctly with right parameters', async () => {
      // Setup mock for getArticleVote
      const mockVote = { id: 'vote-123', type: 'up' };

      vi.mocked(VoteAPI.getArticleVote).mockResolvedValue({
        status: 'success',
        data: mockVote,
      } as any);

      // Directly call the API with the parameters we expect to be used
      await VoteAPI.getArticleVote(TEST_IDS.ARTICLE_ID);

      // Verify call was made correctly
      expect(VoteAPI.getArticleVote).toHaveBeenCalledWith(TEST_IDS.ARTICLE_ID);
    });

    it('should not fetch article vote when isArticleVoting is true', async () => {
      // Get the hook
      const { result, rerender } = renderHook(() => useArticleDetailContext(), { wrapper });

      // Set currentArticle and isArticleVoting to trigger the effect
      await act(async () => {
        // Force setting internal state for testing
        // Cast to any to avoid the property access errors
        (result.current as any).currentArticle = COMPLETE_MOCK_ARTICLE;
        (result.current as any).isArticleVoting = true;

        // Trigger re-render to run the effect
        rerender();

        // Wait for promises to resolve
        await new Promise((resolve) => setTimeout(resolve, 0));
      });

      expect(VoteAPI.getArticleVote).not.toHaveBeenCalled();
    });
  });

  describe('uploadFileInArticle and uploadFileWhenCreatingArticle', () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    it('should upload file in article successfully', async () => {
      const testFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const uploadResponse = {
        status: '200',
        data: {
          url: 'https://example.com/test.jpg',
          imageUrl: 'https://example.com/test.jpg',
        },
      };

      vi.mocked(ArticleAPI.uploadFile).mockResolvedValue(uploadResponse);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      await act(async () => {
        const response = await result.current.uploadFileInArticle(
          TEST_IDS.BASE_ID,
          TEST_IDS.ARTICLE_ID,
          testFile
        );
        expect(response).toEqual(uploadResponse.data);
      });

      expect(ArticleAPI.uploadFile).toHaveBeenCalledWith(
        TEST_IDS.BASE_ID,
        TEST_IDS.ARTICLE_ID,
        testFile
      );
    });

    it('should upload file when creating article successfully', async () => {
      const testFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const uploadResponse: AxiosResponse = {
        data: {
          url: 'https://example.com/test.jpg',
          imageUrl: 'https://example.com/test.jpg',
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: mockAxiosConfig,
      };

      vi.mocked(ArticleAPI.uploadFileWhenCreating).mockResolvedValue(uploadResponse);

      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      await act(async () => {
        const response = await result.current.uploadFileWhenCreatingArticle(
          TEST_IDS.BASE_ID,
          testFile
        );
        expect(response).toEqual(uploadResponse.data);
      });

      expect(ArticleAPI.uploadFileWhenCreating).toHaveBeenCalledWith(TEST_IDS.BASE_ID, testFile);
    });
  });

  describe('articleViewer', () => {
    it('should provide article viewer functionality', () => {
      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      expect(result.current.articleViewer).toBeDefined();
      expect(result.current.articleViewer.scrollToTopOfArticle).toBeDefined();
      expect(result.current.articleViewer.topOfArticleRef).toBeDefined();
      expect(result.current.articleViewer.articleViewerScrollableRef).toBeDefined();
    });
  });

  describe('articleContentRef and articleContentRect', () => {
    it('should provide article content ref and rect', () => {
      const { result } = renderHook(() => useArticleDetailContext(), { wrapper });

      expect(result.current.articleContentRef).toBeDefined();
      expect(result.current.articleContentRect).toBeDefined();
    });
  });
});
