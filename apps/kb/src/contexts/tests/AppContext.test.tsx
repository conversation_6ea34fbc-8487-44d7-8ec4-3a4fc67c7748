import { AllTheProviders } from '@/utils/unitTest';
import { act, renderHook } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { AppContextProvider, type AppContextType, useAppContext } from '../AppContext';

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  useTolgee: () => ({}),
  useTolgeeContext: () => ({}),
}));

// Mock constant for CONFIRM_MODAL_ALIGNMENT that would normally come from @/constants/modal
const CONFIRM_MODAL_ALIGNMENT = {
  CENTER: 'center',
  RIGHT: 'right',
};

// Mock the modal components
vi.mock('@/components/common/ConfirmModal', () => ({
  CONFIRM_MODAL_ALIGNMENT: {
    CENTER: 'center',
    RIGHT: 'right',
  },
  ConfirmModal: () => null,
}));

vi.mock('@/components/common/NoticeModal', () => ({
  default: () => null,
}));

// Test constants
const TEST_MODAL_TITLE = 'Test Modal Title';
const TEST_MODAL_CONTENT = 'Test Modal Content';
const TEST_CONFIRM_TEXT = 'Confirm';
const TEST_CANCEL_TEXT = 'Cancel';

describe('AppContext', () => {
  let result: { current: AppContextType };
  const mockOnConfirm = vi.fn();
  const mockOnCancel = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    const hook = renderHook(() => useAppContext(), {
      wrapper: ({ children }) => (
        <AllTheProviders>
          <AppContextProvider>{children}</AppContextProvider>
        </AllTheProviders>
      ),
    });
    result = hook.result;
  });

  afterEach(() => {
    vi.resetAllMocks();
    vi.useRealTimers();
  });

  describe('Confirm Modal', () => {
    it('should initialize with default values', () => {
      expect(result.current.confirmModal).toEqual({
        alignment: CONFIRM_MODAL_ALIGNMENT.CENTER,
        opened: false,
        title: 'confirmModalTitleDefault',
        content: '',
        confirmText: '',
        cancelText: '',
        onConfirm: expect.any(Function),
        onCancel: expect.any(Function),
        options: {},
      });
    });

    it('should open confirm modal with provided values', () => {
      act(() => {
        result.current.openConfirmModal({
          title: TEST_MODAL_TITLE,
          content: TEST_MODAL_CONTENT,
          confirmText: TEST_CONFIRM_TEXT,
          cancelText: TEST_CANCEL_TEXT,
          onConfirm: mockOnConfirm,
          onCancel: mockOnCancel,
        });
      });

      expect(result.current.confirmModal).toEqual({
        alignment: CONFIRM_MODAL_ALIGNMENT.CENTER,
        opened: true,
        title: TEST_MODAL_TITLE,
        content: TEST_MODAL_CONTENT,
        confirmText: TEST_CONFIRM_TEXT,
        cancelText: TEST_CANCEL_TEXT,
        onConfirm: mockOnConfirm,
        onCancel: expect.any(Function),
        options: {},
      });
    });

    it('should use default title when not provided', () => {
      act(() => {
        result.current.openConfirmModal({
          content: TEST_MODAL_CONTENT,
          onConfirm: mockOnConfirm,
        });
      });

      expect(result.current.confirmModal.title).toBe('confirmModalTitleDefault');
    });

    it('should close confirm modal', () => {
      vi.useFakeTimers();

      // First open the modal
      act(() => {
        result.current.openConfirmModal({
          title: TEST_MODAL_TITLE,
          onConfirm: mockOnConfirm,
        });
      });
      expect(result.current.confirmModal.opened).toBe(true);

      // Then close it
      act(() => {
        result.current.closeConfirmModal();
      });
      expect(result.current.confirmModal.opened).toBe(false);

      // Fast-forward time to complete the timeout
      act(() => {
        vi.advanceTimersByTime(200);
      });

      // Check that modal state is reset
      expect(result.current.confirmModal).toEqual({
        alignment: CONFIRM_MODAL_ALIGNMENT.CENTER,
        opened: false,
        title: 'confirmModalTitleDefault',
        content: '',
        confirmText: '',
        cancelText: '',
        onConfirm: expect.any(Function),
        onCancel: expect.any(Function),
        options: {},
      });
    });

    it('should call onCancel when cancel button is clicked', () => {
      act(() => {
        result.current.openConfirmModal({
          onCancel: mockOnCancel,
          onConfirm: mockOnConfirm,
        });
      });

      act(() => {
        result.current.confirmModal.onCancel();
      });

      expect(mockOnCancel).toHaveBeenCalledTimes(1);
    });
  });

  describe('Notice Modal', () => {
    it('should initialize with default values', () => {
      expect(result.current.noticeModal).toEqual({
        opened: false,
        title: '',
        content: '',
        cancelText: '',
        showLoader: false,
        onCancel: expect.any(Function),
      });
    });

    it('should open notice modal with provided values', () => {
      act(() => {
        result.current.openNoticeModal({
          title: TEST_MODAL_TITLE,
          content: TEST_MODAL_CONTENT,
          cancelText: TEST_CANCEL_TEXT,
          showLoader: true,
          onCancel: mockOnCancel,
        });
      });

      expect(result.current.noticeModal).toEqual({
        opened: true,
        title: TEST_MODAL_TITLE,
        content: TEST_MODAL_CONTENT,
        cancelText: TEST_CANCEL_TEXT,
        showLoader: true,
        onCancel: mockOnCancel,
      });
    });

    it('should use default values when not provided', () => {
      act(() => {
        result.current.openNoticeModal({});
      });

      expect(result.current.noticeModal).toEqual({
        opened: true,
        title: '',
        content: '',
        cancelText: undefined,
        showLoader: undefined,
        onCancel: expect.any(Function),
      });
    });

    it('should use closeNoticeModal as default onCancel', () => {
      act(() => {
        result.current.openNoticeModal({
          title: TEST_MODAL_TITLE,
        });
      });

      expect(result.current.noticeModal.opened).toBe(true);

      act(() => {
        result.current.noticeModal.onCancel();
      });

      expect(result.current.noticeModal.opened).toBe(false);
    });

    it('should close notice modal', () => {
      // First open the modal
      act(() => {
        result.current.openNoticeModal({
          title: TEST_MODAL_TITLE,
        });
      });
      expect(result.current.noticeModal.opened).toBe(true);

      // Then close it
      act(() => {
        result.current.closeNoticeModal();
      });

      // Check that modal state is reset
      expect(result.current.noticeModal).toEqual({
        opened: false,
        title: '',
        content: '',
        cancelText: '',
        showLoader: false,
        onCancel: expect.any(Function),
      });
    });
  });
});
