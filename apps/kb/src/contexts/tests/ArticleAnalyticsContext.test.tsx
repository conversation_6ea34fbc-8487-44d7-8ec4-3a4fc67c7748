import { ArticleDetailLayout } from '@/types/article';
import { act, renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock dependencies
vi.mock('@/services/api/v2', () => ({
  ArticleAPI: {
    getAnalytics: vi.fn(),
  },
}));

vi.mock('@/utils/article', () => ({
  summarizeAnalyticsData: vi.fn((data) => ({
    totalViewCount: data.length,
    totalFeedbackCount: data.length * 2,
    totalLikeCount: data.length,
    totalDislikeCount: data.length,
    products: {},
  })),
}));

// Import after all vi.mock calls
import {
  ArticleAnalyticsProvider,
  useArticleAnalyticsContext,
} from '@/contexts/ArticleAnalyticsContext';
import { ArticleAPI } from '@/services/api/v2';
import { summarizeAnalyticsData } from '@/utils/article';

// Test constants
const TEST_VALUES = {
  ARTICLE_ID: 'article-123',
  LAYOUT: ArticleDetailLayout.HORIZONTAL,
  ANALYTICS_DATA: [
    { id: '1', views: 10, likes: 5, dislikes: 2 },
    { id: '2', views: 20, likes: 10, dislikes: 3 },
  ],
  DATE_RANGE: {
    from: '2023-01-01',
    to: '2023-01-31',
  },
};

describe('ArticleAnalyticsContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  // Wrapper component for the provider
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <ArticleAnalyticsProvider
      articleId={TEST_VALUES.ARTICLE_ID}
      layout={TEST_VALUES.LAYOUT}
      withOriginalArticle={false}
    >
      {children}
    </ArticleAnalyticsProvider>
  );

  describe('Provider initialization', () => {
    it('should initialize with correct default values', () => {
      const { result } = renderHook(() => useArticleAnalyticsContext(), { wrapper });

      expect(result.current.articleAnalyticsState.articleId).toBe(TEST_VALUES.ARTICLE_ID);
      expect(result.current.articleAnalyticsState.layout).toBe(TEST_VALUES.LAYOUT);
      expect(result.current.articleAnalyticsState.withOriginalArticle).toBe(false);
      expect(result.current.articleAnalyticsState.loading).toBe(false);
      expect(result.current.articleAnalyticsState.data).toEqual([]);
    });
  });

  describe('setLayout', () => {
    it('should update the layout', () => {
      const { result } = renderHook(() => useArticleAnalyticsContext(), { wrapper });

      act(() => {
        result.current.setLayout(ArticleDetailLayout.VERTICAL);
      });

      expect(result.current.articleAnalyticsState.layout).toBe(ArticleDetailLayout.VERTICAL);
    });
  });

  describe('setArticleId', () => {
    it('should update the article ID', () => {
      const { result } = renderHook(() => useArticleAnalyticsContext(), { wrapper });
      const newArticleId = 'new-article-456';

      act(() => {
        result.current.setArticleId(newArticleId);
      });

      expect(result.current.articleAnalyticsState.articleId).toBe(newArticleId);
    });
  });

  describe('setWithOriginalArticle', () => {
    it('should update the withOriginalArticle flag', () => {
      const { result } = renderHook(() => useArticleAnalyticsContext(), { wrapper });

      act(() => {
        result.current.setWithOriginalArticle(true);
      });

      expect(result.current.articleAnalyticsState.withOriginalArticle).toBe(true);
    });
  });

  describe('getAnalytics', () => {
    it('should fetch analytics data successfully', async () => {
      // Mock successful API response
      (ArticleAPI.getAnalytics as any).mockResolvedValue({
        status: 'success',
        data: TEST_VALUES.ANALYTICS_DATA,
      });

      const { result } = renderHook(() => useArticleAnalyticsContext(), { wrapper });

      await act(async () => {
        await result.current.getAnalytics(TEST_VALUES.ARTICLE_ID);
      });

      expect(ArticleAPI.getAnalytics).toHaveBeenCalledWith(
        TEST_VALUES.ARTICLE_ID,
        undefined,
        false
      );
      expect(summarizeAnalyticsData).toHaveBeenCalledWith(TEST_VALUES.ANALYTICS_DATA);
      expect(result.current.articleAnalyticsState.data).toEqual(TEST_VALUES.ANALYTICS_DATA);
      expect(result.current.articleAnalyticsState.loading).toBe(false);
    });

    it('should handle date range parameters', async () => {
      (ArticleAPI.getAnalytics as any).mockResolvedValue({
        status: 'success',
        data: TEST_VALUES.ANALYTICS_DATA,
      });

      const { result } = renderHook(() => useArticleAnalyticsContext(), { wrapper });

      await act(async () => {
        await result.current.getAnalytics(TEST_VALUES.ARTICLE_ID, TEST_VALUES.DATE_RANGE);
      });

      expect(ArticleAPI.getAnalytics).toHaveBeenCalledWith(
        TEST_VALUES.ARTICLE_ID,
        TEST_VALUES.DATE_RANGE,
        false
      );
    });

    it('should handle withOriginalArticle parameter', async () => {
      (ArticleAPI.getAnalytics as any).mockResolvedValue({
        status: 'success',
        data: TEST_VALUES.ANALYTICS_DATA,
      });

      const { result } = renderHook(() => useArticleAnalyticsContext(), { wrapper });

      await act(async () => {
        await result.current.getAnalytics(TEST_VALUES.ARTICLE_ID, undefined, true);
      });

      expect(ArticleAPI.getAnalytics).toHaveBeenCalledWith(TEST_VALUES.ARTICLE_ID, undefined, true);
    });

    it('should handle API failure', async () => {
      (ArticleAPI.getAnalytics as any).mockResolvedValue({
        status: 'error',
        message: 'Failed to fetch analytics',
      });

      const { result } = renderHook(() => useArticleAnalyticsContext(), { wrapper });

      let response: any;
      await act(async () => {
        response = await result.current.getAnalytics(TEST_VALUES.ARTICLE_ID);
      });

      expect(response).toEqual({
        status: 'error',
        message: 'Failed to fetch analytics',
      });
      expect(result.current.articleAnalyticsState.loading).toBe(false);
      expect(result.current.articleAnalyticsState.data).toEqual([]);
    });

    it('should set loading state during API call', async () => {
      // Mock a delayed API response to test loading state
      (ArticleAPI.getAnalytics as any).mockImplementation(() => {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              status: 'success',
              data: TEST_VALUES.ANALYTICS_DATA,
            });
          }, 100);
        });
      });

      const { result } = renderHook(() => useArticleAnalyticsContext(), { wrapper });

      let promise: Promise<any>;
      act(() => {
        promise = result.current.getAnalytics(TEST_VALUES.ARTICLE_ID);
      });

      // Check loading state is true during the API call
      expect(result.current.articleAnalyticsState.loading).toBe(true);

      // Wait for the API call to complete
      await act(async () => {
        await promise;
      });

      expect(result.current.articleAnalyticsState.loading).toBe(false);
    });
  });

  describe('useArticleAnalyticsContext hook', () => {
    it('should throw error when used outside provider', () => {
      // Suppress console.error for this test
      const originalConsoleError = console.error;
      console.error = vi.fn();

      expect(() => {
        renderHook(() => useArticleAnalyticsContext());
      }).toThrow('useArticleAnalyticsContext must be used within an ArticleAnalyticsProvider');

      // Restore console.error
      console.error = originalConsoleError;
    });
  });
});
