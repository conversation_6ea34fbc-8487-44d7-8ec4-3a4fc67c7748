import { ArticleAPI } from '@/services/api/v2';
import { KBDirectionQueryEnum, type KnowledgeBaseType } from '@/types';
import { act, renderHook, waitFor } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { ArticleContextProvider, useArticleContext } from '../ArticleContext';

// Mock dependencies
vi.mock('react-router-dom', () => ({
  useSearchParams: vi.fn(),
}));

vi.mock('@/services/api/v2', () => ({
  ArticleAPI: {
    getCollection: vi.fn(),
    create: vi.fn(),
    delete: vi.fn(),
    import: vi.fn(),
  },
}));

vi.mock('@/hooks/useArticleDeleteConfirmation', () => ({
  useArticleDeleteConfirmation: vi.fn(),
}));

import { useArticleDeleteConfirmation } from '@/hooks/useArticleDeleteConfirmation';
// Import mocked modules
import { useSearchParams } from 'react-router-dom';

// Constants for test data
const TEST_KB = {
  id: 'kb-123',
  name: 'Test Knowledge Base',
  description: 'Test KB Description',
  baseType: 'kb' as KnowledgeBaseType,
  type: 'kb' as KnowledgeBaseType,
};

const TEST_ARTICLE = {
  id: 'article-123',
  title: 'Test Article',
  content: 'Test Content',
  contentRaw: 'Test Content Raw',
  baseId: 'kb-123',
  status: 'published',
  updatedAt: new Date('2023-01-01T00:00:00Z'),
  createdAt: new Date('2023-01-01T00:00:00Z'),
  createdBy: {
    id: 'user-1',
    orgId: 'org-1',
    createdAt: '2023-01-01T00:00:00Z',
    displayName: 'Test User',
    email: '<EMAIL>',
    familyName: 'User',
    givenName: 'Test',
    picture: 'https://example.com/avatar.png',
    updatedAt: '2023-01-01T00:00:00Z',
  },
  keywords: [],
  relatedArticles: [],
  isShortcut: false,
  customData: [],
  base: {
    id: 'kb-123',
    name: 'Test Knowledge Base',
    description: 'Test KB Description',
    baseType: 'kb' as KnowledgeBaseType,
    type: 'kb' as KnowledgeBaseType,
  },
};

const TEST_ARTICLES = [
  TEST_ARTICLE,
  {
    id: 'article-456',
    title: 'Another Article',
    content: 'More Content',
    contentRaw: 'More Content Raw',
    baseId: 'kb-123',
    status: 'published',
    updatedAt: new Date('2023-01-02T00:00:00Z'),
    createdAt: new Date('2023-01-02T00:00:00Z'),
    createdBy: {
      id: 'user-1',
      orgId: 'org-1',
      createdAt: '2023-01-01T00:00:00Z',
      displayName: 'Test User',
      email: '<EMAIL>',
      familyName: 'User',
      givenName: 'Test',
      picture: 'https://example.com/avatar.png',
      updatedAt: '2023-01-01T00:00:00Z',
    },
    keywords: [],
    relatedArticles: [],
    isShortcut: false,
    customData: [],
    base: {
      id: 'kb-123',
      name: 'Test Knowledge Base',
      description: 'Test KB Description',
      baseType: 'kb' as KnowledgeBaseType,
      type: 'kb' as KnowledgeBaseType,
    },
  },
];

const TEST_COLLECTION_RESPONSE = {
  data: TEST_ARTICLES,
  pagination: {
    total: 2,
    limit: 10,
    has_more: false,
    cursor: null,
  },
};

const TEST_FILE = new File(['test content'], 'test.csv', { type: 'text/csv' });

describe('ArticleContext', () => {
  // Setup mock functions
  const mockSetSearchParams = vi.fn();
  const mockGet = vi.fn();
  const mockDelete = vi.fn();
  const mockSet = vi.fn();
  const mockSearchParams = {
    get: mockGet,
    delete: mockDelete,
    set: mockSet,
  };
  const mockConfirmThenDeleteArticle = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mock implementations
    mockGet.mockImplementation((key) => {
      if (key === 'limit') return '10';
      if (key === 'articleId') return '';
      return null;
    });

    // Setup search params mock
    (useSearchParams as any).mockReturnValue([mockSearchParams, mockSetSearchParams]);

    // Setup article delete confirmation mock
    (useArticleDeleteConfirmation as any).mockReturnValue({
      confirmThenDeleteArticle: mockConfirmThenDeleteArticle.mockImplementation(
        (_article, callback, loading) => {
          loading?.(true);
          callback?.();
          loading?.(false);
        }
      ),
    });

    // Setup default API mock implementations using vi.fn()
    (ArticleAPI.getCollection as any) = vi.fn().mockResolvedValue(TEST_COLLECTION_RESPONSE);
    (ArticleAPI.create as any) = vi.fn().mockResolvedValue(TEST_ARTICLE);
    (ArticleAPI.delete as any) = vi.fn().mockResolvedValue({ success: true });
    (ArticleAPI.import as any) = vi.fn().mockResolvedValue({ success: true });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  // Create a wrapper for the provider
  const wrapper = ({ children }) => <ArticleContextProvider>{children}</ArticleContextProvider>;

  describe('Initial state', () => {
    it('should initialize with default values', () => {
      const { result } = renderHook(() => useArticleContext(), { wrapper });

      expect(result.current.state).toEqual({
        isLoadingArticles: false,
        isCreating: false,
        shouldCloseCreateNewArticleDrawer: undefined,
        currentKb: null,
        pageLimit: 10,
        activeArticleId: '',
        searchKeyword: '',
        capturedPayload: {
          cursor: '',
          direction: KBDirectionQueryEnum.Backward,
        },
      });
      expect(result.current.articleCollection).toEqual({
        data: undefined,
        pagination: undefined,
      });
    });
  });

  describe('Actions', () => {
    it('should set current KB', () => {
      const { result } = renderHook(() => useArticleContext(), { wrapper });

      act(() => {
        result.current.actions.setCurrentKb(TEST_KB);
      });

      expect(result.current.state.currentKb).toEqual(TEST_KB);
    });

    it('should set active article ID', () => {
      const { result } = renderHook(() => useArticleContext(), { wrapper });

      act(() => {
        result.current.actions.setActiveArticleId('article-123');
      });

      expect(result.current.state.activeArticleId).toBe('article-123');
      expect(mockSetSearchParams).toHaveBeenCalled();
    });

    it('should set search keyword', () => {
      const { result } = renderHook(() => useArticleContext(), { wrapper });

      act(() => {
        result.current.actions.setSearchKeyword('test query');
      });

      expect(result.current.state.searchKeyword).toBe('test query');
      expect(mockSetSearchParams).toHaveBeenCalled();
    });

    it('should set page limit', () => {
      const { result } = renderHook(() => useArticleContext(), { wrapper });

      act(() => {
        result.current.actions.setPageLimit(20);
      });

      expect(result.current.state.pageLimit).toBe(20);
      expect(mockSetSearchParams).toHaveBeenCalled();
    });

    it('should handle create article drawer state', () => {
      const { result } = renderHook(() => useArticleContext(), { wrapper });

      act(() => {
        result.current.actions.closeCreateNewArticleDrawer();
      });

      expect(result.current.state.shouldCloseCreateNewArticleDrawer).toBe(true);

      act(() => {
        result.current.actions.resetShouldCloseCreateNewArticleDrawer();
      });

      expect(result.current.state.shouldCloseCreateNewArticleDrawer).toBe(false);
    });
  });

  describe('Services', () => {
    it('should get article collection', async () => {
      const { result } = renderHook(() => useArticleContext(), { wrapper });

      act(() => {
        result.current.actions.setCurrentKb(TEST_KB);
      });

      await act(async () => {
        await result.current.services.getCollection(TEST_KB.id);
      });

      // Just verify that ArticleAPI.getCollection was called with the kb ID
      // and don't worry about the other parameters
      expect(ArticleAPI.getCollection).toHaveBeenCalledWith(
        TEST_KB.id,
        expect.anything(),
        expect.anything(),
        expect.anything(),
        expect.anything()
      );

      await waitFor(() => {
        expect(result.current.articleCollection).toEqual({
          data: TEST_ARTICLES,
          pagination: TEST_COLLECTION_RESPONSE.pagination,
        });
      });
    });

    it('should create an article', async () => {
      const { result } = renderHook(() => useArticleContext(), { wrapper });
      const newArticleData = {
        title: 'New Article',
        content: 'New Content',
      };

      act(() => {
        result.current.actions.setCurrentKb(TEST_KB);
      });

      await act(async () => {
        await result.current.services.createArticle(TEST_KB.id, newArticleData);
      });

      expect(ArticleAPI.create).toHaveBeenCalledWith(TEST_KB.id, newArticleData);
      expect(result.current.state.isCreating).toBe(false);
    });

    it('should delete an article', async () => {
      const { result } = renderHook(() => useArticleContext(), { wrapper });

      act(() => {
        result.current.actions.setCurrentKb(TEST_KB);
      });

      await act(async () => {
        await result.current.services.deleteArticle(TEST_ARTICLE);
      });

      expect(mockConfirmThenDeleteArticle).toHaveBeenCalled();
    });

    it('should import articles from CSV', async () => {
      const { result } = renderHook(() => useArticleContext(), { wrapper });

      act(() => {
        result.current.actions.setCurrentKb(TEST_KB);
      });

      await act(async () => {
        await result.current.services.importArticleFromCSV(TEST_KB.id, TEST_FILE);
      });

      expect(ArticleAPI.import).toHaveBeenCalledWith(TEST_KB.id, TEST_FILE);
    });
  });

  describe('Error handling', () => {
    it('should handle API errors when getting collection', async () => {
      // Setup spy to track if the API was called
      const getCollectionSpy = vi.fn();
      (ArticleAPI.getCollection as any) = vi.fn().mockImplementationOnce(() => {
        getCollectionSpy();
        return Promise.reject(new Error('API Error'));
      });

      const { result } = renderHook(() => useArticleContext(), { wrapper });

      act(() => {
        result.current.actions.setCurrentKb(TEST_KB);
      });

      // Use try/catch to handle the expected error
      try {
        await act(async () => {
          await result.current.services.getCollection(TEST_KB.id);
        });
      } catch (error) {
        // Expect the error to be caught by the context implementation
      }

      // Verify the API was called
      expect(getCollectionSpy).toHaveBeenCalled();
      expect(result.current.state.isLoadingArticles).toBe(false);
      expect(result.current.articleCollection).toEqual({
        data: undefined,
        pagination: undefined,
      });
    });

    it('should handle API errors when creating article', async () => {
      // Setup spy to track if the API was called
      const createSpy = vi.fn();
      (ArticleAPI.create as any) = vi.fn().mockImplementationOnce(() => {
        createSpy();
        return Promise.reject(new Error('API Error'));
      });

      const { result } = renderHook(() => useArticleContext(), { wrapper });
      const newArticleData = {
        title: 'New Article',
        content: 'New Content',
      };

      act(() => {
        result.current.actions.setCurrentKb(TEST_KB);
      });

      try {
        await act(async () => {
          await result.current.services.createArticle(TEST_KB.id, newArticleData);
        });
      } catch (error) {
        // Expect the error to be caught or propagated by the context implementation
      }

      // Verify the API was called
      expect(createSpy).toHaveBeenCalled();
      expect(result.current.state.isCreating).toBe(false);
    });
  });

  describe('URL search params integration', () => {
    it('should use search params for initial state', () => {
      // Update mock implementation for this test
      mockGet.mockImplementation((key) => {
        if (key === 'limit') return '20';
        if (key === 'articleId') return 'article-456';
        if (key === 'query') return 'search term';
        return null;
      });

      const { result } = renderHook(() => useArticleContext(), { wrapper });

      expect(result.current.state.pageLimit).toBe(20);
      expect(result.current.state.activeArticleId).toBe('article-456');
      expect(result.current.state.searchKeyword).toBe('');
    });

    it('should clear search query on unmount', () => {
      // Update mock for this test
      mockGet.mockImplementation((key) => {
        if (key === 'query') return 'search term';
        return null;
      });

      const { unmount } = renderHook(() => useArticleContext(), { wrapper });

      unmount();

      expect(mockSetSearchParams).toHaveBeenCalled();
    });
  });
});
