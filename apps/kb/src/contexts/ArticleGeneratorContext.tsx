import {
  DEFAULT_ARTICLE_GENERATOR_PROMPT_CONTENT,
  DEFAULT_ARTICLE_GENERATOR_PROMPT_TITLE,
  PROMPT_LIST_MAX_LENGTH,
} from '@/constants/job';
import { JobAPI } from '@/services/api/v2';
import { CustomPromptAPI } from '@/services/api/v2/customPrompt';
import type { GenerateJobPayload, Job, Prompt, PromptPayload } from '@/types';
import { sortByDate } from '@/utils/dateTime';
import { createContext, useCallback, useContext, useEffect, useState } from 'react';

interface ArticleGeneratorContextValue {
  isGenerating: boolean;
  customPrompt: Prompt;
  prompts: Prompt[];
  setCustomPrompt: (prompt: Prompt) => void;
  generateArticle: (payload: GenerateJobPayload) => Promise<Job | undefined>;
  fetchPrompts: () => Promise<void>;
  deletePrompt: (promptId: string) => Promise<void>;
  updatePrompt: (prompt: Prompt) => Promise<void>;
  createPrompt: (prompt: PromptPayload) => Promise<void>;
}

const ArticleGeneratorContext = createContext<ArticleGeneratorContextValue | undefined>(undefined);

export const useArticleGeneratorContext = () => {
  const context = useContext(ArticleGeneratorContext);
  if (!context) {
    throw new Error(
      'useArticleGeneratorContext must be used within ArticleGeneratorContextProvider'
    );
  }
  return context;
};

interface ArticleGeneratorProviderProps {
  children: React.ReactNode;
}

export const ArticleGeneratorContextProvider: React.FC<ArticleGeneratorProviderProps> = ({
  children,
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [customPrompt, setCustomPrompt] = useState<Prompt>({
    id: '',
    title: DEFAULT_ARTICLE_GENERATOR_PROMPT_TITLE,
    content: DEFAULT_ARTICLE_GENERATOR_PROMPT_CONTENT,
  });
  const [prompts, setPrompts] = useState<Prompt[]>([]);

  const generateArticle = useCallback(async (payload: GenerateJobPayload) => {
    setIsGenerating(true);

    try {
      return await JobAPI.createJob(payload);
    } catch (error) {
      console.error('Failed to generate article:', error);
      throw error;
    } finally {
      setIsGenerating(false);
    }
  }, []);

  const deletePrompt = useCallback(
    async (promptId: string) => {
      try {
        await CustomPromptAPI.deleteCustomPrompt(promptId);
        fetchPrompts();
      } catch (error) {
        console.error('Failed to delete prompt:', error);
      }
    },
    [customPrompt.id]
  );

  const fetchPrompts = useCallback(async () => {
    const response = await CustomPromptAPI.getCustomPrompts();
    const sortedPrompts = sortByDate(response?.data ?? [], 'updatedAt');
    setPrompts(sortedPrompts);
    if (sortedPrompts.length) {
      setCustomPrompt(sortedPrompts[0]);
    } else {
      setCustomPrompt({
        id: '',
        title: DEFAULT_ARTICLE_GENERATOR_PROMPT_TITLE,
        content: DEFAULT_ARTICLE_GENERATOR_PROMPT_CONTENT,
      });
    }
  }, []);

  const updatePrompt = useCallback(async (prompt: Prompt) => {
    await CustomPromptAPI.updateCustomPrompt(prompt);
    fetchPrompts();
  }, []);

  const createPrompt = useCallback(
    async (prompt: PromptPayload) => {
      if (prompts.length === PROMPT_LIST_MAX_LENGTH) {
        await CustomPromptAPI.deleteCustomPrompt(prompts[PROMPT_LIST_MAX_LENGTH - 1].id);
      }

      await CustomPromptAPI.createCustomPrompt(prompt);
      fetchPrompts();
    },
    [prompts]
  );

  useEffect(() => {
    fetchPrompts();
  }, [fetchPrompts]);

  const value = {
    isGenerating,
    customPrompt,
    setCustomPrompt,
    generateArticle,
    prompts,
    fetchPrompts,
    deletePrompt,
    updatePrompt,
    createPrompt,
  };

  return (
    <ArticleGeneratorContext.Provider value={value}>{children}</ArticleGeneratorContext.Provider>
  );
};
