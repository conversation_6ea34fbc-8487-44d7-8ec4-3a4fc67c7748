import { useCallback, useState } from 'react';

import { DEFAULT_EXPLORER_RETRIEVE_LIMIT } from '@/constants/explorer';
import { ROOT_PATH } from '@/constants/folder';
import { ExplorerAPI } from '@/services/api/v2/explorer';
import type { Explorer, KnowledgeBaseDirectionQuery } from '@/types';
import { sortByCreatedAt } from '@/utils/dateTime';
import type { IPaginationNextPrevious } from '@resola-ai/models';
import { createContextHook } from '@resola-ai/utils';

interface IPaginationCapture {
  cursor: string;
  direction: KnowledgeBaseDirectionQuery;
  parentDirId: string;
}

const useExplorer = () => {
  const [explorers, setExplorers] = useState<Explorer[]>();
  const [pagination, setPagination] = useState<IPaginationNextPrevious>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [paginationCapture, setPaginationCapture] = useState<IPaginationCapture>({
    cursor: '',
    direction: 'backward',
    parentDirId: '',
  });

  /**
   * Get explorers with parent directory id
   * @param parentDirId
   * @param cursor
   */
  const getExplorers = useCallback(
    async (
      parentDirId: string = ROOT_PATH,
      cursor = '',
      direction: KnowledgeBaseDirectionQuery = 'backward',
      take: number = DEFAULT_EXPLORER_RETRIEVE_LIMIT
    ) => {
      setIsLoading(true);
      const res = await ExplorerAPI.getList(parentDirId, cursor, direction, take);
      if (res) {
        setExplorers(sortByCreatedAt(res.data ?? []));
        setPagination(res.pagination);
        setPaginationCapture({ cursor, direction, parentDirId });
      }

      setIsLoading(false);
    },
    []
  );

  /**
   * Fetch more explorers with direction and cursor
   * @returns void
   */
  const fetchMoreExplorers = useCallback(async () => {
    const { parentDirId, direction } = paginationCapture;
    const cursor = direction === 'backward' ? pagination?.first : pagination?.last;

    if (
      (direction === 'forward' && !pagination?.hasNextPage) ||
      (direction === 'backward' && !pagination?.hasPreviousPage) ||
      !cursor
    )
      return;

    setIsLoading(true);

    const res = await ExplorerAPI.getList(
      parentDirId,
      cursor,
      direction,
      DEFAULT_EXPLORER_RETRIEVE_LIMIT
    );
    if (res) {
      const additionalData = sortByCreatedAt(res.data ?? []);

      setExplorers((prev) => [...(prev ?? []), ...additionalData]);
      setPagination(res.pagination);
      setPaginationCapture({ cursor, direction, parentDirId });
    }

    setIsLoading(false);
  }, [pagination, paginationCapture, getExplorers, setExplorers, setIsLoading]);

  return {
    isLoading,
    setIsLoading,
    explorers,
    pagination,
    getExplorers,
    fetchMoreExplorers,
    setExplorers,
  };
};

export const [ExplorerContextProvider, useExplorerContext] = createContextHook(
  useExplorer,
  'ExplorerContext'
);
