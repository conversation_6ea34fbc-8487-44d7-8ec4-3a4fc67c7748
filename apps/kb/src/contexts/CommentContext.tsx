import { DEFAULT_COMMENTS_RETRIEVE_LIMIT } from '@/constants/api';
import { CommentAPI } from '@/services/api/v2';
import type { Comment } from '@/types';
import type { IPaginationNextPrevious } from '@resola-ai/models';
import { createContext, useContext, useState } from 'react';

const useComment = () => {
  const [isCommentsLoading, setIsCommentsLoading] = useState<boolean>(false);
  const [isCommentSending, setIsCommentSending] = useState<boolean>(false);
  const [selectedComment, setSelectedComment] = useState<Comment | undefined>(undefined);
  const [comments, setComments] = useState<Comment[]>([]);
  const [pagination, setPagination] = useState<IPaginationNextPrevious | undefined>(undefined);

  /**
   * Get Comments
   * @param {string} kbId
   * @param {string} articleId
   */
  const getComments = async (kbId: string, articleId: string, cursor = '') => {
    setIsCommentsLoading(true);

    const commentsResponse = await CommentAPI.getComments(
      kbId,
      articleId,
      DEFAULT_COMMENTS_RETRIEVE_LIMIT,
      'backward',
      cursor
    );

    if (commentsResponse) {
      setComments(commentsResponse?.data ?? []);
      setPagination(commentsResponse?.pagination ?? {});
    }

    setIsCommentsLoading(false);
  };

  /**
   * Load More Comments
   * @param {string} kbId
   * @param {string} articleId
   */
  const loadMoreComments = async (kbId: string, articleId: string) => {
    setIsCommentsLoading(true);

    const commentsResponse = await CommentAPI.getComments(
      kbId,
      articleId,
      DEFAULT_COMMENTS_RETRIEVE_LIMIT,
      'backward',
      pagination?.first ?? ''
    );

    if (commentsResponse) {
      setComments((prev) => [...prev, ...(commentsResponse?.data ?? [])]);
      setPagination(commentsResponse?.pagination ?? {});
    }

    setIsCommentsLoading(false);
  };

  /**
   * Create Comment
   * @param {string} kbId
   * @param {string} articleId
   * @param {string} content
   */
  const createComment = async (kbId: string, articleId: string, content: string) => {
    setIsCommentSending(true);

    await CommentAPI.createComment(kbId, articleId, {
      text: content,
    });
    await getComments(kbId, articleId);

    setIsCommentSending(false);
  };

  /**
   * Update Comment
   * @param {string} kbId
   * @param {string} articleId
   * @param {string} commentId
   * @param {string} content
   */
  const updateComment = async (
    kbId: string,
    articleId: string,
    commentId: string,
    content: string
  ) => {
    setIsCommentSending(true);

    await CommentAPI.updateComment(kbId, articleId, commentId, {
      text: content,
    });
    await getComments(kbId, articleId);

    setIsCommentSending(false);
  };

  /**
   * Delete Comment
   * @param {string} kbId
   * @param {string} articleId
   * @param {string} commentId
   */
  const deleteComment = async (kbId: string, articleId: string, commentId: string) => {
    setIsCommentsLoading(true);

    await CommentAPI.deleteComment(kbId, articleId, commentId);
    await getComments(kbId, articleId);

    setIsCommentsLoading(false);
  };

  return {
    isCommentsLoading,
    isCommentSending,
    selectedComment,
    setSelectedComment,
    comments,
    pagination,
    getComments,
    loadMoreComments,
    createComment,
    updateComment,
    deleteComment,
  };
};

export type CommentContextType = ReturnType<typeof useComment>;
const context = createContext<CommentContextType | null>(null);

export const CommentContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useComment();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useCommentContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useCommentContext must be used within a CommentContextType');
  }

  return value;
};
