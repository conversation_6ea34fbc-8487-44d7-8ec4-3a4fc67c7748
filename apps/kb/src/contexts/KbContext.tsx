import { DEFAULT_EXPLORER_RETRIEVE_LIMIT } from '@/constants/explorer';
import { DEFAULT_DOCUMENT_RETRIEVE_LIMIT } from '@/constants/file';
import { DEFAULT_RETRIEVE_LIMIT } from '@/constants/kb';
import { DEFAULT_QNA_RETRIEVE_LIMIT } from '@/constants/qna';
import { FileAPI, KbAPI, QnaAPI } from '@/services/api/v1';
import { ExplorerAPI } from '@/services/api/v2/explorer';
import type { KnowledgeBase, KnowledgeBaseDirectionQuery } from '@/types';
import type { QnA, QnAGenerateRequest, QnAStatus } from '@/types/qna';
/**
 * This context use for Knowledge Base version 1 only
 * @deprecated Use `useKnowledgeBaseContext` instead
 */
import { createContext, useCallback, useContext, useState } from 'react';

const useKb = () => {
  const [currentKnowledgeBase, setCurrentKnowledgeBase] = useState<KnowledgeBase>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isKbListForceUpdate, setIsKbListForceUpdate] = useState<boolean>(false);

  const saveKnowledgeBase = useCallback(async (kb: KnowledgeBase) => {
    if (kb.id) {
      const res = await KbAPI.update(kb.id, kb);
      setCurrentKnowledgeBase(res);
    } else {
      await KbAPI.create(kb);
    }
    setIsKbListForceUpdate(true);
  }, []);

  const getKnowledgeBase = useCallback(async (id: string) => {
    const res = await KbAPI.get(id);
    setCurrentKnowledgeBase(res?.data);
    return res?.data;
  }, []);

  const getKnowledgeBaseList = useCallback(
    async (
      cursor: string,
      direction: KnowledgeBaseDirectionQuery = 'backward',
      take: number = DEFAULT_RETRIEVE_LIMIT
    ) => {
      const res = await KbAPI.getList(cursor, direction, take);
      setIsKbListForceUpdate(false);
      return res;
    },
    []
  );

  const getExplorerList = useCallback(
    async (
      parentDirId: string,
      cursor: string,
      direction: KnowledgeBaseDirectionQuery = 'backward',
      take: number = DEFAULT_EXPLORER_RETRIEVE_LIMIT
    ) => {
      const res = await ExplorerAPI.getList(parentDirId, cursor, direction, take);
      return res;
    },
    []
  );

  const getQnAList = useCallback(
    async (
      kbId: string,
      direction: KnowledgeBaseDirectionQuery,
      cursor: string,
      take: number = DEFAULT_QNA_RETRIEVE_LIMIT,
      status?: QnAStatus,
      qaGenId?: string
    ) => {
      return await QnaAPI.getList(cursor, direction, take, kbId, status, qaGenId);
    },
    []
  );

  const saveQnA = useCallback(async (kbId: string, qna: QnA) => {
    const res = qna.id ? await QnaAPI.update(kbId, qna.id, qna) : await QnaAPI.create(kbId, qna);
    return res?.data;
  }, []);

  const removeQnA = useCallback(async (kbId: string, qnaId: string) => {
    return await QnaAPI.remove(kbId, qnaId);
  }, []);

  const importQnAFromCSV = useCallback(async (kbId: string, formData: FormData) => {
    return await QnaAPI.import(kbId, formData);
  }, []);

  const getQnAJobStatus = useCallback(async (kbId: string) => {
    const res = await QnaAPI.getJobStatus(kbId);
    return res?.data;
  }, []);

  const generateQnA = useCallback(async (req: QnAGenerateRequest) => {
    return await QnaAPI.generateQnA(req);
  }, []);

  const saveGeneratedQnA = useCallback(
    async (kbId: string, ids: Array<string>, qaGenId: string) => {
      return await QnaAPI.saveGeneratedQnA(kbId, ids, qaGenId);
    },
    []
  );

  const getDocumentList = useCallback(
    async (kbId: string, direction: KnowledgeBaseDirectionQuery, cursor: string) => {
      return await FileAPI.getList(kbId, cursor, direction, DEFAULT_DOCUMENT_RETRIEVE_LIMIT);
    },
    []
  );

  const cancelGenerateQnA = useCallback(async (kbId: string) => {
    return await QnaAPI.cancelGenerate(kbId);
  }, []);

  const getDocumentLink = useCallback(async (kbId: string, fileId: string) => {
    return await FileAPI.getLink(kbId, fileId);
  }, []);

  const removeDocument = useCallback(async (kbId: string, fileId: string) => {
    return await FileAPI.delete(kbId, fileId);
  }, []);

  return {
    isLoading,
    setIsLoading,
    currentKnowledgeBase,
    setCurrentKnowledgeBase,
    isKbListForceUpdate,
    saveKnowledgeBase,
    getKnowledgeBase,
    getKnowledgeBaseList,
    getQnAList,
    saveQnA,
    removeQnA,
    importQnAFromCSV,
    getQnAJobStatus,
    generateQnA,
    cancelGenerateQnA,
    saveGeneratedQnA,
    getDocumentList,
    removeDocument,
    getDocumentLink,
    getExplorerList,
  };
};

export type KbContextType = ReturnType<typeof useKb>;

const context = createContext<KbContextType | null>(null);

export const KbContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useKb();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useKbContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useKbContext must be used within a KbContextProvider');
  }

  return value;
};
