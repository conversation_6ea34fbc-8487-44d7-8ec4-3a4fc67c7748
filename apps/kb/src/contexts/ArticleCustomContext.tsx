import { Template<PERSON><PERSON> } from '@/services/api/v2';
import type { KBTemplate, KBTemplateField, KBTemplateStaticFields } from '@/types/template';
import { createFieldsMapFromList, randomFieldId } from '@/utils/template';
import find from 'lodash/find';
import type React from 'react';
import { createContext, useContext, useState } from 'react';

/**
 * Interface for Templates state
 * @interface IArticleCustomState
 */
interface IArticleCustomState {
  customFields: Record<string, KBTemplateField> | undefined;
  staticFields: KBTemplateStaticFields | undefined;
  defaultTemplate: KBTemplate | undefined;
  defaultTemplateCustomFields: KBTemplateField[] | undefined;
  loading: boolean;
}

/**
 * Initial state for Templates
 */
const initialArticleCustomState: IArticleCustomState = {
  customFields: undefined,
  staticFields: undefined,
  defaultTemplate: undefined,
  defaultTemplateCustomFields: undefined,
  loading: false,
};

/**
 * Custom hook to manage the Templates state and related actions
 * Provides the state and functions to manage templates
 */
const useArticleCustom = () => {
  const [articleCustomState, setArticleCustomState] =
    useState<IArticleCustomState>(initialArticleCustomState);

  /**
   * Fetches the default template for articles
   * @returns {Promise<KBTemplate | undefined>} The default template for articles
   * @dependencies TemplateAPI: TemplateAPI, setArticleCustomState: function
   * @throws {Error} If the API call fails
   */
  const fetchDefaultTemplate = async () => {
    const response = await TemplateAPI.getArticleTemplates({});

    if (response?.status === 'success') {
      const defaultTemplate = find(response.data, { isDefault: true }) ?? response.data[0];
      setArticleCustomState((prev) => ({
        ...prev,
        defaultTemplate,
        staticFields: defaultTemplate.article,
      }));

      return defaultTemplate;
    }

    return undefined;
  };

  /**
   * Fetches the custom fields for the default template
   * @param {string} templateId - The ID of the template to fetch custom fields for
   * @dependencies TemplateAPI: TemplateAPI, setArticleCustomState: function
   * @returns {Promise<void>}
   */
  const fetchDefaultTemplateCustomFields = async (templateId: string) => {
    setArticleCustomState((prev) => ({ ...prev, loading: true }));
    const response = await TemplateAPI.getTemplateCustomFields({ templateId });

    if (response?.status === 'success') {
      setArticleCustomState((prev) => ({
        ...prev,
        defaultTemplateCustomFields: response.data,
        loading: false,
      }));
    } else {
      setArticleCustomState((prev) => ({ ...prev, loading: false }));
    }

    return response;
  };

  /**
   * Sets the custom fields value for the article from Default Templates or Article Custom Data
   * @param {Partial<KBTemplateField>[]} customData - The new custom values to set
   * @dependencies setArticleCustomState: function
   */
  const setInitialCustomFields = (customData: Partial<KBTemplateField>[]) => {
    const normalizedCustomFields = createFieldsMapFromList(
      customData.map((field) => {
        const fieldId = randomFieldId();

        return {
          ...field,
          id: fieldId,
        };
      })
    );

    setArticleCustomState((prev) => ({
      ...prev,
      customFields: normalizedCustomFields,
    }));

    return normalizedCustomFields;
  };

  /**
   * Resets the state to the initial state
   * @dependencies setArticleCustomState
   * @returns void
   */
  const resetState = () => {
    setArticleCustomState(initialArticleCustomState);
  };

  /**
   * Updates a custom field value
   * @param {fieldId} fieldId - The ID of field to update
   * @param {string} value - The new value for the field
   */
  const updateCustomFieldValue = (fieldId: string, value: any) => {
    if (!articleCustomState.customFields) {
      return {};
    }

    const newCustomFields = {
      ...(articleCustomState.customFields || {}),
      [fieldId]: {
        ...articleCustomState.customFields[fieldId],
        value,
      },
    };

    setArticleCustomState((prev) => ({
      ...prev,
      customFields: newCustomFields,
    }));

    return newCustomFields;
  };

  return {
    articleCustomState,
    setInitialCustomFields,
    resetState,
    updateCustomFieldValue,
    fetchDefaultTemplate,
    fetchDefaultTemplateCustomFields,
  };
};

/**
 * Type for the ArticleCustomContext
 * @typedef {ReturnType<typeof useTemplates>} ArticleCustomContextType
 */
type ArticleCustomContextType = ReturnType<typeof useArticleCustom>;

/**
 * Create the ArticleCustomContext with a default value of `null`
 */
const ArticleCustomContext = createContext<ArticleCustomContextType | null>(null);

/**
 * Context Provider component for ArticleCustomContext
 * Wraps children components and provides the templates context value
 * @param {React.ReactNode} children - Children components that will consume the context
 */
export const ArticleCustomContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useArticleCustom();

  return <ArticleCustomContext.Provider value={value}>{children}</ArticleCustomContext.Provider>;
};

/**
 * Custom hook to access the ArticleCustomContext
 * Ensures the hook is only used within a valid ArticleCustomContextProvider
 * @returns {ArticleCustomContextType} The context value (templates state and actions)
 * @throws {Error} If used outside of the ArticleCustomContextProvider
 */
export const useArticleCustomContext = () => {
  const context = useContext(ArticleCustomContext);

  if (!context) {
    throw new Error('useArticleCustomContext must be used within a ArticleCustomContextProvider');
  }

  return context;
};
