import { createContext, useCallback, useContext, useState } from 'react';

import { FolderAPI } from '@/services/api/v2';
import type { Folder } from '@/types/tree';

const useFolder = () => {
  const [shouldRefresh, setShouldRefresh] = useState(false);

  const getFolders = useCallback(async (parentDirId: string, depth = 2) => {
    return await FolderAPI.getAll(parentDirId, depth);
  }, []);

  const createFolder = useCallback(async (folder: Partial<Folder>) => {
    return await FolderAPI.create(folder);
  }, []);

  const updateFolder = useCallback(async (folder: Partial<Folder>) => {
    return await FolderAPI.update(folder);
  }, []);

  const deleteFolder = useCallback(async (folderId: string) => {
    return await FolderAPI.remove(folderId);
  }, []);

  const getFolder = useCallback(async (folderId: string) => {
    return await FolderAPI.get(folderId);
  }, []);

  const getRecentlyViewed = useCallback(async () => {
    return await FolderAPI.recent();
  }, []);

  return {
    getFolder,
    getFolders,
    createFolder,
    deleteFolder,
    updateFolder,
    shouldRefresh,
    setShouldRefresh,
    getRecentlyViewed,
  };
};

export type FolderContextType = ReturnType<typeof useFolder>;
const context = createContext<FolderContextType | null>(null);

export const FolderContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useFolder();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useFolderContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useFolderContext must be used within a FolderContextProvider');
  }

  return value;
};
