import { Template<PERSON>I } from '@/services/api/v2';
import type {
  KBTemplate,
  KBTemplateField,
  KBTemplateFieldCreate,
  KBTemplateFieldsSet,
} from '@/types/template';
import { createFieldsMapFromList } from '@/utils/template';
import type React from 'react';
import { createContext, useContext, useState } from 'react';

interface ITemplateDetailState {
  templateLoading: boolean;
  fieldsLoading: boolean;
  fieldsCreating: boolean;
  fieldsUpdating: boolean;
  fieldsDeleting: boolean;
  currentTemplate: KBTemplate | undefined;
  customFields: KBTemplateFieldsSet;
}

const initialTemplateDetailState: ITemplateDetailState = {
  currentTemplate: undefined,
  templateLoading: false,
  fieldsLoading: false,
  fieldsCreating: false,
  fieldsUpdating: false,
  fieldsDeleting: false,
  customFields: {},
};

/**
 * Custom hook to manage template detail state and API calls
 * Handles fetching of template details and custom fields
 */
const useTemplateDetail = () => {
  const [templateDetail, setTemplateDetail] = useState<ITemplateDetailState>(
    initialTemplateDetailState
  );

  /**
   * Fetches the template details by templateId
   * @param {string} templateId - The ID of the template to fetch details for
   */
  const fetchTemplateDetail = async (templateId: string) => {
    setTemplateDetail((prev) => ({ ...prev, templateLoading: true }));

    const templateResponse = await TemplateAPI.getArticleTemplate(templateId);

    if (templateResponse?.status === 'success') {
      setTemplateDetail((prev) => ({
        ...prev,
        currentTemplate: templateResponse.data,
        templateLoading: false,
      }));

      return templateResponse.data;
    }
    setTemplateDetail((prev) => ({ ...prev, templateLoading: false }));
    return undefined;
  };

  /**
   * Updates the template details for the specified template
   * @param {string} templateId - The ID of the template to update
   * @param {KBTemplate} templateData - The updated data for the template
   * @returns {Promise<void>}
   */
  const updateTemplateDetail = async (templateId: string, templateData: KBTemplate) => {
    setTemplateDetail((prev) => ({ ...prev, templateLoading: true }));

    const templateResponse = await TemplateAPI.updateArticleTemplate(templateId, templateData);

    if (templateResponse?.status === 'success') {
      setTemplateDetail((prev) => ({
        ...prev,
        currentTemplate: templateResponse.data,
        templateLoading: false,
      }));
    } else {
      setTemplateDetail((prev) => ({ ...prev, templateLoading: false }));
    }

    return templateResponse;
  };

  /**
   * Fetches the custom fields for the specified template
   * @param {string} templateId - The ID of the template to fetch custom fields for
   */
  const fetchTemplateCustomFields = async (templateId: string) => {
    setTemplateDetail((prev) => ({ ...prev, fieldsLoading: true }));

    const customFieldsResponse = await TemplateAPI.getTemplateCustomFields({
      templateId,
    });

    if (customFieldsResponse?.status === 'success') {
      // Map the custom fields by their ID
      const customFields = createFieldsMapFromList(customFieldsResponse.data);

      setTemplateDetail((prev) => ({
        ...prev,
        customFields,
        fieldsLoading: false,
      }));
    } else {
      // Handle unsuccessful response
      setTemplateDetail((prev) => ({ ...prev, fieldsLoading: false }));
    }
  };

  /**
   * Creates a new custom field for the specified template
   * @param {string} templateId - The ID of the template to create the custom field for
   * @param {KBTemplateFieldCreate} fieldData - The data for the new custom field
   * @returns {Promise<void>}
   */
  const createCustomField = async (templateId: string, fieldData: KBTemplateFieldCreate) => {
    setTemplateDetail((prev) => ({ ...prev, fieldsCreating: true }));
    const customFieldResponse = await TemplateAPI.createTemplateCustomField(templateId, fieldData);

    if (customFieldResponse?.status === 'success') {
      // Add the new custom field to the existing custom fields
      const newCustomField = customFieldResponse.data?.[0];
      if (!newCustomField) return;

      setTemplateDetail((prev) => ({
        ...prev,
        customFields: {
          ...prev.customFields,
          [newCustomField.id]: newCustomField,
        },
        fieldsCreating: false,
      }));
    } else {
      setTemplateDetail((prev) => ({ ...prev, fieldsCreating: false }));
    }

    return customFieldResponse;
  };

  /**
   * Updates an existing custom field for the specified template
   * @param {string} templateId - The ID of the template to update the custom field for
   * @param {string} fieldId - The ID of the custom field to update
   * @param {KBTemplateField} fieldData - The updated data for the custom field
   * @returns {Promise<void>}
   */
  const updateCustomField = async (
    templateId: string,
    fieldId: string,
    fieldData: KBTemplateField
  ) => {
    setTemplateDetail((prev) => ({ ...prev, fieldsUpdating: true }));
    const customFieldResponse = await TemplateAPI.updateTemplateCustomField(templateId, fieldId, {
      ...fieldData,
      description: fieldData.description || '', // TODO: Cheat code to by pass API issue and remove it after the API is fixed
    });

    if (customFieldResponse?.status === 'success') {
      // Update the existing custom field
      setTemplateDetail((prev) => ({
        ...prev,
        customFields: {
          ...prev.customFields,
          [fieldId]: customFieldResponse.data,
        },
        fieldsUpdating: false,
      }));
    } else {
      setTemplateDetail((prev) => ({ ...prev, fieldsUpdating: false }));
    }

    return customFieldResponse;
  };

  /**
   * Deletes an existing custom field for the specified template
   * @param {string} templateId - The ID of the template to delete the custom field from
   * @param {string} fieldId - The ID of the custom field to delete
   * @returns {Promise<void>}
   */
  const deleteCustomField = async (templateId: string, fieldId: string) => {
    setTemplateDetail((prev) => ({ ...prev, fieldsDeleting: true }));
    const customFieldResponse = await TemplateAPI.deleteTemplateCustomField(templateId, fieldId);
    setTemplateDetail((prev) => ({ ...prev, fieldsDeleting: false }));

    return customFieldResponse;
  };

  /**
   * Sets the value of a custom field
   * @param {string} fieldId - The ID of the custom field to update
   * @param {string} value - The new value for the custom field
   * @returns {void}
   */
  const setCustomFieldValue = (fieldId: string, value: string) => {
    if (!templateDetail.customFields) return;

    const updatedCustomField = { ...templateDetail.customFields[fieldId], value };
    setTemplateDetail((prev) => ({
      ...prev,
      customFields: {
        ...prev.customFields,
        [fieldId]: {
          ...updatedCustomField,
        },
      },
    }));

    return updatedCustomField;
  };

  return {
    templateDetailState: templateDetail,
    fetchTemplateDetail,
    updateTemplateDetail,
    fetchTemplateCustomFields,
    createCustomField,
    updateCustomField,
    deleteCustomField,
    setCustomFieldValue,
  };
};

type TemplateDetailContextType = ReturnType<typeof useTemplateDetail>;

// Create the context with a default null value
export const TemplateDetailContext = createContext<TemplateDetailContextType | null>(null);

/**
 * Context Provider component for TemplateDetailContext
 * Provides the context value (useTemplateDetail) to its children
 */
export const TemplateDetailContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useTemplateDetail();

  return <TemplateDetailContext.Provider value={value}>{children}</TemplateDetailContext.Provider>;
};

/**
 * Custom hook to access TemplateDetailContext
 * Throws an error if used outside of a TemplateDetailContextProvider
 */
export const useTemplateDetailContext = () => {
  const context = useContext(TemplateDetailContext);

  if (!context) {
    throw new Error('useTemplateDetailContext must be used within a TemplateDetailContextProvider');
  }

  return context;
};
