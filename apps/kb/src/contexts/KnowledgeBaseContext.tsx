import { createContext, useCallback, useContext, useState } from 'react';

import { ROOT_PATH } from '@/constants/folder';
import { DEFAULT_KB_RETRIEVE_LIMIT } from '@/constants/kb';
import { KbAPI } from '@/services/api/v2';
import { ExplorerAPI } from '@/services/api/v2/explorer';
import type { KnowledgeBase, KnowledgeBaseDirectionQuery } from '@/types';
import { sortByCreatedAt } from '@/utils/dateTime';
import type { IPaginationNextPrevious } from '@resola-ai/models';

interface IPaginationCapture {
  cursor: string;
  direction: KnowledgeBaseDirectionQuery;
  parentDirId: string;
}

const useKnowledgeBase = () => {
  const [knowledgeBases, setKnowledgeBases] = useState<KnowledgeBase[]>();
  const [pagination, setPagination] = useState<IPaginationNextPrevious>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [paginationCapture, setPaginationCapture] = useState<IPaginationCapture>({
    cursor: '',
    direction: 'backward',
    parentDirId: '',
  });

  /**
   * Save knowledge base
   * @param kb
   * @returns void
   */
  const saveKnowledgeBase = useCallback(async (kb: KnowledgeBase) => {
    if (kb.id) {
      return await KbAPI.update(kb.id, kb);
    }
    return await KbAPI.create(kb);
  }, []);

  /**
   * Get knowledge base by id
   * @param id
   * @returns KnowledgeBase
   */
  const getKnowledgeBase = useCallback(async (id: string) => {
    const res = await KbAPI.get(id);
    return res?.data;
  }, []);

  /**
   * Get knowledge bases with parent directory id
   * @param parentDirId
   * @param cursor
   */
  const getKnowledgeBases = useCallback(
    async (
      parentDirId: string = ROOT_PATH,
      cursor = '',
      direction: KnowledgeBaseDirectionQuery = 'backward',
      take: number = DEFAULT_KB_RETRIEVE_LIMIT
    ) => {
      setIsLoading(true);
      const res = await ExplorerAPI.getList(parentDirId, cursor, direction, take);

      if (res) {
        setKnowledgeBases(sortByCreatedAt(res.data ?? []));
        setPagination(res.pagination);
        setPaginationCapture({ cursor, direction, parentDirId });
      }

      setIsLoading(false);
    },
    []
  );

  /**
   * Fetch more knowledge bases with direction and cursor
   * @returns void
   */
  const fetchMoreKnowledgeBases = useCallback(async () => {
    const { parentDirId, direction } = paginationCapture;
    const cursor = direction === 'backward' ? pagination?.first : pagination?.last;

    if (
      (direction === 'forward' && !pagination?.hasNextPage) ||
      (direction === 'backward' && !pagination?.hasPreviousPage) ||
      !cursor
    )
      return;

    setIsLoading(true);

    const res = await KbAPI.getList(parentDirId, cursor, direction, DEFAULT_KB_RETRIEVE_LIMIT);
    if (res) {
      const additionalData = sortByCreatedAt(res.data ?? []);

      setKnowledgeBases((prev) => [...(prev ?? []), ...additionalData]);
      setPagination(res.pagination);
      setPaginationCapture({ cursor, direction, parentDirId });
    }

    setIsLoading(false);
  }, [pagination, paginationCapture, getKnowledgeBases, setKnowledgeBases, setIsLoading]);

  /**
   * Delete knowledge base by id
   * @param id
   * @returns void
   */
  const deleteKnowledgeBase = useCallback(async (id: string) => {
    return await KbAPI.remove(id);
  }, []);

  return {
    isLoading,
    setIsLoading,
    knowledgeBases,
    pagination,
    saveKnowledgeBase,
    getKnowledgeBase,
    getKnowledgeBases,
    deleteKnowledgeBase,
    fetchMoreKnowledgeBases,
  };
};

export type KnowledgeBaseContextType = ReturnType<typeof useKnowledgeBase>;

const context = createContext<KnowledgeBaseContextType | null>(null);

export const KnowledgeBaseContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useKnowledgeBase();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useKnowledgeBaseContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useKnowledgeBaseContext must be used within a KbContextProvider');
  }

  return value;
};
