import { TemplateAPI } from '@/services/api/v2';
import type { KBTemplate } from '@/types/template';
import { createContextHook } from '@resola-ai/utils';
import { useState } from 'react';

/**
 * Interface for Templates state
 * @interface ITemplatesState
 */
interface ITemplatesState {
  templates: KBTemplate[];
  loading: boolean;
}

/**
 * Initial state for Templates
 */
const initialTemplatesState: ITemplatesState = {
  templates: [],
  loading: false,
};

/**
 * Custom hook to manage the Templates state and related actions
 * Provides the state and functions to manage templates
 */
const useTemplates = () => {
  const [templatesState, setTemplatesState] = useState<ITemplatesState>(initialTemplatesState);

  // Placeholder for future API call to fetch templates
  const fetchTemplates = async () => {
    setTemplatesState((prev) => ({ ...prev, loading: true }));
    const response = await TemplateAPI.getArticleTemplates({});

    if (response?.status === 'success') {
      setTemplatesState({ templates: response.data, loading: false });
    } else {
      setTemplatesState((prev) => ({ ...prev, loading: false }));
    }
  };

  return {
    templatesState,
    fetchTemplates,
  };
};

/**
 * Type for the TemplatesContext
 * @typedef {ReturnType<typeof useTemplates>} TemplatesContextType
 */
export type TemplatesContextType = ReturnType<typeof useTemplates>;

export const [TemplatesContextProvider, useTemplatesContext, TemplatesContext] = createContextHook(
  useTemplates,
  'Templates'
);
