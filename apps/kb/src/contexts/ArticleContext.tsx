import { DEFAULT_ARTICLES_RETRIEVE_LIMIT } from '@/constants/api';
import { useArticleDeleteConfirmation } from '@/hooks/useArticleDeleteConfirmation';
import { ArticleAPI } from '@/services/api/v2';
import {
  type Article,
  type ArticleCollection,
  KBDirectionQueryEnum,
  type KnowledgeBase,
  type KnowledgeBaseDirectionQuery,
} from '@/types';
import { sortByCreatedAt } from '@/utils/dateTime';
import { createContextHook } from '@resola-ai/utils';
import { useCallback, useEffect, useMemo, useReducer, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

/**
 * Article Context Interfaces
 * @interface IParamsCapture
 * @interface ICollectionState
 */
interface IParamsCapture {
  cursor: string;
  direction: KnowledgeBaseDirectionQuery;
}

interface ICollectionState {
  isLoadingArticles: boolean;
  isCreating: boolean;
  shouldCloseCreateNewArticleDrawer: boolean | undefined;
  currentKb: KnowledgeBase | null;
  pageLimit: number;
  activeArticleId: string;
  searchKeyword: string;
  capturedPayload: IParamsCapture;
}

/**
 * Article Context Action Types
 * @const actionTypes
 * @enum {string}
 */
const actionTypes = {
  setKb: 'setKb',
  activatedArticle: 'activatedArticle',
  searched: 'searched',
  fetchedCollection: 'fetchedCollection',
  loading: 'loading',
  creating: 'creating',
  setPageLimit: 'setPageLimit',
  updateShouldCloseCreateNewArticleDrawer: 'updateShouldCloseCreateNewArticleDrawer',
};

/**
 * Article Collection Reducer
 * @param {ICollectionState} state
 * @param {any} action
 */
const articleCollectionReducer = (state: ICollectionState, action) => {
  switch (action.type) {
    case actionTypes.setKb:
      return {
        ...state,
        currentKb: action.kb,
      };

    case actionTypes.activatedArticle:
      return {
        ...state,
        activeArticleId: action.id,
      };

    case actionTypes.searched:
      return {
        ...state,
        searchKeyword: action.keyword,
      };

    case actionTypes.fetchedCollection:
      return {
        ...state,
        capturedPayload: action.payload,
      };

    case actionTypes.loading:
      return {
        ...state,
        isLoadingArticles: action.value,
      };

    case actionTypes.creating:
      return {
        ...state,
        isCreating: action.value,
      };

    case actionTypes.setPageLimit:
      return {
        ...state,
        pageLimit: action.value,
      };

    case actionTypes.updateShouldCloseCreateNewArticleDrawer:
      return {
        ...state,
        shouldCloseCreateNewArticleDrawer: action.value,
      };
  }

  throw Error('Unknown action.');
};

/**
 * Use Article
 * @returns {ArticleContextType}
 * @dependencies useState: React Hook, useSearchParams: function, useReducer: React Hook, ArticleAPI.getCollection: function, ArticleAPI.create: function, ArticleAPI.delete: function
 */
const useArticle = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [articleCollection, setArticleCollection] = useState<ArticleCollection>({
    data: undefined,
    pagination: undefined,
  });
  const { confirmThenDeleteArticle } = useArticleDeleteConfirmation();

  const defaultState: ICollectionState = useMemo(
    () => ({
      isLoadingArticles: false,
      isCreating: false,
      shouldCloseCreateNewArticleDrawer: undefined,
      currentKb: null,
      pageLimit: Number(searchParams.get('limit')) || DEFAULT_ARTICLES_RETRIEVE_LIMIT,
      activeArticleId: searchParams.get('articleId') || '',
      searchKeyword: '',
      capturedPayload: {
        cursor: '',
        direction: KBDirectionQueryEnum.Backward,
      },
    }),
    []
  );

  const [articleCollectionState, dispatch] = useReducer(articleCollectionReducer, defaultState);

  /**
   * Get Collection
   * @param {string} kbId
   * @dependencies ArticleAPI.getCollection: function
   * @returns {void}
   */
  const getCollection = useCallback(
    async (
      kbId: string,
      take: number = DEFAULT_ARTICLES_RETRIEVE_LIMIT,
      direction: KnowledgeBaseDirectionQuery = KBDirectionQueryEnum.Backward,
      cursor = '',
      query = '',
      showLoader = true
    ) => {
      showLoader && dispatch({ type: actionTypes.loading, value: true });
      const data = await ArticleAPI.getCollection(kbId, take, direction, cursor, query);

      setArticleCollection({
        data: query ? data?.data : sortByCreatedAt(data?.data || []),
        pagination: data?.pagination,
      });
      dispatch({ type: actionTypes.fetchedCollection, payload: { cursor, direction } });
      showLoader && dispatch({ type: actionTypes.loading, value: false });
    },
    [dispatch, setArticleCollection]
  );

  /**
   * Create Article
   * @param {string} kbId
   * @param {any} payload
   * @dependencies ArticleAPI.create: function, getCollection: function
   * @returns {void}
   */
  const createArticle = useCallback(
    async (kbId: string, payload: any) => {
      dispatch({ type: actionTypes.creating, value: true });
      const createdResponse = await ArticleAPI.create(kbId, payload);
      await getCollection(kbId);
      dispatch({ type: actionTypes.creating, value: false });

      return createdResponse;
    },
    [getCollection, dispatch]
  );

  /**
   * Handle Article Delete with Confirmation
   * @param {Article} article
   * @param {Function} onDeleted
   */
  const deleteArticle = useCallback(
    (article: Article, onDeleted?: () => void) => {
      confirmThenDeleteArticle(
        article,
        () => {
          onDeleted?.();
          if (article.baseId) {
            getCollection(article.baseId);
          }
        },
        (isLoading) => dispatch({ type: actionTypes.loading, value: isLoading })
      );
    },
    [confirmThenDeleteArticle, getCollection, dispatch]
  );

  /**
   * Update Keyword Action (Search)
   * @param {string} keyword
   * @dependencies setSearchParams: function, dispatch: React Hook
   */
  const updateKeywordAction = useCallback(
    (keyword: string) => {
      setSearchParams((prev) => {
        // Remove query param if keyword is empty
        if (!keyword) {
          prev.delete('query');
        } else {
          prev.set('query', keyword);
        }

        return prev;
      });

      dispatch({ type: actionTypes.searched, keyword });
    },
    [setSearchParams, dispatch]
  );

  /**
   * Update Active Article Action
   * @param {string} id
   * @dependencies setSearchParams: function, dispatch: React Hook
   */
  const updateActiveArticleAction = useCallback(
    (id: string) => {
      setSearchParams((prev) => {
        prev.set('articleId', id);
        return prev;
      });

      dispatch({ type: actionTypes.activatedArticle, id });
    },
    [setSearchParams, dispatch]
  );

  /**
   * Update Page Limit Action
   * @param {number} limit
   * @dependencies setSearchParams: function, dispatch: React Hook
   */
  const updatePageLimitAction = useCallback(
    (limit: number) => {
      setSearchParams((prev) => {
        prev.set('limit', limit.toString());
        return prev;
      });

      dispatch({ type: actionTypes.setPageLimit, value: limit });
    },
    [setSearchParams, dispatch]
  );

  /**
   * Close Create New Article Drawer
   * @dependencies dispatch: React Hook
   */
  const closeCreateNewArticleDrawer = useCallback(() => {
    dispatch({ type: actionTypes.updateShouldCloseCreateNewArticleDrawer, value: true });
  }, [dispatch]);

  /**
   * Reset Close Create New Article Drawer
   * @dependencies dispatch: React Hook
   */
  const resetShouldCloseCreateNewArticleDrawer = useCallback(() => {
    dispatch({ type: actionTypes.updateShouldCloseCreateNewArticleDrawer, value: false });
  }, [dispatch]);

  /**
   * Import Article From CSV
   * @param {string} kbId
   * @param {File} file
   * @dependencies ArticleAPI.import: function
   * @returns {void}
   */
  const importArticleFromCSV = useCallback(async (kbId: string, file: File) => {
    const response = await ArticleAPI.import(kbId, file);
    return response;
  }, []);

  /**
   * Clear Search Query on Unmount
   */
  useEffect(() => {
    return () => {
      if (!searchParams.get('query')) return;
      setSearchParams((prev) => {
        prev.delete('query');
        return prev;
      });
    };
  }, []);

  return {
    articleCollection,
    state: articleCollectionState,
    actions: {
      setCurrentKb: (kb: KnowledgeBase) => dispatch({ type: actionTypes.setKb, kb }),
      setActiveArticleId: updateActiveArticleAction,
      setSearchKeyword: updateKeywordAction,
      setPageLimit: updatePageLimitAction,
      closeCreateNewArticleDrawer,
      resetShouldCloseCreateNewArticleDrawer,
    },
    services: {
      getCollection,
      createArticle,
      deleteArticle,
      importArticleFromCSV,
    },
  };
};

export type ArticleContextType = ReturnType<typeof useArticle>;
export const [ArticleContextProvider, useArticleContext] = createContextHook<ArticleContextType>(
  useArticle,
  'ArticleContext'
);
