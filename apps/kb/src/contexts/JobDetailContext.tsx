import { JOB_ARTICLE_DOWNLOAD_FORMATS } from '@/constants/file';
import { useApi<PERSON>and<PERSON> } from '@/hooks/useApiHandler';
import { GenArticleAPI, JobAPI } from '@/services/api/v2';
import {
  JOB_STATUS,
  type Job,
  type JobArticle,
  type JobArticleDownloadFormat,
  type JobExportResult,
} from '@/types';
import { downloadBlob } from '@/utils/file';
import type { IPaginationNextPrevious } from '@resola-ai/models';
import { createContextHook } from '@resola-ai/utils';
import { useTranslate } from '@tolgee/react';
import { useState } from 'react';

interface JobDetailContextState {
  isLoading: boolean;
  isEmpty: boolean;
  jobDetail: Job | null;
  jobResult: JobExportResult | null;
  articles: JobArticle[];
  pagination: IPaginationNextPrevious | null;
  selectedArticleIds: string[];
}

const defaultContext: JobDetailContextState = {
  isLoading: false,
  isEmpty: false,
  jobDetail: null,
  jobResult: null,
  articles: [],
  pagination: null,
  selectedArticleIds: [],
};

const NOT_RESULT_FOUND_JOB_STATUS = [
  JOB_STATUS.failed,
  JOB_STATUS.aborted,
  JOB_STATUS.retry,
  JOB_STATUS.queued,
  JOB_STATUS.running,
] as const;
/**
 * Custom hook for managing job detail state and operations
 * @returns {Object} Job detail state and operations
 */
const useJobDetail = () => {
  const [contextState, setContextState] = useState<JobDetailContextState>(defaultContext);
  const { t } = useTranslate('job');
  const { handleApiRequest } = useApiHandler();

  /**
   * Fetches job detail and results for a specific job
   * @param {string} jobId - The ID of the job to fetch
   * @returns {Promise<void>} Promise that resolves when the job details are fetched
   */
  const getJobDetail = async (jobId: string): Promise<void> => {
    setContextState((prev) => ({
      ...prev,
      isLoading: true,
    }));

    try {
      const jobDetailResponse = await JobAPI.getJobDetail(jobId);

      if (jobDetailResponse) {
        if (NOT_RESULT_FOUND_JOB_STATUS.includes(jobDetailResponse.status as any)) {
          setContextState((prev) => ({
            ...prev,
            isLoading: false,
            isEmpty: true,
            articles: [],
            jobResult: null,
            jobDetail: jobDetailResponse,
            selectedArticleIds: [],
          }));
          return;
        }

        const jobResultResponse = await JobAPI.getJobResult(jobId);
        setContextState((prev) => ({
          ...prev,
          jobDetail: jobDetailResponse,
          jobResult: jobResultResponse as JobExportResult | null,
          articles: Array.isArray(jobResultResponse) ? jobResultResponse : [],
          isLoading: false,
          isEmpty: Array.isArray(jobResultResponse) ? jobResultResponse.length === 0 : true,
          selectedArticleIds: [],
        }));
      }
    } catch (error) {
      setContextState((prev) => ({
        ...prev,
        isLoading: false,
        isEmpty: false,
      }));
    }
  };

  /**
   * Fetches job result for a specific job
   * @param {string} jobId - The ID of the job to fetch
   * @returns {Promise<void>} Promise that resolves when the job results are fetched
   */
  const getJobResult = async (jobId: string): Promise<void> => {
    if (!jobId) return;
    setContextState((prev) => ({
      ...prev,
      isLoading: true,
    }));

    try {
      const response = await JobAPI.getJobResult(jobId);
      if (response) {
        setContextState((prev) => ({
          ...prev,
          jobResult: response as JobExportResult | null,
          articles: Array.isArray(response) ? response : [],
          isEmpty: Array.isArray(response) ? response.length === 0 : true,
          isLoading: false,
        }));
      }
    } catch (error: unknown) {
      console.error('Error fetching job results:', error);
      setContextState((prev) => ({
        ...prev,
        isLoading: false,
      }));
    }
  };

  /**
   * Retries a failed job
   * @param {string} jobId - The ID of the job to retry
   * @throws {Error} When job retry fails
   * @returns {Promise<void>} Promise that resolves when the job retry is complete
   */
  const retryJob = async (jobId: string): Promise<void> => {
    setContextState((prev) => ({
      ...prev,
      isLoading: true,
    }));

    await handleApiRequest(JobAPI.retryJob(jobId), {
      fallbackMessage: t('retryJob.fallbackMessage'),
      fallbackTitle: t('retryJob.fallbackTitle'),
      successMessage: t('retryJob.successMessage'),
      successTitle: t('retryJob.successTitle'),
      successCallback: () => {
        getJobDetail(jobId);

        setContextState((prev) => ({
          ...prev,
          isLoading: false,
        }));
      },
      errorCallback: () => {
        setContextState((prev) => ({
          ...prev,
          isLoading: false,
        }));
      },
    });
  };

  /**
   * Deletes a job
   * @param {string} jobId - The ID of the job to delete
   * @throws {Error} When job deletion fails
   * @returns {Promise<unknown>} The deletion response data
   */
  const deleteJob = async (jobId: string): Promise<unknown> => {
    const response = await JobAPI.deleteJob(jobId);

    if (!response) {
      throw new Error('Failed to delete job');
    }

    return response.data;
  };

  /**
   * Adds an article to the selection
   * @param {string} articleId - The ID of the article to select
   */
  const selectArticle = (articleId: string): void => {
    setContextState((prev) => ({
      ...prev,
      selectedArticleIds: [...prev.selectedArticleIds, articleId],
    }));
  };

  /**
   * Removes an article from the selection
   * @param {string} articleId - The ID of the article to deselect
   */
  const deselectArticle = (articleId: string): void => {
    setContextState((prev) => ({
      ...prev,
      selectedArticleIds: prev.selectedArticleIds.filter((id) => id !== articleId),
    }));
  };

  /**
   * Selects all available articles
   */
  const selectAllArticles = (): void => {
    setContextState((prev) => ({
      ...prev,
      selectedArticleIds: prev.articles.map((article) => article.id),
    }));
  };

  /**
   * Clears all selected articles
   */
  const deselectAllArticles = (): void => {
    setContextState((prev) => ({
      ...prev,
      selectedArticleIds: [],
    }));
  };

  /**
   * Downloads the generated articles for a specific job
   * @param {string} jobId - The ID of the job to download
   * @param {string} filename - The filename of the downloaded file
   * @param {JobArticleDownloadFormat} format - The format of the downloaded file
   * @returns {Promise<void>} Promise that resolves when the download is complete
   */
  const downloadGenArticles = async (
    jobId: string,
    filename?: string,
    format: JobArticleDownloadFormat = 'csv'
  ): Promise<void> => {
    if (!jobId) return;

    try {
      const formatInfo = JOB_ARTICLE_DOWNLOAD_FORMATS[format];
      const isExcel = format === 'excel';
      const options = isExcel ? { responseType: 'arraybuffer' } : undefined;

      // For Excel files, need to handle as binary data
      const responseData = await handleApiRequest(GenArticleAPI.download(jobId, format, options), {
        fallbackMessage: t('downloadGenArticles.fallbackMessage'),
        fallbackTitle: t('downloadGenArticles.fallbackTitle'),
        successMessage: t('downloadGenArticles.successMessage'),
        successTitle: t('downloadGenArticles.successTitle'),
      });

      // Create appropriate blob with correct MIME type
      const blob = new Blob([responseData], { type: formatInfo.mimeType });

      downloadBlob(blob, `${filename ?? jobId}.${formatInfo.extension}`);
    } catch (error: unknown) {
      console.error('Error downloading articles:', error);
    }
  };

  return {
    getJobDetail,
    getJobResult,
    selectArticle,
    deselectArticle,
    selectAllArticles,
    deselectAllArticles,
    retryJob,
    deleteJob,
    downloadGenArticles,
    ...contextState,
  };
};

export const [JobDetailProvider, useJobDetailContext] = createContextHook(
  useJobDetail,
  'JobDetail'
);
