import { SearchAPI } from '@/services/api/v2';
import type { SearchFilters, SearchResult, SearchType } from '@/types';
import { createContextHook } from '@resola-ai/utils';
import { useState } from 'react';

const useSearch = () => {
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);

  /**
   * Search by query
   * @param {string} query
   * @returns {void}
   */
  const search = async (query: string, folderId?: string, showLoader = true) => {
    if (isSearching) return;

    showLoader && setIsSearching(true);
    const searchResponse = await SearchAPI.search(query, folderId);

    if (searchResponse && searchResponse.status === 'success') {
      setSearchResults(searchResponse.data);
    }
    showLoader && setIsSearching(false);
  };

  /**
   * Search by filters
   * @param {string} query
   * @param {string} folderId
   * @param {SearchFilters} filters
   * @param {SearchType[]} entities
   * @param {boolean} showLoader
   * @returns {void}
   */
  const searchByFilters = async ({
    query,
    folderId,
    filters,
    entities,
    showLoader = true,
  }: {
    query: string;
    folderId?: string;
    showLoader?: boolean;
    filters: SearchFilters;
    entities: SearchType[];
  }) => {
    if (isSearching) return;

    showLoader && setIsSearching(true);
    const searchResponse = await SearchAPI.search(query, folderId, entities, filters);

    if (searchResponse && searchResponse.status === 'success') {
      setSearchResults(searchResponse.data);
    }
    showLoader && setIsSearching(false);
  };

  return {
    isSearching,
    searchResults,
    search,
    searchByFilters,
  };
};

export type SearchContextType = ReturnType<typeof useSearch>;

export const [SearchContextProvider, useSearchContext, SearchContext] = createContextHook(
  useSearch,
  'Search'
);
