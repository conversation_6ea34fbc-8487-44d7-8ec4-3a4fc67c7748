import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { createContext, useContext, useState } from 'react';

import {
  CONFIRM_MODAL_ALIGNMENT,
  ConfirmModal,
  type ConfirmModalAlignmentType,
  type ConfirmModalOptions,
  type ConfirmModalProps,
} from '@/components/common/ConfirmModal';
import NoticeModal, { type NoticeModalProps } from '@/components/common/NoticeModal';

interface OpenConfirmModalProps {
  alignment?: ConfirmModalAlignmentType;
  onConfirm: any;
  onCancel?: any;
  title?: string;
  content?: string;
  confirmText?: string;
  cancelText?: string;
  options?: ConfirmModalOptions;
}

const useApp = () => {
  const { t } = useTranslate('common');
  const [confirmModal, setConfirmModal] = useState<ConfirmModalProps>({
    alignment: CONFIRM_MODAL_ALIGNMENT.CENTER,
    opened: false,
    title: t('confirmModalTitleDefault'),
    content: '',
    confirmText: '',
    cancelText: '',
    onConfirm: () => {},
    onCancel: () => {},
    options: {},
  });
  const [noticeModal, setNoticeModal] = useState<NoticeModalProps>({
    opened: false,
    title: '',
    content: '',
    cancelText: '',
    showLoader: false,
    onCancel: () => {},
  });

  const openConfirmModal = ({
    alignment = CONFIRM_MODAL_ALIGNMENT.CENTER,
    onConfirm = () => {},
    onCancel = () => {},
    title = '',
    content = '',
    confirmText = '',
    cancelText = '',
    options = {},
  }: OpenConfirmModalProps) => {
    setConfirmModal({
      alignment,
      opened: true,
      title: title || t('confirmModalTitleDefault'),
      content: content || '',
      confirmText,
      cancelText,
      onConfirm,
      onCancel: () => {
        onCancel?.();
        closeConfirmModal(options);
      },
      options,
    });
  };

  const closeConfirmModal = (options: ConfirmModalOptions = {}) => {
    setConfirmModal((prev) => ({
      ...prev,
      opened: false,
    }));

    setTimeout(() => {
      setConfirmModal({
        alignment: CONFIRM_MODAL_ALIGNMENT.CENTER,
        opened: false,
        title: t('confirmModalTitleDefault'),
        content: '',
        confirmText: '',
        cancelText: '',
        onConfirm: () => {},
        onCancel: () => {},
        options,
      });
    }, 200);
  };

  const openNoticeModal = ({
    title,
    content,
    cancelText,
    showLoader,
    onCancel,
  }: Partial<NoticeModalProps>) => {
    setNoticeModal({
      opened: true,
      title: title ?? '',
      content: content ?? '',
      cancelText,
      showLoader,
      onCancel: onCancel ?? closeNoticeModal,
    });
  };

  const closeNoticeModal = () => {
    setNoticeModal({
      opened: false,
      title: '',
      content: '',
      cancelText: '',
      showLoader: false,
      onCancel: () => {},
    });
  };

  return {
    confirmModal,
    openConfirmModal,
    closeConfirmModal,
    noticeModal,
    openNoticeModal,
    closeNoticeModal,
  };
};

export type AppContextType = ReturnType<typeof useApp>;

const context = createContext<AppContextType | null>(null);

export const AppContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useApp();

  return (
    <context.Provider value={value}>
      {children}
      <ConfirmModal {...value.confirmModal} />
      <NoticeModal {...value.noticeModal} />
    </context.Provider>
  );
};

export const useAppContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useAppContext must be used inside AppContextProvider');
  }

  return value;
};
