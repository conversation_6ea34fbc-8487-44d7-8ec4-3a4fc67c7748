import {
  useArticleSelection,
  useBaseSelection,
  useDocumentSelection,
  useFolderSelection,
} from '@/hooks';
import { SearchAPI } from '@/services/api/v2';
import {
  type Article,
  type DocumentFile,
  type Folder,
  type KnowledgeBase,
  type SearchResult,
  SearchTypeEnums,
} from '@/types';
import filter from 'lodash/filter';
import type React from 'react';
import { createContext, useCallback, useContext } from 'react';

type KBSelectionContextType = ReturnType<typeof useKBSelection>;

const KBSelectionContext = createContext<KBSelectionContextType | null>(null);

const useKBSelection = (articleIds?: string[], documentIds?: string[]) => {
  const baseSelectionHook = useBaseSelection();
  const folderSelectionHook = useFolderSelection();
  const articleSelectionHook = useArticleSelection(articleIds);
  const documentSelectionHook = useDocumentSelection(documentIds);

  /**
   * Search for folders and Knowledge Bases
   * @param {string} searchText - The text to search for
   * @returns {Promise<void>}
   */
  const search = useCallback(
    async (
      searchText: string,
      folderId = '',
      entities: SearchTypeEnums[] = [SearchTypeEnums.folder, SearchTypeEnums.base]
    ) => {
      const response = await SearchAPI.search(searchText, folderId, entities);

      // Filter the response data to get the folders and Knowledge Bases
      if (response?.status === 'success') {
        if (entities.includes(SearchTypeEnums.base)) {
          baseSelectionHook.setBases(
            filter(response?.data, { type: SearchTypeEnums.base }).map(
              (baseItem: SearchResult) => baseItem.data as KnowledgeBase
            )
          );
        }

        if (entities.includes(SearchTypeEnums.folder)) {
          folderSelectionHook.setFolders(
            filter(response?.data, { type: SearchTypeEnums.folder }).map(
              (folderItem: SearchResult) => folderItem.data as Folder
            )
          );
        }

        if (entities.includes(SearchTypeEnums.article)) {
          articleSelectionHook.setArticles(
            filter(response?.data, { type: SearchTypeEnums.article }).map(
              (articleItem: SearchResult) => articleItem.data as Article
            )
          );
        }

        if (entities.includes(SearchTypeEnums.document)) {
          documentSelectionHook.setDocuments(
            filter(response?.data, { type: SearchTypeEnums.document }).map(
              (documentItem: SearchResult) => documentItem.data as DocumentFile
            )
          );
        }
      }

      return response;
    },
    [
      baseSelectionHook.setBases,
      folderSelectionHook.setFolders,
      articleSelectionHook.setArticles,
      documentSelectionHook.setDocuments,
    ]
  );

  return {
    ...baseSelectionHook,
    ...folderSelectionHook,
    ...articleSelectionHook,
    ...documentSelectionHook,
    search,
  };
};

export const KBSelectionContextProvider = ({
  children,
  articleIds,
  documentIds,
}: {
  children: React.ReactNode;
  articleIds?: string[];
  documentIds?: string[];
}) => {
  const value = useKBSelection(articleIds, documentIds);

  return <KBSelectionContext.Provider value={value}>{children}</KBSelectionContext.Provider>;
};

export const useKBSelectionContext = () => {
  const context = useContext(KBSelectionContext);
  if (!context) {
    throw new Error('useKBSelectionContext must be used within a KBSelectionContextProvider');
  }
  return context;
};
