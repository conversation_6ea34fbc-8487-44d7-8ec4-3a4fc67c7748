import { useArticleValidation } from '@/hooks/useArticleValidation';
import { GenArticleAPI } from '@/services/api/v2/genArticle';
import type { ArticleGeneratorActivity, JobArticle } from '@/types';
import { createContextHook } from '@resola-ai/utils';
import { useState } from 'react';

interface GenArticleContextState {
  updating: boolean;
  deleting: boolean;
  activitiesLoading: boolean;
  editingGenArticle: JobArticle | null;
  articleActivities: ArticleGeneratorActivity[];
}

const defaultContext: GenArticleContextState = {
  updating: false,
  deleting: false,
  activitiesLoading: false,
  editingGenArticle: null,
  articleActivities: [],
};

/**
 * Custom hook for managing generic article detail state and operations
 * @returns {Object} Generic article detail state and operations
 */
const useGenArticle = () => {
  const { validateArticleContent, articleErrors } = useArticleValidation();
  const [contextState, setContextState] = useState<GenArticleContextState>(defaultContext);

  /**
   * Updates the article in the database with the provided payload
   * @param {JobArticle} payload - Partial article data containing fields to update
   * @returns {Promise<JobArticle>} The updated article if successful, undefined if failed
   */
  const updateGenArticle = async (payload: Partial<JobArticle>) => {
    if (!payload.id) return;

    setContextState((prev) => ({
      ...prev,
      updating: true,
    }));

    try {
      const updatedGenArticle = await GenArticleAPI.update(payload.id, payload);
      setContextState((prev) => ({
        ...prev,
        updating: false,
      }));

      return updatedGenArticle;
    } catch (error) {
      console.error(error);
      setContextState((prev) => ({
        ...prev,
        updating: false,
      }));
    }
  };

  /**
   * Fetches activity history for a generated article
   * @param {string} articleId - ID of the article
   */
  const getArticleActivities = async (articleId: string) => {
    if (!articleId) return;

    setContextState((prev) => ({
      ...prev,
      activitiesLoading: true,
    }));

    try {
      const history = await GenArticleAPI.getHistory(articleId);
      setContextState((prev) => ({
        ...prev,
        activitiesLoading: false,
        articleActivities: history?.data || [],
      }));
    } catch (error) {
      console.error(error);
      setContextState((prev) => ({
        ...prev,
        activitiesLoading: false,
      }));
    }
  };

  /**
   * Sets the article data
   * @param {JobArticle} article - The article data to set
   */
  const setEditingGenArticle = (article: JobArticle) => {
    setContextState((prev) => ({
      ...prev,
      editingGenArticle: {
        ...(prev.editingGenArticle || {}),
        ...article,
      },
    }));
  };

  return {
    updateGenArticle,
    setEditingGenArticle,
    getArticleActivities,
    validateArticleContent,
    articleErrors,
    ...contextState,
  };
};

export type GenArticleContextType = ReturnType<typeof useGenArticle>;

export const [GenArticleProvider, useGenArticleContext, GenArticleContext] = createContextHook(
  useGenArticle,
  'GenArticle'
);
