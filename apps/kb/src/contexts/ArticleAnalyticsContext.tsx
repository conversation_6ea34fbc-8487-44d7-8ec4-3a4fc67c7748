import { ArticleAPI } from '@/services/api/v2';
import {
  type ArticleAnalyticData,
  type ArticleAnalyticDateRangeParams,
  type ArticleAnalyticSummary,
  ArticleDetailLayout,
} from '@/types/article';
import { summarizeAnalyticsData } from '@/utils/article';
import type React from 'react';
import { createContext, useCallback, useContext, useEffect, useState } from 'react';

interface IArticleAnalyticsState {
  loading: boolean;
  articleId: string;
  layout: ArticleDetailLayout;
  data: ArticleAnalyticData;
  summary: ArticleAnalyticSummary;
  withOriginalArticle: boolean;
}

const initialArticleAnalyticsState: IArticleAnalyticsState = {
  loading: false,
  articleId: '',
  layout: ArticleDetailLayout.HORIZONTAL,
  data: [],
  summary: {
    totalViewCount: 0,
    totalFeedbackCount: 0,
    totalLikeCount: 0,
    totalDislikeCount: 0,
    products: {},
  },
  withOriginalArticle: false,
};

const useArticleAnalytics = () => {
  const [articleAnalyticsState, setArticleAnalyticsState] = useState<IArticleAnalyticsState>(
    initialArticleAnalyticsState
  );

  const setLayout = useCallback((newLayout: ArticleDetailLayout) => {
    setArticleAnalyticsState((prev) => ({ ...prev, layout: newLayout }));
  }, []);

  const setArticleId = useCallback((newArticleId: string) => {
    setArticleAnalyticsState((prev) => ({ ...prev, articleId: newArticleId }));
  }, []);

  const setWithOriginalArticle = useCallback((newWithOriginalArticle: boolean) => {
    setArticleAnalyticsState((prev) => ({ ...prev, withOriginalArticle: newWithOriginalArticle }));
  }, []);

  const getAnalytics = useCallback(
    async (
      articleId: string,
      dateRange?: ArticleAnalyticDateRangeParams,
      withOriginalArticle = false
    ) => {
      setArticleAnalyticsState((prev) => ({ ...prev, loading: true }));
      const response = await ArticleAPI.getAnalytics(articleId, dateRange, withOriginalArticle);

      if (response && response.status === 'success') {
        setArticleAnalyticsState((prev) => ({
          ...prev,
          data: response.data,
          summary: summarizeAnalyticsData(response.data),
          loading: false,
        }));
      }

      setArticleAnalyticsState((prev) => ({ ...prev, loading: false }));
      return response;
    },
    []
  );

  return {
    articleAnalyticsState,
    setLayout,
    setArticleId,
    setWithOriginalArticle,
    getAnalytics,
  };
};

/**
 * Type for the ArticleAnalyticsContext
 * @typedef {ReturnType<typeof useArticleAnalytics>} ArticleAnalyticsContextType
 */
type ArticleAnalyticsContextType = ReturnType<typeof useArticleAnalytics>;

/**
 * Create the ArticleAnalyticsContext with a default value of `null`
 */
const ArticleAnalyticsContext = createContext<ArticleAnalyticsContextType | null>(null);

/**
 * Context Provider component for ArticleAnalyticsContext
 * Wraps children components and provides the analytics context value
 * @param {React.ReactNode} children - Children components that will consume the context
 */
export const ArticleAnalyticsProvider = ({
  children,
  layout,
  articleId,
  withOriginalArticle,
}: {
  children: React.ReactNode;
  layout: ArticleDetailLayout;
  articleId: string;
  withOriginalArticle: boolean;
}) => {
  const contextValue = useArticleAnalytics();

  useEffect(() => {
    contextValue.setLayout(layout);
  }, [layout]);

  useEffect(() => {
    if (articleId) {
      contextValue.setArticleId(articleId);
    }
  }, [articleId]);

  useEffect(() => {
    if (withOriginalArticle) {
      contextValue.setWithOriginalArticle(withOriginalArticle);
    }
  }, [withOriginalArticle]);

  return (
    <ArticleAnalyticsContext.Provider value={contextValue}>
      {children}
    </ArticleAnalyticsContext.Provider>
  );
};

/**
 * Custom hook to access the ArticleAnalyticsContext
 * Ensures the hook is only used within a valid ArticleAnalyticsProvider
 * @returns {ArticleAnalyticsContextType} The context value (analytics state and actions)
 * @throws {Error} If used outside of the ArticleAnalyticsProvider
 */
export const useArticleAnalyticsContext = () => {
  const context = useContext(ArticleAnalyticsContext);

  if (!context) {
    throw new Error('useArticleAnalyticsContext must be used within an ArticleAnalyticsProvider');
  }

  return context;
};
