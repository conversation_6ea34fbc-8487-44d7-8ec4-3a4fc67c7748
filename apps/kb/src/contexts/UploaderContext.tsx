import { useFileUpload } from '@/hooks';
import { useDisclosure } from '@mantine/hooks';
import { createContextHook } from '@resola-ai/utils';
const useUploader = () => {
  const { uploadFiles, uploaders, validateFiles } = useFileUpload();
  const [openedUploadStatus, { open: openUploadStatus, close: closeUploadStatus }] =
    useDisclosure(false);
  return {
    uploadFiles,
    uploaders,
    validateFiles,
    openedUploadStatus,
    openUploadStatus,
    closeUploadStatus,
  };
};

export type UploaderContextType = ReturnType<typeof useUploader>;
export const [UploaderContextProvider, useUploaderContext] = createContextHook<UploaderContextType>(
  useUploader,
  'UploaderContext'
);
