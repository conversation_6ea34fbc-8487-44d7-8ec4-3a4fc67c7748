import { useCallback, useState } from 'react';

import { DocumentAPI } from '@/services/api/v2/document';
import type { DocumentFile, FILE_UPLOAD_STATUS } from '@/types';
import { downloadFile } from '@/utils/file';
import { createContextHook } from '@resola-ai/utils';

const useDocument = () => {
  const [shouldRefresh, setShouldRefresh] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);
  const deleteDocument = useCallback((documentId: string) => {
    return DocumentAPI.delete(documentId);
  }, []);

  const getDocument = useCallback((documentId: string) => {
    return DocumentAPI.get(documentId, true);
  }, []);

  const updateDocument = useCallback((documentId: string, data: Partial<DocumentFile>) => {
    return DocumentAPI.put(documentId, data);
  }, []);

  const updateStatusDocument = useCallback((documentId: string, status: FILE_UPLOAD_STATUS) => {
    return DocumentAPI.patch(documentId, { status });
  }, []);

  const getLink = useCallback((documentId: string) => {
    return DocumentAPI.getLink(documentId);
  }, []);

  const downloadDocument = useCallback(async (documentId: string, filename?: string) => {
    setIsDownloading(true);
    const link = await DocumentAPI.getLink(documentId);
    if (link?.downloadUrl) {
      await downloadFile(link.downloadUrl, filename || 'download');
    }

    setIsDownloading(false);
  }, []);

  return {
    getDocument,
    deleteDocument,
    downloadDocument,
    isDownloading,
    setIsDownloading,
    shouldRefresh,
    setShouldRefresh,
    updateStatusDocument,
    updateDocument,
    getLink,
  };
};

export type DocumentContextType = ReturnType<typeof useDocument>;
export const [DocumentContextProvider, useDocumentContext] = createContextHook<DocumentContextType>(
  useDocument,
  'DocumentContext'
);
