import { KB_API_PATH, TEMP_KB_PAYLOAD } from '@/constants/api';
import { ROOT_PATH } from '@/constants/folder';
import { DEFAULT_KB_RETRIEVE_LIMIT } from '@/constants/kb';
import type { KnowledgeBase, KnowledgeBaseDirectionQuery } from '@/types';
import { handleResponse } from '@/utils/api';
import type { ISuccessListNextPreviousResponse } from '@resola-ai/models';
import { axiosService } from '@resola-ai/services-shared';

export const KbAPI = {
  getList: (
    parentDirId: string = ROOT_PATH,
    cursor = '',
    direction: KnowledgeBaseDirectionQuery = 'backward',
    take: number = DEFAULT_KB_RETRIEVE_LIMIT
  ) => {
    return handleResponse<ISuccessListNextPreviousResponse<KnowledgeBase>>(
      axiosService.instance.get(
        `${KB_API_PATH}/?parentDirId=${parentDirId}&cursor=${cursor}&direction=${direction}&take=${take}`
      )
    );
  },
  get: async (id: string) => {
    return axiosService.instance.get(`${KB_API_PATH}/${id}`);
  },
  create: async (payload) => {
    return handleResponse<KnowledgeBase>(
      axiosService.instance.post(`${KB_API_PATH}/?knowledgebase_type=${payload.baseType}`, {
        ...TEMP_KB_PAYLOAD,
        ...payload,
      })
    );
  },
  update: async (id: string, payload) => {
    return handleResponse<KnowledgeBase>(
      axiosService.instance.put(`${KB_API_PATH}/${id}`, payload)
    );
  },
  remove: async (id: string) => {
    return handleResponse<KnowledgeBase>(axiosService.instance.delete(`${KB_API_PATH}/${id}`));
  },
};
