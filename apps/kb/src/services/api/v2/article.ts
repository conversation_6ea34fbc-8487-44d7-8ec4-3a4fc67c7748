import { KB_ARTICLES_PATH, KB_ARTICLE_API_PATH, KB_STATISTICS_PATH } from '@/constants/api';
import type {
  Article,
  ArticleAnalyticData,
  ArticleAnalyticDateRangeParams,
  ArticleFileUpload,
  KnowledgeBaseDirectionQuery,
  PartOfArticle,
} from '@/types';
import { handleResponse, handleResponseWithError } from '@/utils/api';
import type { ISuccessListNextPreviousResponse, ISuccessResponse } from '@resola-ai/models';
import { axiosService } from '@resola-ai/services-shared';

export const ArticleAPI = {
  getCollection: async (
    knowledgeBaseId: string,
    take: number,
    direction: KnowledgeBaseDirectionQuery = 'backward',
    cursor = '',
    query = ''
  ) => {
    return handleResponse<ISuccessListNextPreviousResponse<Article>>(
      axiosService.instance.get(
        `${KB_ARTICLE_API_PATH(knowledgeBaseId)}?cursor=${cursor}&direction=${direction}&take=${take}&query=${query}`
      )
    );
  },
  get: async (articleId: string) => {
    return axiosService.instance.get(`${KB_ARTICLES_PATH}/${articleId}`);
  },
  create: async (knowledgeBaseId: string, payload: any) => {
    return handleResponse<ISuccessResponse<PartOfArticle>>(
      axiosService.instance.post(`${KB_ARTICLE_API_PATH(knowledgeBaseId)}`, payload)
    );
  },
  update: async (articleId: string, payload: any) => {
    return axiosService.instance.put(`${KB_ARTICLES_PATH}/${articleId}`, payload);
  },
  uploadFile: async (knowledgeBaseId: string, articleId: string, file: File) => {
    const formData = new FormData();
    formData.append('image', file);

    return await handleResponse<ISuccessResponse<ArticleFileUpload>>(
      axiosService.instance.post(
        `${KB_ARTICLE_API_PATH(knowledgeBaseId)}${articleId}/uploadImage`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      )
    );
  },
  uploadFileWhenCreating: async (knowledgeBaseId: string, file: File) => {
    const formData = new FormData();
    formData.append('image', file);

    return await handleResponse(
      axiosService.instance.post(`${KB_ARTICLE_API_PATH(knowledgeBaseId)}uploadImage`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    );
  },
  delete: async (knowledgeBaseId: string, articleId: string) => {
    return await handleResponseWithError(
      axiosService.instance.delete(`${KB_ARTICLE_API_PATH(knowledgeBaseId)}${articleId}`)
    );
  },
  getAnalytics: async (
    articleId: string,
    dateRange?: ArticleAnalyticDateRangeParams,
    withOriginalArticle = false
  ) => {
    const queryParams = new URLSearchParams();

    if (dateRange?.from && dateRange?.to) {
      queryParams.append('from_time', dateRange.from);
      queryParams.append('to_time', dateRange.to);
    }

    if (withOriginalArticle) {
      queryParams.append('withOriginArticle', 'true');
    }

    return await handleResponseWithError<ISuccessResponse<ArticleAnalyticData>>(
      axiosService.instance.get(
        `${KB_STATISTICS_PATH}/articles/${articleId}?${queryParams.toString()}`
      )
    );
  },
  import: async (knowledgeBaseId: string, file: File) => {
    const formData = new FormData();
    formData.append('uploadFile', file);

    return handleResponseWithError<any>(
      axiosService.instance.post(`${KB_ARTICLE_API_PATH(knowledgeBaseId)}import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
    );
  },
  createShortcut: async (articleId: string, kbId: string) => {
    return await axiosService.instance.post(`${KB_ARTICLES_PATH}/${articleId}/shortcut`, {
      kbId,
    });
  },
};
