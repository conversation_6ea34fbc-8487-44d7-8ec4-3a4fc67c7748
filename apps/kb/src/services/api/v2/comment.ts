import { KB_COMMENT_API_PATH } from '@/constants/api';
import type { Comment, KnowledgeBaseDirectionQuery, PartOfComment } from '@/types';
import { handleResponse } from '@/utils/api';
import type { ISuccessListNextPreviousResponse, ISuccessResponse } from '@resola-ai/models';
import { axiosService } from '@resola-ai/services-shared';

export const CommentAPI = {
  getComments: async (
    knowledgeBaseId: string,
    articleId: string,
    take: number,
    direction: KnowledgeBaseDirectionQuery = 'backward',
    cursor = ''
  ) => {
    return handleResponse<ISuccessListNextPreviousResponse<Comment>>(
      axiosService.instance.get(
        `${KB_COMMENT_API_PATH(knowledgeBaseId, articleId)}?cursor=${cursor}&direction=${direction}&take=${take}`
      )
    );
  },
  createComment: async (knowledgeBaseId: string, articleId: string, payload: any) => {
    return handleResponse<ISuccessResponse<PartOfComment>>(
      axiosService.instance.post(`${KB_COMMENT_API_PATH(knowledgeBaseId, articleId)}`, payload)
    );
  },
  updateComment: async (
    knowledgeBaseId: string,
    articleId: string,
    commentId: string,
    payload: any
  ) => {
    return handleResponse<ISuccessResponse<PartOfComment>>(
      axiosService.instance.put(
        `${KB_COMMENT_API_PATH(knowledgeBaseId, articleId)}${commentId}`,
        payload
      )
    );
  },
  deleteComment: async (knowledgeBaseId: string, articleId: string, commentId: string) => {
    return handleResponse<ISuccessResponse<PartOfComment>>(
      axiosService.instance.delete(`${KB_COMMENT_API_PATH(knowledgeBaseId, articleId)}${commentId}`)
    );
  },
};
