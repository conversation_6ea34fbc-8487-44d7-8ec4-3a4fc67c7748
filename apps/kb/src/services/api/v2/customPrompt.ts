import type { ISuccessResponse } from '@resola-ai/models';

import { KB_CUSTOM_PROMPT_PATH } from '@/constants/api';
import { ARTICLE_GENERATOR_PROMPT_MAX_ROWS } from '@/constants/job';
import { KBDirectionQueryEnum } from '@/types';
import type { Prompt, PromptPayload } from '@/types/job';
import { handleResponseWithError } from '@/utils/api';
import type { ISuccessListNextPreviousResponse } from '@resola-ai/models';
import { axiosService } from '@resola-ai/services-shared';

export const CustomPromptAPI = {
  getCustomPrompts: async (
    cursor = '',
    direction = KBDirectionQueryEnum.Backward,
    take = ARTICLE_GENERATOR_PROMPT_MAX_ROWS
  ) => {
    const queryParams = new URLSearchParams({
      cursor,
      direction,
      take: take.toString(),
    });

    return handleResponseWithError<ISuccessListNextPreviousResponse<Prompt>>(
      axiosService.instance.get(`${KB_CUSTOM_PROMPT_PATH}?${queryParams.toString()}`)
    );
  },
  createCustomPrompt: async (prompt: PromptPayload) => {
    return handleResponseWithError<ISuccessResponse<Prompt>>(
      axiosService.instance.post(KB_CUSTOM_PROMPT_PATH, prompt)
    );
  },
  deleteCustomPrompt: async (promptId: string) => {
    return handleResponseWithError<ISuccessResponse<Prompt>>(
      axiosService.instance.delete(`${KB_CUSTOM_PROMPT_PATH}${promptId}`)
    );
  },
  updateCustomPrompt: async (prompt: Prompt) => {
    return handleResponseWithError<ISuccessResponse<Prompt>>(
      axiosService.instance.put(`${KB_CUSTOM_PROMPT_PATH}${prompt.id}`, {
        title: prompt.title,
        content: prompt.content,
      })
    );
  },
};
