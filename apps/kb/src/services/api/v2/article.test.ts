import { handleResponse, handleResponseWithError } from '@/utils/api';
import { axiosService } from '@resola-ai/services-shared';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { ArticleAPI } from './article';

// Mock dependencies
vi.mock('@resola-ai/services-shared', () => ({
  axiosService: {
    instance: {
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
    },
  },
}));

vi.mock('@/utils/api', () => ({
  handleResponse: vi.fn((promise) => promise),
  handleResponseWithError: vi.fn((promise) => promise),
}));

export default describe('ArticleAPI', () => {
  const mockResponse = { data: { success: true, data: {} } };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('getCollection', () => {
    it('should call the correct endpoint with parameters', async () => {
      vi.mocked(axiosService.instance.get).mockResolvedValue(mockResponse);

      const knowledgeBaseId = 'kb123';
      const take = 10;
      const direction = 'backward';
      const cursor = 'cursor123';
      const query = 'search';

      await ArticleAPI.getCollection(knowledgeBaseId, take, direction, cursor, query);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `/v2/bases/${knowledgeBaseId}/articles/?cursor=${cursor}&direction=${direction}&take=${take}&query=${query}`
      );
      expect(handleResponse).toHaveBeenCalled();
    });
  });

  describe('get', () => {
    it('should call the correct endpoint with article ID', async () => {
      vi.mocked(axiosService.instance.get).mockResolvedValue(mockResponse);

      const articleId = 'article123';
      const result = await ArticleAPI.get(articleId);

      expect(axiosService.instance.get).toHaveBeenCalledWith(`/v2/articles/${articleId}`);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('create', () => {
    it('should call the correct endpoint with payload', async () => {
      vi.mocked(axiosService.instance.post).mockResolvedValue(mockResponse);

      const knowledgeBaseId = 'kb123';
      const payload = { title: 'Test Article', content: 'Test Content' };

      await ArticleAPI.create(knowledgeBaseId, payload);

      expect(axiosService.instance.post).toHaveBeenCalledWith(
        `/v2/bases/${knowledgeBaseId}/articles/`,
        payload
      );
      expect(handleResponse).toHaveBeenCalled();
    });
  });

  describe('update', () => {
    it('should call the correct endpoint with article ID and payload', async () => {
      vi.mocked(axiosService.instance.put).mockResolvedValue(mockResponse);

      const articleId = 'article123';
      const payload = { title: 'Updated Article', content: 'Updated Content' };

      const result = await ArticleAPI.update(articleId, payload);

      expect(axiosService.instance.put).toHaveBeenCalledWith(`/v2/articles/${articleId}`, payload);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('uploadFile', () => {
    it('should call the correct endpoint with form data', async () => {
      vi.mocked(axiosService.instance.post).mockResolvedValue(mockResponse);

      const knowledgeBaseId = 'kb123';
      const articleId = 'article123';
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      await ArticleAPI.uploadFile(knowledgeBaseId, articleId, file);

      expect(axiosService.instance.post).toHaveBeenCalledWith(
        `/v2/bases/${knowledgeBaseId}/articles/${articleId}/uploadImage`,
        expect.any(FormData),
        expect.objectContaining({
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
      );
      expect(handleResponse).toHaveBeenCalled();
    });
  });

  describe('uploadFileWhenCreating', () => {
    it('should call the correct endpoint with form data', async () => {
      vi.mocked(axiosService.instance.post).mockResolvedValue(mockResponse);

      const knowledgeBaseId = 'kb123';
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      await ArticleAPI.uploadFileWhenCreating(knowledgeBaseId, file);

      expect(axiosService.instance.post).toHaveBeenCalledWith(
        `/v2/bases/${knowledgeBaseId}/articles/uploadImage`,
        expect.any(FormData),
        expect.objectContaining({
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
      );
      expect(handleResponse).toHaveBeenCalled();
    });
  });

  describe('delete', () => {
    it('should call the correct endpoint with knowledge base ID and article ID', async () => {
      vi.mocked(axiosService.instance.delete).mockResolvedValue(mockResponse);

      const knowledgeBaseId = 'kb123';
      const articleId = 'article123';

      await ArticleAPI.delete(knowledgeBaseId, articleId);

      expect(axiosService.instance.delete).toHaveBeenCalledWith(
        `/v2/bases/${knowledgeBaseId}/articles/${articleId}`
      );
      expect(handleResponseWithError).toHaveBeenCalled();
    });
  });

  describe('getAnalytics', () => {
    it('should call the correct endpoint with article ID', async () => {
      vi.mocked(axiosService.instance.get).mockResolvedValue(mockResponse);

      const articleId = 'article123';

      await ArticleAPI.getAnalytics(articleId);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `/v2/statistics/articles/${articleId}?`
      );
      expect(handleResponseWithError).toHaveBeenCalled();
    });

    it('should include date range parameters when provided', async () => {
      vi.mocked(axiosService.instance.get).mockResolvedValue(mockResponse);

      const articleId = 'article123';
      const dateRange = { from: '2023-01-01', to: '2023-01-31' };

      await ArticleAPI.getAnalytics(articleId, dateRange);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `/v2/statistics/articles/${articleId}?from_time=${dateRange.from}&to_time=${dateRange.to}`
      );
      expect(handleResponseWithError).toHaveBeenCalled();
    });

    it('should include withOriginalArticle parameter when provided', async () => {
      vi.mocked(axiosService.instance.get).mockResolvedValue(mockResponse);

      const articleId = 'article123';
      const withOriginalArticle = true;

      await ArticleAPI.getAnalytics(articleId, undefined, withOriginalArticle);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `/v2/statistics/articles/${articleId}?withOriginArticle=true`
      );
      expect(handleResponseWithError).toHaveBeenCalled();
    });

    it('should include both date range and withOriginalArticle parameters when provided', async () => {
      vi.mocked(axiosService.instance.get).mockResolvedValue(mockResponse);

      const articleId = 'article123';
      const dateRange = { from: '2023-01-01', to: '2023-01-31' };
      const withOriginalArticle = true;

      await ArticleAPI.getAnalytics(articleId, dateRange, withOriginalArticle);

      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `/v2/statistics/articles/${articleId}?from_time=${dateRange.from}&to_time=${dateRange.to}&withOriginArticle=true`
      );
      expect(handleResponseWithError).toHaveBeenCalled();
    });
  });

  describe('import', () => {
    it('should call the correct endpoint with form data', async () => {
      vi.mocked(axiosService.instance.post).mockResolvedValue(mockResponse);

      const knowledgeBaseId = 'kb123';
      const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });

      await ArticleAPI.import(knowledgeBaseId, file);

      expect(axiosService.instance.post).toHaveBeenCalledWith(
        `/v2/bases/${knowledgeBaseId}/articles/import`,
        expect.any(FormData),
        expect.objectContaining({
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
      );
      expect(handleResponseWithError).toHaveBeenCalled();
    });
  });

  describe('createShortcut', () => {
    it('should call the correct endpoint with knowledge base ID and article ID', async () => {
      vi.mocked(axiosService.instance.post).mockResolvedValue(mockResponse);

      const articleId = 'article123';
      const kbId = 'kb456';

      const result = await ArticleAPI.createShortcut(articleId, kbId);

      expect(axiosService.instance.post).toHaveBeenCalledWith(
        `/v2/articles/${articleId}/shortcut`,
        { kbId }
      );
      expect(result).toEqual(mockResponse);
    });
  });
});
