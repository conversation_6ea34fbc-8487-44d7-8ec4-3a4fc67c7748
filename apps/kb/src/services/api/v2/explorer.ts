import { KB_EXPLORER_PATH } from '@/constants/api';
import { DEFAULT_EXPLORER_RETRIEVE_LIMIT } from '@/constants/explorer';
import { ROOT_PATH } from '@/constants/folder';
import type { ExplorerResponse, KnowledgeBaseDirectionQuery } from '@/types';
import { handleResponse } from '@/utils/api';
import { axiosService } from '@resola-ai/services-shared';

export const ExplorerAPI = {
  getList: (
    parentDirId: string = ROOT_PATH,
    cursor = '',
    direction: KnowledgeBaseDirectionQuery = 'backward',
    take: number = DEFAULT_EXPLORER_RETRIEVE_LIMIT
  ) => {
    return handleResponse<ExplorerResponse>(
      axiosService.instance.get(
        `${KB_EXPLORER_PATH}?parentDirId=${parentDirId}&cursor=${cursor}&direction=${direction}&take=${take}`
      )
    );
  },
};
