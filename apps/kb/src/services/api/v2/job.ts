import type { ISuccessListNextPreviousResponse, ISuccessResponse } from '@resola-ai/models';
import { axiosService } from '@resola-ai/services-shared';

import { KB_JOBS_PATH } from '@/constants/api';
import { DEFAULT_JOB_RETRIEVE_LIMIT } from '@/constants/job';
import { KBDirectionQueryEnum } from '@/types';
import {
  type GenerateJobPayload,
  type IJobsRequestParams,
  type Job,
  type JobDetailResultResponse,
  JobType,
} from '@/types/job';
import { handleResponseWithError } from '@/utils/api';

export const JobAPI = {
  getJobs: async ({
    cursor = '',
    direction = KBDirectionQueryEnum.Backward,
    take = DEFAULT_JOB_RETRIEVE_LIMIT,
    jobType,
  }: IJobsRequestParams) => {
    const queryParams = new URLSearchParams({
      cursor,
      direction,
      take: take.toString(),
    });

    if (jobType) {
      if (Array.isArray(jobType)) {
        // If jobType is an array, add each job type as a separate query parameter
        jobType.forEach((type) => {
          queryParams.append('jobType', type);
        });
      } else {
        // If jobType is a single value, add it as before
        queryParams.append('jobType', jobType);
      }
    }

    return handleResponseWithError<ISuccessListNextPreviousResponse<Job>>(
      axiosService.instance.get(`${KB_JOBS_PATH}?${queryParams.toString()}`)
    );
  },
  getJobDetail: async (jobId: string) => {
    return handleResponseWithError<Job>(axiosService.instance.get(`${KB_JOBS_PATH}/${jobId}`));
  },
  createJob: async (payload: GenerateJobPayload) => {
    return handleResponseWithError<Job>(axiosService.instance.post(`${KB_JOBS_PATH}`, payload));
  },
  createExportJob: async (payload: {
    metadata: {
      locationFilter: { baseIds: string[]; folderIds: string[] };
      fileType: 'csv' | 'excel';
    };
  }) => {
    const exportPayload: GenerateJobPayload = {
      jobType: JobType.ArticleExport,
      metadata: {
        documentIds: [],
        locationFilter: payload.metadata.locationFilter,
        fileType: payload.metadata.fileType,
      },
    };
    return handleResponseWithError<Job>(
      axiosService.instance.post(`${KB_JOBS_PATH}`, exportPayload)
    );
  },
  getJobResult: async (jobId: string) => {
    return handleResponseWithError<JobDetailResultResponse>(
      axiosService.instance.get(`${KB_JOBS_PATH}/${jobId}/result`),
      'data.data'
    );
  },
  retryJob: async (jobId: string) => {
    return axiosService.instance.post(`${KB_JOBS_PATH}/${jobId}/retry`);
  },
  deleteJob: async (jobId: string) => {
    return handleResponseWithError<ISuccessResponse<Job>>(
      axiosService.instance.delete(`${KB_JOBS_PATH}/${jobId}`)
    );
  },
};
