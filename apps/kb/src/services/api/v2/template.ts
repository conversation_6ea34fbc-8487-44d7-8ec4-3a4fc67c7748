import {
  DEFAULT_TEMPLATES_RETRIEVE_LIMIT,
  DEFAULT_TEMPLATE_FIELDS_RETRIEVE_LIMIT,
  KB_ARTICLE_TEMPLATE_PATH,
} from '@/constants/api';
import type { IPaginationRequestParams, KBTemplate, KBTemplateField } from '@/types';
import { handleResponse } from '@/utils/api';
import type { ISuccessListNextPreviousResponse, ISuccessResponse } from '@resola-ai/models';
import { axiosService } from '@resola-ai/services-shared';

export const TemplateAPI = {
  getArticleTemplate: async (templateId: string) => {
    return handleResponse<ISuccessResponse<KBTemplate>>(
      axiosService.instance.get(`${KB_ARTICLE_TEMPLATE_PATH}/${templateId}`)
    );
  },
  updateArticleTemplate: async (templateId: string, payload) => {
    return handleResponse<ISuccessResponse<KBTemplate>>(
      axiosService.instance.put(`${KB_ARTICLE_TEMPLATE_PATH}/${templateId}`, payload)
    );
  },
  createArticleTemplate: async (payload) => {
    return handleResponse<ISuccessResponse<KBTemplate>>(
      axiosService.instance.post(`${KB_ARTICLE_TEMPLATE_PATH}`, payload)
    );
  },
  deleteArticleTemplate: async (templateId: string) => {
    return handleResponse<ISuccessResponse<KBTemplate>>(
      axiosService.instance.delete(`${KB_ARTICLE_TEMPLATE_PATH}/${templateId}`)
    );
  },
  getArticleTemplates: async ({
    take = DEFAULT_TEMPLATES_RETRIEVE_LIMIT,
    direction = 'backward',
    cursor = '',
    query = '',
  }: IPaginationRequestParams) => {
    return handleResponse<ISuccessListNextPreviousResponse<KBTemplate>>(
      axiosService.instance.get(
        `${KB_ARTICLE_TEMPLATE_PATH}/?cursor=${cursor}&direction=${direction}&take=${take}&query=${query}`
      )
    );
  },
  getTemplateCustomField: async (templateId: string, templateFieldId: string) => {
    return handleResponse<ISuccessResponse<KBTemplateField>>(
      axiosService.instance.get(
        `${KB_ARTICLE_TEMPLATE_PATH}/${templateId}/customData/${templateFieldId}`
      )
    );
  },
  updateTemplateCustomField: async (templateId: string, templateFieldId: string, payload) => {
    return handleResponse<ISuccessResponse<KBTemplateField>>(
      axiosService.instance.put(
        `${KB_ARTICLE_TEMPLATE_PATH}/${templateId}/customData/${templateFieldId}`,
        payload
      )
    );
  },
  createTemplateCustomField: async (templateId: string, payload) => {
    return handleResponse<ISuccessResponse<KBTemplateField>>(
      axiosService.instance.post(`${KB_ARTICLE_TEMPLATE_PATH}/${templateId}/customData`, payload)
    );
  },
  getTemplateCustomFields: async ({
    templateId,
    take = DEFAULT_TEMPLATE_FIELDS_RETRIEVE_LIMIT,
    direction = 'backward',
    cursor = '',
    query = '',
  }: IPaginationRequestParams & { templateId: string }) => {
    return handleResponse<ISuccessListNextPreviousResponse<KBTemplateField>>(
      axiosService.instance.get(
        `${KB_ARTICLE_TEMPLATE_PATH}/${templateId}/customData?cursor=${cursor}&direction=${direction}&take=${take}&query=${query}`
      )
    );
  },
  deleteTemplateCustomField: async (templateId: string, templateFieldId: string) => {
    return handleResponse<ISuccessResponse<KBTemplateField>>(
      axiosService.instance.delete(
        `${KB_ARTICLE_TEMPLATE_PATH}/${templateId}/customData/${templateFieldId}`
      )
    );
  },
};
