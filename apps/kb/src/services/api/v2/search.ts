import { KB_AUTOCOMPLETE_PATH, KB_SEARCH_PATH } from '@/constants/api';
import {
  type SearchFilters,
  type SearchHistory,
  type SearchResult,
  type SearchType,
  SearchTypeEnums,
  type User,
} from '@/types';
import { handleResponse } from '@/utils/api';
import type { ISuccessListNextPreviousResponse, ISuccessResponse } from '@resola-ai/models';
import { axiosService } from '@resola-ai/services-shared';

export const SearchAPI = {
  search: async (
    query = '',
    folderId = '',
    entities: SearchType[] = [
      SearchTypeEnums.folder,
      SearchTypeEnums.base,
      SearchTypeEnums.article,
      SearchTypeEnums.document,
    ],
    filters?: SearchFilters
  ) => {
    return handleResponse<ISuccessListNextPreviousResponse<SearchResult>>(
      axiosService.instance.post(`${KB_SEARCH_PATH}`, {
        query,
        dirId: folderId,
        entities,
        filters,
      })
    );
  },
  autocompleteCustomData: async () => {
    return handleResponse<ISuccessResponse<string[]>>(
      axiosService.instance.get(`${KB_AUTOCOMPLETE_PATH}/customDataKeys`)
    );
  },
  searchHistory: async () => {
    return handleResponse<ISuccessResponse<SearchHistory[]>>(
      axiosService.instance.get(`${KB_AUTOCOMPLETE_PATH}/searchHistory`)
    );
  },
  getSearchUsers: async () => {
    return handleResponse<ISuccessResponse<User[]>>(
      axiosService.instance.get(`${KB_AUTOCOMPLETE_PATH}/users`)
    );
  },
};
