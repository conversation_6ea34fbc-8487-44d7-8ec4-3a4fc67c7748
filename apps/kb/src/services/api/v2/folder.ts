import type {
  ISuccessListNextPreviousResponse,
  ISuccessListResponseWithoutPagination,
  ISuccessResponse,
} from '@resola-ai/models';
import { axiosService } from '@resola-ai/services-shared';

import { KB_FOLDERS_PATH, KB_RECENTLY_VIEWED_PATH } from '@/constants/api';
import type { Folder, Recent } from '@/types/tree';
import { handleResponse } from '@/utils/api';

export const FolderAPI = {
  getAll: async (parentDirId: string, depth = 2) => {
    return handleResponse<ISuccessListResponseWithoutPagination<Folder>>(
      axiosService.instance.get(`${KB_FOLDERS_PATH}?parentDirId=${parentDirId}&depth=${depth}`)
    );
  },
  create: async (folder: Partial<Folder>) => {
    return handleResponse<ISuccessResponse<Folder>>(
      axiosService.instance.post(K<PERSON>_FOLDERS_PATH, folder)
    );
  },
  update: async (folder: Partial<Folder>) => {
    return handleResponse<ISuccessResponse<Folder>>(
      axiosService.instance.put(`${KB_FOLDERS_PATH}${folder.id}`, folder)
    );
  },
  get: async (folderId: string) => {
    return axiosService.instance.get(`${KB_FOLDERS_PATH}${folderId}`);
  },
  remove: async (folderId: string) => {
    return handleResponse<string>(axiosService.instance.delete(`${KB_FOLDERS_PATH}${folderId}`));
  },
  recent: async () => {
    return handleResponse<ISuccessListNextPreviousResponse<Recent>>(
      axiosService.instance.get(`${KB_RECENTLY_VIEWED_PATH}?direction=backward`)
    );
  },
};
