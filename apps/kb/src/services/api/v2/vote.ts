import { KB_ARTICLES_PATH } from '@/constants/api';
import type { ArticleVote, VoteType } from '@/types';
import { handleResponse } from '@/utils/api';
import type { ISuccessResponse } from '@resola-ai/models';
import { axiosService } from '@resola-ai/services-shared';

export const VoteAPI = {
  getArticleVote: async (articleId: string) => {
    return handleResponse<ISuccessResponse<ArticleVote>>(
      axiosService.instance.get(`${KB_ARTICLES_PATH}/${articleId}/feedback`)
    );
  },
  updateArticleVote: async (articleId: string, feedbackType: VoteType) => {
    return axiosService.instance.put(`${KB_ARTICLES_PATH}/${articleId}/feedback`, {
      feedbackType,
    });
  },
};
