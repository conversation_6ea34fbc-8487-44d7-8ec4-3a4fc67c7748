import { KB_DOCUMENT_PATH } from '@/constants/api';
import { DEFAULT_KB_RETRIEVE_LIMIT } from '@/constants/kb';
import type {
  DocumentFile,
  FILE_UPLOAD_STATUS,
  FileLink,
  FileMetadata,
  KnowledgeBaseDirectionQuery,
  PresignedURLResult,
} from '@/types';
import { handleResponse } from '@/utils/api';
import type { ISuccessListNextPreviousResponse } from '@resola-ai/models';
import { axiosService } from '@resola-ai/services-shared';

export const DocumentAPI = {
  get: (id: string, resolveBreadcrumb = false) => {
    return axiosService.instance.get(
      `${KB_DOCUMENT_PATH}/${id}?resolveBreadcrumb=${resolveBreadcrumb}`
    );
  },
  delete: (id: string) => {
    return axiosService.instance.delete(`${KB_DOCUMENT_PATH}/${id}`);
  },
  put: (id: string, payload: Partial<DocumentFile>) => {
    return handleResponse<DocumentFile>(
      axiosService.instance.put(`${KB_DOCUMENT_PATH}/${id}`, payload),
      'data.data'
    );
  },
  patch: (id: string, payload: { status: FILE_UPLOAD_STATUS }) => {
    return handleResponse<DocumentFile>(
      axiosService.instance.patch(`${KB_DOCUMENT_PATH}/${id}`, payload),
      'data.data'
    );
  },
  getLink: async (id: string) => {
    return handleResponse<FileLink>(
      axiosService.instance.post(`${KB_DOCUMENT_PATH}/${id}/link`),
      'data.data'
    );
  },
  getPresignedUrl: async (payload: FileMetadata[]) => {
    return handleResponse<{ uploadResults: PresignedURLResult[] }>(
      axiosService.instance.post(`${KB_DOCUMENT_PATH}/generateUploadUrl`, payload),
      'data.data'
    );
  },
  getDocuments: (
    parentDirId = '',
    cursor = '',
    direction: KnowledgeBaseDirectionQuery = 'backward',
    take: number = DEFAULT_KB_RETRIEVE_LIMIT
  ) => {
    return handleResponse<ISuccessListNextPreviousResponse<DocumentFile>>(
      axiosService.instance.get(
        `${KB_DOCUMENT_PATH}?parentDirId=${parentDirId}&cursor=${cursor}&direction=${direction}&take=${take}`
      )
    );
  },
};
