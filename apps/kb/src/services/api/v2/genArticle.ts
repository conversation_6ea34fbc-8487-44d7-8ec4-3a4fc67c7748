import type { ISuccessListNextPreviousResponse, ISuccessResponse } from '@resola-ai/models';
import { axiosService } from '@resola-ai/services-shared';

import { KB_GEN_ARTICLE_PATH } from '@/constants/api';
import type { ArticleGeneratorActivity, JobArticle, JobArticleDownloadFormat } from '@/types/job';
import { handleResponseWithError } from '@/utils/api';

export const GenArticleAPI = {
  update: async (articleId: string, payload: any) => {
    return handleResponseWithError<ISuccessResponse<JobArticle>>(
      axiosService.instance.put(`${KB_GEN_ARTICLE_PATH}/${articleId}`, payload)
    );
  },
  saveToKB: async (baseId: string, articleIds: string[]) => {
    return handleResponseWithError<ISuccessResponse<JobArticle>>(
      axiosService.instance.post(`${KB_GEN_ARTICLE_PATH}/bases/${baseId}`, {
        articleIds,
      })
    );
  },
  getHistory: async (baseId: string) => {
    return handleResponseWithError<ISuccessListNextPreviousResponse<ArticleGeneratorActivity>>(
      axiosService.instance.get(`${KB_GEN_ARTICLE_PATH}/${baseId}/history`)
    );
  },
  download: async (jobId: string, format: JobArticleDownloadFormat, options?: any) => {
    const params = new URLSearchParams();
    params.append('job_id', jobId);
    params.append('download', 'true');
    params.append('data_format', format);

    return axiosService.instance.get(`${KB_GEN_ARTICLE_PATH}?${params.toString()}`, options);
  },
};
