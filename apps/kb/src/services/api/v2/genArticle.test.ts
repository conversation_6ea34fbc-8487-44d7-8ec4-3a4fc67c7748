import { KB_GEN_ARTICLE_PATH } from '@/constants/api';
import type { JobArticleDownloadFormat } from '@/types/job';
import { handleResponseWithError } from '@/utils/api';
import { axiosService } from '@resola-ai/services-shared';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { GenArticleAPI } from './genArticle';

// Mock dependencies
vi.mock('@resola-ai/services-shared', () => ({
  axiosService: {
    instance: {
      put: vi.fn(),
      post: vi.fn(),
      get: vi.fn(),
    },
  },
}));

vi.mock('@/utils/api', () => ({
  handleResponseWithError: vi.fn(),
}));

// Constants and mock data
const MOCK_ARTICLE_ID = 'article-123';
const MOCK_BASE_ID = 'base-456';
const MOCK_JOB_ID = 'job-789';
const MOCK_ARTICLE_IDS = ['article-123', 'article-456'];
const MOCK_PAYLOAD = { title: 'Updated Article', content: 'New content' };
const MOCK_RESPONSE = { data: { id: MOCK_ARTICLE_ID, title: 'Article Title' } };

describe('GenArticleAPI', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('update', () => {
    it('should call the correct endpoint with the right payload', async () => {
      // Setup
      vi.mocked(axiosService.instance.put).mockResolvedValueOnce(MOCK_RESPONSE);
      vi.mocked(handleResponseWithError).mockResolvedValueOnce(MOCK_RESPONSE);

      // Execute
      const result = await GenArticleAPI.update(MOCK_ARTICLE_ID, MOCK_PAYLOAD);

      // Verify
      expect(axiosService.instance.put).toHaveBeenCalledWith(
        `${KB_GEN_ARTICLE_PATH}/${MOCK_ARTICLE_ID}`,
        MOCK_PAYLOAD
      );
      expect(handleResponseWithError).toHaveBeenCalled();
      expect(result).toEqual(MOCK_RESPONSE);
    });

    it('should propagate errors from the API call', async () => {
      // Setup
      const mockError = new Error('API Error');
      vi.mocked(axiosService.instance.put).mockRejectedValueOnce(mockError);
      vi.mocked(handleResponseWithError).mockRejectedValueOnce(mockError);

      // Execute & Verify
      await expect(GenArticleAPI.update(MOCK_ARTICLE_ID, MOCK_PAYLOAD)).rejects.toThrow(
        'API Error'
      );
    });
  });

  describe('saveToKB', () => {
    it('should call the correct endpoint with the right payload', async () => {
      // Setup
      vi.mocked(axiosService.instance.post).mockResolvedValueOnce(MOCK_RESPONSE);
      vi.mocked(handleResponseWithError).mockResolvedValueOnce(MOCK_RESPONSE);

      // Execute
      const result = await GenArticleAPI.saveToKB(MOCK_BASE_ID, MOCK_ARTICLE_IDS);

      // Verify
      expect(axiosService.instance.post).toHaveBeenCalledWith(
        `${KB_GEN_ARTICLE_PATH}/bases/${MOCK_BASE_ID}`,
        { articleIds: MOCK_ARTICLE_IDS }
      );
      expect(handleResponseWithError).toHaveBeenCalled();
      expect(result).toEqual(MOCK_RESPONSE);
    });

    it('should propagate errors from the API call', async () => {
      // Setup
      const mockError = new Error('API Error');
      vi.mocked(axiosService.instance.post).mockRejectedValueOnce(mockError);
      vi.mocked(handleResponseWithError).mockRejectedValueOnce(mockError);

      // Execute & Verify
      await expect(GenArticleAPI.saveToKB(MOCK_BASE_ID, MOCK_ARTICLE_IDS)).rejects.toThrow(
        'API Error'
      );
    });
  });

  describe('getHistory', () => {
    it('should call the correct endpoint', async () => {
      // Setup
      vi.mocked(axiosService.instance.get).mockResolvedValueOnce(MOCK_RESPONSE);
      vi.mocked(handleResponseWithError).mockResolvedValueOnce(MOCK_RESPONSE);

      // Execute
      const result = await GenArticleAPI.getHistory(MOCK_BASE_ID);

      // Verify
      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `${KB_GEN_ARTICLE_PATH}/${MOCK_BASE_ID}/history`
      );
      expect(handleResponseWithError).toHaveBeenCalled();
      expect(result).toEqual(MOCK_RESPONSE);
    });

    it('should propagate errors from the API call', async () => {
      // Setup
      const mockError = new Error('API Error');
      vi.mocked(axiosService.instance.get).mockRejectedValueOnce(mockError);
      vi.mocked(handleResponseWithError).mockRejectedValueOnce(mockError);

      // Execute & Verify
      await expect(GenArticleAPI.getHistory(MOCK_BASE_ID)).rejects.toThrow('API Error');
    });
  });

  describe('download', () => {
    it('should call the correct endpoint with CSV format', async () => {
      // Setup
      const format: JobArticleDownloadFormat = 'csv';
      vi.mocked(axiosService.instance.get).mockResolvedValueOnce(MOCK_RESPONSE);

      // Execute
      const result = await GenArticleAPI.download(MOCK_JOB_ID, format);

      // Verify
      const expectedUrl = `${KB_GEN_ARTICLE_PATH}?job_id=${MOCK_JOB_ID}&download=true&data_format=${format}`;
      expect(axiosService.instance.get).toHaveBeenCalledWith(expectedUrl, undefined);
      expect(result).toEqual(MOCK_RESPONSE);
    });

    it('should call the correct endpoint with Excel format and options', async () => {
      // Setup
      const format: JobArticleDownloadFormat = 'excel';
      const options = { responseType: 'arraybuffer' };
      vi.mocked(axiosService.instance.get).mockResolvedValueOnce(MOCK_RESPONSE);

      // Execute
      const result = await GenArticleAPI.download(MOCK_JOB_ID, format, options);

      // Verify
      const expectedUrl = `${KB_GEN_ARTICLE_PATH}?job_id=${MOCK_JOB_ID}&download=true&data_format=${format}`;
      expect(axiosService.instance.get).toHaveBeenCalledWith(expectedUrl, options);
      expect(result).toEqual(MOCK_RESPONSE);
    });

    it('should propagate errors from the API call', async () => {
      // Setup
      const format: JobArticleDownloadFormat = 'csv';
      const mockError = new Error('API Error');
      vi.mocked(axiosService.instance.get).mockRejectedValueOnce(mockError);

      // Execute & Verify
      await expect(GenArticleAPI.download(MOCK_JOB_ID, format)).rejects.toThrow('API Error');
    });
  });
});
