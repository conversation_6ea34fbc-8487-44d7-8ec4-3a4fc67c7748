import { K<PERSON>_JOBS_PATH } from '@/constants/api';
import { DEFAULT_JOB_RETRIEVE_LIMIT } from '@/constants/job';
import { KBDirectionQueryEnum } from '@/types';
import { JobType } from '@/types/job';
import { axiosService } from '@resola-ai/services-shared';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { JobAPI } from './job';

// Test constants
const TEST_VALUES = {
  JOB_ID: 'job-123',
  CURSOR: 'cursor-value',
  JOB_TYPE: JobType.ContentGeneration,
  MOCK_JOB: {
    id: 'job-123',
    title: 'Test Job',
    status: 'completed',
    createdAt: '2023-01-01T00:00:00Z',
  },
  MOCK_JOBS_RESPONSE: {
    status: 'success',
    data: {
      items: [
        {
          id: 'job-123',
          title: 'Test Job 1',
          status: 'completed',
          createdAt: '2023-01-01T00:00:00Z',
        },
        {
          id: 'job-456',
          title: 'Test Job 2',
          status: 'pending',
          createdAt: '2023-01-02T00:00:00Z',
        },
      ],
      next: 'next-cursor',
      previous: 'prev-cursor',
    },
  },
  MOCK_JOB_RESULT: {
    status: 'success',
    data: {
      data: [
        {
          id: 'article-1',
          title: 'Test Article 1',
          content: 'Test content 1',
        },
        {
          id: 'article-2',
          title: 'Test Article 2',
          content: 'Test content 2',
        },
      ],
    },
  },
  GENERATE_JOB_PAYLOAD: {
    title: 'New Job',
    prompt: 'Generate articles about AI',
    jobType: JobType.ContentGeneration,
    metadata: {
      documentIds: ['doc-1', 'doc-2'],
      customPrompt: {
        title: 'Test Prompt',
        content: 'Content generation prompt',
      },
    },
  },
};

// Mock axiosService
vi.mock('@resola-ai/services-shared', () => ({
  axiosService: {
    instance: {
      get: vi.fn(),
      post: vi.fn(),
      delete: vi.fn(),
    },
  },
  logger: {
    error: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
  },
}));

describe('JobAPI', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('getJobs', () => {
    it('should fetch jobs with default parameters', async () => {
      // Mock successful response
      (axiosService.instance.get as any).mockResolvedValue({
        data: TEST_VALUES.MOCK_JOBS_RESPONSE,
      });

      // Call the API
      const result = await JobAPI.getJobs({});

      // Verify the API was called with correct parameters
      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `${KB_JOBS_PATH}?cursor=&direction=${KBDirectionQueryEnum.Backward}&take=${DEFAULT_JOB_RETRIEVE_LIMIT}`
      );

      // Verify the result
      expect(result).toEqual(TEST_VALUES.MOCK_JOBS_RESPONSE);
    });

    it('should fetch jobs with custom parameters', async () => {
      // Mock successful response
      (axiosService.instance.get as any).mockResolvedValue({
        data: TEST_VALUES.MOCK_JOBS_RESPONSE,
      });

      // Call the API with custom parameters
      const result = await JobAPI.getJobs({
        cursor: TEST_VALUES.CURSOR,
        direction: KBDirectionQueryEnum.Forward,
        take: 5,
        jobType: TEST_VALUES.JOB_TYPE,
      });

      // Verify the API was called with correct parameters
      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `${KB_JOBS_PATH}?cursor=${TEST_VALUES.CURSOR}&direction=${KBDirectionQueryEnum.Forward}&take=5&jobType=${TEST_VALUES.JOB_TYPE}`
      );

      // Verify the result
      expect(result).toEqual(TEST_VALUES.MOCK_JOBS_RESPONSE);
    });

    it('should fetch jobs with multiple job types', async () => {
      // Mock successful response
      (axiosService.instance.get as any).mockResolvedValue({
        data: TEST_VALUES.MOCK_JOBS_RESPONSE,
      });

      // Call the API with an array of job types
      const result = await JobAPI.getJobs({
        cursor: TEST_VALUES.CURSOR,
        direction: KBDirectionQueryEnum.Forward,
        take: 5,
        jobType: [JobType.ContentGeneration, JobType.ArticleExport],
      });

      // Verify the API was called with correct parameters
      // We can't predict the exact order of parameters in the URL string
      // so we'll check that the call was made and then verify each parameter is present
      expect(axiosService.instance.get).toHaveBeenCalled();
      const callArg = (axiosService.instance.get as any).mock.calls[0][0];
      expect(callArg).toContain(`${KB_JOBS_PATH}?`);
      expect(callArg).toContain(`cursor=${TEST_VALUES.CURSOR}`);
      expect(callArg).toContain(`direction=${KBDirectionQueryEnum.Forward}`);
      expect(callArg).toContain('take=5');
      expect(callArg).toContain(`jobType=${JobType.ContentGeneration}`);
      expect(callArg).toContain(`jobType=${JobType.ArticleExport}`);

      // Verify the result
      expect(result).toEqual(TEST_VALUES.MOCK_JOBS_RESPONSE);
    });

    it('should handle API errors', async () => {
      // Mock error response
      const errorResponse = new Error('Network Error');
      (axiosService.instance.get as any).mockRejectedValueOnce(errorResponse);

      // Call the API and verify it rejects with the error
      await expect(JobAPI.getJobs({})).rejects.toBe(errorResponse);
    });
  });

  describe('getJobDetail', () => {
    it('should fetch job details by ID', async () => {
      // Mock successful response
      (axiosService.instance.get as any).mockResolvedValue({
        data: {
          status: 'success',
          data: TEST_VALUES.MOCK_JOB,
        },
      });

      // Call the API
      const result = await JobAPI.getJobDetail(TEST_VALUES.JOB_ID);

      // Verify the API was called with correct parameters
      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `${KB_JOBS_PATH}/${TEST_VALUES.JOB_ID}`
      );

      // Verify the result
      expect(result).toEqual({
        status: 'success',
        data: TEST_VALUES.MOCK_JOB,
      });
    });
  });

  describe('createJob', () => {
    it('should create a new job with the provided payload', async () => {
      // Mock successful response
      (axiosService.instance.post as any).mockResolvedValue({
        data: {
          status: 'success',
          data: TEST_VALUES.MOCK_JOB,
        },
      });

      // Call the API
      const result = await JobAPI.createJob(TEST_VALUES.GENERATE_JOB_PAYLOAD);

      // Verify the API was called with correct parameters
      expect(axiosService.instance.post).toHaveBeenCalledWith(
        KB_JOBS_PATH,
        TEST_VALUES.GENERATE_JOB_PAYLOAD
      );

      // Verify the result
      expect(result).toEqual({
        status: 'success',
        data: TEST_VALUES.MOCK_JOB,
      });
    });
  });

  describe('getJobResult', () => {
    it('should fetch job results by ID', async () => {
      // Mock successful response
      (axiosService.instance.get as any).mockResolvedValue({
        data: TEST_VALUES.MOCK_JOB_RESULT,
      });

      // Call the API
      const result = await JobAPI.getJobResult(TEST_VALUES.JOB_ID);

      // Verify the API was called with correct parameters
      expect(axiosService.instance.get).toHaveBeenCalledWith(
        `${KB_JOBS_PATH}/${TEST_VALUES.JOB_ID}/result`
      );

      // Verify the result
      expect(result).toEqual({ data: TEST_VALUES.MOCK_JOB_RESULT.data.data });
    });
  });

  describe('retryJob', () => {
    it('should retry a job by ID', async () => {
      // Mock successful response
      const mockResponse = { data: { status: 'success' } };
      (axiosService.instance.post as any).mockResolvedValue(mockResponse);

      // Call the API
      const result = await JobAPI.retryJob(TEST_VALUES.JOB_ID);

      // Verify the API was called with correct parameters
      expect(axiosService.instance.post).toHaveBeenCalledWith(
        `${KB_JOBS_PATH}/${TEST_VALUES.JOB_ID}/retry`
      );

      // Verify the result
      expect(result).toEqual(mockResponse);
    });
  });

  describe('deleteJob', () => {
    it('should delete a job by ID', async () => {
      // Mock successful response
      (axiosService.instance.delete as any).mockResolvedValue({
        data: {
          status: 'success',
          data: TEST_VALUES.MOCK_JOB,
        },
      });

      // Call the API
      const result = await JobAPI.deleteJob(TEST_VALUES.JOB_ID);

      // Verify the API was called with correct parameters
      expect(axiosService.instance.delete).toHaveBeenCalledWith(
        `${KB_JOBS_PATH}/${TEST_VALUES.JOB_ID}`
      );

      // Verify the result
      expect(result).toEqual({
        status: 'success',
        data: TEST_VALUES.MOCK_JOB,
      });
    });
  });
});
