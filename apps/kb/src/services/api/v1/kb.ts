import { KB_API_PATH_V1 } from '@/constants/api';
import type { KnowledgeBase, KnowledgeBaseDirectionQuery } from '@/types';
import type { ISuccessListNextPreviousResponse, ISuccessResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';

export const KbAPI = {
  getList: async (cursor, direction: KnowledgeBaseDirectionQuery, take: number) => {
    try {
      const response = await axiosService.instance.get<
        ISuccessListNextPreviousResponse<KnowledgeBase>
      >(`${KB_API_PATH_V1}/?cursor=${cursor}&direction=${direction}&take=${take}`);
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  get: async (id: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<KnowledgeBase>>(
        `${KB_API_PATH_V1}/${id}/`
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  create: async (payload) => {
    try {
      const response = await axiosService.instance.post<KnowledgeBase>(
        `${KB_API_PATH_V1}/`,
        payload
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  update: async (id: string, payload) => {
    try {
      const response = await axiosService.instance.put<KnowledgeBase>(
        `${KB_API_PATH_V1}/${id}/`,
        payload
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
};
