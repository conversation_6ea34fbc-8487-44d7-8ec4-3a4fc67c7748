import { KB_API_PATH_V1 } from '@/constants/api';
import type { KnowledgeBaseDirectionQuery } from '@/types';
import type { QnA, QnAGenerateRequest, QnAJob, QnAStatus } from '@/types/qna';
import type { ISuccessListNextPreviousResponse, ISuccessResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';

export const QnaAPI = {
  getList: async (
    cursor,
    direction: KnowledgeBaseDirectionQuery,
    take: number,
    kbId: string,
    status?: QnAStatus,
    qaGenId?: string
  ) => {
    try {
      const statusQuery = status ? `&status=${status}` : '';
      const qaGenIdQuery = qaGenId ? `&qa_gen_id=${qaGenId}` : '';

      const response = await axiosService.instance.get<ISuccessListNextPreviousResponse<QnA>>(
        `${KB_API_PATH_V1}/${kbId}/qnas?cursor=${cursor}&direction=${direction}&take=${take}${statusQuery}${qaGenIdQuery}`
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  create: async (kbId: string, payload) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<QnA>>(
        `${KB_API_PATH_V1}/${kbId}/qnas`,
        payload
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  update: async (kbId: string, qnaId: string, payload) => {
    try {
      const response = await axiosService.instance.put<ISuccessResponse<QnA>>(
        `${KB_API_PATH_V1}/${kbId}/qnas/${qnaId}`,
        payload
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  remove: async (kbId: string, qnaId: string) => {
    try {
      const response = await axiosService.instance.delete<ISuccessResponse<QnA>>(
        `${KB_API_PATH_V1}/${kbId}/qnas/${qnaId}`
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  import: async (kbId: string, formData: FormData) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<QnA>>(
        `${KB_API_PATH_V1}/${kbId}/qnas:import`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  generateQnA: async (req: QnAGenerateRequest) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<QnAJob>>(
        `${KB_API_PATH_V1}/${req.kbId}/actions/generate`,
        {
          customPrompt: req.customPrompt || '',
          docKbId: req.documentId,
        }
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  saveGeneratedQnA: async (kbId: string, ids: Array<string>, qaGenId: string) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<QnA>>(
        `${KB_API_PATH_V1}/${kbId}/actions/save`,
        {
          ids,
          qaGenId,
        }
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  getJobStatus: async (kbId: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<QnAJob>>(
        `${KB_API_PATH_V1}/${kbId}/actions/jobs`
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  cancelGenerate: async (kbId: string) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<QnAJob>>(
        `${KB_API_PATH_V1}/${kbId}/actions/cancel`
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
};
