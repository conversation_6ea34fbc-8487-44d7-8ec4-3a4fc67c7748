import { KB_API_PATH_V1 } from '@/constants/api';
import type {
  DocumentFile,
  FileLink,
  FileMetadataV1,
  KnowledgeBaseDirectionQuery,
  PresignedURLResultV1,
} from '@/types';
import type { ISuccessListNextPreviousResponse, ISuccessResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';
import get from 'lodash/get';

const filesPath = (kbId: string) => `${KB_API_PATH_V1}/${kbId}/files`;

export const FileAPI = {
  getList: async (kbId: string, cursor, direction: KnowledgeBaseDirectionQuery, take: number) => {
    try {
      const response = await axiosService.instance.get<
        ISuccessListNextPreviousResponse<DocumentFile>
      >(`${filesPath(kbId)}/?cursor=${cursor}&direction=${direction}&take=${take}`);
      return response.data;
    } catch (error) {
      logger.error(error);
      return get(error, 'response', undefined);
    }
  },
  get: async (kbId: string, id: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<DocumentFile>>(
        `${filesPath(kbId)}/${id}/`
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  delete: async (kbId: string, id: string) => {
    try {
      await axiosService.instance.delete(`${filesPath(kbId)}/${id}`);
    } catch (error) {
      logger.error(error);
    }
  },
  patch: async (kbId: string, id: string, payload) => {
    try {
      const response = await axiosService.instance.patch<DocumentFile>(
        `${filesPath(kbId)}/${id}/`,
        payload
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  getLink: async (kbId: string, id: string) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<FileLink>>(
        `${filesPath(kbId)}/${id}/link`
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  getPresignedUrl: async (kbId: string, payload: FileMetadataV1[]) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<PresignedURLResultV1>>(
        `${filesPath(kbId)}/generateUploadUrl`,
        payload
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  upload: async (presignedUrl: string, file: File) => {
    try {
      await fetch(presignedUrl, {
        method: 'PUT',
        body: file,
      });
    } catch (error) {
      console.error('Error uploading file:', error);
    }
  },
};
