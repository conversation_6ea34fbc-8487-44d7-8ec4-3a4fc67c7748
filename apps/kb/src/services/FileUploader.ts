import { type ExtraDataFile, FILE_UPLOAD_STATUS } from '@/types/file';

const COMPLETED_PERCENTAGE = 100;

export class FileUploader {
  fileId: string;
  uploadURL: string;
  file: File;
  status: FILE_UPLOAD_STATUS;
  percentage: number;
  extraDataFile?: ExtraDataFile;

  constructor(fileId, uploadURL, file, extraDataFile?: ExtraDataFile) {
    this.fileId = fileId;
    this.uploadURL = uploadURL;
    this.file = file;
    this.status = FILE_UPLOAD_STATUS.unknown;
    this.percentage = 0;
    this.extraDataFile = extraDataFile;
  }

  /**
   * Upload the file to the presigned URL
   * @param onCompleted - Callback function to execute when the upload is completed
   * @return Promise<FileUploadResult>
   * @property fileId - File ID
   * @property status - File upload status
   * @property percentage - File upload percentage
   * @example
   * const result = await uploader.upload({ onCompleted: (fileId, status, percentage) => {} });
   */
  upload({ onCompleted, onUploadProgress, onError, onTimeout }) {
    return new Promise((resolve, reject) => {
      try {
        const xhr = new XMLHttpRequest();
        this.status = FILE_UPLOAD_STATUS.unknown;
        this.percentage = 0;

        /**
         * Calculate the percentage number via XHR upload tracking,
         * then sync to the progress bar of the uploader
         * @param event - Progress event
         * @return void
         */
        xhr.upload.addEventListener(
          'progress',
          (event) => {
            if (event.lengthComputable) {
              const percentage = Math.round((event.loaded * 100) / event.total);
              this.percentage = percentage;
              const payload = {
                fileId: this.fileId,
                status: this.status,
                percentage: this.percentage,
              };
              onUploadProgress?.(payload);
              resolve(payload);
            }
          },
          false
        );

        /**
         * Handle the completion of the file upload
         * @return void
         */
        xhr.upload.addEventListener(
          'load',
          async () => {
            this.percentage = COMPLETED_PERCENTAGE;
            onUploadProgress?.({
              fileId: this.fileId,
              status: this.status,
              percentage: this.percentage,
            });

            let hasStatus = !!xhr.status;
            // await until the status is populated
            let attempt = 0;
            const maxAttempts = 20;
            const interval = 200;
            while (!hasStatus && attempt < maxAttempts) {
              await new Promise((resolve) => setTimeout(resolve, interval));
              hasStatus = !!xhr.status;
              attempt++;
            }

            if (xhr.status < 200 || xhr.status >= 300) {
              this.status = FILE_UPLOAD_STATUS.failed;
              const payload = {
                fileId: this.fileId,
                status: this.status,
                percentage: this.percentage,
              };
              onError?.(payload);
              reject(payload);
              return;
            }

            this.status = FILE_UPLOAD_STATUS.uploaded;
            // Sync the uploader status with the state to the single File Uploader
            const payload = {
              fileId: this.fileId,
              status: this.status,
              percentage: this.percentage,
            };
            onCompleted?.(payload);
            resolve(payload);
          },
          false
        );

        xhr.onerror = () => {
          this.status = FILE_UPLOAD_STATUS.failed;
          this.percentage = COMPLETED_PERCENTAGE;
          const payload = {
            fileId: this.fileId,
            status: this.status,
            percentage: this.percentage,
          };
          onError?.(payload);
          reject(payload);
        };

        xhr.ontimeout = () => {
          this.status = FILE_UPLOAD_STATUS.failed;
          this.percentage = COMPLETED_PERCENTAGE;
          const payload = {
            fileId: this.fileId,
            status: this.status,
            percentage: this.percentage,
          };
          onTimeout?.(payload);
          reject(payload);
        };

        xhr.open('PUT', this.uploadURL);
        xhr.setRequestHeader('Content-Type', this.file.type);
        xhr.overrideMimeType('text/plain; charset=x-user-defined-binary');
        xhr.send(this.file);
      } catch (error) {
        this.status = FILE_UPLOAD_STATUS.failed;

        // Sync the uploader status with the state to the single File Uploader
        const payload = {
          fileId: this.fileId,
          status: this.status,
          percentage: this.percentage,
        };
        onError?.(payload);
        reject(payload);
      }
    });
  }
}
