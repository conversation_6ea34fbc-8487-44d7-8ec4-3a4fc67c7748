import { type SearchFilters, type SearchType, SearchTypeEnums } from '@/types';

const SearchSuggestionTypes = {
  STATIC: 'static',
  DYNAMIC: 'dynamic',
} as const;

type SearchSuggestionType = (typeof SearchSuggestionTypes)[keyof typeof SearchSuggestionTypes];

interface SearchSuggestion {
  key: string;
  text: string; // Translated display text
  value: string; // Original/untranslated value
  type: SearchSuggestionType;
}

type SuggestionFieldItem = {
  [key: string]: string | undefined;
};

interface StaticTemplate {
  key: string;
  text: string;
  value: string;
  filters: {
    entities?: SearchType[];
    fields?: SuggestionFieldItem;
  };
}

export enum SearchEngineLanguage {
  EN = 'en',
  JA = 'ja',
}

const replaceQueryInTemplate = (template: string, query: string) => {
  return template.replace('{query}', query);
};

export class SearchEngine {
  private static readonly STATIC_TYPES: Record<SearchEngineLanguage, StaticTemplate[]> = {
    [SearchEngineLanguage.EN]: [
      {
        key: 'is:document',
        text: 'is:document {query}',
        value: 'is:document {query}',
        filters: { entities: ['document'] },
      },
      {
        key: 'is:article',
        text: 'is:article {query}',
        value: 'is:article {query}',
        filters: { entities: ['article'] },
      },
      {
        key: 'is:folder',
        text: 'is:folder {query}',
        value: 'is:folder {query}',
        filters: { entities: ['folder'] },
      },
      {
        key: 'is:knowledgebase',
        text: 'is:knowledgebase {query}',
        value: 'is:knowledgebase {query}',
        filters: { entities: ['base'] },
      },
    ],
    [SearchEngineLanguage.JA]: [
      {
        key: 'is:document',
        text: '種類:文書 {query}',
        value: 'is:document {query}',
        filters: { entities: ['document'] },
      },
      {
        key: 'is:article',
        text: '種類:記事 {query}',
        value: 'is:article {query}',
        filters: { entities: ['article'] },
      },
      {
        key: 'is:folder',
        text: '種類:フォルダ {query}',
        value: 'is:folder {query}',
        filters: { entities: ['folder'] },
      },
      {
        key: 'is:knowledgebase',
        text: '種類:ナレッジベース {query}',
        value: 'is:knowledgebase {query}',
        filters: { entities: ['base'] },
      },
    ],
  };

  private static readonly STATIC_FIELD_TEMPLATES = {
    [SearchEngineLanguage.EN]: [
      {
        key: 'title',
        text: 'title: {query}',
        value: 'title: {query}',
        filters: { fields: { title: '{query}' } },
      },
      {
        key: 'content',
        text: 'content: {query}',
        value: 'content: {query}',
        filters: { fields: { content: '{query}' } },
      },
      {
        key: 'key phrase',
        text: 'key phrase: {query}',
        value: 'key phrase: {query}',
        filters: { fields: { keyword: '{query}' } },
      },
    ],
    [SearchEngineLanguage.JA]: [
      {
        key: 'title',
        text: 'タイトル: {query}',
        value: 'title: {query}',
        filters: { fields: { title: '{query}' } },
      },
      {
        key: 'content',
        text: 'コンテンツ：{query}',
        value: 'content: {query}',
        filters: { fields: { content: '{query}' } },
      },
      {
        key: 'key phrase',
        text: 'キーフレーズ：{query}',
        value: 'key phrase: {query}',
        filters: { fields: { keyword: '{query}' } },
      },
    ],
  };

  private static readonly DYNAMIC_FIELD_TEMPLATES = {
    [SearchEngineLanguage.EN]: [
      {
        key: 'creator',
        text: 'creator: {query}',
        value: 'creator: {query}',
        filters: { fields: { creator: '{query}' } },
      },
      {
        key: 'custom data name',
        text: '#custom data name: {query}',
        value: '#custom data name: {query}',
        filters: { fields: { customDatas: '{query}' } },
      },
    ],
    [SearchEngineLanguage.JA]: [
      {
        key: 'creator',
        text: '作成者: {query}',
        value: 'creator: {query}',
        filters: { fields: { creator: '{query}' } },
      },
      {
        key: 'custom data name',
        text: '#カスタムデータ名: {query}',
        value: '#custom data name: {query}',
        filters: { fields: { customDatas: '{query}' } },
      },
    ],
  };

  constructor(private language: SearchEngineLanguage = SearchEngineLanguage.EN) {}

  setLanguage(language: SearchEngineLanguage): void {
    this.language = language;
  }
  /**
   * Adjusts the search query by removing any static type or field prefixes
   * @param query The raw search query string
   * @returns The adjusted query with prefixes removed, or empty string if query matches a prefix exactly or partially
   */
  adjustQuery(query: string): string {
    const normalizedQuery = query.toLowerCase();
    const templates = Object.values(SearchEngineLanguage).flatMap((language) => [
      ...SearchEngine.STATIC_TYPES[language],
      ...SearchEngine.STATIC_FIELD_TEMPLATES[language],
      ...SearchEngine.DYNAMIC_FIELD_TEMPLATES[language],
    ]);

    // Find exact or partial matching template prefix
    const matchingTemplate = templates.find((template) => {
      const prefixValue = template.text.replace('{query}', '').trim().toLowerCase();
      // Check for exact match or if query starts with prefix
      if (normalizedQuery === prefixValue || normalizedQuery.startsWith(prefixValue)) {
        return true;
      }
      // Check for partial match (e.g., "is:fol" matching "is:folder")
      if (prefixValue.startsWith('is:')) {
        return normalizedQuery.startsWith('is:') && prefixValue.startsWith(normalizedQuery);
      }
      return false;
    });

    if (!matchingTemplate) {
      return query;
    }

    const prefixLength = matchingTemplate.text.replace('{query}', '').trim().length;

    // If query exactly matches prefix or is a partial match, return empty string
    if (query.length <= prefixLength) {
      return '';
    }

    // Otherwise return query with prefix removed
    return query.substring(prefixLength).trim();
  }

  private createSuggestion(
    template: StaticTemplate,
    query: string,
    type: SearchSuggestionType
  ): SearchSuggestion {
    return {
      key: template.key,
      text: replaceQueryInTemplate(template.text, query).trim(),
      value: replaceQueryInTemplate(template.value, query).trim(),
      type,
    };
  }

  private getTemplatePrefix(template: StaticTemplate): string {
    return template.text.replace('{query}', '').trim().toLowerCase();
  }

  getSuggestions(query: string): SearchSuggestion[] {
    const normalizedQuery = query.toLowerCase();
    const adjustedQuery = this.adjustQuery(query);

    // Handle static type queries (is:)
    if (normalizedQuery.startsWith('is:') || normalizedQuery.startsWith('種類:')) {
      const types = SearchEngine.STATIC_TYPES[this.language];
      const matchingTypes = types.filter((type) =>
        this.getTemplatePrefix(type).startsWith(normalizedQuery)
      );

      return matchingTypes.length > 0
        ? matchingTypes.map((type) =>
            this.createSuggestion(type, adjustedQuery, SearchSuggestionTypes.STATIC)
          )
        : this.getStaticTypeSuggestions(adjustedQuery);
    }

    // Handle field-specific queries
    const fieldMatch = this.findMatchingFieldTemplate(normalizedQuery);
    if (fieldMatch) {
      const { template, type } = fieldMatch;
      const prefix = this.getTemplatePrefix(template);
      const fieldName = `${prefix.split(':')[0]}:`;

      // Return template if only field prefix is typed
      if (normalizedQuery === fieldName) {
        return [this.createSuggestion(template, adjustedQuery, type)];
      }

      // Handle field-specific suggestions
      if (normalizedQuery.startsWith(prefix)) {
        return type === SearchSuggestionTypes.DYNAMIC
          ? this.getDynamicFieldSuggestions(query.substring(prefix.length).trim())
          : [this.createSuggestion(template, adjustedQuery, type)];
      }
    }

    // Return all suggestions if no specific prefix matches
    return [
      ...this.getStaticTypeSuggestions(adjustedQuery),
      ...this.getStaticFieldSuggestions(adjustedQuery),
      ...this.getDynamicFieldSuggestions(adjustedQuery),
    ];
  }

  private findMatchingFieldTemplate(
    query: string
  ): { template: StaticTemplate; type: SearchSuggestionType } | null {
    // Check static fields first
    const staticMatch = SearchEngine.STATIC_FIELD_TEMPLATES[this.language].find((template) =>
      this.isFieldMatch(query, template)
    );
    if (staticMatch) {
      return { template: staticMatch || '', type: SearchSuggestionTypes.STATIC };
    }

    return null;
  }

  private isFieldMatch(query: string, template: StaticTemplate): boolean {
    const prefix = this.getTemplatePrefix(template);
    return query.startsWith(`${prefix.split(':')[0]}:`);
  }

  getEntities(value: string): SearchType[] {
    const staticTypeMatch = SearchEngine.STATIC_TYPES[this.language].find((type) =>
      value.startsWith(type.value.replace('{query}', '').trim())
    );
    return (
      staticTypeMatch?.filters.entities || [
        SearchTypeEnums.article,
        SearchTypeEnums.folder,
        SearchTypeEnums.base,
        SearchTypeEnums.document,
      ]
    );
  }

  getFilters(filterValue: string, keyword: string): SearchFilters {
    const staticFieldMatch = SearchEngine.STATIC_FIELD_TEMPLATES[this.language].find((template) =>
      filterValue.startsWith(replaceQueryInTemplate(template.value, keyword))
    );

    if (staticFieldMatch) {
      return {
        customDataKeys: [],
        customDatas: [],
        fields: {
          ...Object.fromEntries(
            Object.entries(staticFieldMatch.filters.fields).map(([key, value]) => [
              key,
              replaceQueryInTemplate(value, keyword),
            ])
          ),
        },
      };
    }

    const dynamicFieldMatch = SearchEngine.DYNAMIC_FIELD_TEMPLATES[this.language].find((template) =>
      filterValue.startsWith(replaceQueryInTemplate(template.value, keyword))
    );
    if (dynamicFieldMatch) {
      return {
        customDataKeys: [],
        customDatas: [],
        fields: {
          ...Object.fromEntries(
            Object.entries(dynamicFieldMatch.filters.fields).map(([key, value]) => [
              key,
              replaceQueryInTemplate(value, keyword),
            ])
          ),
        },
      };
    }

    return {
      customDataKeys: [],
      customDatas: [],
      fields: {
        content: '',
        name: '',
        title: '',
        creator: '',
        keyPhrase: '',
      },
    };
  }

  private getStaticTypeSuggestions(query: string): SearchSuggestion[] {
    return SearchEngine.STATIC_TYPES[this.language].map((type) => ({
      key: type.key,
      text: replaceQueryInTemplate(type.text, query),
      value: replaceQueryInTemplate(type.value, query),
      type: SearchSuggestionTypes.STATIC,
    }));
  }

  private getStaticFieldSuggestions(query: string): SearchSuggestion[] {
    return SearchEngine.STATIC_FIELD_TEMPLATES[this.language].map((template) => ({
      key: template.key,
      text: replaceQueryInTemplate(template.text, query),
      value: replaceQueryInTemplate(template.value, query),
      type: SearchSuggestionTypes.STATIC,
    }));
  }

  private getDynamicFieldSuggestions(_query: string): SearchSuggestion[] {
    // TODO: Replace Dynamic Data with actual data service
    const dynamicData = {
      users: [],
      customData: [],
    };

    const suggestions: SearchSuggestion[] = [];
    const templates = SearchEngine.DYNAMIC_FIELD_TEMPLATES[this.language];

    // Handle creator suggestions
    dynamicData.users.forEach((user) => {
      suggestions.push({
        key: templates[0].key,
        text: replaceQueryInTemplate(templates[0].text, user),
        value: replaceQueryInTemplate(templates[0].value, user),
        type: SearchSuggestionTypes.DYNAMIC,
      });
    });

    // Handle custom data suggestions
    dynamicData.customData.forEach((data) => {
      suggestions.push({
        key: templates[1].key,
        text: replaceQueryInTemplate(templates[1].text, data),
        value: replaceQueryInTemplate(templates[1].value, data),
        type: SearchSuggestionTypes.DYNAMIC,
      });
    });

    return suggestions;
  }
}
