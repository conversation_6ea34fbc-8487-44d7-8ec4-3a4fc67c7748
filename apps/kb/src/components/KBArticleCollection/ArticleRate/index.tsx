import { VoteType } from '@/types';
import { Flex, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import {
  IconThumbDown,
  IconThumbDownFilled,
  IconThumbUp,
  IconThumbUpFilled,
} from '@tabler/icons-react';

const useStyles = createStyles((theme) => ({
  thumbUp: {
    color: `${theme.colors.decaGreen[6]} !important`,
  },
  thumbDown: {
    color: `${theme.colors.decaRed[5]} !important`,
  },
}));

interface ArticleRateProps {
  type: VoteType;
  fill?: boolean;
  count?: number;
}

export default function ArticleRate({ type, fill, count }: ArticleRateProps) {
  const { classes } = useStyles();

  return (
    <Flex gap='xxs' align='center'>
      {type === VoteType.LIKE ? (
        fill ? (
          <IconThumbUpFilled className={classes.thumbUp} />
        ) : (
          <IconThumbUp className={classes.thumbUp} />
        )
      ) : fill ? (
        <IconThumbDownFilled className={classes.thumbDown} />
      ) : (
        <IconThumbDown className={classes.thumbDown} />
      )}
      <Text size='lg' color='decaGrey.5'>
        {count}
      </Text>
    </Flex>
  );
}
