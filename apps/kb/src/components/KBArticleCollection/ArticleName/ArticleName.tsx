import type { Article } from '@/types/article';
import { Box, Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { TextEllipsis } from '@resola-ai/ui';
import { IconLicense } from '@tabler/icons-react';
import type React from 'react';
import { ArticleShortcutBadge } from '../ArticleShortcutBadge';

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    svg: {
      marginRight: theme.spacing.xs,
      marginTop: rem(4),
      minWidth: rem(20),
    },
  },
  name: {
    fontSize: rem(16),
    lineHeight: rem(24),
    fontWeight: 500,
    wordWrap: 'break-word',
    wordBreak: 'break-word',
  },
  description: {
    fontSize: rem(12),
    lineHeight: rem(18),
    fontWeight: 400,
    color: theme.colors.decaGrey[5],
    wordWrap: 'break-word',
    wordBreak: 'break-word',
  },
  articleIcon: {
    width: rem(20),
    height: rem(20),
  },
}));

interface ArticleNameProps {
  article: Article;
}

const ArticleName: React.FC<ArticleNameProps> = ({ article }) => {
  const { classes } = useStyles();

  return (
    <Box className={classes.root}>
      <IconLicense className={classes.articleIcon} />
      <Box>
        <Flex align='center' gap='sm' mb='xs'>
          <TextEllipsis className={classes.name} lines={2}>
            {article.title}
          </TextEllipsis>
          {article.isShortcut && <ArticleShortcutBadge />}
        </Flex>
        <TextEllipsis className={classes.description} lines={1}>
          {article.content}
        </TextEllipsis>
      </Box>
    </Box>
  );
};

export default ArticleName;
