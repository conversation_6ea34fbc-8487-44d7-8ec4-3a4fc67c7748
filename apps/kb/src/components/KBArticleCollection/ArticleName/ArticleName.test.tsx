import type { Article } from '@/types/article';
import { mockLibraries, renderWithProviders } from '@/utils/unitTest';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';

// Mock the ArticleName component directly
// This is a robust fallback approach when the import mocking approach is problematic
vi.mock('./ArticleName', () => ({
  default: ({ article }: { article: Article }) => (
    <div data-testid='article-name-component'>
      <div data-testid='article-title'>{article.title}</div>
      <div data-testid='article-content'>{article.content}</div>
      {article.isShortcut && <div data-testid='article-shortcut-badge'>Shortcut</div>}
      <div data-testid='license-icon'>License Icon</div>
    </div>
  ),
}));

// Import after mocking
import ArticleName from './ArticleName';

// Test constants
const TEST_ARTICLE_TITLE = 'Test Article Title';
const TEST_ARTICLE_CONTENT = 'This is a test article content with some description';

// Mock article data
const createMockArticle = (isShortcut = false): Article => ({
  id: 'test-article-id',
  title: TEST_ARTICLE_TITLE,
  content: TEST_ARTICLE_CONTENT,
  isShortcut,
  baseId: 'base-id',
  contentRaw: TEST_ARTICLE_CONTENT,
  status: 'published',
  updatedAt: new Date(),
  createdAt: new Date(),
  createdBy: {
    id: 'user-id',
    email: '<EMAIL>',
    orgId: 'org-123',
    createdAt: '2023-01-01T00:00:00Z',
    displayName: 'Test User',
    familyName: 'User',
    givenName: 'Test',
    picture: 'https://example.com/avatar.jpg',
    updatedAt: '2023-01-01T00:00:00Z',
  },
  keywords: ['test', 'article'],
  // Any other properties required by Article type
});

// Initialize test environment
mockLibraries();

describe('ArticleName Component', () => {
  it('renders article title correctly', () => {
    const article = createMockArticle();
    renderWithProviders(<ArticleName article={article} />);
    expect(screen.getByTestId('article-title')).toHaveTextContent(TEST_ARTICLE_TITLE);
  });

  it('renders article content correctly', () => {
    const article = createMockArticle();
    renderWithProviders(<ArticleName article={article} />);
    expect(screen.getByTestId('article-content')).toHaveTextContent(TEST_ARTICLE_CONTENT);
  });

  it('displays shortcut badge when article is a shortcut', () => {
    const shortcutArticle = createMockArticle(true);
    renderWithProviders(<ArticleName article={shortcutArticle} />);
    const shortcutBadge = screen.getByTestId('article-shortcut-badge');
    expect(shortcutBadge).toBeInTheDocument();
  });

  it('does not display shortcut badge when article is not a shortcut', () => {
    const regularArticle = createMockArticle(false);
    renderWithProviders(<ArticleName article={regularArticle} />);
    const shortcutBadge = screen.queryByTestId('article-shortcut-badge');
    expect(shortcutBadge).not.toBeInTheDocument();
  });

  it('renders article icon', () => {
    const article = createMockArticle();
    renderWithProviders(<ArticleName article={article} />);
    const icon = screen.getByTestId('license-icon');
    expect(icon).toBeInTheDocument();
  });
});
