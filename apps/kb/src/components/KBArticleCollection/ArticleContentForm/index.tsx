import { Box, LoadingOverlay, Text, TextInput } from '@mantine/core';
import { BlockNoteEditor, isBlockNoteMarkdownContent } from '@resola-ai/blocknote-editor';
import { ErrorMessage } from '@resola-ai/ui';
import { useConfirmationBeforeUnload } from '@resola-ai/ui/hooks';
import { useTolgee, useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { EditorActions } from '@/components';
import { DEFAULT_EMPTY_ARTICLE_CONTENT } from '@/constants/kb';
import { useArticleCustomContext, useArticleDetailContext } from '@/contexts';
import { useArticleDetailStyles } from '@/hooks/useArticleStyles';
import { useArticleValidation } from '@/hooks/useArticleValidation';
import type { Article, PartOfArticle } from '@/types/article';
import ArticleKeyPhrases from '../ArticleKeyPhrases';

interface ArticleContentFormProps {
  baseId: string;
  article?: Article;
  isShownActions?: boolean;
  onCancel: () => void;
  onSave: (data: PartOfArticle, callback?: () => void) => void;
}

const ArticleContentForm: React.FC<ArticleContentFormProps> = ({
  baseId,
  article,
  isShownActions = true,
  onCancel,
  onSave,
}) => {
  const { t } = useTranslate('article');
  const tolgee = useTolgee();
  const { classes } = useArticleDetailStyles();

  const {
    articleCustomState: { loading: templateLoading, staticFields },
  } = useArticleCustomContext();
  const {
    articleViewer: { scrollToTopOfArticle },
    articleSaving,
    setArticleSaving,
    setIsArticleChanged,
    uploadFileInArticle,
    uploadFileWhenCreatingArticle,
  } = useArticleDetailContext();

  const isCreateNew = useMemo(() => !article, [article]);
  const initialContentRaw = useMemo(() => {
    if (isCreateNew) {
      return staticFields ? staticFields.contentRaw : '';
    }
    return article?.contentRaw;
  }, [isCreateNew, staticFields, article]);

  const [isTouched, setIsTouched] = useState(false);
  const [articleTitle, setArticleTitle] = useState<string>(article?.title ?? '');
  const [articleContentRaw, setArticleContentRaw] = useState<string>(article?.contentRaw ?? '');
  const [articleContentText, setArticleContentText] = useState<string>(article?.content ?? '');
  const [articleKeywords, setArticleKeywords] = useState<string[]>(article?.keywords ?? []);
  const { validateArticleContent, articleErrors } = useArticleValidation();

  /**
   * Handle Article Validation
   * @returns {void}
   */
  const validateArticle = useCallback(() => {
    const { hasEmptyTitle, hasEmptyContent, hasTitleLengthError, hasContentLengthError } =
      validateArticleContent({
        title: articleTitle,
        content: articleContentText,
        contentRaw: articleContentRaw,
      });

    return hasEmptyTitle || hasEmptyContent || hasTitleLengthError || hasContentLengthError;
  }, [articleTitle, articleContentText, articleContentRaw, validateArticleContent]);

  /**
   * Handle Editor Change
   * @param {string} contentHTML
   * @param {string} contentText
   * @returns {void}
   */
  const handleEditorChange = useCallback(
    (contentRaw: string, contentText: string) => {
      const isChanged = contentRaw !== articleContentRaw && contentText !== articleContentText;

      setArticleContentRaw(contentRaw);
      setArticleContentText(contentText || DEFAULT_EMPTY_ARTICLE_CONTENT);
      setIsArticleChanged(isChanged);

      if (isTouched) {
        validateArticle();
      }
    },
    [setArticleContentRaw, setArticleContentText, validateArticle, isTouched]
  );

  /**
   * Handle Save
   * @returns {void}
   */
  const handleSave = useCallback(() => {
    const hasErrors = validateArticle();

    if (hasErrors) {
      setArticleSaving(false);
      scrollToTopOfArticle();
      setIsTouched(true);
      return;
    }

    const articleData: PartOfArticle = {
      title: articleTitle.trim(),
      content: articleContentText,
      contentRaw: articleContentRaw,
      keywords: articleKeywords,
    };

    const handleSaveComplete = () => {
      setArticleSaving(false);
    };

    onSave(articleData, handleSaveComplete);
  }, [
    articleTitle,
    articleContentText,
    articleContentRaw,
    articleKeywords,
    onSave,
    setArticleSaving,
    t,
    scrollToTopOfArticle,
    validateArticle,
    setIsTouched,
  ]);

  /**
   * Handle File Upload in Editor
   * @param {File} file
   * @returns {Promise<string>}
   */
  const uploadFile = useCallback(
    async (file: File) => {
      if (!file) return '';

      const responseData = article
        ? await uploadFileInArticle(article.baseId, article.id, file)
        : await uploadFileWhenCreatingArticle(baseId, file);

      return responseData?.imageUrl ?? '';
    },
    [baseId, article, uploadFileInArticle]
  );

  /**
   * Handle Keywords Change
   * @param {string[]}
   * @returns {void}
   */
  const handleKeywordsChange = useCallback(
    (keywords: string[]) => {
      setArticleKeywords(keywords);
    },
    [setArticleKeywords]
  );

  useEffect(() => {
    if (articleSaving) {
      handleSave();
    }
  }, [articleSaving]);

  useEffect(() => {
    if (isCreateNew && staticFields) {
      setArticleTitle(staticFields.title);
      setArticleContentRaw(staticFields.contentRaw);
      setArticleContentText(staticFields.content);
      setArticleKeywords(staticFields.keywords ?? []);
    }
  }, [isCreateNew, staticFields]);

  useConfirmationBeforeUnload(isTouched);

  return isCreateNew && templateLoading ? (
    <LoadingOverlay visible />
  ) : (
    <Box className={classes.articleFormSection}>
      {isShownActions && (
        <EditorActions
          className={classes.articleFormActions}
          onCancel={onCancel}
          onSave={handleSave}
        />
      )}
      <Box className={classes.articleFormField}>
        <Box className={classes.articleFormFieldLabel}>
          <Text className={classes.articleLabel}>{t('articleCollection.title')}</Text>
          {articleErrors.title && isTouched && (
            <ErrorMessage className={classes.articleErrorMessage} message={articleErrors.title} />
          )}
        </Box>
        <TextInput
          placeholder={t('articleCollection.title')}
          className={classes.articleInputField}
          value={articleTitle}
          onChange={(event) => setArticleTitle(event?.currentTarget.value)}
          onBlur={validateArticle}
          onFocus={() => setIsTouched(true)}
        />
      </Box>
      <Box className={classes.articleFormField}>
        <Box className={classes.articleFormFieldLabel}>
          <Text className={classes.articleLabel}>{t('articleCollection.content')}</Text>
          {articleErrors.content && isTouched && (
            <ErrorMessage className={classes.articleErrorMessage} message={articleErrors.content} />
          )}
        </Box>
        <BlockNoteEditor
          className={classes.articleEditorField}
          autoFocus={false}
          isMarkdown={isBlockNoteMarkdownContent(initialContentRaw ?? '')}
          initialHTML={initialContentRaw}
          language={tolgee.getLanguage()}
          isBordered={false}
          uploadFile={uploadFile}
          onChange={handleEditorChange}
          onBlur={validateArticle}
          onFocus={() => setIsTouched(true)}
        />
      </Box>
      {isCreateNew && (
        <Box className={classes.articleFormField}>
          <ArticleKeyPhrases
            article={{ keywords: articleKeywords }}
            onKeywordsChange={handleKeywordsChange}
          />
        </Box>
      )}
    </Box>
  );
};

export default ArticleContentForm;
