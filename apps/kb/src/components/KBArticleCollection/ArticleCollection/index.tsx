import { GeneratorButton } from '@/components/KBArticleGenerator';
import ExportJobByEntityButton from '@/components/KBExport/components/ExportJobByEntityButton';
import { useArticleMovingModal } from '@/components/KBMoving';
import {
  CustomPagination,
  GridTable,
  type GridTableColumn,
  type GridTableRow,
  SearchBox,
} from '@/components/common';
import { useArticleContext } from '@/contexts';
import { useKbAccessControl } from '@/hooks';
import {
  type Article,
  EntityType,
  KBDirectionQueryEnum,
  type KnowledgeBase,
  type KnowledgeBaseDirectionQuery,
  type PartOfArticle,
} from '@/types';
import { cleanBadCharacters } from '@/utils/article';
import { Box, Flex, LoadingOverlay, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { formatDateTime } from '@resola-ai/ui/utils/dateTime';
import { useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import type React from 'react';
import { useCallback, useEffect, useMemo, useRef } from 'react';
import ArticleActions from '../ArticleActions';
import ArticleCreateNew from '../ArticleCreateNew';
import ArticleEmpty from '../ArticleEmpty';
import { ArticleFeedback } from '../ArticleFeedback';
import { ArticleName } from '../ArticleName';
import ArticleViewer, { type ArticleViewerRef } from '../ArticleViewer';

const useStyles = createStyles((theme) => ({
  toolbarSection: {
    marginBottom: theme.spacing.xl,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: theme.spacing.md,
    flexWrap: 'wrap',
  },
  toolbarRight: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing.md,
  },
  collectionListSection: {
    marginBottom: theme.spacing.xl,
    position: 'relative',
  },
  paginationWrapper: {
    paddingBottom: theme.spacing.xl,
    display: 'flex',
    justifyContent: 'center',
  },
  detailOpened: {
    justifyContent: 'flex-start',
  },
  searchBox: {
    width: rem(400),
    '& input': {
      width: rem(400),
      height: rem(38),
    },
  },
}));

export interface KBArticleCollectionProps {
  kb: KnowledgeBase;
}

/**
 * KBArticleCollection
 * @param {KBArticleCollectionProps} props
 * @returns {JSX.Element}
 * @dependencies t: i18n function, useStyles: styles
 */
const KBArticleCollection: React.FC<KBArticleCollectionProps> = ({ kb }) => {
  const { t } = useTranslate(['article', 'common']);
  const { cx, classes } = useStyles();
  const articleViewerRef = useRef<ArticleViewerRef>(null);
  const { openArticleMovingModal } = useArticleMovingModal({
    afterMoving: () => fetchCollection(),
  });
  const { permArticle } = useKbAccessControl();
  /**
   * Init Article Context and get necessary states, actions and services
   * @dependencies useArticleContext: function
   */
  const {
    articleCollection,
    state: contextState,
    actions: contextActions,
    services: contextServices,
  } = useArticleContext();

  const { data: articlesData, pagination } = articleCollection;
  const { isLoadingArticles, capturedPayload, activeArticleId, pageLimit, searchKeyword } =
    contextState;
  const { setActiveArticleId, setCurrentKb, setSearchKeyword, setPageLimit } = contextActions;
  const { getCollection, createArticle, deleteArticle } = contextServices;

  const isEmptyArticles = useMemo(() => isEmpty(articlesData), [articlesData]);
  const isInitializedArticles = useMemo(() => articlesData !== undefined, [articlesData]);

  /**
   * Fetch Collection
   * @dependencies kb: KnowledgeBase, getCollection: function
   * @returns {void}
   */
  const fetchCollection = useCallback(
    (options?: {
      direction?: KnowledgeBaseDirectionQuery;
      cursor?: string;
      pageSize?: number;
      showLoader?: boolean;
    }) => {
      const {
        direction = 'backward',
        cursor = '',
        pageSize = pageLimit,
        showLoader = true,
      } = options || {};
      getCollection(kb?.id, pageSize, direction, cursor, searchKeyword, showLoader);
    },
    [kb?.id, getCollection, searchKeyword, pageLimit]
  );

  /**
   * Handle Search Articles
   * @param {string} keyword
   * @dependencies kb: KnowledgeBase, getCollection: function
   * @returns {void}
   */
  const handleSearchArticles = useCallback(
    async (keyword: string) => {
      await getCollection(
        kb?.id,
        pageLimit,
        keyword ? KBDirectionQueryEnum.Forward : KBDirectionQueryEnum.Backward,
        '',
        keyword
      );
      setSearchKeyword(keyword);
    },
    [kb?.id, getCollection, pageLimit]
  );

  /**
   * Open Article Viewer
   * @param {Article} article
   * @dependencies articleViewerRef: ArticleViewerRef
   * @returns {void}
   */
  const openArticleViewer = useCallback(
    (articleId: string, baseId: string) => {
      if (!articleViewerRef.current || !articleId || !baseId) return;

      articleViewerRef.current?.setData?.({
        articleId,
        baseId,
      });
      articleViewerRef.current?.open?.();

      setActiveArticleId(articleId);
      contextActions.closeCreateNewArticleDrawer();
    },
    [articleViewerRef, kb, setActiveArticleId, contextActions]
  );

  /**
   * Handle Create Article
   * @param {PartOfArticle} data
   * @dependencies kb: KnowledgeBase, createArticle: function
   * @returns {void}
   */
  const handleCreateArticle = useCallback(
    async (data: PartOfArticle) => {
      if (!kb?.id) return;

      await createArticle(kb?.id, {
        title: data.title,
        content: data.content,
        contentRaw: data.contentRaw,
        status: 'published',
        keywords: [],
      });
    },
    [kb?.id, createArticle]
  );

  /**
   * Handle Page Size Change
   * @param {string} pageSize
   * @dependencies setPageLimit: function
   * @returns {void}
   */
  const handlePageSizeChange = useCallback(
    async (pageSize: number) => {
      await fetchCollection({ pageSize, showLoader: false });
      setPageLimit(pageSize);
    },
    [setPageLimit, fetchCollection]
  );

  /**
   * Handle Delete Article
   * @param {Article} article
   * @dependencies deleteArticle: function
   * @returns {void}
   */
  const handleDeleteArticle = useCallback(
    (article: Article) => {
      deleteArticle(article, () => {
        fetchCollection({
          direction: capturedPayload.direction,
          cursor: capturedPayload.cursor,
        });
      });
    },
    [deleteArticle, fetchCollection, capturedPayload]
  );

  /**
   * Article Collection Columns
   * @returns {JSX.Element[]}
   * @dependencies t: i18n function
   */
  const ArticleCollectionColumns: GridTableColumn[] = useMemo(
    () => [
      {
        title: t('articleCollection.name'),
        key: 'name',
        size: 8,
      },
      {
        title: t('articleCollection.lastUpdated'),
        key: 'lastUpdated',
      },
      {
        title: t('articleCollection.createdAt'),
        key: 'createdAt',
      },
      {
        title: '',
        key: 'actions',
        size: 'content',
      },
    ],
    [t]
  );

  /**
   * Article Collection Rows
   * @returns {JSX.Element[]}
   * @dependencies ArticleCollection
   */
  const ArticleCollectionRows: GridTableRow[] = useMemo(
    () =>
      articlesData
        ? articlesData.map((articleItem: Article) => ({
            props: {
              onClick: () => openArticleViewer(articleItem.id, articleItem.baseId),
              style: {
                cursor: 'pointer',
              },
              activated: articleItem.id === activeArticleId,
            },
            data: {
              id: articleItem.id,
              name: (
                <Flex align='center' justify='space-between' w='100%' gap='lg' pr='lg'>
                  <ArticleName article={articleItem} />
                  <ArticleFeedback article={articleItem} />
                </Flex>
              ),
              lastUpdated: formatDateTime(articleItem.updatedAt),
              createdAt: formatDateTime(articleItem.createdAt),
              status: articleItem.status,
              actions: (
                <ArticleActions
                  onOpen={() => openArticleViewer(articleItem.id, articleItem.baseId)}
                  onDelete={() => handleDeleteArticle(articleItem)}
                  onMove={() => openArticleMovingModal(articleItem)}
                />
              ),
            },
          }))
        : [],
    [articlesData, activeArticleId]
  );

  /**
   * Fetch Collection and set current kb to state
   * when mounted and kb id changed
   */
  useEffect(() => {
    setCurrentKb(kb);
    fetchCollection();
  }, [kb.id]);

  /**
   * Open Article Viewer when activeArticleId and kb.id changed
   * and activeArticleId is getting from URL
   * @dependencies activeArticleId: string, kb: KnowledgeBase, openArticleViewer: function
   */
  useEffect(() => {
    if (activeArticleId && kb.id) {
      openArticleViewer(activeArticleId, kb.id);
    }
  }, [kb.id]);

  return (
    <>
      <LoadingOverlay visible={isLoadingArticles} />
      <Box className={classes.toolbarSection}>
        <SearchBox
          className={classes.searchBox}
          placeholder={t('search', { ns: 'common' })}
          onSearch={handleSearchArticles}
        />
        <Box className={classes.toolbarRight}>
          <ExportJobByEntityButton
            entityId={kb.id}
            entityType={EntityType.BASE}
            onSuccess={fetchCollection}
          />
          {permArticle.canGenerate && <GeneratorButton baseId={kb.id} />}
          {permArticle.canCreate && !isEmptyArticles && (
            <ArticleCreateNew onImport={fetchCollection} />
          )}
        </Box>
      </Box>

      {!isEmptyArticles && (
        <>
          <Box className={classes.collectionListSection}>
            <GridTable columns={ArticleCollectionColumns} rows={ArticleCollectionRows} />
          </Box>
          {pagination && (
            <Box className={cx(classes.paginationWrapper, activeArticleId && classes.detailOpened)}>
              <CustomPagination
                direction={
                  searchKeyword ? KBDirectionQueryEnum.Forward : KBDirectionQueryEnum.Backward
                }
                pagination={pagination}
                onChange={(direction, cursor) =>
                  fetchCollection({
                    direction,
                    cursor,
                    pageSize: pageLimit,
                    showLoader: false,
                  })
                }
                pageSize={pageLimit}
                onPageSizeChange={handlePageSizeChange}
              />
            </Box>
          )}
        </>
      )}
      {isInitializedArticles && isEmptyArticles && (
        <ArticleEmpty onCreate={handleCreateArticle} onImport={fetchCollection} />
      )}

      <ArticleViewer
        ref={articleViewerRef}
        backTitle={(() => {
          const kbName = cleanBadCharacters(kb?.name) || t('unknownKBName', { ns: 'kb' });
          return t('backToKBDetail', { kbName } as any, { ns: 'common' });
        })()}
        onUpdated={(showLoader) =>
          fetchCollection({
            direction: capturedPayload.direction,
            cursor: capturedPayload.cursor,
            pageSize: pageLimit,
            showLoader,
          })
        }
        onClosed={() => setActiveArticleId('')}
      />
    </>
  );
};

export default KBArticleCollection;
