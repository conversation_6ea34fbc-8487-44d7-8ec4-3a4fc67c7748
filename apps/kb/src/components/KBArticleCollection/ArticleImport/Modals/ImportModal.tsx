import ArticleImport from '@/components/KBArticleCollection/ArticleImport';
import { useArticleContext } from '@/contexts/ArticleContext';
import { Modal, Text } from '@mantine/core';
import { HTTP_ERROR_STATUS } from '@resola-ai/shared-constants';
import { getPublicUrl } from '@resola-ai/utils';
import { useTranslate } from '@tolgee/react';
import { AxiosError } from 'axios';
import type React from 'react';
import { useCallback, useState } from 'react';

export interface ImportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (count: number) => void;
}

export const ImportModal: React.FC<ImportModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const { t } = useTranslate('article');
  const [isUploading, setIsUploading] = useState(false);
  const { state: articleCollectionState, services: articleCollectionServices } =
    useArticleContext();

  const handleUpload = useCallback(
    async (file: File, onError: (error: { message: string; expandData?: any }) => void) => {
      const kbId = articleCollectionState.currentKb?.id;
      if (!kbId) {
        onError({ message: 'uploadingError' });
        return;
      }

      setIsUploading(true);
      try {
        const response = await articleCollectionServices.importArticleFromCSV(
          articleCollectionState.currentKb?.id,
          file
        );

        onSuccess?.(response?.data?.rows);
      } catch (error) {
        let rows = [];
        let message = 'uploadingError';
        if (
          error instanceof AxiosError &&
          error?.response?.status === HTTP_ERROR_STATUS.UNPROCESSABLE_ENTITY &&
          error?.response?.data?.detail?.length
        ) {
          rows = (error?.response?.data?.detail[0]?.rows || [])
            .map((row: number) => row + 1)
            .join(', ');
          message =
            error.response?.data?.detail[0]?.message ||
            error.response?.data?.detail[0] ||
            'uploadingError';
        }

        onError({ message, expandData: { rows } });
      } finally {
        setIsUploading(false);
      }
    },
    [onSuccess, articleCollectionState.currentKb?.id, articleCollectionServices]
  );

  return (
    <Modal
      opened={isOpen}
      onClose={onClose}
      size='xl'
      centered
      withCloseButton={!isUploading}
      closeOnClickOutside={!isUploading}
      title={
        !isUploading ? (
          <Text size='xl' fw={700}>
            {t('articleCollection.importArticle')}
          </Text>
        ) : null
      }
    >
      <ArticleImport
        onChange={onSuccess}
        template={{
          description: t('articleCollection.importArticleDescription'),
          url: `${getPublicUrl('templates/import_article.csv')}`,
        }}
        KBName={articleCollectionState.currentKb?.name}
        onUpload={handleUpload}
        onClose={onClose}
        isUploading={isUploading}
      />
    </Modal>
  );
};

export default ImportModal;
