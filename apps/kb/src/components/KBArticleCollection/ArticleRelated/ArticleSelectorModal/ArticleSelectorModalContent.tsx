import ArticleSelector from '@/components/TreeViewSelector/ArticleSelector';
import { useKBSelectionContext } from '@/contexts';
import { Box, Flex, Group, Stack, Text } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';

interface ArticleSelectorModalContentProps {
  onClose: () => void;
  onSubmit: (articleIds: string[]) => void;
  disabledArticles?: string[];
  options?: {
    limit?: number;
  };
}

const ArticleSelectorModalContent = ({
  onClose,
  onSubmit,
  disabledArticles = [],
  options,
}: ArticleSelectorModalContentProps) => {
  const { t } = useTranslate(['article', 'common']);
  const {
    articleSelection: { selectedArticles },
  } = useKBSelectionContext();
  const handleSubmit = useCallback(() => {
    onSubmit(selectedArticles);
    onClose();
  }, [onSubmit, onClose, selectedArticles]);

  return (
    <Stack gap='md'>
      <Box>
        <Text component='p'>{t('articleSelector.description', { limit: options?.limit })}</Text>
      </Box>

      <ArticleSelector disabledArticles={disabledArticles} options={options} />

      <Flex gap='md' justify='flex-end'>
        <Group gap='md'>
          <Text>
            {t('selected', { ns: 'common' })}: {selectedArticles.length}
          </Text>
          <DecaButton variant='neutral' radius='xl' onClick={onClose}>
            {t('actions.cancel', { ns: 'common' })}
          </DecaButton>
          <DecaButton variant='primary' radius='xl' onClick={handleSubmit}>
            {t('actions.link', { ns: 'common' })}
          </DecaButton>
        </Group>
      </Flex>
    </Stack>
  );
};

export default ArticleSelectorModalContent;
