import { KBSelectionContextProvider } from '@/contexts';
import { useModalManager } from '@/hooks';
import { modals } from '@mantine/modals';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';
import ArticleSelectorModalContent from './ArticleSelectorModalContent';

interface ArticleSelectorModalProps {
  onSubmit: (articleIds: string[]) => void;
  onClose?: () => void;
}

export const useArticleSelectorModal = ({ onSubmit, onClose }: ArticleSelectorModalProps) => {
  const { t } = useTranslate(['article', 'common']);
  const { modalClasses, createModal } = useModalManager();

  const handleClose = useCallback(() => {
    modals.closeAll();
    onClose?.();
  }, [onClose]);

  const handleSubmit = useCallback(
    (articleIds: string[]) => {
      onSubmit(articleIds);
      onClose?.();
    },
    [onSubmit, onClose]
  );

  const buildArticleSelectorModal = useCallback(
    (articleIds: string[], disabledArticles: string[], options?: { limit?: number }) =>
      createModal({
        title: t('articleSelector.title'),
        classNames: {
          content: modalClasses.largeModal,
        },
        onClose: handleClose,
        children: (
          <KBSelectionContextProvider articleIds={articleIds}>
            <ArticleSelectorModalContent
              onClose={handleClose}
              onSubmit={handleSubmit}
              disabledArticles={disabledArticles}
              options={options}
            />
          </KBSelectionContextProvider>
        ),
      }),
    [handleClose, t, modalClasses, createModal]
  );

  return {
    openArticleSelectorModal: useCallback(
      (articleIds: string[], disabledArticles: string[], options?: { limit?: number }) => {
        modals.open(buildArticleSelectorModal(articleIds, disabledArticles, options));
      },
      [buildArticleSelectorModal]
    ),
  };
};
