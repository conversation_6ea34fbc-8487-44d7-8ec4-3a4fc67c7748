import { IconLink, IconUnlink } from '@/components/Icons';
import { useKbAccessControl } from '@/hooks';
import type { Article } from '@/types';
import { Box, rem } from '@mantine/core';
import { createStyles, getStylesRef } from '@mantine/emotion';
import { useCallback } from 'react';
import { Link } from 'react-router-dom';

const useStyles = createStyles((theme) => ({
  relatedArticle: {
    display: 'flex',
    alignItems: 'center',
    gap: rem(8),
    padding: rem(4),
    '&:hover': {
      backgroundColor: theme.colors.gray[0],
      borderRadius: theme.radius.sm,
      [`& .${getStylesRef('unlinkIcon')}`]: {
        opacity: 1,
        visibility: 'visible',
      },
    },
  },
  articleTitle: {
    flex: 1,
    color: theme.colors.decaNavy[5],
    textDecoration: 'none',
    '&:hover': {
      cursor: 'pointer',
      textDecoration: 'underline',
    },
  },
  unlinkIcon: {
    ref: getStylesRef('unlinkIcon'),
    opacity: 0,
    visibility: 'hidden',
    color: theme.colors.decaRed[6],
  },
  linkIcon: {
    ref: getStylesRef('linkIcon'),
    color: theme.colors.gray[5],
  },
}));

interface ArticleRelatedItemProps {
  article: Article;
  onRemove?: (articleId: string) => void;
}

const ArticleRelatedItem: React.FC<ArticleRelatedItemProps> = ({ article, onRemove }) => {
  const { classes } = useStyles();
  const { permArticleRelated } = useKbAccessControl();

  const handleRemove = useCallback(() => {
    if (onRemove) {
      onRemove(article.id);
    }
  }, [article.id, onRemove]);

  return (
    <Box className={classes.relatedArticle}>
      <IconLink className={classes.linkIcon} />
      <Link
        to={`/kb/${article.baseId}?lang=ja&articleId=${article.id}`}
        target='_blank'
        className={classes.articleTitle}
      >
        {article.title}
      </Link>
      {permArticleRelated.canDelete && onRemove && (
        <IconUnlink className={classes.unlinkIcon} onClick={handleRemove} />
      )}
    </Box>
  );
};

export default ArticleRelatedItem;
