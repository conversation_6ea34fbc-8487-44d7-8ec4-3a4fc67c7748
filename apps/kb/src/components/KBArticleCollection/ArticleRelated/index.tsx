import { MAX_ARTICLE_RELATED_ARTICLES_LENGTH } from '@/constants/kb';
import { useAppContext } from '@/contexts/AppContext';
import type { Article } from '@/types';
import { Box, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useMemo } from 'react';
import ArticleRelatedItem from './ArticleRelatedItem';
import { useArticleSelectorModal } from './ArticleSelectorModal/useArticleSelectorModal';

const useStyles = createStyles((theme) => ({
  section: {
    marginBottom: rem(24),
    padding: rem(6),
  },
  sectionTitle: {
    color: theme.colors.decaNavy[5],
    marginBottom: rem(16),
  },
  relatedArticles: {
    border: `1px solid ${theme.colors.gray[3]}`,
    borderRadius: theme.radius.md,
    padding: rem(8),
    marginBottom: rem(12),
    maxHeight: rem(170),
    overflowY: 'auto',
  },
  placeholderButton: {
    '& .mantine-Button-label': {
      fontSize: theme.fontSizes.sm,
    },
  },
  actions: {
    justifyContent: 'flex-end',
  },
}));

interface ArticleRelatedProps {
  article: Partial<Article> | null;
  disabled?: boolean;
  onRelatedArticlesChange: (articles: string[]) => void;
}

const ArticleRelated: React.FC<ArticleRelatedProps> = ({
  article,
  disabled = false,
  onRelatedArticlesChange,
}) => {
  const { t } = useTranslate(['article', 'common']);
  const { classes } = useStyles();
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const { openArticleSelectorModal } = useArticleSelectorModal({
    onSubmit: (articleIds: string[]) => {
      onRelatedArticlesChange(articleIds);
    },
  });

  const isShortcut = useMemo(() => !!article?.isShortcut, [article?.isShortcut]);

  const handleRemoveRelatedArticle = (articleId: string) => {
    if (!article?.relatedArticles || !article.relatedArticles.length) {
      return;
    }

    openConfirmModal({
      onConfirm: () => {
        const relatedArticleIds = (article?.relatedArticles || [])
          .filter((a) => typeof a !== 'string' && a.id !== articleId)
          .map((a) => (typeof a === 'string' ? a : a.id));
        onRelatedArticlesChange(relatedArticleIds);

        closeConfirmModal();
      },
      title: t('articleCollection.removeRelatedArticle'),
      confirmText: t('actions.unlink', { ns: 'common' }),
      cancelText: t('actions.cancel', { ns: 'common' }),
      options: { isRemoving: true },
    });
  };

  const handleOpenRelatedArticleModal = useCallback(() => {
    if (!article?.baseId || disabled) return;
    const articleIds = article.relatedArticles?.map((a) => a.id) || [];
    const disabledArticles = article?.id ? [article?.id] : [];
    openArticleSelectorModal(articleIds, disabledArticles, {
      limit: MAX_ARTICLE_RELATED_ARTICLES_LENGTH,
    });
  }, [article?.baseId, openArticleSelectorModal, disabled]);

  return (
    <Box className={classes.section}>
      <Title className={classes.sectionTitle} order={5}>
        {t('articleCollection.related')}
      </Title>
      {!!article?.relatedArticles?.length && (
        <Box className={classes.relatedArticles}>
          {article?.relatedArticles?.map((relatedArticle) => (
            <ArticleRelatedItem
              key={relatedArticle.id}
              article={relatedArticle}
              onRemove={isShortcut ? undefined : handleRemoveRelatedArticle}
            />
          ))}
        </Box>
      )}
      <DecaButton
        disabled={disabled}
        className={classes.placeholderButton}
        leftSection={<IconPlus size={16} />}
        size='sm'
        radius={'xl'}
        variant='neutral'
        onClick={handleOpenRelatedArticleModal}
      >
        {t('articleCollection.addRelatedArticle')}
      </DecaButton>
    </Box>
  );
};

export default ArticleRelated;
