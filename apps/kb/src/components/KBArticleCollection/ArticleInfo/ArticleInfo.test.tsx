import { ArticleDetailLayout } from '@/types/article';
import type { User } from '@/types/user';
import { renderWithProviders } from '@/utils/unitTest';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import ArticleInfo from './ArticleInfo';

// Mock ArticleAnalyticsProvider and its children
vi.mock('@/contexts/ArticleAnalyticsContext', () => ({
  ArticleAnalyticsProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='article-analytics-provider'>{children}</div>
  ),
}));

vi.mock('../ArticleAnalytics', () => ({
  AnalyticsFilter: ({ className }: { className: string }) => (
    <div data-testid='analytics-filter' className={className}>
      Analytics Filter
    </div>
  ),
  AnalyticsViews: () => <div data-testid='analytics-views'>Analytics Views</div>,
  AnalyticsRating: () => <div data-testid='analytics-rating'>Analytics Rating</div>,
}));

// Mock utils
vi.mock('@/utils/article', () => ({
  getUserName: vi.fn().mockImplementation((user) => {
    return `${user.givenName} ${user.familyName}`;
  }),
}));

vi.mock('@resola-ai/ui/utils/dateTime', () => ({
  formatDateTime: vi.fn().mockImplementation((date) => {
    return date.toISOString().split('T')[0];
  }),
}));

// Constants for test
const TEST_ARTICLE_ID = 'article-123';
const TEST_ORIGIN_ARTICLE_ID = 'origin-article-456';
const TEST_LAST_UPDATED = new Date('2023-01-15');
const TEST_CREATED_AT = new Date('2023-01-01');
const TEST_USER: User = {
  id: 'user-123',
  givenName: 'John',
  familyName: 'Doe',
  email: '<EMAIL>',
  orgId: '',
  createdAt: '',
  displayName: '',
  picture: '',
  updatedAt: '',
};
const TEST_USER_DISPLAY_NAME = 'John Doe';
const TEST_FORMATTED_DATE_UPDATED = '2023-01-15';
const TEST_FORMATTED_DATE_CREATED = '2023-01-01';

describe('ArticleInfo', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderArticleInfo = (props = {}) => {
    const defaultProps = {
      articleId: TEST_ARTICLE_ID,
      lastUpdated: TEST_LAST_UPDATED,
      createdAt: TEST_CREATED_AT,
      createdBy: TEST_USER,
    };

    return renderWithProviders(<ArticleInfo {...defaultProps} {...props} />);
  };

  it('renders with horizontal layout by default', () => {
    renderArticleInfo();

    // Check if the component renders with the correct data
    expect(screen.getByText(TEST_FORMATTED_DATE_UPDATED)).toBeInTheDocument();
    expect(screen.getByText(TEST_FORMATTED_DATE_CREATED)).toBeInTheDocument();
    expect(screen.getByText(TEST_USER_DISPLAY_NAME)).toBeInTheDocument();
  });

  it('renders with vertical layout when specified', () => {
    renderArticleInfo({ layout: ArticleDetailLayout.VERTICAL });

    // Check if the component renders with the correct data
    expect(screen.getByText(TEST_FORMATTED_DATE_UPDATED)).toBeInTheDocument();
    expect(screen.getByText(TEST_FORMATTED_DATE_CREATED)).toBeInTheDocument();
    expect(screen.getByText(TEST_USER_DISPLAY_NAME)).toBeInTheDocument();
  });

  it('renders analytics components', () => {
    renderArticleInfo();

    // Check if analytics components are rendered
    expect(screen.getByTestId('article-analytics-provider')).toBeInTheDocument();
    expect(screen.getByTestId('analytics-filter')).toBeInTheDocument();
    expect(screen.getByTestId('analytics-views')).toBeInTheDocument();
    expect(screen.getByTestId('analytics-rating')).toBeInTheDocument();
  });

  it('passes originArticleId to ArticleAnalyticsProvider when provided', () => {
    renderArticleInfo({ originArticleId: TEST_ORIGIN_ARTICLE_ID });

    // The withOriginalArticle prop is passed to ArticleAnalyticsProvider
    // We can't directly test the prop value, but we can verify the component is rendered
    expect(screen.getByTestId('article-analytics-provider')).toBeInTheDocument();
  });

  it('displays last updated date correctly', () => {
    const customDate = new Date('2023-02-15');
    renderArticleInfo({ lastUpdated: customDate });

    // Check if the formatted date is displayed
    expect(screen.getByText('2023-02-15')).toBeInTheDocument();
  });

  it('displays created date and user correctly', () => {
    const customDate = new Date('2023-03-01');
    const customUser: User = {
      id: 'user-456',
      givenName: 'Jane',
      familyName: 'Smith',
      email: '<EMAIL>',
      orgId: '',
      createdAt: '',
      displayName: '',
      picture: '',
      updatedAt: '',
    };

    renderArticleInfo({
      createdAt: customDate,
      createdBy: customUser,
    });

    // Check if the formatted date and user name are displayed
    expect(screen.getByText('2023-03-01')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
  });
});
