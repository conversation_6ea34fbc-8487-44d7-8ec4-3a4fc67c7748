import { ArticleAnalyticsProvider } from '@/contexts/ArticleAnalyticsContext';
import { ArticleDetailLayout, type User } from '@/types';
import { getUserName } from '@/utils/article';
import { Box, Divider, Grid, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { formatDateTime } from '@resola-ai/ui/utils/dateTime';
import { useTolgee, useTranslate } from '@tolgee/react';
import type React from 'react';
import { useMemo } from 'react';
import { AnalyticsFilter, AnalyticsRating, AnalyticsViews } from '../ArticleAnalytics';

const useStyles = createStyles((theme, { layout }: { layout: ArticleDetailLayout }) => ({
  wrapper: {
    backgroundColor: theme.colors?.decaNavy?.[0] ?? '#f8f9fa',
    borderRadius: rem(4),
    padding: rem(16),
  },
  grid: {
    flexDirection: layout === ArticleDetailLayout.HORIZONTAL ? 'row' : 'column',
  },
  col: {
    display: 'flex',
    flexDirection: 'row',
    gap: rem(8),
    flexWrap: 'wrap',
    justifyContent: layout === ArticleDetailLayout.HORIZONTAL ? 'flex-start' : 'space-between',
  },
  label: {
    fontSize: rem(12),
    lineHeight: rem(18),
    fontWeight: 500,
    color: theme.colors?.decaNavy?.[5] ?? '#6c757d',
  },
  valueGroup: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: layout === ArticleDetailLayout.HORIZONTAL ? 'flex-start' : 'space-between',
  },
  value: {
    fontSize: rem(12),
    lineHeight: rem(18),
    fontWeight: 500,
    color: theme.colors?.decaGrey?.[9] ?? '#212529',
  },
  divider: {
    margin: `${rem(16)} 0`,
    borderColor: theme.colors?.decaGrey?.[0] ?? '#f8f9fa',
  },
  analyticsFilter: {
    margin: `0 0 ${rem(16)}`,
  },
}));

interface ArticleInfoProps {
  articleId: string;
  lastUpdated: Date;
  createdAt: Date;
  createdBy: User;
  layout?: ArticleDetailLayout;
  originArticleId?: string;
}

const ArticleInfo: React.FC<ArticleInfoProps> = ({
  articleId,
  lastUpdated,
  createdAt,
  createdBy,
  layout = ArticleDetailLayout.HORIZONTAL,
  originArticleId,
}) => {
  const { t } = useTranslate('article');
  const tolgee = useTolgee();
  const { classes } = useStyles({ layout });
  const userName = useMemo(() => getUserName(createdBy, tolgee.getLanguage()), [createdBy, tolgee]);

  return (
    <Box className={classes.wrapper}>
      <Grid className={classes.grid}>
        <Grid.Col className={classes.col} span={layout === ArticleDetailLayout.HORIZONTAL ? 5 : 12}>
          <Text className={classes.label}>{t('articleCollection.lastUpdated')}</Text>
          <Box className={classes.valueGroup}>
            <Text className={classes.value}>{formatDateTime(lastUpdated)}</Text>
          </Box>
        </Grid.Col>
        <Grid.Col className={classes.col} span={layout === ArticleDetailLayout.HORIZONTAL ? 7 : 12}>
          <Text className={classes.label}>{t('articleCollection.createdAt')}</Text>
          <Box className={classes.valueGroup}>
            <Text className={classes.value}>{formatDateTime(createdAt)}</Text>
            <Text className={classes.value}>{userName}</Text>
          </Box>
        </Grid.Col>
      </Grid>
      <ArticleAnalyticsProvider
        articleId={originArticleId ?? articleId}
        layout={layout}
        withOriginalArticle={!!originArticleId}
      >
        <Divider className={classes.divider} />
        <AnalyticsFilter className={classes.analyticsFilter} />
        <AnalyticsViews />

        <Divider className={classes.divider} />
        <AnalyticsRating />
      </ArticleAnalyticsProvider>
    </Box>
  );
};

export default ArticleInfo;
