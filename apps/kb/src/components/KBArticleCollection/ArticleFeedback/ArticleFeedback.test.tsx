import { VoteAPI } from '@/services/api/v2/vote';
import { type Article, VoteType } from '@/types';
import { render, screen, waitFor } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import ArticleFeedback from './ArticleFeedback';

// Mock the VoteAPI
vi.mock('@/services/api/v2/vote', () => ({
  VoteAPI: {
    getArticleVote: vi.fn(),
  },
}));

// Mock console.error
vi.spyOn(console, 'error').mockImplementation(() => {});

// Mock Mantine components
vi.mock('@mantine/core', () => ({
  Flex: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='flex-container'>{children}</div>
  ),
  Loader: () => <div data-testid='loader'>Loading...</div>,
}));

// Mock ArticleRate component
vi.mock('../ArticleRate', () => ({
  default: ({ type, count }: { type: VoteType; count: number }) => (
    <div data-testid={`article-rate-${type}`} data-count={count}>
      {type === VoteType.LIKE ? 'Like' : 'Dislike'}: {count}
    </div>
  ),
}));

// Constants for test data
const TEST_ARTICLE_ID = 'article-123';
const TEST_ORIGIN_ARTICLE_ID = 'origin-article-456';
const TEST_LIKE_COUNT = 10;
const TEST_DISLIKE_COUNT = 5;

describe('ArticleFeedback', () => {
  // Regular article mock
  const regularArticle: Article = {
    id: TEST_ARTICLE_ID,
    baseId: 'base-123',
    title: 'Test Article',
    content: 'Test content',
    contentRaw: 'Test content raw',
    status: 'published',
    isShortcut: false,
    likeCount: TEST_LIKE_COUNT,
    dislikeCount: TEST_DISLIKE_COUNT,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: { id: 'user-123', email: '<EMAIL>' } as any,
    keywords: [],
  };

  // Shortcut article mock
  const shortcutArticle: Article = {
    id: TEST_ARTICLE_ID,
    baseId: 'base-123',
    title: 'Shortcut Article',
    content: 'Shortcut content',
    contentRaw: 'Shortcut content raw',
    status: 'published',
    isShortcut: true,
    originArticleId: TEST_ORIGIN_ARTICLE_ID,
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: { id: 'user-123', email: '<EMAIL>' } as any,
    keywords: [],
  };

  beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should render like and dislike counts for a regular article', async () => {
    render(<ArticleFeedback article={regularArticle} />);

    // Check if like and dislike components are rendered with correct counts
    await waitFor(() => {
      const likeElement = screen.getByTestId(`article-rate-${VoteType.LIKE}`);
      const dislikeElement = screen.getByTestId(`article-rate-${VoteType.DISLIKE}`);

      expect(likeElement).toBeInTheDocument();
      expect(dislikeElement).toBeInTheDocument();
      expect(likeElement.getAttribute('data-count')).toBe(TEST_LIKE_COUNT.toString());
      expect(dislikeElement.getAttribute('data-count')).toBe(TEST_DISLIKE_COUNT.toString());
    });
  });

  it('should fetch and render vote data for a shortcut article', async () => {
    // Mock API response
    (VoteAPI.getArticleVote as any).mockResolvedValue({
      status: 'success',
      data: {
        likeCount: TEST_LIKE_COUNT,
        dislikeCount: TEST_DISLIKE_COUNT,
      },
    });

    render(<ArticleFeedback article={shortcutArticle} />);

    // Verify API was called with correct article ID
    expect(VoteAPI.getArticleVote).toHaveBeenCalledWith(TEST_ORIGIN_ARTICLE_ID);

    // Check if like and dislike components are rendered with correct counts from API
    await waitFor(
      () => {
        const likeElement = screen.getByTestId(`article-rate-${VoteType.LIKE}`);
        expect(likeElement).toBeInTheDocument();
      },
      { timeout: 3000 }
    );

    const likeElement = screen.getByTestId(`article-rate-${VoteType.LIKE}`);
    const dislikeElement = screen.getByTestId(`article-rate-${VoteType.DISLIKE}`);

    expect(likeElement.getAttribute('data-count')).toBe(TEST_LIKE_COUNT.toString());
    expect(dislikeElement.getAttribute('data-count')).toBe(TEST_DISLIKE_COUNT.toString());
  });

  it('should show loading state while fetching vote data', async () => {
    // Delay API response to test loading state
    (VoteAPI.getArticleVote as any).mockImplementation(
      () =>
        new Promise((resolve) =>
          setTimeout(
            () =>
              resolve({
                status: 'success',
                data: {
                  likeCount: TEST_LIKE_COUNT,
                  dislikeCount: TEST_DISLIKE_COUNT,
                },
              }),
            100
          )
        )
    );

    render(<ArticleFeedback article={shortcutArticle} />);

    // Check if loader is displayed
    expect(screen.getByTestId('loader')).toBeInTheDocument();

    // Wait for API response to complete
    await waitFor(
      () => {
        expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
        const likeElement = screen.getByTestId(`article-rate-${VoteType.LIKE}`);
        expect(likeElement).toBeInTheDocument();
      },
      { timeout: 2000 }
    );
  });

  it('should handle API errors gracefully', async () => {
    // Mock API error
    (VoteAPI.getArticleVote as any).mockImplementation(() => {
      console.error('API Error occurred');
      throw new Error('API Error');
    });

    render(<ArticleFeedback article={shortcutArticle} />);

    // Wait for the loading state to finish
    await waitFor(() => {
      expect(screen.queryByTestId('loader')).not.toBeInTheDocument();
    });

    // Verify no vote content is displayed when there's an error
    expect(screen.queryByTestId(`article-rate-${VoteType.LIKE}`)).not.toBeInTheDocument();
    expect(screen.queryByTestId(`article-rate-${VoteType.DISLIKE}`)).not.toBeInTheDocument();
  });

  it('should handle articles with undefined vote counts', async () => {
    const articleWithoutVotes: Article = {
      ...regularArticle,
      likeCount: undefined,
      dislikeCount: undefined,
    };

    render(<ArticleFeedback article={articleWithoutVotes} />);

    // Check if like and dislike components are rendered with zero counts
    await waitFor(() => {
      const likeElement = screen.getByTestId(`article-rate-${VoteType.LIKE}`);
      expect(likeElement).toBeInTheDocument();
    });

    const likeElement = screen.getByTestId(`article-rate-${VoteType.LIKE}`);
    const dislikeElement = screen.getByTestId(`article-rate-${VoteType.DISLIKE}`);

    expect(likeElement.getAttribute('data-count')).toBe('0');
    expect(dislikeElement.getAttribute('data-count')).toBe('0');
  });
});
