import { VoteAPI } from '@/services/api/v2/vote';
import { type Article, type ArticleVote, VoteType } from '@/types';
import { Flex, Loader } from '@mantine/core';
import { useCallback, useEffect, useMemo, useState } from 'react';
import ArticleRate from '../ArticleRate';

/**
 * Props for the ArticleFeedback component
 */
interface ArticleFeedbackProps {
  article: Article;
}

/**
 * ArticleFeedback - Component that displays like/dislike feedback for an article
 *
 * This component handles two scenarios:
 * 1. Regular articles - Uses the like/dislike counts directly from the article object
 * 2. Shortcut articles - Fetches like/dislike counts from the original article via API
 */
const ArticleFeedback: React.FC<ArticleFeedbackProps> = ({ article }) => {
  // State for storing vote data, loading state, and potential errors
  const [articleVote, setArticleVote] = useState<Partial<ArticleVote> | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch vote data from API for shortcut articles
   * Memoized to prevent unnecessary recreation on component re-renders
   *
   * @param articleId - The ID of the original article to fetch votes for
   */
  const fetchArticleVote = useCallback(async (articleId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await VoteAPI.getArticleVote(articleId);
      if (response?.status === 'success') {
        setArticleVote({
          likeCount: response.data.likeCount,
          dislikeCount: response.data.dislikeCount,
        });
      } else {
        setArticleVote(null);
      }
    } catch (err) {
      setError('Failed to load vote data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Set vote data directly from local article properties
   * Used for non-shortcut articles that have vote data embedded
   *
   * @param likeCount - Number of likes for the article
   * @param dislikeCount - Number of dislikes for the article
   */
  const setLocalArticleVote = useCallback((likeCount?: number, dislikeCount?: number) => {
    setArticleVote({
      likeCount: likeCount ?? 0,
      dislikeCount: dislikeCount ?? 0,
    });
  }, []);

  /**
   * Effect to load vote data when article changes
   * Determines whether to fetch from API or use local data based on article type
   */
  useEffect(() => {
    if (article.isShortcut && article.originArticleId) {
      fetchArticleVote(article.originArticleId);
    } else {
      setLocalArticleVote(article.likeCount, article.dislikeCount);
    }
  }, [
    article.isShortcut,
    article.originArticleId,
    article.likeCount,
    article.dislikeCount,
    fetchArticleVote,
    setLocalArticleVote,
  ]);

  /**
   * Memoized vote content to optimize rendering
   * Shows loading indicator, handles error states, and renders vote UI
   */
  const voteContent = useMemo(() => {
    if (isLoading) return <Loader size='xs' />;
    if (error) return null; // Could be enhanced with error UI
    if (!articleVote) return null;

    return (
      <>
        <ArticleRate type={VoteType.LIKE} count={articleVote.likeCount ?? 0} />
        <ArticleRate type={VoteType.DISLIKE} count={articleVote.dislikeCount ?? 0} />
      </>
    );
  }, [articleVote, isLoading, error]);

  return (
    <Flex gap='xl' align='center'>
      {voteContent}
    </Flex>
  );
};

export default ArticleFeedback;
