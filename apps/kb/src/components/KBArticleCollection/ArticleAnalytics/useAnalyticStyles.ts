import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

export const useAnalyticStyles = createStyles((theme) => ({
  analyticDetailsContainer: {
    marginTop: rem(4),
  },

  skeleton: {
    height: rem(20),
    width: '80%',
    borderRadius: rem(4),
  },

  title: {
    fontWeight: 500,
    color: theme.colors.decaNavy[5],
    fontSize: rem(12),
  },

  viewCount: {
    fontSize: rem(12),
    fontWeight: 500,
  },

  titleGroup: {
    cursor: 'pointer',
    gap: rem(4),
  },

  horizontalRatingRow: {
    marginBottom: 0,
  },

  verticalRatingRow: {
    '& > .mantine-Grid-col': {
      paddingTop: rem(4),
      paddingBottom: rem(4),
    },
    '&:not(:last-child)': {
      marginBottom: rem(12),
    },
  },

  chevron: {
    width: rem(16),
    height: rem(16),
    color: theme.colors.decaNavy[5],
  },

  eyeIcon: {
    width: rem(20),
    height: rem(20),
    color: theme.colors.decaBlue[5],
    marginRight: rem(4),
  },

  noDataText: {
    color: theme.colors.decaGrey[7],
    fontStyle: 'italic',
    fontSize: rem(12),
    fontWeight: 500,
    textAlign: 'center',
    padding: `${rem(24)} 0 ${rem(16)}`,
  },
}));
