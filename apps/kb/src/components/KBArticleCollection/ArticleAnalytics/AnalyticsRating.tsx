import { useArticleAnalyticsContext } from '@/contexts/ArticleAnalyticsContext';
import { ArticleDetailLayout } from '@/types/article';
import { Box, Collapse, Grid, Group, Skeleton, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconChevronDown, IconChevronUp } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import AnalyticRatingWithIcon from './AnalyticRatingWithIcon';
import AnalyticsSkeleton from './AnalyticsSkeleton';
import { useAnalyticStyles } from './useAnalyticStyles';

/**
 * RatingRow is a component that displays a rating row with a title, positive and negative views.
 * It is used in the Ratings section of the AnalyticsRating component.
 */
interface RatingRowProps {
  title: string;
  positive: number;
  negative: number;
}

const RatingRow: React.FC<RatingRowProps> = ({ title, positive, negative }) => {
  const {
    articleAnalyticsState: { layout },
  } = useArticleAnalyticsContext();
  const { classes } = useAnalyticStyles();

  return (
    <Grid
      className={
        layout === ArticleDetailLayout.HORIZONTAL
          ? classes.horizontalRatingRow
          : classes.verticalRatingRow
      }
      align='center'
    >
      <Grid.Col span={layout === ArticleDetailLayout.HORIZONTAL ? 4 : 12}>
        <Text className={classes.title}>{title}</Text>
      </Grid.Col>
      <Grid.Col span={layout === ArticleDetailLayout.HORIZONTAL ? 4 : 6}>
        <AnalyticRatingWithIcon rateCount={positive} isPositive />
      </Grid.Col>
      <Grid.Col span={layout === ArticleDetailLayout.HORIZONTAL ? 4 : 6}>
        <AnalyticRatingWithIcon rateCount={negative} />
      </Grid.Col>
    </Grid>
  );
};

/**
 * AnalyticsRating is a component that displays the rating analytics.
 * Please make sure this component is used inside ArticleAnalyticsContextProvider.
 */
const AnalyticsRating: React.FC = () => {
  const { t } = useTranslate('article');
  const {
    articleAnalyticsState: { layout, summary, loading },
  } = useArticleAnalyticsContext();
  const { classes } = useAnalyticStyles();
  const [ratingDetailsOpen, { toggle }] = useDisclosure(true);

  return (
    <Box>
      <Grid>
        <Grid.Col span={layout === ArticleDetailLayout.HORIZONTAL ? 4 : 12}>
          <Group className={classes.titleGroup} onClick={toggle}>
            <Text className={classes.title}>{t('articleAnalytics.rating')}</Text>
            {ratingDetailsOpen ? (
              <IconChevronUp className={classes.chevron} />
            ) : (
              <IconChevronDown className={classes.chevron} />
            )}
          </Group>
        </Grid.Col>
        <Grid.Col span={layout === ArticleDetailLayout.HORIZONTAL ? 4 : 6}>
          {loading ? (
            <Skeleton className={classes.skeleton} />
          ) : (
            <AnalyticRatingWithIcon rateCount={summary.totalLikeCount} isPositive />
          )}
        </Grid.Col>
        <Grid.Col span={layout === ArticleDetailLayout.HORIZONTAL ? 4 : 6}>
          {loading ? (
            <Skeleton className={classes.skeleton} />
          ) : (
            <AnalyticRatingWithIcon rateCount={summary.totalDislikeCount} />
          )}
        </Grid.Col>
      </Grid>
      <Collapse in={ratingDetailsOpen}>
        {loading ? (
          <AnalyticsSkeleton isRating />
        ) : !isEmpty(summary.products) ? (
          <Box className={classes.analyticDetailsContainer}>
            {Object.entries(summary.products).map(([product, { likeCount, dislikeCount }]) => (
              <RatingRow
                key={product}
                title={t(`articleAnalytics.${product}`)}
                positive={likeCount}
                negative={dislikeCount}
              />
            ))}
          </Box>
        ) : (
          <Text className={classes.noDataText}>{t('articleAnalytics.noData')}</Text>
        )}
      </Collapse>
    </Box>
  );
};

export default AnalyticsRating;
