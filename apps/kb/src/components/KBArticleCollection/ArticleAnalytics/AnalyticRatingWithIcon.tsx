import { Group, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconMoodHappy, IconMoodSad } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useMemo } from 'react';

const useStyles = createStyles((theme) => ({
  title: {
    fontWeight: 500,
    color: theme.colors.decaNavy[5],
    fontSize: rem(12),
  },

  rateCount: {
    fontSize: rem(12),
    fontWeight: 500,
  },

  happyIcon: {
    color: theme.colors.green[5],
    width: rem(20),
    height: rem(20),
  },

  sadIcon: {
    color: theme.colors.red[5],
    width: rem(20),
    height: rem(20),
  },
}));

interface AnalyticRatingWithIconProps {
  rateCount: number;
  isPositive?: boolean;
}

const AnalyticRatingWithIcon: React.FC<AnalyticRatingWithIconProps> = ({
  rateCount,
  isPositive = false,
}) => {
  const { t } = useTranslate('article');
  const { classes } = useStyles();
  const icon = useMemo(
    () =>
      isPositive ? (
        <IconMoodHappy className={classes.happyIcon} />
      ) : (
        <IconMoodSad className={classes.sadIcon} />
      ),
    [isPositive, classes]
  );
  const title = useMemo(
    () => (isPositive ? t('articleAnalytics.goodRating') : t('articleAnalytics.badRating')),
    [isPositive, t]
  );

  return (
    <Group gap={rem(8)}>
      {icon}
      <Text className={classes.title}>{title}</Text>
      <Text className={classes.rateCount}>{rateCount}</Text>
    </Group>
  );
};

export default AnalyticRatingWithIcon;
