import { useArticleAnalyticsContext } from '@/contexts/ArticleAnalyticsContext';
import { ArticleAnalyticPeriod, type ArticleDateRangeValue } from '@/types/article';
import { getDateRangeByPeriod, getPeriodByDateRange } from '@/utils/article';
import { Box, Select, rem } from '@mantine/core';
import { DatePickerInput, DatesProvider } from '@mantine/dates';
import { createStyles } from '@mantine/emotion';
import { JA_DATE_FORMAT } from '@resola-ai/ui/constants';
import { convertToBeginOfDateInJA, convertToEndOfDateInJA } from '@resola-ai/ui/utils/dateTime';
import { IconCalendar, IconCalendarTime } from '@tabler/icons-react';
import { useTolgee, useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useMemo, useState } from 'react';

import 'dayjs/locale/ja';

const useStyles = createStyles(() => ({
  wrapper: {
    display: 'flex',
    gap: rem(16),
    alignItems: 'center',
  },
  select: {
    width: rem(200),
    '& .mantine-Select-input': {
      paddingRight: rem(8),
      fontSize: rem(12),
    },
  },
  datePicker: {
    width: rem(300),
    '& .mantine-DatePickerInput-input': {
      fontSize: rem(12),
      paddingRight: rem(8),
    },
  },
  calendarIcon: {
    width: rem(16),
    height: rem(16),
  },
}));

interface AnalyticsFilterProps {
  className?: string;
}

const AnalyticsFilter: React.FC<AnalyticsFilterProps> = ({ className }) => {
  const { t } = useTranslate('article');
  const tolgee = useTolgee();
  const { cx, classes } = useStyles();
  const {
    articleAnalyticsState: { articleId, withOriginalArticle },
    getAnalytics,
  } = useArticleAnalyticsContext();
  const [period, setPeriod] = useState<ArticleAnalyticPeriod>(ArticleAnalyticPeriod.LAST_7_DAYS);
  const [dateRange, setDateRange] = useState<ArticleDateRangeValue>(
    getDateRangeByPeriod(ArticleAnalyticPeriod.LAST_7_DAYS)
  );

  const periodOptions = useMemo(
    () => [
      {
        value: ArticleAnalyticPeriod.LAST_7_DAYS,
        label: t('articleAnalytics.quickFilter.last7Days'),
      },
      {
        value: ArticleAnalyticPeriod.LAST_28_DAYS,
        label: t('articleAnalytics.quickFilter.last28Days'),
      },
      {
        value: ArticleAnalyticPeriod.LAST_90_DAYS,
        label: t('articleAnalytics.quickFilter.last90Days'),
      },
      { value: ArticleAnalyticPeriod.ALL_TIME, label: t('articleAnalytics.quickFilter.allTime') },
    ],
    [t]
  );

  const handleFilterChange = useCallback(
    (dateRange: ArticleDateRangeValue) => {
      if (articleId) {
        getAnalytics(
          articleId,
          {
            from: dateRange[0] ? convertToBeginOfDateInJA(dateRange[0]) : null,
            to: dateRange[1] ? convertToEndOfDateInJA(dateRange[1]) : null,
          },
          withOriginalArticle
        );
      }
    },
    [articleId, getAnalytics, withOriginalArticle]
  );

  const handlePeriodChange = useCallback(
    (value: ArticleAnalyticPeriod) => {
      setPeriod(value);

      const dateRange =
        value !== ArticleAnalyticPeriod.ALL_TIME
          ? getDateRangeByPeriod(value)
          : ([null, null] as ArticleDateRangeValue);

      setDateRange(dateRange);
      handleFilterChange(dateRange);
    },
    [handleFilterChange]
  );

  const handleDateRangeChange = useCallback(
    (value: ArticleDateRangeValue) => {
      setDateRange(value);
      if (value[0] && value[1]) {
        const updatedPeriod = getPeriodByDateRange(value[0], value[1]);
        setPeriod(updatedPeriod);
        handleFilterChange(value);
      }
    },
    [handleFilterChange]
  );

  // When the articleId is changed, we need to reset the date range to the last 7 days
  useEffect(() => {
    if (articleId) {
      handlePeriodChange(ArticleAnalyticPeriod.LAST_7_DAYS);
    }
  }, [articleId, handlePeriodChange]);

  return (
    <DatesProvider settings={{ locale: tolgee.getLanguage() }}>
      <Box className={cx(classes.wrapper, className)}>
        <Select
          withCheckIcon={false}
          className={classes.select}
          value={period}
          onChange={(value) => value && handlePeriodChange(value as ArticleAnalyticPeriod)}
          data={periodOptions}
          placeholder={t('articleAnalytics.placeholder.selectPeriod')}
          leftSection={<IconCalendarTime className={classes.calendarIcon} />}
        />
        <DatePickerInput
          type='range'
          valueFormat={JA_DATE_FORMAT}
          allowSingleDateInRange
          className={classes.datePicker}
          value={dateRange}
          onChange={handleDateRangeChange}
          leftSection={<IconCalendar className={classes.calendarIcon} />}
          placeholder={t('articleAnalytics.placeholder.dateRange')}
        />
      </Box>
    </DatesProvider>
  );
};

export default AnalyticsFilter;
