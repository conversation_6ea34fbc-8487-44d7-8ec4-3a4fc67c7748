import { useArticleAnalyticsContext } from '@/contexts/ArticleAnalyticsContext';
import { ArticleDetailLayout } from '@/types/article';
import { Box, Grid, Skeleton } from '@mantine/core';
import { useMemo } from 'react';
import { useAnalyticStyles } from './useAnalyticStyles';

interface AnalyticsSkeletonProps {
  isRating?: boolean;
}

const AnalyticsSkeleton = ({ isRating }: AnalyticsSkeletonProps) => {
  const {
    articleAnalyticsState: { layout },
  } = useArticleAnalyticsContext();
  const { classes } = useAnalyticStyles();

  const span = useMemo(() => {
    return layout === ArticleDetailLayout.HORIZONTAL ? 4 : 6;
  }, [layout]);

  return (
    <Box className={classes.analyticDetailsContainer}>
      {[1, 2, 3].map((item) => (
        <Grid key={item}>
          <Grid.Col span={span}>
            <Skeleton className={classes.skeleton} />
          </Grid.Col>
          <Grid.Col span={span}>
            <Skeleton className={classes.skeleton} />
          </Grid.Col>
          {isRating && layout === ArticleDetailLayout.HORIZONTAL && (
            <Grid.Col span={span}>
              <Skeleton className={classes.skeleton} />
            </Grid.Col>
          )}
        </Grid>
      ))}
    </Box>
  );
};

export default AnalyticsSkeleton;
