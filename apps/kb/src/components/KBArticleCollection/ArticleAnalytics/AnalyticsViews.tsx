import { useArticleAnalyticsContext } from '@/contexts/ArticleAnalyticsContext';
import { ArticleAnalyticProduct, ArticleDetailLayout } from '@/types/article';
import { Box, Collapse, Grid, Group, Skeleton, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconChevronDown, IconChevronUp, IconEye } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import { useCallback } from 'react';
import AnalyticsSkeleton from './AnalyticsSkeleton';
import { useAnalyticStyles } from './useAnalyticStyles';

/**
 * ReviewRow is a component that displays a review row with a title, positive and negative views.
 * It is used in the Reviews section of the AnalyticsView component.
 */
interface ViewRowProps {
  title: string;
  viewCount: number;
}

const ViewRow: React.FC<ViewRowProps> = ({ title, viewCount }) => {
  const { t } = useTranslate('article');
  const {
    articleAnalyticsState: { layout },
  } = useArticleAnalyticsContext();
  const { classes } = useAnalyticStyles();

  return (
    <Grid align='center'>
      <Grid.Col span={layout === ArticleDetailLayout.HORIZONTAL ? 4 : 10}>
        <Group className={classes.titleGroup}>
          <IconEye className={classes.eyeIcon} />
          <Text className={classes.title}>{title}</Text>
        </Group>
      </Grid.Col>
      <Grid.Col span={layout === ArticleDetailLayout.HORIZONTAL ? 8 : 2}>
        <Text className={classes.viewCount}>
          {viewCount}
          {t('articleAnalytics.countSuffix')}
        </Text>
      </Grid.Col>
    </Grid>
  );
};

/**
 * AnalyticsViews is a component that displays the views analytics.
 * Please make sure this component is used inside ArticleAnalyticsContextProvider.
 */
const AnalyticsViews: React.FC = () => {
  const { t } = useTranslate('article');
  const {
    articleAnalyticsState: { layout, summary, loading },
  } = useArticleAnalyticsContext();
  const { classes } = useAnalyticStyles();
  const [viewsOpen, { toggle }] = useDisclosure(true);

  const getProductName = useCallback(
    (product: ArticleAnalyticProduct) => {
      switch (product) {
        case ArticleAnalyticProduct.CHATBOT:
          return `${t('articleAnalytics.chatbot')} (${t('articleAnalytics.customerSide')})`;

        case ArticleAnalyticProduct.FAQ:
          return `${t('articleAnalytics.faq')} (${t('articleAnalytics.customerSide')})`;

        case ArticleAnalyticProduct.MANAGEMENT:
          return `${t('articleAnalytics.management')} (${t('articleAnalytics.operatorSide')})`;

        case ArticleAnalyticProduct.AI_WIDGET:
          return `${t('articleAnalytics.aiwidget')} (${t('articleAnalytics.operatorSide')})`;

        case ArticleAnalyticProduct.CHAT_WINDOW:
          return `${t('articleAnalytics.chatwindow')} (${t('articleAnalytics.customerSide')})`;

        default:
          return '';
      }
    },
    [t]
  );

  return (
    <Box>
      <Grid>
        <Grid.Col span={layout === ArticleDetailLayout.HORIZONTAL ? 4 : 10}>
          <Group className={classes.titleGroup} onClick={toggle}>
            <Text className={classes.title}>{t('articleAnalytics.viewCount')}</Text>
            {viewsOpen ? (
              <IconChevronUp className={classes.chevron} />
            ) : (
              <IconChevronDown className={classes.chevron} />
            )}
          </Group>
        </Grid.Col>
        <Grid.Col span={layout === ArticleDetailLayout.HORIZONTAL ? 4 : 6}>
          {loading ? (
            <Skeleton className={classes.skeleton} />
          ) : (
            <Text className={classes.viewCount}>
              {summary.totalViewCount}
              {t('articleAnalytics.countSuffix')}
            </Text>
          )}
        </Grid.Col>
      </Grid>

      <Collapse in={viewsOpen}>
        {loading ? (
          <AnalyticsSkeleton />
        ) : !isEmpty(summary.products) ? (
          <Box className={classes.analyticDetailsContainer}>
            {Object.values(summary.products).map((product) => (
              <ViewRow
                key={product.product}
                title={getProductName(product.product)}
                viewCount={product.viewCount}
              />
            ))}
          </Box>
        ) : (
          <Text className={classes.noDataText}>{t('articleAnalytics.noData')}</Text>
        )}
      </Collapse>
    </Box>
  );
};

export default AnalyticsViews;
