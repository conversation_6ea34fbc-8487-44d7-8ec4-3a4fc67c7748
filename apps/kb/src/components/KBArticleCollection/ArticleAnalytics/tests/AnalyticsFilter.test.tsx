import { act, fireEvent, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock scrollIntoView method for all HTML elements to fix Mantine component errors
Object.defineProperty(HTMLElement.prototype, 'scrollIntoView', {
  value: vi.fn(),
  writable: true,
});

// Mock other DOM methods that might be called by Mantine components
Object.defineProperty(Element.prototype, 'scrollTo', {
  value: vi.fn(),
  writable: true,
});

Object.defineProperty(Element.prototype, 'scroll', {
  value: vi.fn(),
  writable: true,
});

// Mock @mantine/emotion
vi.mock('@mantine/emotion', async () => {
  const actual = await vi.importActual('@mantine/emotion');
  return {
    ...actual,
    createStyles: () => () => ({
      cx: (...args: any[]) => args.filter(Boolean).join(' '),
      classes: {
        wrapper: 'wrapper',
        select: 'select',
        datePicker: 'datePicker',
        calendarIcon: 'calendarIcon',
      },
    }),
  };
});

// Mock @mantine/dates
vi.mock('@mantine/dates', () => ({
  DatesProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  DatePickerInput: ({ placeholder, value, onChange, leftSection }: any) => (
    <div data-testid='date-picker'>
      {leftSection}
      <input
        placeholder={placeholder}
        value={
          value && Array.isArray(value)
            ? value
                .map((d) =>
                  d && d instanceof Date && !Number.isNaN(d.getTime())
                    ? d.toISOString().split('T')[0]
                    : ''
                )
                .filter(Boolean)
                .join(' - ')
            : ''
        }
        onChange={(e) => {
          // Simulate date range change
          const input = e.target.value.trim();
          if (input.includes(' - ')) {
            const dateStrings = input.split(' - ');
            const dates = dateStrings.map((d) => {
              try {
                return d ? new Date(d) : null;
              } catch {
                return null;
              }
            });
            onChange(dates);
          } else {
            onChange([null, null]);
          }
        }}
        data-testid='date-picker-input'
      />
    </div>
  ),
}));

// Import the required modules after the mocks
import { ArticleAnalyticsProvider } from '@/contexts/ArticleAnalyticsContext';
import { ArticleDetailLayout } from '@/types/article';
import { renderWithProviders } from '@/utils/unitTest';
import AnalyticsFilter from '../AnalyticsFilter';

// Store for the mock functions
const mockFunctions = {
  getAnalytics: vi.fn(),
  setArticleAnalyticsState: vi.fn(),
};

// Mock ArticleAnalyticsProvider
vi.mock('@/contexts/ArticleAnalyticsContext', () => {
  // We need to track the current articleId to allow test cases to override it
  let currentArticleId = 'article-123';

  return {
    ArticleAnalyticsProvider: ({
      children,
      articleId,
    }: {
      children: React.ReactNode;
      articleId: string;
    }) => {
      // When rendered, update the current articleId for the hook to use
      currentArticleId = articleId;
      return <div data-testid='article-analytics-context'>{children}</div>;
    },
    useArticleAnalyticsContext: () => ({
      articleAnalyticsState: {
        articleId: currentArticleId,
        withOriginalArticle: false,
      },
      getAnalytics: mockFunctions.getAnalytics,
      setArticleAnalyticsState: mockFunctions.setArticleAnalyticsState,
    }),
  };
});

// Mock article utils - Updated to use correct enum values
vi.mock('@/utils/article', () => ({
  getDateRangeByPeriod: (period: string) => {
    const today = new Date('2023-01-10');

    switch (period) {
      case 'last7days': {
        const weekAgo = new Date('2023-01-03');
        return [weekAgo, today];
      }
      case 'last28days': {
        const monthAgo = new Date('2022-12-13');
        return [monthAgo, today];
      }
      case 'last90days': {
        const threeMonthsAgo = new Date('2022-10-12');
        return [threeMonthsAgo, today];
      }
      default:
        return [null, null];
    }
  },
  getPeriodByDateRange: () => 'last7days',
}));

// Mock time utils
vi.mock('@resola-ai/ui/utils/dateTime', () => ({
  convertToBeginOfDateInJA: (date: Date) => date,
  convertToEndOfDateInJA: (date: Date) => date,
}));

// Mock constants
vi.mock('@resola-ai/ui/constants', async () => {
  const actual = await vi.importActual('@resola-ai/ui/constants');
  return {
    ...actual,
    JA_DATE_FORMAT: 'YYYY/MM/DD',
  };
});

// Mock icons
vi.mock('@tabler/icons-react', async () => {
  const actual = await vi.importActual('@tabler/icons-react');
  return {
    ...actual,
    IconCalendarTime: (props: any) => (
      <div data-testid='icon-calendar-time' {...props}>
        IconCalendarTime
      </div>
    ),
    IconCalendar: (props: any) => (
      <div data-testid='icon-calendar' {...props}>
        IconCalendar
      </div>
    ),
  };
});

// Constants for test
const TEST_ARTICLE_ID = 'article-123';
const TEST_CLASS_NAME = 'test-class';

// Test component wrapper
const renderAnalyticsFilter = () => {
  return renderWithProviders(
    <ArticleAnalyticsProvider
      articleId={TEST_ARTICLE_ID}
      layout={ArticleDetailLayout.HORIZONTAL}
      withOriginalArticle={false}
    >
      <AnalyticsFilter className={TEST_CLASS_NAME} />
    </ArticleAnalyticsProvider>
  );
};

describe('AnalyticsFilter', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();
    mockFunctions.getAnalytics.mockClear();
    mockFunctions.setArticleAnalyticsState.mockClear();
  });

  it('renders with default period (Last 7 days)', async () => {
    const { container } = renderAnalyticsFilter();

    // Check if the Mantine Select element is rendered (it's a textbox with aria-haspopup="listbox")
    const selectInput = container.querySelector('input[aria-haspopup="listbox"]');
    expect(selectInput).toBeInTheDocument();
    expect(screen.getByTestId('date-picker')).toBeInTheDocument();

    // Check if icons are rendered properly (they render as SVG elements with classes)
    expect(document.querySelector('.tabler-icon-calendar-time')).toBeInTheDocument();
    expect(document.querySelector('.tabler-icon-calendar')).toBeInTheDocument();

    // The component calls getAnalytics on mount due to useEffect
    await waitFor(() => {
      expect(mockFunctions.getAnalytics).toHaveBeenCalled();
    });
  });

  it('applies className prop correctly', () => {
    const { container } = renderAnalyticsFilter();

    // Find the wrapper element with the test class
    const wrapperElement = container.querySelector(`.${TEST_CLASS_NAME}`);
    expect(wrapperElement).toBeInTheDocument();
  });

  it('calls getAnalytics when period changes', async () => {
    const user = userEvent.setup();
    const { container } = renderAnalyticsFilter();

    // Clear initial call
    mockFunctions.getAnalytics.mockClear();

    // Click on the select to open it - use the input with aria-haspopup="listbox"
    const selectInput = container.querySelector('input[aria-haspopup="listbox"]');
    expect(selectInput).toBeInTheDocument();
    await user.click(selectInput!);

    // Wait for the dropdown to open and find the option
    await waitFor(() => {
      expect(screen.getByText('articleAnalytics.quickFilter.last28Days')).toBeInTheDocument();
    });

    // Click on the "Last 28 days" option
    await user.click(screen.getByText('articleAnalytics.quickFilter.last28Days'));

    // Verify getAnalytics was called
    await waitFor(() => {
      expect(mockFunctions.getAnalytics).toHaveBeenCalled();
    });
  });

  it('calls getAnalytics when date range changes', async () => {
    renderAnalyticsFilter();

    // Clear initial call
    mockFunctions.getAnalytics.mockClear();

    // Change date range by simulating a fireEvent
    const datePickerInput = screen.getByTestId('date-picker-input');

    // Simulate typing a complete date range that should trigger onChange
    await act(async () => {
      fireEvent.change(datePickerInput, { target: { value: '2023-01-01 - 2023-01-31' } });
    });

    // Verify getAnalytics was called when date range changes
    await waitFor(() => {
      expect(mockFunctions.getAnalytics).toHaveBeenCalled();
    });
  });

  it('handles "All time" period selection correctly', async () => {
    const user = userEvent.setup();
    const { container } = renderAnalyticsFilter();

    // Clear initial call
    mockFunctions.getAnalytics.mockClear();

    // Click on the select to open it - use the input with aria-haspopup="listbox"
    const selectInput = container.querySelector('input[aria-haspopup="listbox"]');
    expect(selectInput).toBeInTheDocument();
    await user.click(selectInput!);

    // Wait for the dropdown to open and find the option
    await waitFor(() => {
      expect(screen.getByText('articleAnalytics.quickFilter.allTime')).toBeInTheDocument();
    });

    // Click on the "All time" option
    await user.click(screen.getByText('articleAnalytics.quickFilter.allTime'));

    // Verify getAnalytics was called with null dates
    await waitFor(() => {
      expect(mockFunctions.getAnalytics).toHaveBeenCalledWith(
        TEST_ARTICLE_ID,
        expect.objectContaining({
          from: null,
          to: null,
        }),
        false
      );
    });
  });

  it('resets filter to Last 7 days when articleId changes', async () => {
    // This test verifies that the useEffect in the component properly handles articleId changes
    // Since the context mock tracks articleId changes, we can verify this behavior indirectly
    const { rerender } = renderWithProviders(
      <ArticleAnalyticsProvider
        articleId={TEST_ARTICLE_ID}
        layout={ArticleDetailLayout.HORIZONTAL}
        withOriginalArticle={false}
      >
        <AnalyticsFilter className={TEST_CLASS_NAME} />
      </ArticleAnalyticsProvider>
    );

    // Clear initial call
    mockFunctions.getAnalytics.mockClear();

    // Change articleId by re-rendering with a new articleId
    const newArticleId = 'article-456';

    await act(async () => {
      rerender(
        <ArticleAnalyticsProvider
          articleId={newArticleId}
          layout={ArticleDetailLayout.HORIZONTAL}
          withOriginalArticle={false}
        >
          <AnalyticsFilter className={TEST_CLASS_NAME} />
        </ArticleAnalyticsProvider>
      );
    });

    // The component should call getAnalytics when it re-renders with new articleId
    // Due to the useEffect dependency on articleId
    await waitFor(() => {
      expect(mockFunctions.getAnalytics).toHaveBeenCalled();
    });
  });

  it('does not call getAnalytics when articleId is not provided', async () => {
    // Clear mock call history
    mockFunctions.getAnalytics.mockClear();

    // For this specific test, render with an empty articleId
    const { container } = renderWithProviders(
      <ArticleAnalyticsProvider
        articleId={''}
        layout={ArticleDetailLayout.HORIZONTAL}
        withOriginalArticle={false}
      >
        <AnalyticsFilter className={TEST_CLASS_NAME} />
      </ArticleAnalyticsProvider>
    );

    // Try to change the period by clicking the select
    const selectInput = container.querySelector('input[aria-haspopup="listbox"]');
    expect(selectInput).toBeInTheDocument();
    await act(async () => {
      fireEvent.click(selectInput!);
    });

    // Wait a bit to ensure no delayed calls
    await new Promise((resolve) => setTimeout(resolve, 100));

    // Verify getAnalytics was not called
    expect(mockFunctions.getAnalytics).not.toHaveBeenCalled();
  });

  it('synchronizes period and date range correctly', async () => {
    const user = userEvent.setup();
    const { container } = renderAnalyticsFilter();

    // Clear initial call
    mockFunctions.getAnalytics.mockClear();

    // Click on the select to open it - use the input with aria-haspopup="listbox"
    const selectInput = container.querySelector('input[aria-haspopup="listbox"]');
    expect(selectInput).toBeInTheDocument();
    await user.click(selectInput!);

    // Wait for the dropdown to open and find the option
    await waitFor(() => {
      expect(screen.getByText('articleAnalytics.quickFilter.last90Days')).toBeInTheDocument();
    });

    // Click on the "Last 90 days" option
    await user.click(screen.getByText('articleAnalytics.quickFilter.last90Days'));

    // Verify getAnalytics was called
    await waitFor(() => {
      expect(mockFunctions.getAnalytics).toHaveBeenCalled();
    });
  });

  it('renders with correct placeholder texts', () => {
    const { container } = renderAnalyticsFilter();

    // Check select placeholder (it should be in the input element)
    const selectInput = container.querySelector('input[aria-haspopup="listbox"]');
    expect(selectInput).toBeInTheDocument();
    expect(selectInput).toHaveAttribute('placeholder', 'articleAnalytics.placeholder.selectPeriod');

    // Check datepicker placeholder (should use the translation key)
    const datePickerInput = screen.getByTestId('date-picker-input');
    expect(datePickerInput).toHaveAttribute(
      'placeholder',
      'articleAnalytics.placeholder.dateRange'
    );
  });
});
