import { FormMessage } from '@/components/common';
import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaBadge, DecaButton } from '@resola-ai/ui';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import some from 'lodash/some';
import type React from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { TagsInput } from 'react-tag-input-component';

const useStyles = createStyles((theme) => ({
  tagsInput: {
    display: 'none',
    '&.shown': {
      display: 'block',
    },
    '.rti--container': {
      border: 'none',
      outline: `${rem(1)} solid ${theme.colors.decaBlue[5]}`,
    },
    '.rti--tag': {
      fontWeight: 400,
      minHeight: rem(20),
      borderRadius: rem(14),
      padding: `${rem(6)} ${rem(6)} ${rem(6)} ${rem(16)}`,
      color: theme.colors.decaGrey[9],
      backgroundColor: theme.colors.decaBlue[0],
      wordBreak: 'break-all',
      display: 'flex',
      alignItems: 'center',
      '& > span': {
        fontSize: theme.fontSizes.sm,
        lineHeight: theme.fontSizes.xl,
        marginRight: rem(4),
      },
    },
    '.rti--input': {
      width: '25%',
    },
  },
  error: {
    '.rti--container': {
      outline: `${rem(1)} solid ${theme.colors.decaRed[5]}`,
    },
  },
  tagsView: {
    display: 'flex',
    flexWrap: 'wrap',
    alignItems: 'center',
    gap: rem(12),
  },
  keywordBadge: {
    textTransform: 'none',
    fontSize: theme.fontSizes.sm,
  },
  placeholderButton: {
    '& .mantine-Button-label': {
      fontSize: theme.fontSizes.sm,
    },
  },
  actionButton: {
    marginTop: rem(12),
    minWidth: rem(90),
    '& .mantine-Button-label': {
      fontSize: theme.fontSizes.sm,
    },
  },
  cancelButton: {
    marginRight: rem(12),
  },
  formMessage: {
    marginTop: rem(8),
  },
  addIcon: {
    width: rem(16),
    height: rem(16),
  },
}));

interface KeyPhrasesInputProps {
  id?: string;
  keywords: string[];
  placeholder?: string;
  disabled?: boolean;
  disallowedCharacters?: string[];
  onChange?: (keywords: string[]) => void;
}

const MAX_KEYWORD_LENGTH = 255;
const InputSeparators = ['Enter'];
const KeyPhrasesInput: React.FC<KeyPhrasesInputProps> = ({
  id = '',
  keywords = [],
  placeholder = '',
  disallowedCharacters = [],
  disabled = false,
  onChange,
}) => {
  const { cx, classes } = useStyles();
  const { t } = useTranslate(['article', 'common']);
  const keywordsRef = useRef<HTMLInputElement>(null);
  const [tags, setTags] = useState<string[]>(keywords || []);
  const [editKeywords, setEditKeywords] = useState(false);
  const [hasError, setHasError] = useState(false);
  const enabledDisallowedCharactersWarning = useMemo(
    () => disallowedCharacters.length > 0,
    [disallowedCharacters]
  );

  /**
   * Check if the value has any disallowed characters
   * @param value - The value to check
   * @returns boolean
   */
  const hasAnyDisallowedCharacters = useCallback(
    (value: string) => some(disallowedCharacters, (char) => value.includes(char)),
    [disallowedCharacters]
  );

  /**
   * Handle validation before Entering and Adding keyword
   */
  const validateBeforeAddingKeyword = useCallback(
    (keyword: string) => {
      const trimmedKeyword = keyword?.trim() || '';
      return (
        !!trimmedKeyword &&
        !hasAnyDisallowedCharacters(trimmedKeyword) &&
        trimmedKeyword.length < MAX_KEYWORD_LENGTH
      );
    },
    [hasAnyDisallowedCharacters]
  );

  /**
   * Enable edit keywords mode
   * @returns void
   */
  const enableEditKeywords = useCallback(() => {
    setEditKeywords(true);
  }, [setEditKeywords]);

  /**
   * Focus on the TagsInput element via keywordsRef
   * @returns void
   * @dependencies [keywordsRef]
   */
  const focusInput = useCallback(() => {
    const input = keywordsRef.current?.querySelector('.rti--input') as HTMLInputElement;
    input.focus();
  }, [keywordsRef.current]);

  /**
   * Disable edit keywords mode
   * @returns void
   */
  const handleSaveKeywords = useCallback(() => {
    const input = keywordsRef.current?.querySelector('.rti--input') as HTMLInputElement;

    if (input.value && !tags.includes(input.value) && validateBeforeAddingKeyword(input.value)) {
      const newTags = [...tags, input.value];

      setTags(newTags);
      onChange?.(newTags);

      input.value = '';
    } else {
      onChange?.(tags);
    }

    setEditKeywords(false);
  }, [tags, onChange]);

  /**
   * Cancel edit keywords mode
   * @returns void
   */
  const handleCancelEditKeywords = useCallback(() => {
    setTags(keywords || []);
    setEditKeywords(false);
  }, [keywords, setTags, setEditKeywords]);

  /**
   * Prevent characters from being entered in the input field
   * @dependencies [onKeyUp]
   * @returns void
   */
  const onKeyUp = useCallback(
    (event: KeyboardEvent) => {
      const value = (event.target as HTMLInputElement).value;
      setHasError(hasAnyDisallowedCharacters(value));
    },
    [hasAnyDisallowedCharacters, setHasError]
  );

  /**
   * Focus on the input element when editKeywords state is true
   * @dependencies [editKeywords]
   */
  useEffect(() => {
    if (editKeywords) {
      focusInput();
    }
  }, [editKeywords, focusInput]);

  /**
   * Reset tags and editKeywords state when id changes
   * because we have the same fields key for different articles
   * @dependencies [id]
   * @returns void
   */
  useEffect(() => {
    setTags(keywords);
    setEditKeywords(false);
  }, [id]);

  /**
   * Add event listener to the input element to prevent disallowed characters
   * @dependencies [onKeyUp]
   * @returns void
   */
  useEffect(() => {
    const input = keywordsRef.current?.querySelector('.rti--input') as HTMLInputElement;
    if (input) {
      input.addEventListener('keyup', onKeyUp);
    }

    return () => {
      if (input) {
        input.removeEventListener('keyup', onKeyUp);
      }
    };
  }, [onKeyUp]);

  return (
    <>
      <Box
        className={cx(
          classes.tagsInput,
          editKeywords ? 'shown' : 'hidden',
          hasError && classes.error
        )}
        ref={keywordsRef}
      >
        <TagsInput
          value={tags}
          separators={InputSeparators}
          isEditOnRemove
          beforeAddValidate={validateBeforeAddingKeyword}
          onChange={setTags}
          onRemoved={focusInput}
        />
        {enabledDisallowedCharactersWarning && (
          <FormMessage
            className={classes.formMessage}
            message={t('errors.disallowedSpaceAndComma', { ns: 'common' })}
            type={hasError ? 'error' : 'info'}
          />
        )}

        <DecaButton
          className={cx(classes.actionButton, classes.cancelButton)}
          size='sm'
          radius={'xl'}
          variant='neutral'
          onClick={handleCancelEditKeywords}
        >
          {t('actions.cancel', { ns: 'common' })}
        </DecaButton>
        <DecaButton
          className={classes.actionButton}
          size='sm'
          radius={'xl'}
          variant='primary'
          onClick={handleSaveKeywords}
        >
          {t('actions.save', { ns: 'common' })}
        </DecaButton>
      </Box>
      {!editKeywords && (
        <Box className={classes.tagsView}>
          {tags.map((tag) => (
            <DecaBadge className={classes.keywordBadge} key={tag} text={tag} />
          ))}
          <DecaButton
            className={classes.placeholderButton}
            leftSection={<IconPlus className={classes.addIcon} />}
            size='sm'
            radius={'xl'}
            variant='neutral'
            disabled={disabled}
            onClick={enableEditKeywords}
          >
            {placeholder || t('articleCollection.keyPhrasesPlaceholder')}
          </DecaButton>
        </Box>
      )}
    </>
  );
};

export default KeyPhrasesInput;
