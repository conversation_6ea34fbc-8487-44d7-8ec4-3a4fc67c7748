import type { Article } from '@/types';
import { Box, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useId } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import KeyPhrasesInput from './KeyPhrasesInput';

const useStyles = createStyles((theme) => ({
  section: {
    marginBottom: rem(24),
    padding: rem(12),
  },
  sectionTitle: {
    color: theme.colors.decaNavy[5],
    marginBottom: rem(16),
  },
}));

interface ArticleKeyPhrasesProps {
  article: Partial<Article> | null;
  onKeywordsChange: (keywords: string[]) => void;
  disabled?: boolean;
}

const ArticleKeyPhrases: React.FC<ArticleKeyPhrasesProps> = ({
  article,
  onKeywordsChange,
  disabled = false,
}) => {
  const { t } = useTranslate('article');
  const { classes } = useStyles();
  const customId = useId();

  return (
    <Box className={classes.section}>
      <Title className={classes.sectionTitle} order={5}>
        {t('articleCollection.keyPhrases')}
      </Title>
      <KeyPhrasesInput
        id={article?.id || customId}
        keywords={article?.keywords || []}
        onChange={onKeywordsChange}
        disabled={disabled}
      />
    </Box>
  );
};

export default ArticleKeyPhrases;
