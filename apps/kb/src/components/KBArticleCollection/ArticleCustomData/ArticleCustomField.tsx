import { LIST_DISALLOWED_CHARACTERS } from '@/constants/template';
import {
  type KBCustomFieldValue,
  KBTemplateDataTypeEnum,
  type KBTemplateField,
} from '@/types/template';
import { checkListValue } from '@/utils/template';
import { Box, Text, Tooltip, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconInfoCircle } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useMemo } from 'react';
import KeyPhrasesInput from '../ArticleKeyPhrases/KeyPhrasesInput';
import ArticleTextInput from '../ArticleTextInput';

const useStyles = createStyles((theme) => ({
  formField: {
    marginBottom: rem(24),
  },
  labelWrapper: {
    marginBottom: rem(16),
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    lineHeight: rem(18),
  },
  label: {
    fontSize: rem(18),
    lineHeight: rem(28),
    fontWeight: 700,
    color: theme.colors.decaNavy[5],
    marginBottom: 0,
  },
  labelTooltip: {
    fontSize: rem(12),
    lineHeight: rem(12),
    fontStyle: 'italic',
    cursor: 'pointer',
    padding: rem(8),
  },
  inputField: {
    '& input': {
      border: 'none',
      fontWeight: 500,
      fontSize: rem(16),
    },
  },
  icon: {
    width: rem(20),
    height: rem(20),
    color: theme.colors.decaBlue[5],
    marginLeft: rem(8),
  },
}));

interface ArticleCustomFieldProps {
  id: string;
  field: KBTemplateField;
  currentValue: any;
  callingSubmitWhenEnter?: boolean;
  disabled?: boolean;
  onChange: (fieldId: string, value: KBCustomFieldValue) => any;
  onSubmitted?: (customData: KBTemplateField[]) => void;
}

const ArticleCustomField: React.FC<ArticleCustomFieldProps> = ({
  id,
  field,
  currentValue,
  callingSubmitWhenEnter = false,
  disabled = false,
  onChange,
  onSubmitted,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate(['settings', 'common']);

  // Check if the field should be submitted when the user presses Enter
  const showSubmitWhenEnterTooltip = useMemo(
    () => field.dataType === KBTemplateDataTypeEnum.text && callingSubmitWhenEnter,
    [field, callingSubmitWhenEnter]
  );

  const fieldControl = useMemo(() => {
    switch (field.dataType) {
      case KBTemplateDataTypeEnum.list:
        return (
          <KeyPhrasesInput
            id={id}
            keywords={checkListValue(currentValue)}
            disallowedCharacters={LIST_DISALLOWED_CHARACTERS}
            placeholder={field.description || t('templateCustomData.listPlaceholder')}
            disabled={disabled}
            onChange={(newList: KBCustomFieldValue) => {
              const updatedData = onChange(field.id, newList);
              onSubmitted?.(updatedData);
            }}
          />
        );

      case KBTemplateDataTypeEnum.text:
      default:
        return (
          <ArticleTextInput
            id={id}
            value={currentValue}
            disabled={disabled}
            className={classes.inputField}
            placeholder={field.description || t('templateCustomData.textPlaceholder')}
            onChange={(newValue: KBCustomFieldValue) => {
              return onChange(field.id, newValue);
            }}
            onSubmitted={onSubmitted}
          />
        );
    }
  }, [id, field, currentValue, onChange, disabled]);

  return (
    <Box className={classes.formField}>
      <Box className={classes.labelWrapper}>
        <Text className={classes.label}>{field.title}</Text>
        {showSubmitWhenEnterTooltip && (
          <Tooltip
            className={classes.labelTooltip}
            color='blue'
            arrowSize={6}
            label={t('form.pressEnterToSave', { ns: 'common' })}
            withArrow
          >
            <IconInfoCircle className={classes.icon} />
          </Tooltip>
        )}
      </Box>

      {fieldControl}
    </Box>
  );
};

export default ArticleCustomField;
