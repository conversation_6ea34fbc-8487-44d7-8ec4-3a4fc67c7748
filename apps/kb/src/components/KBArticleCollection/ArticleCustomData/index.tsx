import { useArticleCustomContext } from '@/contexts/ArticleCustomContext';
import type { Article, KBCustomFieldValue, KBTemplateField, KBTemplateFieldsSet } from '@/types';
import { orderCustomFieldsByTemplateOrders } from '@/utils/template';
import { Box, LoadingOverlay, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useId } from '@mantine/hooks';
import isEmpty from 'lodash/isEmpty';
import type React from 'react';
import { useCallback, useEffect, useMemo } from 'react';
import ArticleCustomField from './ArticleCustomField';

const useStyles = createStyles((theme) => ({
  section: {
    borderTop: `${rem(1)} solid ${theme.colors.gray[2]}`,
    padding: `${rem(24)} ${rem(12)} ${rem(12)}`,
    marginBottom: rem(24),
  },
}));

interface ArticleCustomDataProps {
  article: Article | null;
  onCustomDataChanged: (customData: Partial<KBTemplateField>[]) => void;
  onCustomDataSubmitted?: (customData: Partial<KBTemplateField>[] | null) => void;
  callingSubmitWhenEnter?: boolean;
  disabled?: boolean;
}

const ArticleCustomData: React.FC<ArticleCustomDataProps> = ({
  article = null,
  onCustomDataChanged,
  onCustomDataSubmitted,
  callingSubmitWhenEnter = false,
  disabled = false,
}) => {
  const uuid = useId();
  const { classes } = useStyles();
  const {
    articleCustomState: { loading, customFields },
    resetState,
    setInitialCustomFields,
    updateCustomFieldValue,
    fetchDefaultTemplate,
    fetchDefaultTemplateCustomFields,
  } = useArticleCustomContext();

  /**
   * Fetches the custom fields for the article's default template
   * @returns {Promise<void>}
   * @dependencies [loading, fetchDefaultTemplate, fetchDefaultTemplateCustomFields]
   */
  const initCustomFieldsFromTemplate = useCallback(async () => {
    if (loading) return;

    // Fetch template and handle potential null case
    const defaultTemplate = await fetchDefaultTemplate();
    if (!defaultTemplate?.id) return;

    // Fetch custom fields for template
    const customFieldsResponse = await fetchDefaultTemplateCustomFields(defaultTemplate.id);
    if (customFieldsResponse?.status !== 'success') return;

    const { customDataOrder: orders } = defaultTemplate;
    const { data: fields } = customFieldsResponse;

    // Skip processing if no fields exist
    if (!fields?.length) return;

    // Order fields if order configuration exists
    const orderedFields = orders?.length
      ? (orderCustomFieldsByTemplateOrders(fields, orders) as KBTemplateField[])
      : fields;

    // Normalize and update fields
    const normalizedFields = setInitialCustomFields(orderedFields);
    if (normalizedFields && Object.keys(normalizedFields).length) {
      onCustomDataChanged(Object.values(normalizedFields));
    }
  }, [fetchDefaultTemplate, fetchDefaultTemplateCustomFields, loading, onCustomDataChanged]);

  /**
   * Initializes the custom fields from the article's custom data
   * @dependencies [article, setInitialCustomFields]
   */
  const initCustomFieldsFromArticle = useCallback(() => {
    if (article && !isEmpty(article.customData)) {
      setInitialCustomFields([...(article.customData || [])]);
    }
  }, [article]);

  /**
   * Handles changes to a custom field value
   * Updates the custom fields state and notifies parent component of changes
   * @param {string} fieldId - The ID of the field that was changed
   * @param {KBCustomFieldValue} value - The new value of the field
   * @returns {KBTemplateFieldsSet | null} The updated custom fields object or null if empty
   */
  const handleCustomFieldChange = useCallback(
    (fieldId: string, value: KBCustomFieldValue): KBTemplateFieldsSet | null => {
      const newCustomFields = updateCustomFieldValue(fieldId, value);

      if (!isEmpty(newCustomFields)) {
        onCustomDataChanged(Object.values(newCustomFields));
        return newCustomFields;
      }

      return null;
    },
    [updateCustomFieldValue, onCustomDataChanged]
  );

  /**
   * Handles submission of custom field values
   * Calls the onCustomDataSubmitted callback with the current or new custom fields
   * @param {Object} newCustomFields - The new custom fields object to submit
   * @dependencies [onCustomDataSubmitted, customFields]
   * @returns {void}
   */
  const handleCustomFieldSubmitted = useCallback(
    (newCustomFields) => {
      onCustomDataSubmitted?.(Object.values(newCustomFields || customFields));
    },
    [onCustomDataSubmitted, customFields]
  );

  /**
   * Renders the custom fields when customFields are ready
   * @dependencies [customFields]
   * @returns {React.ReactNode} The rendered custom fields
   */
  const renderCustomFields = useMemo(() => {
    if (isEmpty(customFields)) return null;

    return (
      <Box className={classes.section}>
        {Object.values(customFields).map((field: KBTemplateField) => (
          <ArticleCustomField
            id={`${uuid}_${field.id}`}
            key={field.id}
            disabled={disabled}
            field={field}
            callingSubmitWhenEnter={callingSubmitWhenEnter}
            currentValue={customFields?.[field.id]?.value ?? ''}
            onChange={handleCustomFieldChange}
            onSubmitted={handleCustomFieldSubmitted}
          />
        ))}
      </Box>
    );
  }, [
    customFields,
    uuid,
    callingSubmitWhenEnter,
    handleCustomFieldChange,
    handleCustomFieldSubmitted,
    disabled,
  ]);

  useEffect(() => {
    if (!article) {
      initCustomFieldsFromTemplate();
    }

    if (article && !isEmpty(article.customData)) {
      initCustomFieldsFromArticle();
    }

    return () => {
      resetState();
    };
  }, [article]);

  return (
    <>
      <LoadingOverlay visible={loading} />
      {renderCustomFields}
    </>
  );
};

export default ArticleCustomData;
