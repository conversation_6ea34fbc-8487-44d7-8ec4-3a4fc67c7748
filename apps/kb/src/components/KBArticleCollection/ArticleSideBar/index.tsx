import { KBComments } from '@/components';
import { CommentContextProvider, useArticleDetailContext } from '@/contexts';
import useKbAccessControl from '@/hooks/useKbAccessControl';
import { type Article, ArticleDetailLayout } from '@/types';
import { Box, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useMemo } from 'react';
import ArticleInfo from '../ArticleInfo';

const useStyles = createStyles((theme) => ({
  sidebar: {
    padding: `${rem(0)} ${rem(12)}`,
    width: rem('100%'),
    backgroundColor: 'transparent',
  },
  sidebarFullView: {
    padding: rem(16),
    width: rem(400),
    backgroundColor: theme.colors.decaLight[0],
    position: 'absolute',
    top: 0,
    right: 0,
    zIndex: 2001, // This zIndex to make sure the Side Bar is always on top of the Drawer Header
    borderLeft: `${rem(1)} solid ${theme.colors.decaLight[2]}`,
  },
  sidebarSticky: {
    width: rem('100%'),
  },
  section: {
    marginBottom: rem(32),
  },
  sectionTitle: {
    color: theme.colors.decaNavy[5],
    marginBottom: rem(16),
    fontWeight: 700,
  },
}));

interface ArticleSideBarProps {
  article: Article;
  isFullView: boolean;
}

const ARTICLE_SIDE_BAR_HEIGHT_OFFSET = 120;
const ArticleSideBar: React.FC<ArticleSideBarProps> = (props) => {
  const { article, isFullView = false } = props;
  const { t } = useTranslate('article');
  const { permComment } = useKbAccessControl();
  const { classes, cx } = useStyles();
  const { articleContentRect } = useArticleDetailContext();

  const sidebarHeight = useMemo(() => {
    return isFullView ? articleContentRect?.height + ARTICLE_SIDE_BAR_HEIGHT_OFFSET : '100%';
  }, [isFullView, articleContentRect]);

  /**
   * Article Comments render
   * @returns {JSX.Element}
   * @dependencies CommentContextProvider: React Context Provider, KBComments: React Component
   */
  const articleComments = useMemo(() => {
    return article ? (
      <CommentContextProvider>
        <KBComments baseId={article.baseId} articleId={article.id} />
      </CommentContextProvider>
    ) : null;
  }, [article]);

  return (
    <Box
      className={cx(classes.sidebar, isFullView && classes.sidebarFullView)}
      style={{
        minHeight: sidebarHeight,
      }}
    >
      <Box className={classes.sidebarSticky}>
        <Box className={classes.section}>
          <Title className={classes.sectionTitle} order={5}>
            {t('articleCollection.info')}
          </Title>
          {article && (
            <ArticleInfo
              articleId={article.id}
              lastUpdated={article.updatedAt}
              createdAt={article.createdAt}
              createdBy={article.createdBy}
              layout={isFullView ? ArticleDetailLayout.VERTICAL : ArticleDetailLayout.HORIZONTAL}
              originArticleId={article.originArticleId}
            />
          )}
        </Box>
        {permComment.canView && (
          <Box className={classes.section}>
            <Title className={classes.sectionTitle} order={5}>
              {t('comments.title')}
            </Title>
            {articleComments}
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default ArticleSideBar;
