import { renderWithProviders } from '@/utils/unitTest';
import { getKeyFromText } from '@resola-ai/ui/utils';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import ArticleThreeDotsMenu from './ArticleThreeDotsMenu';

// Mock ThreeDotsMenu from @resola-ai/ui
vi.mock('@resola-ai/ui', async () => {
  const actual = await vi.importActual('@resola-ai/ui');
  return {
    ...actual,
    ThreeDotsMenu: ({ items }) => (
      <div data-testid='three-dots-menu'>
        {items.map((item, index) => (
          <button
            type='button'
            key={`${getKeyFromText(item.label)}-${index}`}
            data-testid={`menu-item-${getKeyFromText(item.label)}`}
            onClick={item.onClick}
            className={item.className || ''}
          >
            {item.label}
          </button>
        ))}
      </div>
    ),
  };
});

describe('ArticleThreeDotsMenu', () => {
  // Define constants for callback functions
  const HANDLERS = {
    onMove: vi.fn(),
    onDelete: vi.fn(),
    onCreateShortcut: vi.fn(),
  };

  // Define translation constants - using the actual translation keys as they appear in the rendered output
  const TRANSLATIONS = {
    move: 'actions.move',
    delete: 'actions.delete',
    createShortcut: 'actions.createshortcut',
  };

  // Reset mocks before each test
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should not render when no handlers are provided', () => {
    // Render with no handlers (all undefined)
    renderWithProviders(<ArticleThreeDotsMenu onMove={undefined} onDelete={undefined} />);

    // Assert: Menu should not be rendered
    expect(screen.queryByTestId('three-dots-menu')).not.toBeInTheDocument();
  });

  it('should render only move option when only onMove is provided', () => {
    // Render with only onMove handler
    renderWithProviders(<ArticleThreeDotsMenu onMove={HANDLERS.onMove} onDelete={undefined} />);

    // Assert: Only move option should be visible
    expect(screen.getByTestId('three-dots-menu')).toBeInTheDocument();
    expect(screen.getByTestId(`menu-item-${TRANSLATIONS.move}`)).toBeInTheDocument();
    expect(screen.queryByTestId(`menu-item-${TRANSLATIONS.delete}`)).not.toBeInTheDocument();
    expect(
      screen.queryByTestId(`menu-item-${TRANSLATIONS.createShortcut}`)
    ).not.toBeInTheDocument();
  });

  it('should render only delete option when only onDelete is provided', () => {
    // Render with only onDelete handler
    renderWithProviders(<ArticleThreeDotsMenu onMove={undefined} onDelete={HANDLERS.onDelete} />);

    // Assert: Only delete option should be visible
    expect(screen.getByTestId('three-dots-menu')).toBeInTheDocument();
    expect(screen.queryByTestId(`menu-item-${TRANSLATIONS.move}`)).not.toBeInTheDocument();
    expect(screen.getByTestId(`menu-item-${TRANSLATIONS.delete}`)).toBeInTheDocument();
    expect(
      screen.queryByTestId(`menu-item-${TRANSLATIONS.createShortcut}`)
    ).not.toBeInTheDocument();
  });

  it('should render all options when all handlers are provided', () => {
    // Render with all handlers
    renderWithProviders(
      <ArticleThreeDotsMenu
        onMove={HANDLERS.onMove}
        onDelete={HANDLERS.onDelete}
        onCreateShortcut={HANDLERS.onCreateShortcut}
      />
    );

    // Assert: All options should be visible
    expect(screen.getByTestId('three-dots-menu')).toBeInTheDocument();
    expect(screen.getByTestId(`menu-item-${TRANSLATIONS.move}`)).toBeInTheDocument();
    expect(screen.getByTestId(`menu-item-${TRANSLATIONS.delete}`)).toBeInTheDocument();
    expect(screen.getByTestId(`menu-item-${TRANSLATIONS.createShortcut}`)).toBeInTheDocument();
  });

  it('should not render createShortcut option if callback is not provided', () => {
    // Render with move and delete but no createShortcut
    renderWithProviders(
      <ArticleThreeDotsMenu
        onMove={HANDLERS.onMove}
        onDelete={HANDLERS.onDelete}
        // onCreateShortcut intentionally omitted
      />
    );

    // Assert: createShortcut option should not be visible
    expect(screen.getByTestId('three-dots-menu')).toBeInTheDocument();
    expect(screen.getByTestId(`menu-item-${TRANSLATIONS.move}`)).toBeInTheDocument();
    expect(screen.getByTestId(`menu-item-${TRANSLATIONS.delete}`)).toBeInTheDocument();
    expect(
      screen.queryByTestId(`menu-item-${TRANSLATIONS.createShortcut}`)
    ).not.toBeInTheDocument();
  });

  it('should call onMove when move option is clicked', () => {
    // Render with only onMove
    renderWithProviders(<ArticleThreeDotsMenu onMove={HANDLERS.onMove} onDelete={undefined} />);

    // Click on move option
    fireEvent.click(screen.getByTestId(`menu-item-${TRANSLATIONS.move}`));

    // Assert
    expect(HANDLERS.onMove).toHaveBeenCalledTimes(1);
  });

  it('should call onDelete when delete option is clicked', () => {
    // Render with only onDelete
    renderWithProviders(<ArticleThreeDotsMenu onMove={undefined} onDelete={HANDLERS.onDelete} />);

    // Click on delete option
    fireEvent.click(screen.getByTestId(`menu-item-${TRANSLATIONS.delete}`));

    // Assert
    expect(HANDLERS.onDelete).toHaveBeenCalledTimes(1);
  });

  it('should call onCreateShortcut when create shortcut option is clicked', () => {
    // Render with all handlers
    renderWithProviders(
      <ArticleThreeDotsMenu
        onMove={HANDLERS.onMove}
        onDelete={HANDLERS.onDelete}
        onCreateShortcut={HANDLERS.onCreateShortcut}
      />
    );

    // Click on create shortcut option
    fireEvent.click(screen.getByTestId(`menu-item-${TRANSLATIONS.createShortcut}`));

    // Assert
    expect(HANDLERS.onCreateShortcut).toHaveBeenCalledTimes(1);
  });
});
