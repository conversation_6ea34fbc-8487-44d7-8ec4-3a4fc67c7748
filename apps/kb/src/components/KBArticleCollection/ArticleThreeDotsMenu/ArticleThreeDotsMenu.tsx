import { createStyles } from '@mantine/emotion';
import { ThreeDotsMenu } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useMemo } from 'react';

interface ArticleThreeDotsMenuProps {
  onMove?: () => void;
  onDelete?: () => void;
  onCreateShortcut?: () => void;
}

const useStyles = createStyles((theme) => ({
  menuDelete: {
    color: theme.colors.red[6],
  },
}));

const ArticleThreeDotsMenu: React.FC<ArticleThreeDotsMenuProps> = ({
  onMove,
  onDelete,
  onCreateShortcut,
}) => {
  const { t } = useTranslate('common');
  const { classes } = useStyles();

  const menuItems = useMemo(
    () =>
      [
        ...(onMove
          ? [
              {
                label: t('actions.move'),
                onClick: onMove,
              },
            ]
          : []),
        ...(onCreateShortcut
          ? [
              {
                label: t('actions.createShortcut'),
                onClick: onCreateShortcut,
              },
            ]
          : []),
        ...(onDelete
          ? [
              {
                label: t('actions.delete'),
                onClick: onDelete,
                className: classes.menuDelete,
              },
            ]
          : []),
      ].filter(Boolean),
    [onMove, onDelete, onCreateShortcut, t, classes.menuDelete]
  );

  if (!onMove && !onDelete && !onCreateShortcut) return null;

  return <ThreeDotsMenu items={menuItems} />;
};

export default ArticleThreeDotsMenu;
