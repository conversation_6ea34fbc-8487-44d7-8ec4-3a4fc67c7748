import useKbAccessControl from '@/hooks/useKbAccessControl';
import { ActionIcon, Box, Tooltip, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconFileExport, IconFileSymlink, IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback } from 'react';

const useStyles = createStyles((theme) => ({
  actions: {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: rem(12),
  },
  actionButton: {
    backgroundColor: 'transparent',
  },
  deleteButton: {
    '&:hover svg': {
      color: theme.colors.decaRed[5],
    },
  },
}));

/**
 * Action Button component that renders a tooltip-enabled action button with an icon
 * @param {string} label - The tooltip text to display on hover
 * @param {React.ReactNode} icon - The icon component to render inside the button
 * @param {() => void} onClick - Click handler function for the button
 * @param {string} testId - The data-testid attribute for testing
 * @returns {React.ReactNode}
 */
const ActionButton = ({
  className,
  label,
  icon,
  onClick,
  testId,
}: {
  className?: string;
  label: string;
  icon: React.ReactNode;
  onClick: (event: React.MouseEvent<HTMLElement>) => void;
  testId?: string;
}) => {
  const { cx, classes } = useStyles();

  return (
    <Tooltip label={label} withinPortal>
      <div data-testid={testId}>
        <ActionIcon className={cx(classes.actionButton, className)} onClick={onClick}>
          {icon}
        </ActionIcon>
      </div>
    </Tooltip>
  );
};

/**
 * Article Actions component that renders a set of action buttons (open, remove, move) for an article
 * Buttons are rendered based on user permissions and available actions
 * Each button has a tooltip and icon, and handles click events with stopPropagation
 * @param {string} className
 * @param {() => void} onOpen
 * @param {() => void} onDelete
 * @param {() => void} onMove
 * @returns {React.ReactNode}
 */
interface ArticleActionsProps {
  className?: string;
  onOpen: () => void;
  onDelete: () => void;
  onMove?: () => void;
}

const ArticleActions: React.FC<ArticleActionsProps> = ({ className, onOpen, onDelete, onMove }) => {
  const { t } = useTranslate('common');
  const { permArticle } = useKbAccessControl();
  const { cx, classes } = useStyles();

  /**
   * Handle Open Click
   * @param {React.MouseEvent<HTMLElement>} event
   * @returns {void}
   */
  const onOpenClick = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      event.stopPropagation();
      onOpen();
    },
    [onOpen]
  );

  /**
   * Handle Delete Click
   * @param {React.MouseEvent<HTMLElement>} event
   * @returns {void}
   */
  const onDeleteClick = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      event.stopPropagation();
      onDelete();
    },
    [onDelete]
  );

  /**
   * Handle Move Click
   * @param {React.MouseEvent<HTMLElement>} event
   * @returns {void}
   */
  const onMoveClick = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      event.stopPropagation();
      onMove?.();
    },
    [onMove]
  );

  return (
    <Box className={cx(classes.actions, className)}>
      {permArticle.canMove && onMove && (
        <ActionButton
          label={t('actions.move')}
          icon={<IconFileExport size={18} />}
          onClick={onMoveClick}
          testId='action-button-actions.move'
        />
      )}
      {permArticle.canView && (
        <ActionButton
          label={t('actions.view')}
          icon={<IconFileSymlink size={18} />}
          onClick={onOpenClick}
          testId='action-button-actions.view'
        />
      )}
      {permArticle.canDelete && (
        <ActionButton
          className={classes.deleteButton}
          label={t('actions.delete')}
          icon={<IconTrash size={18} />}
          onClick={onDeleteClick}
          testId='action-button-actions.delete'
        />
      )}
    </Box>
  );
};

export default ArticleActions;
