import { EditorActions } from '@/components';
import { DEFAULT_DRAWER_OFFSET, DEFAULT_DRAWER_WIDTH } from '@/constants/ui';
import { useArticleContext, useArticleDetailContext } from '@/contexts';
import withArticleDetailProviders from '@/hocs/withArticleDetailProviders';
import { useArticleViewerStyles } from '@/hooks/useArticleStyles';
import type { KBTemplateField, PartOfArticle } from '@/types';
import { cleanBadCharacters } from '@/utils/article';
import { LoadingOverlay, Menu, Text } from '@mantine/core';
import { modals } from '@mantine/modals';
import { DecaButton, DynamicDrawer } from '@resola-ai/ui';
import { IconChevronDown } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';
import ArticleContentForm from '../ArticleContentForm';
import ArticleCustomData from '../ArticleCustomData';
import ImportModal from '../ArticleImport/Modals/ImportModal';
import { SuccessBox } from '../ArticleImport/SuccessBox';

/**
 * ArticleCreateNew
 * @returns {JSX.Element}
 * @dependencies t: i18n function, useState: React Hook
 */
interface ArticleCreateNewProps {
  onClosed?: () => void;
  onCreated?: () => void;
  onImport?: () => void;
}

const ArticleCreateNew: React.FC<ArticleCreateNewProps> = ({ onClosed, onCreated, onImport }) => {
  const { t } = useTranslate(['article', 'kb', 'common']);
  const [isDrawerOpen, setIsDrawerOpen] = useState<boolean>(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const {
    state: articleCollectionState,
    services: articleCollectionServices,
    actions: articleCollectionActions,
  } = useArticleContext();
  const { setArticleSaving } = useArticleDetailContext();
  const { currentKb, isCreating } = articleCollectionState;
  const [customData, setCustomData] = useState<Partial<KBTemplateField>[]>([]);
  const { classes } = useArticleViewerStyles({
    drawerWidth: DEFAULT_DRAWER_WIDTH,
  });

  /**
   * Handle Create Article
   * @param {PartOfArticle} data
   * @dependencies kb: KnowledgeBase, articleCollectionServices.createArticle: function
   * @returns {void}
   */
  const handleCreateArticle = useCallback(
    async (data: PartOfArticle, onSaved?: () => void) => {
      if (!currentKb?.id) return;

      const createResponse = await articleCollectionServices.createArticle(currentKb?.id, {
        title: data.title,
        content: data.content,
        contentRaw: data.contentRaw,
        status: 'published',
        keywords: data.keywords || [],
        customData,
      });

      if (createResponse) {
        setIsDrawerOpen(false);
        onCreated?.();
      }

      onSaved?.();
    },
    [currentKb?.id, articleCollectionServices, customData, onCreated, setIsDrawerOpen]
  );

  /**
   * Handle Open Drawer
   * @returns {void}
   * @dependencies setIsDrawerOpen: React Hook
   */
  const handleOpenDrawer = useCallback(() => {
    setIsDrawerOpen(true);
  }, [setIsDrawerOpen]);

  /**
   * Handle Update Custom Data
   * @param {Partial<KBTemplateField>[]} data
   * @dependencies setCustomData: React Hook
   * @returns {void}
   */
  const handleUpdateCustomData = useCallback(
    (data: Partial<KBTemplateField>[]) => {
      setCustomData(data);
    },
    [setCustomData]
  );

  /**
   * Handle Close Drawer
   * @returns {void}
   * @dependencies setIsDrawerOpen: React Hook, onCancel: function
   */
  const handleCloseDrawer = useCallback(() => {
    setIsDrawerOpen(false);
    onClosed?.();
  }, [onClosed, setIsDrawerOpen]);

  const handleImportSuccess = useCallback(
    (count: number) => {
      setIsModalOpen(false);
      onClosed?.();
      onImport?.();
      modals.open({
        children: <SuccessBox count={count} />,
        withCloseButton: false,
        centered: true,
        size: 'lg',
      });
    },
    [onImport, onClosed]
  );

  useEffect(() => {
    if (articleCollectionState.shouldCloseCreateNewArticleDrawer) {
      handleCloseDrawer();
      articleCollectionActions.resetShouldCloseCreateNewArticleDrawer();
    }
  }, [
    articleCollectionState.shouldCloseCreateNewArticleDrawer,
    handleCloseDrawer,
    articleCollectionActions,
  ]);

  return (
    <>
      <Menu position='bottom-end'>
        <Menu.Target>
          <DecaButton rightSection={<IconChevronDown size={24} />} radius={'sm'} variant='primary'>
            {t('articleCollection.createNew')}
          </DecaButton>
        </Menu.Target>
        <Menu.Dropdown>
          <Menu.Item onClick={handleOpenDrawer}>
            <Text size='md' fw={500}>
              {t('articleCollection.createArticleManually')}
            </Text>
          </Menu.Item>
          <Menu.Item onClick={() => setIsModalOpen(true)}>
            <Text size='md' fw={500}>
              {t('articleCollection.importArticle')}
            </Text>
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>

      <ImportModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleImportSuccess}
      />

      {currentKb && (
        <DynamicDrawer
          offset={DEFAULT_DRAWER_OFFSET}
          opened={isDrawerOpen}
          className={classes.articleDrawer}
          backTitle={(() => {
            const kbName = cleanBadCharacters(currentKb.name) || t('unknownKBName', { ns: 'kb' });
            return t('backToKBDetail', { kbName } as any, { ns: 'common' });
          })()}
          rightAction={
            <EditorActions onCancel={handleCloseDrawer} onSave={() => setArticleSaving(true)} />
          }
          closeOnEscape={false}
          lockScroll={false}
          zIndex={100}
          withOverlay={false}
          onClose={handleCloseDrawer}
        >
          <LoadingOverlay visible={isCreating} />
          <ArticleContentForm
            isShownActions={false}
            baseId={currentKb.id}
            onCancel={handleCloseDrawer}
            onSave={handleCreateArticle}
          />

          <ArticleCustomData article={null} onCustomDataChanged={handleUpdateCustomData} />
        </DynamicDrawer>
      )}
    </>
  );
};

export default withArticleDetailProviders(ArticleCreateNew);
