import { TextInput } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';

const useStyles = createStyles((theme) => ({
  input: {
    '& .mantine-TextInput-input:disabled': {
      color: theme.colors.decaMono[0],
      opacity: 1,
    },
  },
}));

interface ArticleTextInputProps {
  id: string;
  value: any;
  label?: string;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
  onChange: (value: string) => void;
  onSubmitted?: (data: any) => void;
}

const ArticleTextInput: React.FC<ArticleTextInputProps> = ({
  id = '',
  label = '',
  placeholder,
  value,
  className = '',
  disabled = false,
  onChange,
  onSubmitted,
}) => {
  const { classes, cx } = useStyles();
  const [inputValue, setInputValue] = useState<string>(value);

  /**
   * Handle input value change
   * @param {React.ChangeEvent<HTMLInputElement>} event
   * @returns void
   */
  const handleInputChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setInputValue(event.target.value);
    },
    [setInputValue]
  );

  /**
   * Save input value to parent component
   * @returns void
   * @dependencies [inputValue, onChange]
   */
  const saveInputValue = useCallback(() => {
    if (inputValue === value) return;
    return onChange(inputValue);
  }, [inputValue, value, onChange]);

  /**
   * Handle Enter key press
   * @param {React.KeyboardEvent<HTMLInputElement>} event
   * @returns void
   * @dependencies [submitInputValue]
   */
  const handleInputKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter') {
        const updatedData = saveInputValue();
        onSubmitted?.(updatedData);
      }
    },
    [saveInputValue]
  );

  /**
   * Reset input value when the id changes
   * @dependencies [id]
   */
  useEffect(() => {
    setInputValue(value);
  }, [id, value]);

  return (
    <TextInput
      className={cx(classes.input, className)}
      label={label}
      placeholder={placeholder}
      disabled={disabled}
      value={inputValue}
      onChange={handleInputChange}
      onKeyDown={handleInputKeyDown}
      onBlur={saveInputValue}
    />
  );
};

export default ArticleTextInput;
