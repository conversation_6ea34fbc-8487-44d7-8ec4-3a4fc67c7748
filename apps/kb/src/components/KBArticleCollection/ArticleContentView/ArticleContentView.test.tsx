import type { Article } from '@/types';
import { renderWithProviders } from '@/utils/unitTest';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import ArticleContentView from './ArticleContentView';

// Mock the imported components used by ArticleContentView
vi.mock('@resola-ai/blocknote-editor', () => ({
  BlockNoteViewer: ({ initialHTML, isMarkdown, isUsingInlineCSS, ...props }) => (
    <div data-testid='block-note-viewer' data-content={initialHTML} {...props}>
      {initialHTML}
    </div>
  ),
  BlockNoteEditor: () => <div>Editor</div>,
  isBlockNoteMarkdownContent: () => false,
}));

vi.mock('../ArticleShortcutBadge', () => ({
  ArticleShortcutBadge: ({ className }) => (
    <span className={className} data-testid='shortcut-badge'>
      Shortcut
    </span>
  ),
}));

vi.mock('../ArticleViewerActions', () => ({
  ArticleViewerActions: ({ onEditToggle, onUpdated, onDeleted }) => (
    <div data-testid='article-viewer-actions'>
      <button type='button' data-testid='edit-button' onClick={onEditToggle}>
        Edit
      </button>
      <button type='button' data-testid='update-button' onClick={() => onUpdated?.(true)}>
        Update
      </button>
      <button type='button' data-testid='delete-button' onClick={onDeleted}>
        Delete
      </button>
    </div>
  ),
}));

const TEST_ARTICLE: Article = {
  id: 'article-123',
  title: 'Test Article Title',
  contentRaw: '<p>This is test content</p>',
  content: 'This is test content',
  isShortcut: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  baseId: 'base-123',
  status: 'published',
  createdBy: {
    id: 'user-123',
    displayName: 'Test User',
    email: '<EMAIL>',
    orgId: 'org-123',
    createdAt: '2023-01-01',
    familyName: 'User',
    givenName: 'Test',
    picture: '',
    updatedAt: '2023-01-01',
  },
  keywords: [],
};

const TEST_ARTICLE_NO_SHORTCUT: Article = {
  ...TEST_ARTICLE,
  isShortcut: false,
};

// Mock functions
const mockOnEditToggle = vi.fn();
const mockOnUpdated = vi.fn();
const mockOnDeleted = vi.fn();

describe('ArticleContentView', () => {
  beforeEach(() => {
    // Reset mocks before each test
    mockOnEditToggle.mockReset();
    mockOnUpdated.mockReset();
    mockOnDeleted.mockReset();
  });

  it('renders article title correctly', () => {
    renderWithProviders(
      <ArticleContentView
        article={TEST_ARTICLE}
        onEditToggle={mockOnEditToggle}
        onUpdated={mockOnUpdated}
        onDeleted={mockOnDeleted}
      />
    );

    expect(screen.getByText(TEST_ARTICLE.title)).toBeInTheDocument();
  });

  it('displays shortcut badge when article is a shortcut', () => {
    renderWithProviders(
      <ArticleContentView article={TEST_ARTICLE} onEditToggle={mockOnEditToggle} />
    );

    // Check if the shortcut badge is rendered
    expect(screen.getByTestId('shortcut-badge')).toBeInTheDocument();
  });

  it('does not display shortcut badge when article is not a shortcut', () => {
    renderWithProviders(
      <ArticleContentView article={TEST_ARTICLE_NO_SHORTCUT} onEditToggle={mockOnEditToggle} />
    );

    // Check that no shortcut badge is rendered
    expect(screen.queryByTestId('shortcut-badge')).not.toBeInTheDocument();
  });

  it('renders article content using BlockNoteViewer', () => {
    renderWithProviders(
      <ArticleContentView article={TEST_ARTICLE} onEditToggle={mockOnEditToggle} />
    );

    // Verify the BlockNoteViewer is rendered with the correct content
    const contentViewer = screen.getByTestId('block-note-viewer');
    expect(contentViewer).toBeInTheDocument();
    expect(contentViewer.textContent).toContain('This is test content');
  });

  it('shows actions when showActions is true', () => {
    renderWithProviders(
      <ArticleContentView
        article={TEST_ARTICLE}
        onEditToggle={mockOnEditToggle}
        onUpdated={mockOnUpdated}
        onDeleted={mockOnDeleted}
        showActions={true}
      />
    );

    // Check that actions container is rendered
    const actionsContainer = screen.getByTestId('article-viewer-actions');
    expect(actionsContainer).toBeInTheDocument();

    // Find and click the edit button
    const editButton = screen.getByTestId('edit-button');
    fireEvent.click(editButton);

    // Verify that onEditToggle was called
    expect(mockOnEditToggle).toHaveBeenCalledTimes(1);
  });

  it('calls onUpdated callback when update button is clicked', () => {
    renderWithProviders(
      <ArticleContentView
        article={TEST_ARTICLE}
        onEditToggle={mockOnEditToggle}
        onUpdated={mockOnUpdated}
        onDeleted={mockOnDeleted}
        showActions={true}
      />
    );

    // Find and click the update button
    const updateButton = screen.getByTestId('update-button');
    fireEvent.click(updateButton);

    // Verify that onUpdated was called with true
    expect(mockOnUpdated).toHaveBeenCalledTimes(1);
    expect(mockOnUpdated).toHaveBeenCalledWith(true);
  });

  it('calls onDeleted callback when delete button is clicked', () => {
    renderWithProviders(
      <ArticleContentView
        article={TEST_ARTICLE}
        onEditToggle={mockOnEditToggle}
        onUpdated={mockOnUpdated}
        onDeleted={mockOnDeleted}
        showActions={true}
      />
    );

    // Find and click the delete button
    const deleteButton = screen.getByTestId('delete-button');
    fireEvent.click(deleteButton);

    // Verify that onDeleted was called
    expect(mockOnDeleted).toHaveBeenCalledTimes(1);
  });

  it('does not show actions when showActions is false', () => {
    renderWithProviders(
      <ArticleContentView
        article={TEST_ARTICLE}
        onEditToggle={mockOnEditToggle}
        onUpdated={mockOnUpdated}
        onDeleted={mockOnDeleted}
        showActions={false}
      />
    );

    // Check that no actions container is rendered
    expect(screen.queryByTestId('article-viewer-actions')).not.toBeInTheDocument();
  });

  it('does not show actions when showActions is not provided (default is false)', () => {
    renderWithProviders(
      <ArticleContentView
        article={TEST_ARTICLE}
        onEditToggle={mockOnEditToggle}
        onUpdated={mockOnUpdated}
        onDeleted={mockOnDeleted}
      />
    );

    // Check that no actions container is rendered
    expect(screen.queryByTestId('article-viewer-actions')).not.toBeInTheDocument();
  });
});
