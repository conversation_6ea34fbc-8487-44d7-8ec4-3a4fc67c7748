import { useArticleMovingModal, useKBSelectionModal } from '@/components';
import { ArticlePublishDropdown, PublishStatus } from '@/components/KBArticlePublish';
import { ROOT_PATH } from '@/constants/folder';
import { useArticleDetailContext } from '@/contexts';
import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, useArticleDetailStyles, useKbAccessControl } from '@/hooks';
import { VoteType } from '@/types';
import { Flex } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { IconEdit, IconEye } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useMemo } from 'react';

import ArticleRate from '../ArticleRate';
import { ArticleThreeDotsMenu } from '../ArticleThreeDotsMenu';

interface ArticleViewerActionsProps {
  onEditToggle: () => void;
  onUpdated?: (showLoader: boolean) => void;
  onDeleted?: () => void;
  onViewOriginal?: (articleId: string) => void;
}

const ArticleViewerActions: React.FC<ArticleViewerActionsProps> = ({
  onEditToggle,
  onUpdated,
  onDeleted,
  onViewOriginal,
}) => {
  const { t } = useTranslate(['kb', 'article', 'common']);
  const { classes } = useArticleDetailStyles();
  const { permArticle, permVoting } = useKbAccessControl();
  const {
    currentArticle,
    articleVote,
    isArticleVoting,
    setIsArticleVoting,
    updateArticle,
    updateArticleVote,
    deleteArticle,
    createShortcut,
    getArticleById,
  } = useArticleDetailContext();
  const { handleApiRequest, API_RESPONSE_STATUS } = useApiHandler();

  const { openArticleMovingModal } = useArticleMovingModal({
    afterMoving: () => {
      onUpdated?.(false);
    },
  });

  const { openKBSelectionModal, closeKBSelectionModal } = useKBSelectionModal({
    title: t('articleShortcut.creating.title', { ns: 'article' }),
    description: t('articleShortcut.creating.description', { ns: 'article' }),
  });

  const canClickVote = useMemo(() => {
    return !isArticleVoting && permVoting.canVote;
  }, [isArticleVoting, permVoting.canVote]);

  /**
   * Convert article status to PublishStatus enum
   */
  const getPublishStatus = useCallback((status: string): PublishStatus => {
    switch (status) {
      case 'draft':
        return PublishStatus.DRAFT;
      case 'published':
        return PublishStatus.PUBLISHED;
      case 'schedule_publish':
        return PublishStatus.SCHEDULE_PUBLISH;
      case 'schedule_unpublish':
        return PublishStatus.SCHEDULE_UNPUBLISH;
      default:
        return PublishStatus.DRAFT;
    }
  }, []);

  /**
   * Handle publish status change
   */
  const handlePublishStatusChange = useCallback(
    async (newStatus: PublishStatus) => {
      if (!currentArticle) return;

      await handleApiRequest(
        updateArticle(currentArticle.id, {
          ...currentArticle,
          status: newStatus,
        }),
        {
          fallbackMessage: t('articleCollection.save.failed.description', { ns: 'article' }),
          fallbackTitle: t('articleCollection.save.failed.title', { ns: 'article' }),
          successMessage: t('articleCollection.save.success.description', { ns: 'article' }),
          successTitle: t('articleCollection.save.success.title', { ns: 'article' }),
          successCallback: async () => {
            await getArticleById(currentArticle.id);
            onUpdated?.(false);
          },
        }
      );
    },
    [currentArticle, updateArticle, handleApiRequest, t, getArticleById, onUpdated]
  );

  /**
   * Handle Update Vote for Article (Like/Dislike)
   * Toggles between like/dislike/neutral states based on current vote status
   */
  const handleVoteClick = useCallback(
    async (type: VoteType) => {
      if (!currentArticle) return;

      setIsArticleVoting(true);
      const voteType = articleVote?.feedbackType === type ? VoteType.NEUTRAL : type;
      const response = await handleApiRequest(updateArticleVote(currentArticle.id, voteType), {
        fallbackMessage: t('articleCollection.vote.failed.description', { ns: 'article' }),
        fallbackTitle: t('articleCollection.vote.failed.title', { ns: 'article' }),
        successMessage: t('articleCollection.vote.success.description', { ns: 'article' }),
        successTitle: t('articleCollection.vote.success.title', { ns: 'article' }),
      });

      if (response?.status === API_RESPONSE_STATUS.SUCCESS) {
        onUpdated?.(false);
      }

      setIsArticleVoting(false);
    },
    [currentArticle, updateArticleVote, handleApiRequest, t, onUpdated, setIsArticleVoting]
  );

  /**
   * Handle Move Article
   * Opens modal for moving article to another knowledge base
   */
  const handleMoveArticle = useCallback(() => {
    if (!currentArticle) return;
    const parentDirId = currentArticle.base?.parentDirId ?? ROOT_PATH;
    openArticleMovingModal(currentArticle, parentDirId);
  }, [currentArticle, openArticleMovingModal]);

  /**
   * Handle Delete Article
   * Opens confirmation modal before deleting the article
   */
  const handleDeleteArticle = useCallback(() => {
    if (!currentArticle) return;

    deleteArticle(currentArticle, () => {
      onDeleted?.();
    });
  }, [currentArticle, deleteArticle, onDeleted]);

  /**
   * Handle creating a shortcut
   * Creates a reference to this article in another knowledge base
   */
  const createShortcutHandler = useCallback(
    async (selectedBaseId: string) => {
      if (!currentArticle) return;

      const response = await handleApiRequest(createShortcut(currentArticle.id, selectedBaseId), {
        fallbackMessage: t('articleShortcut.creating.failedDescription', { ns: 'article' }),
        fallbackTitle: t('articleShortcut.creating.failedTitle', { ns: 'article' }),
        successMessage: t('articleShortcut.creating.successDescription', { ns: 'article' }),
        successTitle: t('articleShortcut.creating.successTitle', { ns: 'article' }),
      });

      if (response?.status === API_RESPONSE_STATUS.SUCCESS) {
        onUpdated?.(false);
        closeKBSelectionModal();
      }
    },
    [currentArticle, createShortcut, closeKBSelectionModal, handleApiRequest, t]
  );

  /**
   * Handle Create Shortcut
   * Opens knowledge base selection modal for shortcut creation
   */
  const handleCreateShortcut = useCallback(() => {
    if (!permArticle.canShortcut || !currentArticle || currentArticle.isShortcut) return;

    openKBSelectionModal({
      parentFolderId: currentArticle.base?.parentDirId ?? ROOT_PATH,
      currentBaseId: currentArticle.baseId,
      onSelect: async (selectedBaseId) => {
        await createShortcutHandler(selectedBaseId);
      },
    });
  }, [currentArticle, createShortcutHandler, permArticle.canShortcut]);

  /**
   * Handle View Original Article
   * Navigates to the original article when viewing a shortcut
   */
  const handleViewOriginal = useCallback(() => {
    if (!currentArticle?.originArticleId) return;

    onViewOriginal?.(currentArticle.originArticleId);
  }, [currentArticle, onViewOriginal]);

  // If no article is loaded yet, don't render anything
  if (!currentArticle) return null;

  return (
    <Flex align='center' gap='lg'>
      {/* Voting buttons (like/dislike) */}
      {permVoting.canView && (
        <>
          <DecaButton
            disabled={!canClickVote}
            radius='xl'
            variant='action'
            onClick={() => canClickVote && handleVoteClick(VoteType.LIKE)}
          >
            <ArticleRate
              type={VoteType.LIKE}
              count={articleVote?.likeCount}
              fill={articleVote?.feedbackType === VoteType.LIKE}
            />
          </DecaButton>
          <DecaButton
            disabled={!canClickVote}
            radius='xl'
            variant='action'
            onClick={() => canClickVote && handleVoteClick(VoteType.DISLIKE)}
          >
            <ArticleRate
              type={VoteType.DISLIKE}
              count={articleVote?.dislikeCount}
              fill={articleVote?.feedbackType === VoteType.DISLIKE}
            />
          </DecaButton>
        </>
      )}

      {/* Publish status dropdown - only for regular articles and if user can update */}
      {!currentArticle.isShortcut && permArticle.canUpdate && (
        <ArticlePublishDropdown
          status={getPublishStatus(currentArticle.status)}
          onStatusChange={handlePublishStatusChange}
        />
      )}

      {/* Edit button for regular articles */}
      {!currentArticle.isShortcut && permArticle.canUpdate && (
        <DecaButton
          className={classes.articleEditButton}
          radius='xl'
          variant='neutral'
          leftSection={<IconEdit size={20} />}
          onClick={onEditToggle}
        >
          {t('edit', { ns: 'kb' })}
        </DecaButton>
      )}

      {/* View original button for shortcut articles */}
      {currentArticle.isShortcut && (
        <DecaButton
          className={classes.articleEditButton}
          radius='xl'
          variant='neutral'
          leftSection={<IconEye size={20} />}
          onClick={handleViewOriginal}
        >
          {t('articleShortcut.viewOriginal', { ns: 'article' })}
        </DecaButton>
      )}

      {/* More actions menu (move, delete, create shortcut) */}
      <ArticleThreeDotsMenu
        onMove={permArticle.canMove ? handleMoveArticle : undefined}
        onDelete={permArticle.canDelete ? handleDeleteArticle : undefined}
        onCreateShortcut={
          permArticle.canShortcut && !currentArticle.isShortcut ? handleCreateShortcut : undefined
        }
      />
    </Flex>
  );
};

export default ArticleViewerActions;
