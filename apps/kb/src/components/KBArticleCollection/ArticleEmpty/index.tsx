import { IconArticle } from '@/components/Icons';
import { useEmptyStyles } from '@/hooks/useEmptyStyles';
import useKbAccessControl from '@/hooks/useKbAccessControl';
import type { PartOfArticle } from '@/types/article';
import { Box, Text } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { memo } from 'react';
import ArticleCreateNew from '../ArticleCreateNew';

export type ArticleEmptyProps = {
  onCreate?: (data: PartOfArticle) => void;
  onImport?: () => void;
};

const ArticleEmpty: React.FC<ArticleEmptyProps> = ({ onImport }) => {
  const { classes } = useEmptyStyles();
  const { t } = useTranslate('article');
  const { permArticle } = useKbAccessControl();

  return (
    <Box className={classes.root}>
      <Box className={classes.iconBox}>
        <IconArticle className={classes.icon} />
      </Box>
      <Box className={classes.contentBox}>
        <Text className={classes.emptyMessage} component='p'>
          {t('articleCollection.emptyTitle')}
          <br />
          {t('articleCollection.emptyMessage')}
        </Text>
        {permArticle.canCreate && <ArticleCreateNew onImport={onImport} />}
      </Box>
    </Box>
  );
};

export default memo(ArticleEmpty);
