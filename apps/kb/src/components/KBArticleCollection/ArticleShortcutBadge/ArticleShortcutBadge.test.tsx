import { renderWithProviders } from '@/utils/unitTest';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import ArticleShortcutBadge from './ArticleShortcutBadge';

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
}));

// Make sure to define this before any vi.mock calls
const EXPECTED_TEXT = 'articleShortcut.badge';
const TEST_CLASS_NAME = 'test-custom-class';

// Mock the modules - these must be at the top level
vi.mock('@resola-ai/ui', () => ({
  DecaBadge: ({ text, className, badgeProps }) => (
    <div
      data-testid='mocked-deca-badge'
      data-variant={badgeProps?.variant}
      data-color={badgeProps?.color}
      data-radius={badgeProps?.radius}
      className={className}
    >
      {text}
    </div>
  ),
}));

describe('ArticleShortcutBadge', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly with default props', () => {
    renderWithProviders(<ArticleShortcutBadge />);
    expect(screen.getByText(EXPECTED_TEXT)).toBeInTheDocument();
  });

  it('applies custom className when provided', () => {
    renderWithProviders(<ArticleShortcutBadge className={TEST_CLASS_NAME} />);

    const badge = screen.getByTestId('mocked-deca-badge');
    expect(badge).toHaveClass(TEST_CLASS_NAME);
  });

  it('uses correct badge props', () => {
    renderWithProviders(<ArticleShortcutBadge />);

    const badge = screen.getByTestId('mocked-deca-badge');
    expect(badge).toHaveAttribute('data-variant', 'outline');
    expect(badge).toHaveAttribute('data-color', 'decaBlue.7');
    expect(badge).toHaveAttribute('data-radius', 'xs');
  });

  it('verifies DecaBadge receives correct props', () => {
    renderWithProviders(<ArticleShortcutBadge className={TEST_CLASS_NAME} />);

    // Check that the rendered element has the expected attributes
    const badge = screen.getByTestId('mocked-deca-badge');
    expect(badge).toHaveAttribute('data-variant', 'outline');
    expect(badge).toHaveAttribute('data-color', 'decaBlue.7');
    expect(badge).toHaveAttribute('data-radius', 'xs');
    expect(badge).toHaveClass(TEST_CLASS_NAME);
    expect(badge).toHaveTextContent(EXPECTED_TEXT);
  });
});
