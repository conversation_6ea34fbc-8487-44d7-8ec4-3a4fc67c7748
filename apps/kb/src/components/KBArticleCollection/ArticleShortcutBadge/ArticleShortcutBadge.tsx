import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaBadge } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import type React from 'react';

const useStyles = createStyles((theme) => ({
  badge: {
    borderColor: theme.colors.decaBlue[7],
    color: theme.colors.decaBlue[7],
    fontSize: rem(12),
    height: rem(24),
    textTransform: 'none',
    fontWeight: 500,
    padding: `0 ${rem(8)}`,
  },
}));

interface ArticleShortcutBadgeProps {
  className?: string;
}

/**
 * ArticleShortcutBadge component displays a badge for article shortcuts
 */
const ArticleShortcutBadge: React.FC<ArticleShortcutBadgeProps> = ({ className }) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('article');

  return (
    <DecaBadge
      text={t('articleShortcut.badge')}
      className={cx(classes.badge, className)}
      badgeProps={{
        variant: 'outline',
        color: 'decaBlue.7',
        radius: 'xs',
      }}
    />
  );
};

export default ArticleShortcutBadge;
