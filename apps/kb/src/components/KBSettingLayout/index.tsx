import { Box, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import SettingsMenu from './SettingsMenu';

const useStyles = createStyles((theme, _, u) => ({
  wrapper: {
    width: '100%',
    paddingLeft: rem(274),
    display: 'flex',
    justifyContent: 'stretch',
  },
  sidebar: {
    width: rem(210),
    backgroundColor: theme.colors.decaLight[0],
    padding: `${rem(24)} ${rem(8)}`,
    margin: 0,
    position: 'fixed',
    height: '100%',
    left: rem(64),
    [u.smallerThan('md')]: {
      left: 0,
    },
  },
  sidebarTitle: {
    padding: `0 ${theme.spacing.sm}`,
    fontSize: rem(15),
    marginBottom: rem(12),
  },
  mainContent: {
    width: '100%',
  },
}));

interface KBSettingLayoutProps {
  children: React.ReactNode;
}

const KBSettingLayout: React.FC<KBSettingLayoutProps> = ({ children }) => {
  const { t } = useTranslate('settings');
  const { classes } = useStyles();

  return (
    <Box className={classes.wrapper}>
      <Box className={classes.sidebar}>
        <Title className={classes.sidebarTitle} order={5}>
          {t('settings')}
        </Title>
        <SettingsMenu />
      </Box>
      <Box className={classes.mainContent}>{children}</Box>
    </Box>
  );
};

export default KBSettingLayout;
