import { SETTING_MENUS } from '@/constants/template';
import { Box, Stack, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import type React from 'react';

const useStyles = createStyles((theme) => ({
  menuItem: {
    padding: `${rem(6)} ${theme.spacing.sm}`,
    cursor: 'pointer',
    borderRadius: theme.radius.sm,
    color: theme.colors.decaGrey[9],
    '&:hover': {
      backgroundColor: theme.colors.decaLight[1],
    },
  },
  active: {
    backgroundColor: theme.colors.decaViolet[0],
  },
}));

/**
 * MenuItem component represents a single item in the SettingsMenu.
 * It displays the provided children and applies styling based on the isActive prop.
 * @param children - The content to display in the MenuItem.
 * @param path - The path to navigate to when the MenuItem is clicked.
 * @param active - Whether the MenuItem is currently active.
 */

interface MenuItemProps {
  children: React.ReactNode;
  active: boolean;
}

const MenuItem: React.FC<MenuItemProps> = ({ children, active }) => {
  const { cx, classes } = useStyles();

  return <Box className={cx(classes.menuItem, active ? classes.active : '')}>{children}</Box>;
};

/**
 * SettingsMenu component displays a list of menu items for the settings page.
 * It maps over the SETTING_MENUS constant to render each menu item.
 * @returns The SettingsMenu component.
 * @see SETTING_MENUS
 * @see MenuItem
 */
const SettingsMenu: React.FC = () => {
  const { t } = useTranslate('settings');

  return (
    <Stack gap='md'>
      {SETTING_MENUS.map((menu) => (
        <MenuItem key={menu.name} active>
          {t(menu.label)}
        </MenuItem>
      ))}
    </Stack>
  );
};

export default SettingsMenu;
