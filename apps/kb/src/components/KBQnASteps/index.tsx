import { Box, Button, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCircleCheckFilled } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useState } from 'react';
import ConfettiExplosion from 'react-confetti-explosion';

import { useAppContext } from '@/contexts/AppContext';
import type { KnowledgeBase } from '@/types';
import { type QnAGenerateRequest, QnAGenerateStep, type QnAJob } from '@/types/qna';
import QnADocumentSelection from './Steps/QnADocumentSelection';
import QnAGenerating from './Steps/QnAGenerating';
import QnASelection from './Steps/QnASelection';

interface KBQnAStepsProps {
  kb: KnowledgeBase;
  job?: QnAJob;
  activeStep: number;
  onGenerateQnA: (data: QnAGenerateRequest) => void;
  onCancelGenerateQnA: () => void;
  onSaveAsDraft: () => void;
  backToKBDetail: () => void;
}

const useStyles = createStyles((theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(20),
  },
  completeContainer: {
    padding: `0 ${rem(220)}`,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: rem(10),
    minHeight: rem(400),
    backgroundColor: theme.colors.decaLight[0],
    position: 'relative',
  },
  successIcon: {
    color: theme.colors.decaViolet[4],
    width: rem(100),
    height: rem(100),
  },
  button: {
    borderRadius: rem(32),
    height: rem(36),
    minHeight: rem(36),
  },
  notifyText: {
    color: theme.colors.decaNavy[5],
    textAlign: 'center',
  },
  confetti: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
  },
}));

const KBQnASteps: React.FC<KBQnAStepsProps> = ({
  kb,
  job,
  activeStep,
  onGenerateQnA,
  onCancelGenerateQnA,
  onSaveAsDraft,
  backToKBDetail,
}) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('kb');
  const { closeConfirmModal } = useAppContext();
  const [selectedDocumentId, setSelectedDocumentId] = useState<string>('');
  const [customPrompt, setCustomPrompt] = useState<string>('');
  const [isExploding, setIsExploding] = useState<boolean>(false);

  const onGenerateCancel = useCallback(() => {
    onCancelGenerateQnA();
    closeConfirmModal();
  }, [closeConfirmModal, onCancelGenerateQnA]);

  const onGenerateStart = useCallback(() => {
    onGenerateQnA({
      kbId: kb.id,
      documentId: selectedDocumentId,
      customPrompt,
    });
  }, [kb.id, selectedDocumentId, customPrompt, onGenerateQnA]);

  useEffect(() => {
    if (activeStep === QnAGenerateStep.FINISHED) {
      setIsExploding(true);
    } else {
      setIsExploding(false);
    }

    return () => {
      setIsExploding(false);
    };
  }, [activeStep]);

  return (
    <Box>
      {activeStep === QnAGenerateStep.DOCUMENT_SELECTION && (
        <QnADocumentSelection
          onGenerateQnA={onGenerateStart}
          onPromptSubmitted={setCustomPrompt}
          onSelectedDocument={setSelectedDocumentId}
          defaultPrompt={customPrompt}
        />
      )}
      {activeStep === QnAGenerateStep.GENERATING && (
        <QnAGenerating backToKBDetail={backToKBDetail} onGenerateCancel={onGenerateCancel} />
      )}
      {activeStep === QnAGenerateStep.QNA_SELECTION && job ? (
        <QnASelection
          kb={kb}
          job={job}
          onGenerateCancel={onGenerateCancel}
          onSaveAsDraftSuccess={onSaveAsDraft}
        />
      ) : null}
      {activeStep === QnAGenerateStep.FINISHED && (
        <Box className={cx(classes.container, classes.completeContainer)}>
          <IconCircleCheckFilled className={classes.successIcon} />
          <Text className={classes.notifyText}>{t('generateQnASuccess', { kbName: kb.name })}</Text>
          <Button className={classes.button} onClick={backToKBDetail}>
            {t('checkTheQnAList')}
          </Button>
          {isExploding && (
            <ConfettiExplosion
              onComplete={() => setIsExploding(false)}
              height={'200vh'}
              width={3000}
              force={0.8}
              particleCount={300}
              className={classes.confetti}
            />
          )}
        </Box>
      )}
    </Box>
  );
};

export default KBQnASteps;
