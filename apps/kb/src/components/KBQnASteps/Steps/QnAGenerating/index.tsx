import { Box, Button, Flex, Text, rem } from '@mantine/core';
import { createStyles, keyframes } from '@mantine/emotion';
import { RoundedButton } from '@resola-ai/ui';
import { IconLoader } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';

import { useAppContext } from '@/contexts/AppContext';

interface QnAGeneratingProps {
  onGenerateCancel: () => void;
  backToKBDetail: () => void;
}

const spin = keyframes({
  '0%': {
    transform: 'rotate(0deg)',
  },
  '100%': {
    transform: 'rotate(359deg)',
  },
});

const useStyles = createStyles((theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(20),
  },
  notifyContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    border: `1px solid ${theme.colors.decaGrey[0]}`,
    borderRadius: rem(10),
    minHeight: rem(400),
  },
  loadingIcon: {
    color: theme.colors.decaViolet[4],
    animation: `${spin} 1s linear infinite`,
    width: rem(37.5),
    height: rem(37.5),
  },
  button: {
    borderRadius: rem(32),
    height: rem(36),
    minHeight: rem(36),
  },
  notifyText: {
    color: theme.colors.decaNavy[5],
    textAlign: 'center',
  },
}));

const QnAGenerating: React.FC<QnAGeneratingProps> = ({ backToKBDetail, onGenerateCancel }) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('kb');
  const { openConfirmModal } = useAppContext();

  return (
    <Box className={cx(classes.container, classes.notifyContainer)}>
      <IconLoader className={classes.loadingIcon} />
      <Text className={classes.notifyText}>{t('generatingQnA')}</Text>
      <Text className={classes.notifyText}>{t('generatingQnADescription')}</Text>
      <Flex direction={'row'} justify={'center'} gap={rem(20)}>
        <RoundedButton
          className={classes.button}
          variant='light'
          onClick={() => openConfirmModal({ onConfirm: onGenerateCancel })}
        >
          {t('cancel')}
        </RoundedButton>
        <Button className={classes.button} onClick={backToKBDetail}>
          {t('continue')}
        </Button>
      </Flex>
    </Box>
  );
};

export default QnAGenerating;
