import { Box, Button, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type { IPaginationNextPrevious } from '@resola-ai/models';
import { RoundedButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import cloneDeep from 'lodash/cloneDeep';
import { useCallback, useEffect, useState } from 'react';

import { useAppContext } from '@/contexts/AppContext';
import { useKbContext } from '@/contexts/KbContext';
import type { KnowledgeBase } from '@/types';
import { QNA_STATUS, type QnACheck, type QnAJob } from '@/types/qna';
import KBQnAList from '../../KBQnAList';

interface QnASelectionProps {
  kb: KnowledgeBase;
  job: QnAJob;
  onGenerateCancel: () => void;
  onSaveAsDraftSuccess: () => void;
}

const useStyles = createStyles(() => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(20),
  },
  stepActions: {
    display: 'flex',
    justifyContent: 'space-between',
    gap: rem(20),
  },
  button: {
    borderRadius: rem(32),
    height: rem(36),
    minHeight: rem(36),
  },
}));

const QnASelection: React.FC<QnASelectionProps> = ({
  kb,
  job,
  onGenerateCancel,
  onSaveAsDraftSuccess,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('kb');
  const [qnaList, setQnaList] = useState<Array<QnACheck>>([]);
  const [selectedQnA, setSelectedQnA] = useState<Array<QnACheck>>([]);
  const [_pagination, setPagination] = useState<IPaginationNextPrevious>();
  const { getQnAList, saveGeneratedQnA } = useKbContext();
  const { openConfirmModal } = useAppContext();

  const onSaveAsDraft = useCallback(async () => {
    const res = await saveGeneratedQnA(
      kb.id,
      selectedQnA.filter((qna) => qna.checked).map((qna) => qna.id),
      job.id
    );
    if (res?.status === 'success') {
      onSaveAsDraftSuccess();
    }
  }, [job.id, kb.id, onSaveAsDraftSuccess, saveGeneratedQnA, selectedQnA]);

  const fetchQnAList = useCallback(async () => {
    const res = await getQnAList(kb.id, 'backward', '', 20, QNA_STATUS.new, job.id);

    setQnaList(res?.data?.map((item) => ({ ...item, checked: false })) || []);
    setSelectedQnA(res?.data?.map((item) => ({ ...item, checked: false })) || []);
    setPagination(res?.pagination);
  }, [getQnAList, kb.id]);

  const onSelectQnA = useCallback((qnaList: Array<QnACheck>) => {
    setSelectedQnA((prev) => {
      const newSelectedQnA = cloneDeep(prev);
      for (const qna of qnaList) {
        const index = newSelectedQnA.findIndex((item) => item.id === qna.id);
        if (index !== -1) {
          newSelectedQnA[index] = qna;
        }
      }

      return newSelectedQnA;
    });
  }, []);

  useEffect(() => {
    fetchQnAList();
  }, []);

  return (
    <Box className={classes.container}>
      <KBQnAList qna={qnaList} qnaSelection={selectedQnA} onQnASelect={onSelectQnA} />
      <Box className={classes.stepActions}>
        <Text>{t('totalSelected', { total: selectedQnA.length })}</Text>
        <Flex direction={'row'} justify={'center'} gap={rem(20)}>
          <RoundedButton
            className={classes.button}
            variant='light'
            onClick={() =>
              openConfirmModal({
                onConfirm: onGenerateCancel,
                content: t('noDataAddedToQnAKB', { kbName: kb.name }),
              })
            }
          >
            {t('cancel')}
          </RoundedButton>
          <Button className={classes.button} onClick={onSaveAsDraft}>
            {t('saveAsDraft')}
          </Button>
        </Flex>
      </Box>
    </Box>
  );
};

export default QnASelection;
