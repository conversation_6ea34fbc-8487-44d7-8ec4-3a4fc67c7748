import { Box, Button, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconPencil } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useState } from 'react';

import { SearchBox } from '@/components/common';
import { useKbContext } from '@/contexts/KbContext';
import type { KnowledgeBase } from '@/types';
import { sortByCreatedAt } from '@/utils';
import KBDocumentList from '../../KBDocumentList';
import PromtModal from '../../PromptModal';

interface QnADocumentSelectionProps {
  onGenerateQnA: () => void;
  onSelectedDocument: (id: string) => void;
  onPromptSubmitted: (customizePrompt: string) => void;
  defaultPrompt?: string;
}

const useStyles = createStyles((theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(20),
  },
  stepHeaderContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stepHeaderTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: 700,
    color: theme.colors.decaNavy[5],
    lineHeight: rem(19),
  },
  stepActions: {
    display: 'flex',
    justifyContent: 'space-between',
    gap: rem(20),
  },
  button: {
    borderRadius: rem(32),
    height: rem(36),
    minHeight: rem(36),
  },
  editIcon: {
    width: rem(18),
    height: rem(18),
  },
}));

const QnADocumentSelection: React.FC<QnADocumentSelectionProps> = ({
  onGenerateQnA,
  onPromptSubmitted,
  onSelectedDocument,
  defaultPrompt,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('kb');
  const [documentList, setDocumentList] = useState<KnowledgeBase[]>();
  const [filteredDocumentList, setFilteredDocumentList] = useState<KnowledgeBase[]>([]);
  const [document, setDocument] = useState<KnowledgeBase>();
  const [promptModalOpened, setPromptModalOpened] = useState<boolean>(false);
  const { getKnowledgeBaseList } = useKbContext();

  const onSelected = (id: string) => {
    setDocument((documentList || []).find((doc) => doc.id === id));
    onSelectedDocument(id);
  };

  const fetchDocumentKb = useCallback(async () => {
    const res = await getKnowledgeBaseList('', 'backward', 50); // TODO: need to have update API to get only document type

    setDocumentList(sortByCreatedAt(res?.data || []).filter((doc) => doc.type === 'document'));
    setFilteredDocumentList(
      sortByCreatedAt(res?.data || []).filter((doc) => doc.type === 'document')
    );
  }, [getKnowledgeBaseList]);

  const onSearch = useCallback(
    (searchText: string) => {
      setFilteredDocumentList(
        (documentList || []).filter((doc) =>
          doc.name.toLowerCase().includes(searchText.toLowerCase())
        )
      );
    },
    [documentList]
  );

  useEffect(() => {
    fetchDocumentKb();
  }, []);

  return documentList ? (
    <>
      <Box className={classes.container}>
        <Box className={classes.stepHeaderContainer}>
          <Text className={classes.stepHeaderTitle}>{t('selectDocument')} </Text>
          <SearchBox placeholder={t('searchByDocumentName')} onSearch={onSearch} />
        </Box>
        <KBDocumentList
          kbDocumentList={documentList}
          filteredDocumentList={filteredDocumentList}
          selectedDocument={document}
          onSelected={onSelected}
        />
        <Box className={classes.stepActions}>
          <Button
            className={classes.button}
            variant='outline'
            leftSection={<IconPencil className={classes.editIcon} />}
            onClick={() => setPromptModalOpened(true)}
          >
            {t('customizePrompt')}
          </Button>
          <Button className={classes.button} onClick={onGenerateQnA} disabled={!document}>
            {t('generateQnA')}
          </Button>
        </Box>
      </Box>
      <PromtModal
        defaultPrompt={defaultPrompt}
        opened={promptModalOpened}
        onClose={() => setPromptModalOpened(false)}
        onPromptSubmitted={(customizePrompt: string) => {
          onPromptSubmitted(customizePrompt);
          setPromptModalOpened(false);
        }}
      />
    </>
  ) : null;
};

export default QnADocumentSelection;
