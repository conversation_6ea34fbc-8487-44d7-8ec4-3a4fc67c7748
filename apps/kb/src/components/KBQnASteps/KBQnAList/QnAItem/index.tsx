import { Box, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { memo } from 'react';

import type { QnA } from '@/types/qna';

interface QnAItemProps {
  qna: QnA;
}

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(10),
    color: theme.colors.decaNavy[5],
  },
  name: {
    fontWeight: 700,
    lineHeight: rem(20),
  },
  description: {
    fontWeight: 400,
    lineHeight: rem(19),
  },
}));

const QnAItem: React.FC<QnAItemProps> = ({ qna }) => {
  const { classes } = useStyles();
  return (
    <Box className={classes.root}>
      <Text className={classes.name}>{qna.question}</Text>
      <Text className={classes.description}>{qna.answer}</Text>
    </Box>
  );
};

export default memo(QnAItem);
