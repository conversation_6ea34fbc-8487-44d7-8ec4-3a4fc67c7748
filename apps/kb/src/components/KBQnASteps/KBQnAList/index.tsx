import { Box, Checkbox, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useCallback, useMemo } from 'react';

import type { QnACheck } from '@/types/qna';
import QnAItem from './QnAItem';

interface KBQnAListProps {
  qna: Array<QnACheck>;
  qnaSelection: Array<QnACheck>;
  onQnASelect: (qnaList: Array<QnACheck>) => void;
}

const useStyles = createStyles((theme) => ({
  container: {
    border: `1px solid ${theme.colors.decaGrey[0]}`,
    borderRadius: rem(10),
    maxHeight: rem(600),
    overflow: 'auto',
  },
  qnaItem: {
    paddingLeft: rem(20),
    borderBottom: `1px solid ${theme.colors.decaGrey[0]}`,
    '&:hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
    '&:last-child': {
      borderBottom: 'none',
    },
    '& .mantine-Checkbox-inner': {
      margin: `${rem(20)} 0`,
    },
    '& .mantine-Checkbox-labelWrapper': {
      width: '100%',
    },
    '& .mantine-Checkbox-label': {
      minHeight: rem(89),
      padding: rem(20),
      paddingLeft: rem(10),
    },
  },
  selectAllItem: {
    paddingLeft: rem(20),
    borderBottom: `1px solid ${theme.colors.decaGrey[0]}`,
    '&:hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
    '& .mantine-Checkbox-inner': {
      margin: `${rem(10)} 0`,
    },
    '& .mantine-Checkbox-labelWrapper': {
      width: '100%',
    },
    '& .mantine-Checkbox-label': {
      fontWeight: 700,
      color: theme.colors.decaNavy[5],
      minHeight: rem(41),
      padding: rem(10),
    },
  },
}));

const KBQnAList: React.FC<KBQnAListProps> = ({ qna, onQnASelect, qnaSelection }) => {
  const { classes } = useStyles();
  const { t } = useTranslate('kb');

  const allChecked = useMemo(
    () => (qnaSelection.length ? qnaSelection.every((value) => value.checked) : false),
    [qnaSelection]
  );
  const indeterminate = useMemo(
    () => qnaSelection.some((value) => value.checked) && !allChecked,
    [qnaSelection, allChecked]
  );

  const onQnASelectHandler = useCallback(
    (index: number, event) => {
      qna[index].checked = event.currentTarget.checked;

      onQnASelect(qna);
    },
    [onQnASelect, qna]
  );

  const onQnASelectAllHandler = useCallback(
    (allChecked) => {
      qna.forEach((item) => {
        item.checked = !allChecked;
      });

      onQnASelect(qna);
    },
    [onQnASelect, qna]
  );

  return (
    <Box>
      {qna.length ? (
        <Box className={classes.container}>
          <Checkbox
            className={classes.selectAllItem}
            checked={allChecked}
            indeterminate={indeterminate}
            label={t('selectAll')}
            onChange={() => onQnASelectAllHandler(allChecked)}
          />
          {qna.map((item, index) => (
            <Checkbox
              className={classes.qnaItem}
              value={item.id as string}
              key={item.id}
              checked={item.checked}
              label={<QnAItem qna={item} />}
              onChange={(event) => onQnASelectHandler(index, event)}
            />
          ))}
        </Box>
      ) : null}
    </Box>
  );
};

export default KBQnAList;
