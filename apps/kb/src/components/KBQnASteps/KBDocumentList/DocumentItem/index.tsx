import type { KnowledgeBase } from '@/types';
import { Box, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { memo } from 'react';

interface DocumentItemProps {
  kb: KnowledgeBase;
}

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(10),
    color: theme.colors.decaNavy[5],
  },
  name: {
    fontWeight: 700,
    lineHeight: rem(20),
  },
  description: {
    fontWeight: 400,
    lineHeight: rem(19),
  },
}));

const DocumentItem: React.FC<DocumentItemProps> = ({ kb }) => {
  const { classes } = useStyles();
  return (
    <Box className={classes.root}>
      <Text className={classes.name}>{kb.name}</Text>
      <Text className={classes.description}>{kb.description}</Text>
    </Box>
  );
};

export default memo(DocumentItem);
