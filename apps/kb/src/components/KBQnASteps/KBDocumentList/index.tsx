import type { KnowledgeBase } from '@/types';
import { Box, Button, Radio, Text, rem } from '@mantine/core';
import { createStyles, getStylesRef } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import DocumentItem from './DocumentItem';

interface KBDocumentListProps {
  kbDocumentList?: KnowledgeBase[];
  filteredDocumentList?: KnowledgeBase[];
  selectedDocument?: KnowledgeBase | null;
  onSelected?: (id: string) => void;
}

const useStyles = createStyles((theme) => ({
  container: {
    width: '100%',
    maxHeight: rem(465),
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    gap: rem(20),
    border: `1px solid ${theme.colors.decaGrey[0]}`,
    borderRadius: rem(10),
  },
  emptyListText: {
    fontSize: theme.fontSizes.md,
    fontWeight: 700,
    lineHeight: rem(29),
    textAlign: 'center',
    color: theme.colors.decaGrey[9],
  },
  emptyListButton: {
    borderRadius: rem(34),
    padding: `0 ${rem(20)}`,
  },
  documentList: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    padding: `${rem(15)} ${rem(20)} ${rem(15)} 0`,
    overflow: 'auto',
  },
  document: {
    paddingLeft: rem(20),
    '&:hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
    [`& .${getStylesRef('icon')}`]: {
      width: rem(10),
      height: rem(10),
      color: theme.colors.decaNavy[5],
      top: `calc(50% - ${rem(10)} / 2)`,
      left: `calc(50% - ${rem(10)} / 2)`,
    },
    '& .mantine-Radio-radio': {
      background: theme.white,
    },
    '& .mantine-Radio-inner': {
      padding: `${rem(20)} 0`,
    },
    '& .mantine-Radio-labelWrapper': {
      width: '100%',
    },
    '& .mantine-Radio-label': {
      minHeight: rem(89),
      padding: rem(20),
      paddingLeft: rem(10),
    },
  },
  empty: {
    minHeight: rem(465),
    justifyContent: 'center',
  },
  emptyFilterText: {
    fontSize: theme.fontSizes.sm,
    fontWeight: 400,
    lineHeight: rem(21.7),
    textAlign: 'center',
    color: theme.colors.decaGrey[5],
  },
}));

const KBDocumentList: React.FC<KBDocumentListProps> = ({
  kbDocumentList = [],
  filteredDocumentList = [],
  selectedDocument,
  onSelected,
}) => {
  const navigate = useNavigate();
  const { classes, cx } = useStyles();
  const { t } = useTranslate('kb');

  const goToKnowledgeBase = useCallback(() => {
    navigate('/kb/qna/');
  }, [navigate]);

  return (
    <Box>
      {kbDocumentList.length ? (
        <Box className={cx(classes.container, { [classes.empty]: !filteredDocumentList.length })}>
          {filteredDocumentList.length ? (
            <Radio.Group
              name='document'
              value={selectedDocument?.id as string | undefined}
              onChange={onSelected}
              className={classes.documentList}
            >
              {filteredDocumentList.map((kb) => (
                <Radio
                  value={kb.id}
                  key={kb.id}
                  className={classes.document}
                  label={<DocumentItem kb={kb} />}
                />
              ))}
            </Radio.Group>
          ) : (
            <Text className={classes.emptyFilterText}>{t('noDocumentFound')}</Text>
          )}
        </Box>
      ) : (
        <Box className={cx(classes.container, classes.empty)}>
          <Text className={classes.emptyListText}>
            <Text>{t('noDocumentList')}</Text>
            <Text>{t('noDocumentListDescription')}</Text>
          </Text>
          <Button className={classes.emptyListButton} onClick={goToKnowledgeBase}>
            {t('goToKnowledgeBase')}
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default KBDocumentList;
