import { zodResolver } from '@hookform/resolvers/zod';
import { Flex, Group, Textarea, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { RoundedButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { Controller, type SubmitHandler, useForm } from 'react-hook-form';
import { z } from 'zod';

import { FormMessage, Modal } from '@/components/common';

export type PromptInputs = {
  prompt: string;
};

interface PromptModalProps {
  opened: boolean;
  onClose: () => void;
  onPromptSubmitted: (customizePrompt: string) => void;
  defaultPrompt?: string;
}

const useStyles = createStyles((theme) => ({
  buttonGroup: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: theme.spacing.md,
  },
  cancelButton: {
    backgroundColor: theme.white,
    border: `1px solid ${theme.colors.decaNavy[5]}`,
  },
}));

const PROPMT_MAX_LENGTH = 300; //TODO: check with backend for max length

const zodSchema = z.object({
  prompt: z.string().max(PROPMT_MAX_LENGTH),
});

const PromtModal: React.FC<PromptModalProps> = ({
  opened,
  onClose,
  onPromptSubmitted,
  defaultPrompt,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('kb');

  const {
    control,
    formState: { errors },
    handleSubmit,
  } = useForm({
    defaultValues: {
      prompt: defaultPrompt || t('defaultPrompt'),
    },
    mode: 'onChange',
    resolver: zodResolver(zodSchema),
  });

  const onSubmit: SubmitHandler<PromptInputs> = (data) => {
    onPromptSubmitted(data.prompt);
  };

  return (
    <Modal title={t('customizePromptModalTitle')} opened={opened} onClose={onClose}>
      <form>
        <Controller
          name='prompt'
          key='prompt'
          control={control}
          render={({ field }) => (
            <Textarea
              key={field.name}
              {...field}
              error={
                errors.prompt?.message ? (
                  <FormMessage message={(errors.prompt?.message as string) ?? ''} type='error' />
                ) : null
              }
              minRows={10}
            />
          )}
        />
      </form>
      <Group className={classes.buttonGroup}>
        <div />
        <Flex gap={rem(10)}>
          <RoundedButton variant='light' onClick={onClose}>
            {t('cancel')}
          </RoundedButton>

          <RoundedButton variant='filled' onClick={handleSubmit(onSubmit)}>
            {t('apply')}
          </RoundedButton>
        </Flex>
      </Group>
    </Modal>
  );
};

export default PromtModal;
