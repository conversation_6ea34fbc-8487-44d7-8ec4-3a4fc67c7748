import { Modal } from '@/components';
import { PROMPT_VIEWER_MAX_ROWS, PROMPT_VIEWER_MIN_ROWS } from '@/constants/job';
import type { Prompt } from '@/types';
import { Box, CopyButton, Group, Text, Textarea, Tooltip } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { IconCheck, IconCopy } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { memo } from 'react';

interface PromptViewerModalProps {
  opened: boolean;
  prompt?: Partial<Prompt>;
  onClose: () => void;
}

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing.md,
  },
  actions: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: theme.spacing.xl,
    gap: theme.spacing.md,
  },
  promptTextarea: {
    '&[data-disabled]': {
      color: theme.colors.decaGrey[9],
      opacity: 1,
    },
  },
}));

/**
 * PromptViewerModal displays prompt content in a modal with copy functionality
 * Features:
 * - Displays prompt title and content
 * - Allows copying prompt content
 * - Shows success state after copying
 * - Responsive textarea with min/max rows
 */
const PromptViewerModal: React.FC<PromptViewerModalProps> = ({ opened, prompt, onClose }) => {
  const { classes } = useStyles();
  const { t } = useTranslate(['kb', 'common']);

  /**
   * Renders copy button with tooltip and success state
   */
  const renderCopyButton = ({ copied, copy }: { copied: boolean; copy: () => void }) => (
    <Tooltip
      label={t(`actions.${copied ? 'copied' : 'copy'}`, { ns: 'common' })}
      withArrow
      position='right'
    >
      <DecaButton
        variant={copied ? 'success' : 'secondary'}
        radius='md'
        leftSection={copied ? <IconCheck size={16} /> : <IconCopy size={16} />}
        onClick={(event: React.MouseEvent<HTMLButtonElement>) => {
          event.preventDefault();
          copy();
        }}
      >
        {t(`actions.${copied ? 'copied' : 'copy'}`, { ns: 'common' })}
      </DecaButton>
    </Tooltip>
  );

  return (
    <Modal
      opened={opened}
      onClose={onClose}
      title={prompt?.title || t('promptViewer.title', { ns: 'kb' })}
      size='lg'
    >
      <Box className={classes.root}>
        <Text>{t('promptViewer.description', { ns: 'kb' })}</Text>

        <Textarea
          value={prompt?.content || ''}
          size='md'
          disabled
          minRows={PROMPT_VIEWER_MIN_ROWS}
          maxRows={PROMPT_VIEWER_MAX_ROWS}
          autosize
          classNames={{
            input: classes.promptTextarea,
          }}
        />

        <Group className={classes.actions}>
          <CopyButton value={prompt?.content || ''} timeout={2000}>
            {renderCopyButton}
          </CopyButton>

          <DecaButton variant='neutral' radius='md' onClick={onClose}>
            {t('actions.close', { ns: 'common' })}
          </DecaButton>
        </Group>
      </Box>
    </Modal>
  );
};

export default memo(PromptViewerModal);
