import { JOB_STATUS, type Job } from '@/types';
import * as articleUtils from '@/utils/article';
import { renderWithMantine } from '@/utils/unitTest';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import ExportJobSummary from './ExportJobSummary';

// Test constants
const MOCK_JOB: Job = {
  id: 'test-job-id',
  name: 'Test Export Job',
  status: JOB_STATUS.succeeded,
  createdAt: new Date('2024-01-15T10:30:00Z'),
  updatedAt: new Date('2024-01-15T10:35:00Z'),
  initiatedBy: 'user-123',
  config: {
    schedule: {},
    retriable: true,
  },
  metadata: {
    documentIds: [],
    fileType: 'csv',
  },
  data: {},
  baseName: 'Test Knowledge Base',
  jobType: 'article-download' as any,
  error: '',
  createdBy: {
    id: 'user-123',
    displayName: '<PERSON>',
    picture: 'https://example.com/avatar.jpg',
    email: '<EMAIL>',
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T10:30:00Z',
    orgId: 'org-123',
    familyName: 'Doe',
    givenName: 'John',
  },
};

const MOCK_JOB_RESULT = {
  downloadUrl: 'https://example.com/download/export.pdf',
  resultData: {
    articlesExported: 25,
    totalSize: '2.5MB',
  },
};

const MOCK_JOB_WITHOUT_CREATOR = {
  ...MOCK_JOB,
  createdBy: null as any,
};

const MOCK_JOB_RESULT_WITHOUT_URL = {
  resultData: {
    articlesExported: 25,
    totalSize: '2.5MB',
  },
};

// Mock JobDetailContext
const mockJobDetailContext = {
  job: MOCK_JOB,
  jobResult: MOCK_JOB_RESULT,
  loading: false,
  error: null,
  refetch: vi.fn(),
};

vi.mock('@/contexts/JobDetailContext', () => ({
  useJobDetailContext: () => mockJobDetailContext,
}));

// Mock react-i18next - Fixed to handle namespace-based translation
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, options?: any) => {
      const translations: Record<string, string> = {
        'jobSummary.label.creator': 'Creator',
        'jobSummary.label.status': 'Status',
        'jobSummary.label.downloadExport': 'Download Export',
        'jobSummary.exportResult': 'Download Export Result',
        'jobSummary.noExportResultFound': 'No Export Result Found',
        unknown: 'Unknown',
      };

      // Handle namespaced keys
      if (options?.ns === 'common') {
        return translations[key] || key;
      }

      return translations[key] || key;
    },
    i18n: {
      language: 'en',
    },
  }),
}));

// Mock getUserName utility
vi.mock('@/utils/article', () => ({
  getUserName: vi.fn((user, _language) => {
    if (!user) return '';
    return user.displayName || user.givenName || user.familyName || user.email || '';
  }),
}));

// Mock JobStatusBadge component
vi.mock('@/components/KBJobs/JobStatus', () => ({
  default: ({ status }: { status: string }) => (
    <span data-testid='job-status-badge' data-status={status}>
      Status: {status}
    </span>
  ),
}));

// Mock Mantine emotion
vi.mock('@mantine/emotion', async () => {
  const actual = await vi.importActual('@mantine/emotion');
  return {
    ...actual,
    emotionTransform: actual.emotionTransform || vi.fn(),
    createStyles: () => () => ({
      classes: {
        root: 'root-class',
        summaryRow: 'summary-row-class',
        rowLabel: 'row-label-class',
        rowContent: 'row-content-class',
        resultLink: 'result-link-class',
      },
    }),
  };
});

// Mock icons - Updated to render the icon properly
vi.mock('@tabler/icons-react', () => ({
  IconDownload: ({ size = 16 }: { size?: number }) => (
    <svg data-testid='icon-download' width={size} height={size}>
      <title>Download icon</title>
      <path d='mock-download-icon' />
    </svg>
  ),
}));

// Mock window.open
const mockWindowOpen = vi.fn();
Object.defineProperty(window, 'open', {
  value: mockWindowOpen,
  writable: true,
});

// Mock hooks module
vi.mock('../hooks', () => ({
  useJobSummaryCommon: () => ({
    classes: {
      root: 'root-class',
      summaryRow: 'summary-row-class',
      rowLabel: 'row-label-class',
      rowContent: 'row-content-class',
      resultLink: 'result-link-class',
      retryLink: 'retry-link-class',
      emptyResult: 'empty-result-class',
    },
    t: (key: string, options?: any) => {
      const translations: Record<string, string> = {
        'jobSummary.label.creator': 'Creator',
        'jobSummary.label.status': 'Status',
        'jobSummary.label.downloadExport': 'Download Export',
        'jobSummary.exportResult': 'Download Export Result',
        'jobSummary.noExportResultFound': 'No Export Result Found',
        'actions.retry': 'Retry',
        unknown: 'Unknown',
      };

      // Handle namespaced keys
      if (options?.ns === 'common') {
        return translations[key] || key;
      }

      return translations[key] || key;
    },
    renderSummaryRow: (label: string, content: React.ReactNode) => (
      <div className='summary-row-class'>
        <div className='row-label-class'>{`jobSummary.label.${label}`}</div>
        <div className='row-content-class'>{content}</div>
      </div>
    ),
    renderCreator: (job: any) => (
      <div>
        <img src={job.createdBy?.picture} alt='' />
        <span>{vi.mocked(articleUtils.getUserName)(job.createdBy, undefined) || 'Unknown'}</span>
      </div>
    ),
    handleDownload: vi.fn(),
    handleRetryJob: vi.fn(),
  }),
  useJobRetry: () => ({
    shouldShowRetry: false,
    isStuckJob: false,
    isFailedExportJob: false,
  }),
}));

// Mock useKbAccessControl hook
vi.mock('@/hooks', () => ({
  useKbAccessControl: () => ({
    permJob: {
      canRetry: true,
    },
  }),
}));

describe('ExportJobSummary', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset mock context to default values
    Object.assign(mockJobDetailContext, {
      job: MOCK_JOB,
      jobResult: MOCK_JOB_RESULT,
      loading: false,
      error: null,
    });
  });

  describe('Component Rendering', () => {
    it('renders all summary sections correctly', () => {
      renderWithMantine(<ExportJobSummary job={MOCK_JOB} />);

      // Check all labels are present - use the translation keys as they appear
      expect(screen.getByText('jobSummary.label.creator')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.label.status')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.label.downloadExport')).toBeInTheDocument();

      // Check creator information
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByRole('img')).toHaveAttribute('src', 'https://example.com/avatar.jpg');

      // Check status badge
      expect(screen.getByTestId('job-status-badge')).toBeInTheDocument();
      expect(screen.getByTestId('job-status-badge')).toHaveAttribute(
        'data-status',
        JOB_STATUS.succeeded
      );
    });

    it('renders download link when downloadUrl is available', () => {
      renderWithMantine(<ExportJobSummary job={MOCK_JOB} />);

      const downloadLink = screen.getByRole('link');
      expect(downloadLink).toBeInTheDocument();
      expect(downloadLink).toHaveAttribute('href', 'https://example.com/download/export.pdf');
      expect(downloadLink).toHaveAttribute('target', '_blank');
      expect(downloadLink).toHaveAttribute('rel', 'noopener noreferrer');
      expect(screen.getByText('Download Export Result')).toBeInTheDocument();
    });

    it('renders clickable text when downloadUrl is not available', () => {
      Object.assign(mockJobDetailContext, {
        jobResult: MOCK_JOB_RESULT_WITHOUT_URL,
      });

      renderWithMantine(<ExportJobSummary job={MOCK_JOB} />);

      const downloadText = screen.getByText('No Export Result Found');
      expect(downloadText).toBeInTheDocument();
      expect(screen.queryByRole('link')).not.toBeInTheDocument();
    });
  });

  describe('Creator Information', () => {
    it('displays creator name and avatar when creator exists', () => {
      renderWithMantine(<ExportJobSummary job={MOCK_JOB} />);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByRole('img')).toHaveAttribute('src', 'https://example.com/avatar.jpg');
    });

    it('displays "unknown" when creator is null', () => {
      renderWithMantine(<ExportJobSummary job={MOCK_JOB_WITHOUT_CREATOR} />);

      expect(screen.getByText('Unknown')).toBeInTheDocument();
      // When no creator, Avatar renders a placeholder SVG instead of img
      expect(screen.getByTestId('job-status-badge')).toBeInTheDocument(); // Check that component renders
    });
  });

  describe('Job Status', () => {
    it('renders job status badge with correct status', () => {
      renderWithMantine(<ExportJobSummary job={MOCK_JOB} />);

      const statusBadge = screen.getByTestId('job-status-badge');
      expect(statusBadge).toBeInTheDocument();
      expect(statusBadge).toHaveAttribute('data-status', JOB_STATUS.succeeded);
    });

    it('handles different job statuses', () => {
      const jobWithFailedStatus = { ...MOCK_JOB, status: JOB_STATUS.failed };

      renderWithMantine(<ExportJobSummary job={jobWithFailedStatus} />);

      const statusBadge = screen.getByTestId('job-status-badge');
      expect(statusBadge).toHaveAttribute('data-status', JOB_STATUS.failed);
    });
  });

  describe('Download Functionality', () => {
    it('opens download URL in new tab when link is clicked', () => {
      renderWithMantine(<ExportJobSummary job={MOCK_JOB} />);

      const downloadLink = screen.getByRole('link');
      fireEvent.click(downloadLink);

      // Since it's a regular link with href, no window.open should be called
      expect(mockWindowOpen).not.toHaveBeenCalled();
    });

    it('calls window.open when clickable text is clicked and no downloadUrl', () => {
      Object.assign(mockJobDetailContext, {
        jobResult: MOCK_JOB_RESULT_WITHOUT_URL,
      });

      renderWithMantine(<ExportJobSummary job={MOCK_JOB} />);

      const downloadText = screen.getByText('No Export Result Found');
      fireEvent.click(downloadText);

      // Should not call window.open since there's no downloadUrl
      expect(mockWindowOpen).not.toHaveBeenCalled();
    });

    it('calls window.open when handleDownload is triggered with downloadUrl', () => {
      Object.assign(mockJobDetailContext, {
        jobResult: null, // No jobResult initially
      });

      renderWithMantine(<ExportJobSummary job={MOCK_JOB} />);

      // When no jobResult, it should show the no export result text
      const downloadText = screen.getByText('No Export Result Found');
      fireEvent.click(downloadText);

      // Should not call window.open since jobResult is null
      expect(mockWindowOpen).not.toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('handles missing jobResult context', () => {
      Object.assign(mockJobDetailContext, {
        jobResult: null,
      });

      renderWithMantine(<ExportJobSummary job={MOCK_JOB} />);

      // Should still render basic information
      expect(screen.getByText('jobSummary.label.creator')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.label.status')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.label.downloadExport')).toBeInTheDocument();

      // Download section should render without link
      expect(screen.queryByRole('link')).not.toBeInTheDocument();
      expect(screen.getByText('No Export Result Found')).toBeInTheDocument();
    });

    it('handles job with minimal data', () => {
      const minimalJob = {
        id: 'minimal-job',
        name: 'Minimal Job',
        status: JOB_STATUS.queued,
        createdAt: new Date('2024-01-15T10:30:00Z'),
        updatedAt: new Date('2024-01-15T10:30:00Z'),
        initiatedBy: 'user-123',
        config: {
          schedule: {},
          retriable: true,
        },
        metadata: {
          documentIds: [],
        },
        data: {},
        baseName: 'Test Base',
        jobType: 'article-download' as any,
        error: '',
        createdBy: null as any,
      } as Job;

      renderWithMantine(<ExportJobSummary job={minimalJob} />);

      expect(screen.getByText('jobSummary.label.creator')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.label.status')).toBeInTheDocument();
      expect(screen.getByText('Unknown')).toBeInTheDocument();
      expect(screen.getByTestId('job-status-badge')).toHaveAttribute(
        'data-status',
        JOB_STATUS.queued
      );
    });
  });

  describe('Component Structure', () => {
    it('applies correct CSS classes from createStyles', () => {
      renderWithMantine(<ExportJobSummary job={MOCK_JOB} />);

      // The exact class names will depend on the mocked createStyles implementation
      // We can verify the structure by checking for the presence of elements
      expect(screen.getByText('jobSummary.label.creator')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.label.status')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.label.downloadExport')).toBeInTheDocument();
    });

    it('renders summary rows with correct structure', () => {
      renderWithMantine(<ExportJobSummary job={MOCK_JOB} />);

      // Check that all three summary sections exist
      const creatorSection = screen.getByText('jobSummary.label.creator').closest('div');
      const statusSection = screen.getByText('jobSummary.label.status').closest('div');
      const downloadSection = screen.getByText('jobSummary.label.downloadExport').closest('div');

      expect(creatorSection).toBeInTheDocument();
      expect(statusSection).toBeInTheDocument();
      expect(downloadSection).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('provides proper accessibility attributes', () => {
      renderWithMantine(<ExportJobSummary job={MOCK_JOB} />);

      // Check that avatar has proper alt text (implicitly through role)
      const avatar = screen.getByRole('img');
      expect(avatar).toBeInTheDocument();

      // Check that download link has proper attributes
      const downloadLink = screen.getByRole('link');
      expect(downloadLink).toHaveAttribute('target', '_blank');
      expect(downloadLink).toHaveAttribute('rel', 'noopener noreferrer');
    });
  });
});
