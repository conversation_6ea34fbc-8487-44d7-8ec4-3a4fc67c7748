import { MantineWrapper } from '@/utils/unitTest';
import { renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useJobSummaryStyles } from './useJobSummaryStyles';

// Test constants
const DEFAULT_ROW_LABEL_WIDTH = 140;
const CUSTOM_ROW_LABEL_WIDTH = 180;
const ALTERNATIVE_ROW_LABEL_WIDTH = 200;

describe('useJobSummaryStyles', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('Default Parameters', () => {
    it('should return classes object with all expected properties', () => {
      const { result } = renderHook(() => useJobSummaryStyles({}), {
        wrapper: MantineWrapper,
      });

      const { classes } = result.current;

      // Verify all expected class names are present
      expect(classes).toHaveProperty('root');
      expect(classes).toHaveProperty('summaryRow');
      expect(classes).toHaveProperty('rowLabel');
      expect(classes).toHaveProperty('rowContent');
      expect(classes).toHaveProperty('resultLink');
      expect(classes).toHaveProperty('clickableText');
      expect(classes).toHaveProperty('emptyResult');
      expect(classes).toHaveProperty('retryLink');
      expect(classes).toHaveProperty('sourcesList');
      expect(classes).toHaveProperty('sourceFileName');
    });

    it('should use default rowLabelWidth of 140px when no parameters provided', () => {
      const { result } = renderHook(() => useJobSummaryStyles({}), {
        wrapper: MantineWrapper,
      });

      const { classes } = result.current;

      // The actual CSS class should contain the default width
      expect(classes.rowLabel).toContain('css-');
      expect(typeof classes.rowLabel).toBe('string');
    });

    it('should return string values for all class properties', () => {
      const { result } = renderHook(() => useJobSummaryStyles({}), {
        wrapper: MantineWrapper,
      });

      const { classes } = result.current;

      // All classes should be strings (CSS class names)
      Object.entries(classes).forEach(([, value]) => {
        expect(typeof value).toBe('string');
        expect(value).toBeTruthy();
        expect(value).toContain('css-');
      });
    });
  });

  describe('Custom Parameters', () => {
    it('should accept empty options object', () => {
      const { result } = renderHook(() => useJobSummaryStyles({}), {
        wrapper: MantineWrapper,
      });

      const { classes } = result.current;

      expect(classes).toBeDefined();
      expect(Object.keys(classes)).toHaveLength(10);
    });

    it('should accept custom rowLabelWidth parameter', () => {
      const { result } = renderHook(
        () => useJobSummaryStyles({ rowLabelWidth: CUSTOM_ROW_LABEL_WIDTH }),
        {
          wrapper: MantineWrapper,
        }
      );

      const { classes } = result.current;

      expect(classes.rowLabel).toContain('css-');
      expect(typeof classes.rowLabel).toBe('string');
    });

    it('should handle different rowLabelWidth values', () => {
      const { result: result1 } = renderHook(
        () => useJobSummaryStyles({ rowLabelWidth: DEFAULT_ROW_LABEL_WIDTH }),
        {
          wrapper: MantineWrapper,
        }
      );

      const { result: result2 } = renderHook(
        () => useJobSummaryStyles({ rowLabelWidth: ALTERNATIVE_ROW_LABEL_WIDTH }),
        {
          wrapper: MantineWrapper,
        }
      );

      // Different parameters should produce different class names
      expect(result1.current.classes.rowLabel).not.toBe(result2.current.classes.rowLabel);
    });

    it('should handle zero rowLabelWidth', () => {
      const { result } = renderHook(() => useJobSummaryStyles({ rowLabelWidth: 0 }), {
        wrapper: MantineWrapper,
      });

      const { classes } = result.current;

      expect(classes.rowLabel).toContain('css-');
      expect(typeof classes.rowLabel).toBe('string');
    });

    it('should handle very large rowLabelWidth values', () => {
      const { result } = renderHook(() => useJobSummaryStyles({ rowLabelWidth: 1000 }), {
        wrapper: MantineWrapper,
      });

      const { classes } = result.current;

      expect(classes.rowLabel).toContain('css-');
      expect(typeof classes.rowLabel).toBe('string');
    });
  });

  describe('Style Consistency', () => {
    it('should return consistent classes for same parameters', () => {
      const { result: result1 } = renderHook(
        () => useJobSummaryStyles({ rowLabelWidth: CUSTOM_ROW_LABEL_WIDTH }),
        {
          wrapper: MantineWrapper,
        }
      );

      const { result: result2 } = renderHook(
        () => useJobSummaryStyles({ rowLabelWidth: CUSTOM_ROW_LABEL_WIDTH }),
        {
          wrapper: MantineWrapper,
        }
      );

      // Same parameters should produce same class names
      expect(result1.current.classes.root).toEqual(result2.current.classes.root);
      expect(result1.current.classes.summaryRow).toEqual(result2.current.classes.summaryRow);
      expect(result1.current.classes.rowLabel).toEqual(result2.current.classes.rowLabel);
      expect(result1.current.classes.rowContent).toEqual(result2.current.classes.rowContent);
    });

    it('should maintain style structure across different parameter sets', () => {
      const testCases = [
        {},
        { rowLabelWidth: DEFAULT_ROW_LABEL_WIDTH },
        { rowLabelWidth: CUSTOM_ROW_LABEL_WIDTH },
        { rowLabelWidth: ALTERNATIVE_ROW_LABEL_WIDTH },
      ];

      testCases.forEach((params) => {
        const { result } = renderHook(() => useJobSummaryStyles(params), {
          wrapper: MantineWrapper,
        });

        const { classes } = result.current;

        // Verify all classes exist regardless of parameters
        expect(classes).toHaveProperty('root');
        expect(classes).toHaveProperty('summaryRow');
        expect(classes).toHaveProperty('rowLabel');
        expect(classes).toHaveProperty('rowContent');
        expect(classes).toHaveProperty('resultLink');
        expect(classes).toHaveProperty('clickableText');
        expect(classes).toHaveProperty('emptyResult');
        expect(classes).toHaveProperty('retryLink');
        expect(classes).toHaveProperty('sourcesList');
        expect(classes).toHaveProperty('sourceFileName');
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle negative rowLabelWidth', () => {
      const { result } = renderHook(() => useJobSummaryStyles({ rowLabelWidth: -100 }), {
        wrapper: MantineWrapper,
      });

      const { classes } = result.current;

      expect(classes.rowLabel).toContain('css-');
      expect(typeof classes.rowLabel).toBe('string');
    });

    it('should handle decimal rowLabelWidth', () => {
      const { result } = renderHook(() => useJobSummaryStyles({ rowLabelWidth: 150.5 }), {
        wrapper: MantineWrapper,
      });

      const { classes } = result.current;

      expect(classes.rowLabel).toContain('css-');
      expect(typeof classes.rowLabel).toBe('string');
    });

    it('should handle undefined rowLabelWidth in options', () => {
      const { result } = renderHook(() => useJobSummaryStyles({ rowLabelWidth: undefined }), {
        wrapper: MantineWrapper,
      });

      const { classes } = result.current;

      expect(classes.rowLabel).toContain('css-');
      expect(typeof classes.rowLabel).toBe('string');
    });
  });

  describe('Hook Stability', () => {
    it('should not cause infinite re-renders with stable parameters', () => {
      let renderCount = 0;

      const { result, rerender } = renderHook(
        () => {
          renderCount++;
          return useJobSummaryStyles({ rowLabelWidth: CUSTOM_ROW_LABEL_WIDTH });
        },
        {
          wrapper: MantineWrapper,
        }
      );

      const initialClasses = result.current.classes;

      // Force re-render
      rerender();

      // Classes should have same content (deep equality)
      expect(result.current.classes).toStrictEqual(initialClasses);
      expect(renderCount).toBe(2); // Initial render + rerender
    });

    it('should update when parameters change', () => {
      let rowLabelWidth = DEFAULT_ROW_LABEL_WIDTH;

      const { result, rerender } = renderHook(() => useJobSummaryStyles({ rowLabelWidth }), {
        wrapper: MantineWrapper,
      });

      const initialClasses = result.current.classes;

      // Change parameter
      rowLabelWidth = CUSTOM_ROW_LABEL_WIDTH;
      rerender();

      // Classes should update
      expect(result.current.classes.rowLabel).not.toBe(initialClasses.rowLabel);
    });
  });

  describe('TypeScript Integration', () => {
    it('should work with TypeScript without type errors', () => {
      // This test ensures the hook can be used with proper TypeScript types
      const { result } = renderHook(
        () => {
          const styles1 = useJobSummaryStyles({});
          const styles2 = useJobSummaryStyles({});
          const styles3 = useJobSummaryStyles({ rowLabelWidth: 160 });

          return { styles1, styles2, styles3 };
        },
        {
          wrapper: MantineWrapper,
        }
      );

      expect(result.current.styles1.classes).toBeDefined();
      expect(result.current.styles2.classes).toBeDefined();
      expect(result.current.styles3.classes).toBeDefined();
    });
  });
});
