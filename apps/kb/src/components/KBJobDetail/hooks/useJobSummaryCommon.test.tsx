import { JOB_STATUS, type Job, JobType } from '@/types';
import type { User } from '@/types/user';
import { getUserName } from '@/utils/article';
import { MantineWrapper } from '@/utils/unitTest';
import { renderHook } from '@testing-library/react';
import { render } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useJobSummaryCommon } from './useJobSummaryCommon';
import { useJobSummaryStyles } from './useJobSummaryStyles';

// Test constants
const MOCK_USER: User = {
  id: 'user-123',
  orgId: 'org-123',
  createdAt: '2023-01-01T00:00:00Z',
  displayName: '<PERSON>',
  email: '<EMAIL>',
  familyName: 'Doe',
  givenName: 'John',
  picture: 'https://example.com/avatar.jpg',
  updatedAt: '2023-01-01T00:00:00Z',
};

const MOCK_JOB: Job = {
  id: 'job-123',
  createdAt: new Date('2023-01-01T00:00:00Z'),
  updatedAt: new Date('2023-01-01T01:00:00Z'),
  name: 'Test Job',
  status: JOB_STATUS.succeeded,
  initiatedBy: 'user-123',
  startedAt: new Date('2023-01-01T00:00:00Z'),
  stoppedAt: new Date('2023-01-01T01:00:00Z'),
  config: {
    schedule: {},
    retriable: true,
    timeoutAfter: 3600,
  },
  metadata: {
    documentIds: ['doc-1', 'doc-2'],
    documents: [
      { id: 'doc-1', name: 'Document 1' },
      { id: 'doc-2', name: 'Document 2' },
    ],
  },
  data: {},
  baseName: 'test-job',
  jobType: JobType.ContentGeneration,
  error: '',
  createdBy: MOCK_USER,
};

const MOCK_JOB_WITHOUT_CREATOR: Job = {
  ...MOCK_JOB,
  createdBy: null as any,
};

const MOCK_JOB_WITHOUT_PICTURE: Job = {
  ...MOCK_JOB,
  createdBy: {
    ...MOCK_USER,
    picture: '',
  },
};

const CUSTOM_ROW_LABEL_WIDTH = 200;

// Mock dependencies
const mockClasses = {
  root: 'css-root-123',
  summaryRow: 'css-summaryRow-123',
  rowLabel: 'css-rowLabel-123',
  rowContent: 'css-rowContent-123',
  resultLink: 'css-resultLink-123',
  clickableText: 'css-clickableText-123',
  emptyResult: 'css-emptyResult-123',
  retryLink: 'css-retryLink-123',
  sourcesList: 'css-sourcesList-123',
  sourceFileName: 'css-sourceFileName-123',
};

const mockTranslate = vi.fn();
const mockTolgee = {
  getLanguage: vi.fn().mockReturnValue('en'),
  addPlugin: vi.fn(),
  use: vi.fn(),
  init: vi.fn(),
  changeLanguage: vi.fn(),
  isLoaded: vi.fn(() => true),
  isLoading: vi.fn(() => false),
  on: vi.fn(),
  off: vi.fn(),
  emit: vi.fn(),
  addActiveNs: vi.fn(),
};

const mockRetryJob = vi.fn();

// Mock useJobSummaryStyles
vi.mock('./useJobSummaryStyles', () => ({
  useJobSummaryStyles: vi.fn(() => ({
    classes: mockClasses,
  })),
}));

// Mock @tolgee/react
vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => ({
    t: mockTranslate,
  })),
  useTolgee: vi.fn(() => mockTolgee),
}));

// Mock @mantine/core
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    Box: ({ children, className, ...props }: any) => (
      <div className={className} {...props}>
        {children}
      </div>
    ),
    Group: ({ children, gap, ...props }: any) => (
      <div data-gap={gap} {...props}>
        {children}
      </div>
    ),
    Text: ({ children, className, ...props }: any) => (
      <span className={className} {...props}>
        {children}
      </span>
    ),
    Avatar: ({ src, radius, size, ...props }: any) => (
      <img src={src} data-radius={radius} data-size={size} {...props} alt='avatar' />
    ),
  };
});

// Mock JobDetailContext
vi.mock('@/contexts/JobDetailContext', () => ({
  useJobDetailContext: vi.fn(() => ({
    retryJob: mockRetryJob,
  })),
}));

// Mock getUserName utility
vi.mock('@/utils/article', () => ({
  getUserName: vi.fn((user: User, _locale: string) => {
    if (!user) return '';
    return user.displayName || user.email || 'Unknown User';
  }),
}));

// Mock window.open
const mockWindowOpen = vi.fn();
Object.defineProperty(window, 'open', {
  value: mockWindowOpen,
  writable: true,
});

describe('useJobSummaryCommon', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockTranslate.mockImplementation((key: string) => key);
  });

  describe('Hook Initialization', () => {
    it('should return all expected properties and functions', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      expect(result.current).toHaveProperty('classes');
      expect(result.current).toHaveProperty('t');
      expect(result.current).toHaveProperty('renderSummaryRow');
      expect(result.current).toHaveProperty('renderCreator');
      expect(result.current).toHaveProperty('handleDownload');
      expect(result.current).toHaveProperty('handleRetryJob');
    });

    it('should use default options when no options provided', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      expect(result.current.classes).toEqual(mockClasses);
      expect(typeof result.current.t).toBe('function');
      expect(typeof result.current.renderSummaryRow).toBe('function');
      expect(typeof result.current.renderCreator).toBe('function');
      expect(typeof result.current.handleDownload).toBe('function');
      expect(typeof result.current.handleRetryJob).toBe('function');
    });

    it('should accept custom options', () => {
      const { result } = renderHook(
        () => useJobSummaryCommon({ rowLabelWidth: CUSTOM_ROW_LABEL_WIDTH }),
        {
          wrapper: MantineWrapper,
        }
      );

      expect(result.current.classes).toEqual(mockClasses);
    });

    it('should handle empty options object', () => {
      const { result } = renderHook(() => useJobSummaryCommon({}), {
        wrapper: MantineWrapper,
      });

      expect(result.current.classes).toEqual(mockClasses);
    });
  });

  describe('renderSummaryRow', () => {
    it('should render a summary row with correct structure', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      const summaryRow = result.current.renderSummaryRow('title', 'Test Content');
      const { container } = render(summaryRow, { wrapper: MantineWrapper });

      // Check if the correct CSS classes are applied
      expect(container.querySelector(`.${mockClasses.summaryRow}`)).toBeInTheDocument();
      expect(container.querySelector(`.${mockClasses.rowLabel}`)).toBeInTheDocument();
      expect(container.querySelector(`.${mockClasses.rowContent}`)).toBeInTheDocument();
    });

    it('should use translate function for labels', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      const summaryRow = result.current.renderSummaryRow('title', 'Test Content');
      const { container } = render(summaryRow, { wrapper: MantineWrapper });

      // Verify the translated label is rendered
      expect(container.textContent).toContain('jobSummary.label.title');
    });

    it('should render content correctly', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      const testContent = 'Test Content';
      const summaryRow = result.current.renderSummaryRow('title', testContent);
      const { container } = render(summaryRow, { wrapper: MantineWrapper });

      expect(container.textContent).toContain(testContent);
    });

    it('should render JSX content correctly', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      const jsxContent = <strong>Bold Content</strong>;
      const summaryRow = result.current.renderSummaryRow('title', jsxContent);
      const { container } = render(summaryRow, { wrapper: MantineWrapper });

      expect(container.querySelector('strong')).toBeInTheDocument();
      expect(container.textContent).toContain('Bold Content');
    });

    it('should handle different label keys', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      const labels = ['title', 'status', 'createdBy', 'duration'];
      labels.forEach((label) => {
        const summaryRow = result.current.renderSummaryRow(label, 'content');
        const { container } = render(summaryRow, { wrapper: MantineWrapper });
        expect(container.textContent).toContain(`jobSummary.label.${label}`);
      });
    });
  });

  describe('renderCreator', () => {
    it('should render creator information with avatar and name', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      const creator = result.current.renderCreator(MOCK_JOB);
      const { container } = render(creator, { wrapper: MantineWrapper });

      // Check if avatar is rendered
      const avatar = container.querySelector('img');
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveAttribute('src', MOCK_USER.picture);

      // Check if name is rendered
      expect(container.textContent).toContain(MOCK_USER.displayName);
    });

    it('should handle job without creator', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      const creator = result.current.renderCreator(MOCK_JOB_WITHOUT_CREATOR);
      const { container } = render(creator, { wrapper: MantineWrapper });

      expect(container.textContent).toContain('unknown');
    });

    it('should handle creator without picture', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      const creator = result.current.renderCreator(MOCK_JOB_WITHOUT_PICTURE);
      const { container } = render(creator, { wrapper: MantineWrapper });

      // Should render creator content even without picture
      expect(container.textContent).toContain(MOCK_USER.displayName);

      const avatar = container.querySelector('img');
      if (avatar) {
        expect(avatar).toHaveAttribute('src', '');
      }
    });

    it('should render creator with proper structure', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      const creator = result.current.renderCreator(MOCK_JOB);
      const { container } = render(creator, { wrapper: MantineWrapper });

      // Verify the creator information is rendered
      expect(container.textContent).toContain(MOCK_USER.displayName);

      // Check for expected elements if they exist
      const avatar = container.querySelector('img');
      const textElements = container.querySelectorAll('span, div');

      if (avatar) {
        expect(avatar).toHaveAttribute('src', MOCK_USER.picture);
      }
      expect(textElements.length).toBeGreaterThan(0);
    });

    it('should display fallback for unknown user', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      // Mock getUserName to return empty string
      const mockedGetUserName = vi.mocked(getUserName);
      mockedGetUserName.mockReturnValue('');

      const creator = result.current.renderCreator(MOCK_JOB);
      const { container } = render(creator, { wrapper: MantineWrapper });

      expect(container.textContent).toContain('unknown');
    });
  });

  describe('handleDownload', () => {
    it('should open download URL in new tab', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      const downloadUrl = 'https://example.com/download';
      result.current.handleDownload(downloadUrl);

      expect(mockWindowOpen).toHaveBeenCalledWith(downloadUrl, '_blank');
    });

    it('should not open window when URL is empty', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      result.current.handleDownload('');

      expect(mockWindowOpen).not.toHaveBeenCalled();
    });

    it('should not open window when URL is null', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      result.current.handleDownload(null as any);

      expect(mockWindowOpen).not.toHaveBeenCalled();
    });

    it('should not open window when URL is undefined', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      result.current.handleDownload(undefined as any);

      expect(mockWindowOpen).not.toHaveBeenCalled();
    });

    it('should handle multiple download calls', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      const urls = [
        'https://example.com/file1.pdf',
        'https://example.com/file2.pdf',
        'https://example.com/file3.pdf',
      ];

      urls.forEach((url) => {
        result.current.handleDownload(url);
      });

      expect(mockWindowOpen).toHaveBeenCalledTimes(3);
      urls.forEach((url) => {
        expect(mockWindowOpen).toHaveBeenCalledWith(url, '_blank');
      });
    });
  });

  describe('handleRetryJob', () => {
    it('should call retryJob with correct job ID', async () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      const jobId = 'job-123';
      await result.current.handleRetryJob(jobId);

      expect(mockRetryJob).toHaveBeenCalledWith(jobId);
    });

    it('should not call retryJob when job ID is empty', async () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      await result.current.handleRetryJob('');

      expect(mockRetryJob).not.toHaveBeenCalled();
    });

    it('should not call retryJob when job ID is null', async () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      await result.current.handleRetryJob(null as any);

      expect(mockRetryJob).not.toHaveBeenCalled();
    });

    it('should not call retryJob when job ID is undefined', async () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      await result.current.handleRetryJob(undefined as any);

      expect(mockRetryJob).not.toHaveBeenCalled();
    });

    it('should handle retry job errors gracefully', async () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      mockRetryJob.mockRejectedValueOnce(new Error('Retry failed'));

      await expect(result.current.handleRetryJob('job-123')).rejects.toThrow('Retry failed');
      expect(mockRetryJob).toHaveBeenCalledWith('job-123');
    });

    it('should handle successful retry', async () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      mockRetryJob.mockResolvedValueOnce(true);

      await result.current.handleRetryJob('job-123');

      expect(mockRetryJob).toHaveBeenCalledWith('job-123');
    });
  });

  describe('Translation and Localization', () => {
    it('should provide translate function', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      // The t function should be available and callable
      expect(typeof result.current.t).toBe('function');
      expect(() => result.current.t('test.key')).not.toThrow();
    });

    it('should render translations in summary rows', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      const summaryRow = result.current.renderSummaryRow('title', 'content');
      const { container } = render(summaryRow, { wrapper: MantineWrapper });

      // Should contain both the translation key and the content
      expect(container.textContent).toContain('jobSummary.label.title');
      expect(container.textContent).toContain('content');
    });
  });

  describe('Styles Integration', () => {
    it('should use styles from useJobSummaryStyles', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      expect(result.current.classes).toEqual(mockClasses);
    });

    it('should pass options to useJobSummaryStyles', () => {
      const mockedUseJobSummaryStyles = vi.mocked(useJobSummaryStyles);
      const options = { rowLabelWidth: CUSTOM_ROW_LABEL_WIDTH };

      renderHook(() => useJobSummaryCommon(options), {
        wrapper: MantineWrapper,
      });

      expect(mockedUseJobSummaryStyles).toHaveBeenCalledWith(options);
    });
  });

  describe('Function Stability', () => {
    it('should return stable function references', () => {
      const { result } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      // Verify that functions are properly defined
      expect(typeof result.current.renderSummaryRow).toBe('function');
      expect(typeof result.current.renderCreator).toBe('function');
      expect(typeof result.current.handleDownload).toBe('function');
      expect(typeof result.current.handleRetryJob).toBe('function');
    });

    it('should maintain function behavior across re-renders', () => {
      const { result, rerender } = renderHook(() => useJobSummaryCommon(), {
        wrapper: MantineWrapper,
      });

      // Test that functions still work after re-render
      rerender();

      // Functions should still be callable
      expect(() => result.current.handleDownload('test-url')).not.toThrow();
      expect(() => result.current.handleRetryJob('test-id')).not.toThrow();
    });
  });
});
