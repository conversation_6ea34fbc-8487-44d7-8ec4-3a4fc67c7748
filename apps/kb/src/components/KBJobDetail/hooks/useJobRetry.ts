import { JOB_STATUS, type Job, JobType } from '@/types';
import { useMemo } from 'react';

interface UseJobRetryOptions {
  job: Job;
  canRetry: boolean;
}

/**
 * Custom hook to determine if a job should show retry action
 * Centralizes retry logic based on job status, type, and permissions
 */
export const useJobRetry = ({ job, canRetry }: UseJobRetryOptions) => {
  /**
   * Check if job has been queued or running for more than 1 hour
   */
  const isJobStuckForOneHour = useMemo(() => {
    if (!job.updatedAt) return false;

    const currentTime = new Date();
    const updatedTime = new Date(job.updatedAt);
    const timeDifferenceInHours =
      (currentTime.getTime() - updatedTime.getTime()) / (1000 * 60 * 60);

    return timeDifferenceInHours > 1;
  }, [job.updatedAt]);

  /**
   * Check if job is failed and is an export job
   */
  const isFailedExportJob = useMemo(() => {
    return job.status === JOB_STATUS.failed && job.jobType === JobType.ArticleExport;
  }, [job.status, job.jobType]);

  /**
   * Check if job is queued or running for more than 1 hour
   */
  const isStuckJob = useMemo(() => {
    const isQueuedOrRunning = job.status === JOB_STATUS.queued || job.status === JOB_STATUS.running;
    return isQueuedOrRunning && isJobStuckForOneHour;
  }, [job.status, isJobStuckForOneHour]);

  /**
   * Main condition to show retry action
   * - Must have permission to retry
   * - Either job is stuck (queued/running > 1h) or is a failed export job
   */
  const shouldShowRetry = useMemo(() => {
    if (!canRetry) return false;

    return isStuckJob || isFailedExportJob;
  }, [canRetry, isStuckJob, isFailedExportJob]);

  return {
    shouldShowRetry,
    isStuckJob,
    isFailedExportJob,
  };
};
