import { JOB_STATUS, type Job, JobType } from '@/types';
import { renderHook } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { useJobRetry } from './useJobRetry';

// Mock current time for consistent testing
const MOCK_CURRENT_TIME = new Date('2024-01-01T12:00:00Z');

describe('useJobRetry', () => {
  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(MOCK_CURRENT_TIME);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  const createMockJob = (overrides: Partial<Job> = {}): Job => ({
    id: 'test-job-id',
    createdAt: new Date(),
    updatedAt: new Date(),
    name: 'Test Job',
    status: JOB_STATUS.queued,
    initiatedBy: 'test-user',
    config: {
      schedule: {},
      retriable: true,
    },
    metadata: {
      documentIds: [],
    },
    data: {},
    baseName: 'test-job',
    jobType: JobType.ContentGeneration,
    error: '',
    createdBy: {
      id: 'user-1',
      orgId: 'org-1',
      createdAt: '2023-01-01T00:00:00Z',
      displayName: 'Test User',
      email: '<EMAIL>',
      familyName: 'User',
      givenName: 'Test',
      picture: 'https://example.com/avatar.jpg',
      updatedAt: '2023-01-01T00:00:00Z',
    },
    ...overrides,
  });

  describe('Permission Requirements', () => {
    it('should not show retry when canRetry is false', () => {
      const job = createMockJob({
        status: JOB_STATUS.failed,
        jobType: JobType.ArticleExport,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: false }));

      expect(result.current.shouldShowRetry).toBe(false);
    });

    it('should show retry when canRetry is true and conditions are met', () => {
      const job = createMockJob({
        status: JOB_STATUS.failed,
        jobType: JobType.ArticleExport,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.shouldShowRetry).toBe(true);
    });
  });

  describe('Failed Export Job Condition', () => {
    it('should show retry for failed export jobs', () => {
      const job = createMockJob({
        status: JOB_STATUS.failed,
        jobType: JobType.ArticleExport,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isFailedExportJob).toBe(true);
      expect(result.current.shouldShowRetry).toBe(true);
    });

    it('should not show retry for failed non-export jobs', () => {
      const job = createMockJob({
        status: JOB_STATUS.failed,
        jobType: JobType.ContentGeneration,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isFailedExportJob).toBe(false);
      expect(result.current.shouldShowRetry).toBe(false);
    });

    it('should not show retry for successful export jobs', () => {
      const job = createMockJob({
        status: JOB_STATUS.succeeded,
        jobType: JobType.ArticleExport,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isFailedExportJob).toBe(false);
      expect(result.current.shouldShowRetry).toBe(false);
    });
  });

  describe('Stuck Job Condition (> 1 hour)', () => {
    it('should show retry for queued job stuck for more than 1 hour', () => {
      const twoHoursAgo = new Date(MOCK_CURRENT_TIME.getTime() - 2 * 60 * 60 * 1000);
      const job = createMockJob({
        status: JOB_STATUS.queued,
        updatedAt: twoHoursAgo,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isStuckJob).toBe(true);
      expect(result.current.shouldShowRetry).toBe(true);
    });

    it('should show retry for running job stuck for more than 1 hour', () => {
      const twoHoursAgo = new Date(MOCK_CURRENT_TIME.getTime() - 2 * 60 * 60 * 1000);
      const job = createMockJob({
        status: JOB_STATUS.running,
        updatedAt: twoHoursAgo,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isStuckJob).toBe(true);
      expect(result.current.shouldShowRetry).toBe(true);
    });

    it('should not show retry for queued job less than 1 hour', () => {
      const thirtyMinutesAgo = new Date(MOCK_CURRENT_TIME.getTime() - 30 * 60 * 1000);
      const job = createMockJob({
        status: JOB_STATUS.queued,
        updatedAt: thirtyMinutesAgo,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isStuckJob).toBe(false);
      expect(result.current.shouldShowRetry).toBe(false);
    });

    it('should not show retry for running job less than 1 hour', () => {
      const thirtyMinutesAgo = new Date(MOCK_CURRENT_TIME.getTime() - 30 * 60 * 1000);
      const job = createMockJob({
        status: JOB_STATUS.running,
        updatedAt: thirtyMinutesAgo,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isStuckJob).toBe(false);
      expect(result.current.shouldShowRetry).toBe(false);
    });

    it('should not show retry for succeeded job even if old', () => {
      const twoHoursAgo = new Date(MOCK_CURRENT_TIME.getTime() - 2 * 60 * 60 * 1000);
      const job = createMockJob({
        status: JOB_STATUS.succeeded,
        updatedAt: twoHoursAgo,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isStuckJob).toBe(false);
      expect(result.current.shouldShowRetry).toBe(false);
    });

    it('should not show retry for failed job stuck for more than 1 hour if not export job', () => {
      const twoHoursAgo = new Date(MOCK_CURRENT_TIME.getTime() - 2 * 60 * 60 * 1000);
      const job = createMockJob({
        status: JOB_STATUS.failed,
        updatedAt: twoHoursAgo,
        jobType: JobType.ContentGeneration,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isStuckJob).toBe(false);
      expect(result.current.shouldShowRetry).toBe(false);
    });
  });

  describe('Edge Cases', () => {
    it('should handle missing updatedAt gracefully', () => {
      const job = createMockJob({
        status: JOB_STATUS.queued,
        updatedAt: undefined,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isStuckJob).toBe(false);
      expect(result.current.shouldShowRetry).toBe(false);
    });

    it('should handle exactly 1 hour difference (boundary case)', () => {
      const exactlyOneHourAgo = new Date(MOCK_CURRENT_TIME.getTime() - 60 * 60 * 1000);
      const job = createMockJob({
        status: JOB_STATUS.queued,
        updatedAt: exactlyOneHourAgo,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isStuckJob).toBe(false);
      expect(result.current.shouldShowRetry).toBe(false);
    });

    it('should handle slightly more than 1 hour difference', () => {
      const slightlyMoreThanOneHour = new Date(MOCK_CURRENT_TIME.getTime() - 61 * 60 * 1000);
      const job = createMockJob({
        status: JOB_STATUS.queued,
        updatedAt: slightlyMoreThanOneHour,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isStuckJob).toBe(true);
      expect(result.current.shouldShowRetry).toBe(true);
    });
  });

  describe('Combined Conditions', () => {
    it('should show retry for failed export job that is also stuck', () => {
      const twoHoursAgo = new Date(MOCK_CURRENT_TIME.getTime() - 2 * 60 * 60 * 1000);
      const job = createMockJob({
        status: JOB_STATUS.failed,
        jobType: JobType.ArticleExport,
        updatedAt: twoHoursAgo,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isFailedExportJob).toBe(true);
      expect(result.current.isStuckJob).toBe(false); // Failed jobs are not considered stuck
      expect(result.current.shouldShowRetry).toBe(true);
    });

    it('should show retry for stuck non-export job', () => {
      const twoHoursAgo = new Date(MOCK_CURRENT_TIME.getTime() - 2 * 60 * 60 * 1000);
      const job = createMockJob({
        status: JOB_STATUS.running,
        jobType: JobType.ContentGeneration,
        updatedAt: twoHoursAgo,
      });

      const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

      expect(result.current.isFailedExportJob).toBe(false);
      expect(result.current.isStuckJob).toBe(true);
      expect(result.current.shouldShowRetry).toBe(true);
    });
  });

  describe('All Job Types', () => {
    const allJobTypes = [
      JobType.ContentGeneration,
      JobType.DocumentCollectionIndexing,
      JobType.ArticleCollectionIndexing,
      JobType.DataSourceIndexing,
      JobType.DataRemoval,
      JobType.DocumentProcessing,
      JobType.DocumentRemoving,
      JobType.ArticleExport,
    ];

    allJobTypes.forEach((jobType) => {
      it(`should handle ${jobType} job type correctly`, () => {
        const job = createMockJob({
          status: JOB_STATUS.failed,
          jobType,
        });

        const { result } = renderHook(() => useJobRetry({ job, canRetry: true }));

        const expectedFailedExportJob = jobType === JobType.ArticleExport;
        expect(result.current.isFailedExportJob).toBe(expectedFailedExportJob);
        expect(result.current.shouldShowRetry).toBe(expectedFailedExportJob);
      });
    });
  });
});
