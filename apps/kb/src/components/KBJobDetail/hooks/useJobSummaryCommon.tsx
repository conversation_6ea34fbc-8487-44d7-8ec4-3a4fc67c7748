import { useJobDetailContext } from '@/contexts/JobDetailContext';
import type { Job } from '@/types';
import { getUserName } from '@/utils/article';
import { Avatar, Box, Group, Text } from '@mantine/core';
import { useTolgee, useTranslate } from '@tolgee/react';
import { useCallback } from 'react';
import { useJobSummaryStyles } from './useJobSummaryStyles';

interface UseJobSummaryCommonOptions {
  /**
   * Width of the row label column
   * @default 140
   */
  rowLabelWidth?: number;
}

/**
 * Custom hook that provides common functionality for job summary components
 * Includes shared rendering utilities and common handlers
 */
export const useJobSummaryCommon = (options: UseJobSummaryCommonOptions = {}) => {
  const { classes } = useJobSummaryStyles(options);
  const { t } = useTranslate(['job', 'common']);
  const tolgee = useTolgee();
  const { retryJob } = useJobDetailContext();

  /**
   * Renders a summary row with label and content
   * Shared between JobSummary and ExportJobSummary components
   */
  const renderSummaryRow = useCallback(
    (label: string, content: React.ReactNode) => (
      <Box className={classes.summaryRow}>
        <Text className={classes.rowLabel}>{t(`jobSummary.label.${label}`)}</Text>
        <Box className={classes.rowContent}>{content}</Box>
      </Box>
    ),
    [classes, t]
  );

  /**
   * Renders the creator information section
   * Shared component that displays user avatar and name
   */
  const renderCreator = useCallback(
    (job: Job) => (
      <Group gap='xs'>
        <Avatar src={job.createdBy?.picture} radius='xl' size='sm' />
        <Text>
          {getUserName(job.createdBy, tolgee.getLanguage()) || t('unknown', { ns: 'common' })}
        </Text>
      </Group>
    ),
    [tolgee, t]
  );

  /**
   * Utility function to handle download operations
   * Opens a URL in a new tab/window
   */
  const handleDownload = useCallback((downloadUrl: string) => {
    if (downloadUrl) {
      window.open(downloadUrl, '_blank');
    }
  }, []);

  /**
   * Handles retry job functionality
   * Can be reused by both components that need retry functionality
   */
  const handleRetryJob = useCallback(
    async (jobId: string) => {
      if (jobId) {
        await retryJob(jobId);
      }
    },
    [retryJob]
  );

  return {
    classes,
    t,
    renderSummaryRow,
    renderCreator,
    handleDownload,
    handleRetryJob,
  };
};
