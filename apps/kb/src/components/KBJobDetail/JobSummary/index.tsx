import JobStatusBadge from '@/components/KBJobs/JobStatus';
import { PromptViewerModal } from '@/components/KBPrompt';
import { useKbAccessControl } from '@/hooks';
import { JOB_STATUS, type Job, type JobStatus } from '@/types';
import { Box, Group, Stack, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { IconReload } from '@tabler/icons-react';
import { memo, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useJobRetry, useJobSummaryCommon } from '../hooks';

const SHOW_RESULT_LINK_STATUSES: Partial<JobStatus>[] = [
  JOB_STATUS.succeeded,
  JOB_STATUS.queued,
  JOB_STATUS.failed,
  JOB_STATUS.running,
];

interface JobSummaryProps {
  job: Job;
}

const JobSummary = ({ job }: JobSummaryProps) => {
  const { classes, t, renderSummaryRow, renderCreator, handleRetryJob } = useJobSummaryCommon({});
  const { permJob } = useKbAccessControl();
  const { shouldShowRetry } = useJobRetry({ job, canRetry: permJob.canRetry });
  const navigate = useNavigate();
  const [isPromptModalOpen, { toggle: togglePromptModal }] = useDisclosure(false);

  const customPrompt = useMemo(() => job.metadata?.customPrompt, [job.metadata]);
  const isShowResultLink = useMemo(
    () => SHOW_RESULT_LINK_STATUSES.includes(job.status),
    [job.status]
  );
  const hasFiles = useMemo(() => Boolean(job.metadata?.documents?.length), [job.metadata]);

  const goToJobResult = useCallback(() => {
    navigate(`/kb/jobs/${job.id}/result`);
  }, [navigate, job.id]);

  const goToDocument = useCallback(
    (fileId: string) => {
      navigate(`/kb/document/${fileId}`);
    },
    [navigate]
  );

  /**
   * Retries a job using the shared handler
   */
  const handleRetryJobClick = useCallback(async () => {
    if (job.id) {
      await handleRetryJob(job.id);
    }
  }, [handleRetryJob, job.id]);

  /**
   * Renders the list of content source files
   */
  const renderSourceFiles = useCallback(
    () => (
      <Stack gap='xs'>
        {hasFiles ? (
          <ul className={classes.sourcesList}>
            {job.metadata?.documents?.map((document, index) => (
              <li key={`${document.id}-${index}`}>
                <Text className={classes.sourceFileName} onClick={() => goToDocument(document.id)}>
                  {document.name}
                </Text>
              </li>
            ))}
          </ul>
        ) : (
          <Text color='dimmed'>{t('jobSummary.noContentSources')}</Text>
        )}
      </Stack>
    ),
    [classes, t, hasFiles, goToDocument]
  );

  return (
    <Box className={classes.root}>
      {renderSummaryRow('creator', renderCreator(job))}

      {renderSummaryRow(
        'status',
        <Group gap='md'>
          <JobStatusBadge status={job.status} />
          {isShowResultLink && (
            <Text className={classes.resultLink} onClick={goToJobResult}>
              {t('jobSummary.viewGeneratedArticles')}
            </Text>
          )}
          {shouldShowRetry && (
            <Text className={classes.retryLink} onClick={handleRetryJobClick}>
              <IconReload size={14} />
              {t('actions.retry', { ns: 'common' })}
            </Text>
          )}
        </Group>
      )}

      {renderSummaryRow(
        'promptUsed',
        <>
          <PromptViewerModal
            opened={isPromptModalOpen}
            prompt={customPrompt}
            onClose={togglePromptModal}
          />
          <Text className={classes.clickableText} onClick={togglePromptModal}>
            {customPrompt?.title || t('unknown', { ns: 'common' })}
          </Text>
        </>
      )}

      {renderSummaryRow('contentSources', renderSourceFiles())}
    </Box>
  );
};

export default memo(JobSummary);
