import { JOB_STATUS, type Job, type JobStatus, JobType } from '@/types';
import * as articleUtils from '@/utils/article';
import { renderWithProviders } from '@/utils/unitTest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import JobSummary from './index';

// Mock dependencies
vi.mock('@/components/KBJobs/JobStatus', () => ({
  default: ({ status }: { status: JobStatus }) => (
    <div data-testid='job-status-badge'>{status}</div>
  ),
}));

vi.mock('@/components/KBPrompt', () => ({
  PromptViewerModal: ({ opened, onClose, prompt }: any) => (
    <div
      data-testid='prompt-viewer-modal'
      data-opened={opened}
      data-prompt-title={prompt?.title}
      onClick={onClose}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          onClose();
        }
      }}
    >
      {opened && (
        <div>
          <span>Prompt Modal</span>
          <button type='button' onClick={onClose}>
            Close
          </button>
        </div>
      )}
    </div>
  ),
}));

vi.mock('../hooks', () => ({
  useJobSummaryStyles: () => ({
    classes: {
      root: 'mock-root',
      summaryRow: 'mock-summary-row',
      rowLabel: 'mock-row-label',
      rowContent: 'mock-row-content',
      sourcesList: 'mock-sources-list',
      sourceFileName: 'mock-source-file-name',
      resultLink: 'mock-result-link',
      clickableText: 'mock-clickable-text',
    },
  }),
  useJobSummaryCommon: () => ({
    classes: {
      root: 'mock-root',
      summaryRow: 'mock-summary-row',
      rowLabel: 'mock-row-label',
      rowContent: 'mock-row-content',
      sourcesList: 'mock-sources-list',
      sourceFileName: 'mock-source-file-name',
      resultLink: 'mock-result-link',
      clickableText: 'mock-clickable-text',
    },
    t: (key: string, options?: any) => {
      if (options?.ns === 'common') return key;
      return key;
    },
    renderSummaryRow: (label: string, content: React.ReactNode) => (
      <div className='mock-summary-row'>
        <div className='mock-row-label'>{`jobSummary.label.${label}`}</div>
        <div className='mock-row-content'>{content}</div>
      </div>
    ),
    renderCreator: (job: any) => (
      <div>
        <img src={job.createdBy?.picture} alt='' />
        <span>{vi.mocked(articleUtils.getUserName)(job.createdBy, undefined) || 'unknown'}</span>
      </div>
    ),
    handleDownload: vi.fn(),
    handleRetryJob: vi.fn(),
  }),
  useJobRetry: () => ({
    shouldShowRetry: false,
    isStuckJob: false,
    isFailedExportJob: false,
  }),
}));

vi.mock('@/utils/article', () => ({
  getUserName: vi.fn(),
}));

// Mock react-router-dom
vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(() => vi.fn()),
}));

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, options?: any) => {
      if (options?.ns === 'common') return key;
      return key;
    },
  }),
  useTolgee: () => ({
    getLanguage: () => undefined, // This matches the actual test behavior
  }),
}));

// Mock useKbAccessControl
vi.mock('@/hooks', () => ({
  useKbAccessControl: () => ({
    permJob: {
      canRetry: false,
    },
  }),
  useFileUpload: () => ({
    uploadFiles: vi.fn(),
    uploaders: [],
    validateFiles: vi.fn(),
  }),
}));

// Test data constants
const MOCK_USER = {
  id: 'user-123',
  orgId: 'org-456',
  createdAt: '2023-01-01T00:00:00Z',
  displayName: 'John Doe',
  email: '<EMAIL>',
  familyName: 'Doe',
  givenName: 'John',
  picture: 'https://example.com/avatar.jpg',
  updatedAt: '2023-01-01T00:00:00Z',
};

const MOCK_PROMPT = {
  id: 'prompt-123',
  title: 'Test Prompt',
  content: 'This is a test prompt content',
  createdAt: '2023-01-01T00:00:00Z',
  updatedAt: '2023-01-01T00:00:00Z',
};

const MOCK_DOCUMENTS = [
  {
    id: 'doc-123',
    name: 'Document 1.pdf',
  },
  {
    id: 'doc-456',
    name: 'Document 2.docx',
  },
];

const MOCK_JOB_BASE: Job = {
  id: 'job-123',
  createdAt: new Date('2023-01-01T00:00:00Z'),
  updatedAt: new Date('2023-01-01T00:00:00Z'),
  name: 'Test Job',
  status: JOB_STATUS.succeeded,
  initiatedBy: 'user-123',
  startedAt: new Date('2023-01-01T00:00:00Z'),
  stoppedAt: new Date('2023-01-01T01:00:00Z'),
  config: {
    schedule: {},
    retriable: true,
    timeoutAfter: 3600,
  },
  metadata: {
    documentIds: ['doc-123', 'doc-456'],
    customPrompt: MOCK_PROMPT,
    documents: MOCK_DOCUMENTS,
  },
  data: {},
  baseName: 'test-base',
  jobType: JobType.ContentGeneration,
  error: '',
  createdBy: MOCK_USER,
};

// Test utility functions
const createMockJob = (overrides: Partial<Job> = {}): Job => {
  const result = {
    ...MOCK_JOB_BASE,
    ...overrides,
  };

  // Handle metadata merging properly
  if ('metadata' in overrides) {
    if (overrides.metadata === undefined) {
      result.metadata = undefined as any;
    } else {
      result.metadata = {
        ...MOCK_JOB_BASE.metadata,
        ...overrides.metadata,
      };
    }
  }

  return result;
};

const renderComponent = renderWithProviders;

describe('JobSummary', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    // Setup default mock return values
    vi.mocked(articleUtils.getUserName).mockReturnValue('John Doe');
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render job summary with all sections', () => {
      const mockJob = createMockJob();

      renderComponent(<JobSummary job={mockJob} />);

      // Check if all main sections are rendered
      expect(screen.getByText('jobSummary.label.creator')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.label.status')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.label.promptUsed')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.label.contentSources')).toBeInTheDocument();
    });

    it('should display job status badge', () => {
      const mockJob = createMockJob({ status: JOB_STATUS.succeeded });

      renderComponent(<JobSummary job={mockJob} />);

      const statusBadge = screen.getByTestId('job-status-badge');
      expect(statusBadge).toBeInTheDocument();
      expect(statusBadge).toHaveTextContent('succeeded');
    });

    it('should display creator information with avatar and name', () => {
      const mockJob = createMockJob();

      renderComponent(<JobSummary job={mockJob} />);

      const avatar = screen.getByRole('img');
      expect(avatar).toHaveAttribute('src', MOCK_USER.picture);
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    it('should display prompt title as clickable text', () => {
      const mockJob = createMockJob();

      renderComponent(<JobSummary job={mockJob} />);

      const promptTitle = screen.getByText('Test Prompt');
      expect(promptTitle).toBeInTheDocument();
      expect(promptTitle).toHaveClass('mock-clickable-text');
    });
  });

  describe('Job Status Variations', () => {
    const testCases: Array<{ status: JobStatus; shouldShowResultLink: boolean }> = [
      { status: JOB_STATUS.succeeded, shouldShowResultLink: true },
      { status: JOB_STATUS.queued, shouldShowResultLink: true },
      { status: JOB_STATUS.failed, shouldShowResultLink: true },
      { status: JOB_STATUS.running, shouldShowResultLink: true },
      { status: JOB_STATUS.aborted, shouldShowResultLink: false },
      { status: JOB_STATUS.retry, shouldShowResultLink: false },
    ];

    testCases.forEach(({ status, shouldShowResultLink }) => {
      it(`should ${shouldShowResultLink ? 'show' : 'not show'} result link for ${status} status`, () => {
        const mockJob = createMockJob({ status });

        renderComponent(<JobSummary job={mockJob} />);

        const resultLink = screen.queryByText('jobSummary.viewGeneratedArticles');

        if (shouldShowResultLink) {
          expect(resultLink).toBeInTheDocument();
          expect(resultLink).toHaveClass('mock-result-link');
        } else {
          expect(resultLink).not.toBeInTheDocument();
        }
      });
    });
  });

  describe('Content Sources', () => {
    it('should display list of documents when available', () => {
      const mockJob = createMockJob();

      renderComponent(<JobSummary job={mockJob} />);

      expect(screen.getByText('Document 1.pdf')).toBeInTheDocument();
      expect(screen.getByText('Document 2.docx')).toBeInTheDocument();
    });

    it('should display "no content sources" message when no documents available', () => {
      const mockJob = createMockJob({
        metadata: {
          ...MOCK_JOB_BASE.metadata,
          documents: [],
        },
      });

      renderComponent(<JobSummary job={mockJob} />);

      expect(screen.getByText('jobSummary.noContentSources')).toBeInTheDocument();
    });

    it('should display "no content sources" message when documents is undefined', () => {
      const mockJob = createMockJob({
        metadata: {
          ...MOCK_JOB_BASE.metadata,
          documents: undefined,
        },
      });

      renderComponent(<JobSummary job={mockJob} />);

      expect(screen.getByText('jobSummary.noContentSources')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should render result link with correct text and styling', () => {
      const mockJob = createMockJob({ status: JOB_STATUS.succeeded });

      renderComponent(<JobSummary job={mockJob} />);

      const resultLink = screen.getByText('jobSummary.viewGeneratedArticles');
      expect(resultLink).toBeInTheDocument();
      expect(resultLink).toHaveClass('mock-result-link');
    });

    it('should render document links with correct text and styling', () => {
      const mockJob = createMockJob();

      renderComponent(<JobSummary job={mockJob} />);

      const documentLink = screen.getByText('Document 1.pdf');
      expect(documentLink).toBeInTheDocument();
      expect(documentLink).toHaveClass('mock-source-file-name');
    });

    it('should open prompt modal when prompt title is clicked', async () => {
      const mockJob = createMockJob();

      renderComponent(<JobSummary job={mockJob} />);

      const promptTitle = screen.getByText('Test Prompt');
      await user.click(promptTitle);

      await waitFor(() => {
        const modal = screen.getByTestId('prompt-viewer-modal');
        expect(modal).toHaveAttribute('data-opened', 'true');
        expect(modal).toHaveAttribute('data-prompt-title', 'Test Prompt');
      });
    });

    it('should close prompt modal when close is triggered', async () => {
      const mockJob = createMockJob();

      renderComponent(<JobSummary job={mockJob} />);

      // Open modal
      const promptTitle = screen.getByText('Test Prompt');
      await user.click(promptTitle);

      // Close modal
      const modal = screen.getByTestId('prompt-viewer-modal');
      await user.click(modal);

      await waitFor(() => {
        expect(modal).toHaveAttribute('data-opened', 'false');
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle missing user information gracefully', () => {
      const mockJob = createMockJob({
        createdBy: undefined as any,
      });

      // Mock getUserName to return empty string for missing user
      vi.mocked(articleUtils.getUserName).mockReturnValue('');

      renderComponent(<JobSummary job={mockJob} />);

      expect(screen.getByText('unknown')).toBeInTheDocument();
    });

    it('should handle missing prompt information gracefully', () => {
      const mockJob = createMockJob({
        metadata: {
          ...MOCK_JOB_BASE.metadata,
          customPrompt: undefined,
        },
      });

      renderComponent(<JobSummary job={mockJob} />);

      expect(screen.getByText('unknown')).toBeInTheDocument();
    });

    it('should handle empty job metadata gracefully', () => {
      const mockJob = {
        ...MOCK_JOB_BASE,
        metadata: {} as any,
      };

      renderComponent(<JobSummary job={mockJob} />);

      expect(screen.getByText('jobSummary.noContentSources')).toBeInTheDocument();
    });

    it('should handle user with no display name', () => {
      const mockJob = createMockJob({
        createdBy: {
          ...MOCK_USER,
          displayName: '',
        },
      });

      // Mock getUserName to return the formatted name
      vi.mocked(articleUtils.getUserName).mockReturnValue('John Doe');

      renderComponent(<JobSummary job={mockJob} />);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });
  });

  describe('Component Memoization', () => {
    it('should not re-render when props are the same', () => {
      const mockJob = createMockJob();

      const { rerender } = renderComponent(<JobSummary job={mockJob} />);

      const initialRender = screen.getByText('jobSummary.label.creator');

      // Re-render with same props
      rerender(<JobSummary job={mockJob} />);

      const afterRerender = screen.getByText('jobSummary.label.creator');

      // Both should be the same DOM element (memoization working)
      expect(initialRender).toBe(afterRerender);
    });
  });

  describe('Translation Integration', () => {
    it('should use correct translation keys for labels', () => {
      const mockJob = createMockJob();

      renderComponent(<JobSummary job={mockJob} />);

      // Check if translation keys are correctly used
      expect(screen.getByText('jobSummary.label.creator')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.label.status')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.label.promptUsed')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.label.contentSources')).toBeInTheDocument();
      expect(screen.getByText('jobSummary.viewGeneratedArticles')).toBeInTheDocument();
    });

    it('should call getUserName with user information', () => {
      const mockJob = createMockJob();

      renderComponent(<JobSummary job={mockJob} />);

      // Verify getUserName was called with the user object and some language parameter
      expect(vi.mocked(articleUtils.getUserName)).toHaveBeenCalledWith(
        MOCK_USER,
        undefined // Language parameter from mocked useTolgee
      );
    });
  });

  describe('Accessibility', () => {
    it('should have proper accessibility attributes', () => {
      const mockJob = createMockJob();

      renderComponent(<JobSummary job={mockJob} />);

      // Check for avatar accessibility
      const avatar = screen.getByRole('img');
      expect(avatar).toBeInTheDocument();

      // Check for clickable elements
      const resultLink = screen.getByText('jobSummary.viewGeneratedArticles');
      expect(resultLink).toBeInTheDocument();

      const promptTitle = screen.getByText('Test Prompt');
      expect(promptTitle).toBeInTheDocument();
    });
  });
});
