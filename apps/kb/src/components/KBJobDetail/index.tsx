import { BackToLink, PageTitle } from '@/components';
import { useJobDetailContext } from '@/contexts';
import { JobType } from '@/types';
import { formatJobTitle } from '@/utils/job';
import { Box, LoadingOverlay, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { memo, useCallback, useEffect } from 'react';
import ExportJobSummary from './ExportJobSummary';
import JobSummary from './JobSummary';

const useStyles = createStyles(() => ({
  container: {
    padding: `${rem(24)} 0`,
  },
  header: {
    marginBottom: rem(40),
    marginTop: rem(16),
  },
  subTitle: {
    marginTop: rem(16),
  },
}));

const JobDetail: React.FC<{ jobId: string }> = ({ jobId }) => {
  const { t } = useTranslate('job');
  const { classes } = useStyles();

  const { getJobDetail, jobDetail, isLoading } = useJobDetailContext();

  /**
   * Fetches job details on component mount
   */
  const loadJobDetails = useCallback(async () => {
    try {
      await getJobDetail(jobId);
    } catch (error) {
      console.error('Failed to fetch job detail:', error);
    }
  }, [getJobDetail, jobId]);

  useEffect(() => {
    if (!jobId) return;
    loadJobDetails();
  }, [jobId]);

  return (
    <Box className={classes.container}>
      <LoadingOverlay visible={isLoading} />
      <BackToLink to={'/kb/jobs'} text={t('backToJobs')} />

      <Box className={classes.header}>
        <PageTitle order={2} title={t('detailPageTitle')} />
        {jobDetail && (
          <PageTitle className={classes.subTitle} order={3} title={formatJobTitle(jobDetail, t)} />
        )}
      </Box>

      {jobDetail && jobDetail.jobType === JobType.ArticleExport ? (
        <ExportJobSummary job={jobDetail} />
      ) : (
        jobDetail && <JobSummary job={jobDetail} />
      )}
    </Box>
  );
};

export default memo(JobDetail);
