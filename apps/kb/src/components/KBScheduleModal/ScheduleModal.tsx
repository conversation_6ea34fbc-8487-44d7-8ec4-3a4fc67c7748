import { Group, Modal, Stack, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { IconX } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useState } from 'react';
import SchedulePicker from './SchedulePicker';

const useStyles = createStyles((theme) => ({
  modal: {
    '& .mantine-Modal-content': {
      borderRadius: rem(12),
      padding: 0,
    },
    '& .mantine-Modal-body': {
      padding: rem(24),
    },
  },
  header: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: rem(8),
  },
  title: {
    fontSize: rem(20),
    fontWeight: 600,
    margin: 0,
  },
  closeButton: {
    padding: rem(8),
    borderRadius: rem(6),
    border: 'none',
    backgroundColor: 'transparent',
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    color: theme.colors.decaGrey[6],
    '&:hover': {
      backgroundColor: theme.colors.decaLight[1],
    },
  },
  description: {
    fontSize: rem(14),
    color: theme.colors.decaGrey[6],
    lineHeight: 1.5,
    marginBottom: rem(16),
  },
  actions: {
    marginTop: rem(24),
    paddingTop: rem(24),
    borderTop: `${rem(1)} solid ${theme.colors.decaLight[2]}`,
  },
  cancelButton: {
    borderColor: theme.colors.decaLight[3],
    color: theme.colors.decaGrey[6],
    '&:hover': {
      borderColor: theme.colors.decaLight[4],
      backgroundColor: theme.colors.decaLight[0],
    },
  },
  scheduleButton: {
    backgroundColor: theme.colors.decaBlue[5],
    '&:hover': {
      backgroundColor: theme.colors.decaBlue[6],
    },
    '&:disabled': {
      backgroundColor: theme.colors.decaLight[2],
      color: theme.colors.decaGrey[4],
    },
  },
}));

export interface ScheduleModalProps {
  /** Whether the modal is open */
  opened: boolean;
  /** Callback to close the modal */
  onClose: () => void;
  /** Callback when schedule is confirmed with selected datetime */
  onSchedule?: (dateTime: Date) => void;
  /** Modal title */
  title?: string;
  /** Modal description */
  description?: string;
  /** Cancel button text */
  cancelText?: string;
  /** Schedule button text */
  scheduleText?: string;
  /** Initial selected date/time */
  initialValue?: Date | null;
  /** Minimum selectable date */
  minDate?: Date;
  /** Maximum selectable date */
  maxDate?: Date;
  /** Whether the modal is in loading state */
  loading?: boolean;
}

/**
 * ScheduleModal component that allows users to select a date and time
 * for scheduling an action, with customizable title and description
 */
const ScheduleModal: React.FC<ScheduleModalProps> = ({
  opened,
  onClose,
  onSchedule,
  title,
  description,
  cancelText,
  scheduleText,
  initialValue,
  minDate,
  maxDate,
  loading = false,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('kb');

  // State for selected datetime
  const [selectedDateTime, setSelectedDateTime] = useState<Date | null>(initialValue || null);

  // Handle datetime change from picker
  const handleDateTimeChange = useCallback((value: Date | null) => {
    setSelectedDateTime(value);
  }, []);

  // Handle schedule action
  const handleSchedule = useCallback(() => {
    if (selectedDateTime && onSchedule) {
      onSchedule(selectedDateTime);
    }
  }, [selectedDateTime, onSchedule]);

  // Handle modal close
  const handleClose = useCallback(() => {
    setSelectedDateTime(initialValue || null);
    onClose();
  }, [initialValue, onClose]);

  // Default values
  const modalTitle = title || t('scheduleModal.title', 'Schedule Publish');
  const modalDescription =
    description ||
    t(
      'scheduleModal.description',
      'Choose the date and time you want this article to be published. It will automatically go live at the scheduled time.'
    );
  const cancelButtonText = cancelText || t('scheduleModal.cancel', 'Cancel');
  const scheduleButtonText = scheduleText || t('scheduleModal.schedulePublish', 'Schedule Publish');

  return (
    <Modal
      opened={opened}
      onClose={handleClose}
      centered
      withCloseButton={false}
      className={classes.modal}
      size={rem(608)}
      data-testid='schedule-modal'
    >
      <Stack gap={0}>
        {/* Header */}
        <div className={classes.header}>
          <Text className={classes.title} data-testid='modal-title'>
            {modalTitle}
          </Text>
          <button
            type='button'
            className={classes.closeButton}
            onClick={handleClose}
            data-testid='close-button'
            aria-label='Close modal'
          >
            <IconX size={24} />
          </button>
        </div>

        {/* Description */}
        <Text className={classes.description} data-testid='modal-description'>
          {modalDescription}
        </Text>

        {/* Schedule Picker */}
        <SchedulePicker
          value={selectedDateTime}
          onChange={handleDateTimeChange}
          minDate={minDate}
          maxDate={maxDate}
          disabled={loading}
          showResult={true}
        />

        {/* Actions */}
        <Group justify='flex-end' className={classes.actions}>
          <DecaButton
            radius={'xl'}
            variant='neutral'
            onClick={handleClose}
            disabled={loading}
            className={classes.cancelButton}
            data-testid='cancel-button'
          >
            {cancelButtonText}
          </DecaButton>
          <DecaButton
            radius={'xl'}
            variant='primary'
            onClick={handleSchedule}
            disabled={!selectedDateTime || loading}
            loading={loading}
            className={classes.scheduleButton}
            data-testid='schedule-button'
          >
            {scheduleButtonText}
          </DecaButton>
        </Group>
      </Stack>
    </Modal>
  );
};

export default ScheduleModal;
