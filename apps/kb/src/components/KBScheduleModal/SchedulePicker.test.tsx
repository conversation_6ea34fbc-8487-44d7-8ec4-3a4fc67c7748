import { fireEvent, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { renderWithProviders } from '../../utils/unitTest';
import SchedulePicker, { type SchedulePickerProps } from './SchedulePicker';

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
}));

// Test constants
const MOCK_DATE = new Date('2024-01-15T10:30:00');
const MOCK_MIN_DATE = new Date('2024-01-01');
const MOCK_MAX_DATE = new Date('2024-12-31');
const MOCK_FORMATTED_DATETIME = '2024/01/15 10:30';

// Mock the formatDateTime utility
vi.mock('@resola-ai/ui/utils/dateTime', () => ({
  formatDateTime: vi.fn((date: Date) => {
    if (!date) return '';
    return MOCK_FORMATTED_DATETIME;
  }),
}));

// Mock @mantine/dates components
vi.mock('@mantine/dates', () => ({
  DatePicker: ({ value, onChange, minDate, maxDate }: any) => (
    <div data-testid='date-picker'>
      <input
        data-testid='date-picker-input'
        type='date'
        value={value ? value.toISOString().split('T')[0] : ''}
        onChange={(e) => {
          const newDate = e.target.value ? new Date(e.target.value) : null;
          onChange(newDate);
        }}
        min={minDate ? minDate.toISOString().split('T')[0] : undefined}
        max={maxDate ? maxDate.toISOString().split('T')[0] : undefined}
      />
    </div>
  ),
  TimeInput: ({ value, onChange, disabled, leftSection, placeholder }: any) => (
    <div data-testid='time-input'>
      {leftSection}
      <input
        data-testid='time-input-field'
        type='time'
        value={value || ''}
        onChange={onChange}
        disabled={disabled}
        placeholder={placeholder}
      />
    </div>
  ),
}));

// Mock @mantine/emotion
vi.mock('@mantine/emotion', async () => {
  const actual = await vi.importActual('@mantine/emotion');
  return {
    ...actual,
    createStyles: () => () => ({
      classes: {
        wrapper: 'wrapper-class',
        datePickerWrapper: 'date-picker-wrapper-class',
        timeInputWrapper: 'time-input-wrapper-class',
        label: 'label-class',
        resultWrapper: 'result-wrapper-class',
        resultLabel: 'result-label-class',
        resultValue: 'result-value-class',
        icon: 'icon-class',
      },
      cx: (...args: any[]) => args.filter(Boolean).join(' '),
    }),
  };
});

// Mock @tabler/icons-react
vi.mock('@tabler/icons-react', () => ({
  IconCalendar: ({ className }: { className?: string }) => (
    <div data-testid='calendar-icon' className={className}>
      📅
    </div>
  ),
  IconClock: ({ className }: { className?: string }) => (
    <div data-testid='clock-icon' className={className}>
      🕐
    </div>
  ),
}));

describe('SchedulePicker', () => {
  const mockOnChange = vi.fn();

  const defaultProps: SchedulePickerProps = {
    value: null,
    onChange: mockOnChange,
    showResult: true,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  const renderSchedulePicker = (props: Partial<SchedulePickerProps> = {}) => {
    return renderWithProviders(<SchedulePicker {...defaultProps} {...props} />);
  };

  describe('Component Rendering', () => {
    it('renders the component with all required elements', () => {
      renderSchedulePicker();

      expect(
        screen.getByText((content) => {
          return content.includes('scheduleModal.selected');
        })
      ).toBeInTheDocument();
      expect(screen.getByTestId('date-picker')).toBeInTheDocument();
      expect(screen.getByTestId('time-input')).toBeInTheDocument();
      expect(document.querySelector('.tabler-icon-clock')).toBeInTheDocument();
    });

    it('renders without result section when showResult is false', () => {
      renderSchedulePicker({ showResult: false });

      expect(screen.getByTestId('date-picker')).toBeInTheDocument();
      expect(screen.getByTestId('time-input')).toBeInTheDocument();
      expect(
        screen.queryByText((content) => {
          return content.includes('scheduleModal.selected');
        })
      ).not.toBeInTheDocument();
    });

    it('displays "No date and time selected" when no value is provided', () => {
      renderSchedulePicker();

      expect(
        screen.getByText((content) => {
          return content.includes('scheduleModal.noDateTimeSelected');
        })
      ).toBeInTheDocument();
    });

    it('applies custom className when provided', () => {
      const customClass = 'custom-schedule-picker';
      renderSchedulePicker({ className: customClass });

      const wrapper = screen.getByTestId('date-picker').closest('.wrapper-class');
      expect(wrapper).toHaveClass(customClass);
    });
  });

  describe('Value Initialization', () => {
    it('initializes with provided value', () => {
      renderSchedulePicker({ value: MOCK_DATE });

      const dateInput = screen.getByTestId('date-picker-input') as HTMLInputElement;
      const timeInput = screen.getByTestId('time-input-field') as HTMLInputElement;

      expect(dateInput.value).toBe('2024-01-15');
      expect(timeInput.value).toBe('10:30');
    });

    it('displays formatted datetime when value is provided', () => {
      renderSchedulePicker({ value: MOCK_DATE });

      // Input values should be initialized correctly
      const dateInput = screen.getByTestId('date-picker-input') as HTMLInputElement;
      const timeInput = screen.getByTestId('time-input-field') as HTMLInputElement;

      expect(dateInput.value).toBe('2024-01-15');
      expect(timeInput.value).toBe('10:30');

      // The result section should be present
      const resultValue = screen.getByTestId('result-value');
      expect(resultValue).toBeInTheDocument();
    });
  });

  describe('Date Selection', () => {
    it('handles date selection correctly', async () => {
      const user = userEvent.setup();
      renderSchedulePicker();

      const dateInput = screen.getByTestId('date-picker-input');
      await user.type(dateInput, '2024-01-20');

      expect(mockOnChange).toHaveBeenCalled();
    });

    it('handles date deselection', async () => {
      const user = userEvent.setup();
      renderSchedulePicker({ value: MOCK_DATE });

      const dateInput = screen.getByTestId('date-picker-input');
      await user.clear(dateInput);

      expect(mockOnChange).toHaveBeenCalledWith(null);
    });

    it('respects minDate and maxDate constraints', () => {
      renderSchedulePicker({
        minDate: MOCK_MIN_DATE,
        maxDate: MOCK_MAX_DATE,
      });

      const dateInput = screen.getByTestId('date-picker-input') as HTMLInputElement;
      expect(dateInput.min).toBe('2024-01-01');
      expect(dateInput.max).toBe('2024-12-31');
    });
  });

  describe('Time Selection', () => {
    it('handles time input correctly', async () => {
      const user = userEvent.setup();
      renderSchedulePicker({ value: MOCK_DATE });

      const timeInput = screen.getByTestId('time-input-field');
      await user.clear(timeInput);
      await user.type(timeInput, '14:30');

      expect(mockOnChange).toHaveBeenCalled();
    });

    it('handles time change with existing date', async () => {
      const user = userEvent.setup();
      renderSchedulePicker({ value: MOCK_DATE });

      const timeInput = screen.getByTestId('time-input-field');
      await user.clear(timeInput);
      await user.type(timeInput, '09:15');

      // Should call onChange with updated datetime
      expect(mockOnChange).toHaveBeenCalled();
    });

    it('does not call onChange when time is changed without date', async () => {
      const user = userEvent.setup();
      renderSchedulePicker();

      const timeInput = screen.getByTestId('time-input-field');
      await user.type(timeInput, '14:30');

      // Should not call onChange because no date is selected
      expect(mockOnChange).toHaveBeenCalledWith(null);
    });
  });

  describe('Combined DateTime Logic', () => {
    it('combines date and time correctly', async () => {
      renderSchedulePicker();

      // First select date
      const dateInput = screen.getByTestId('date-picker-input');
      fireEvent.change(dateInput, { target: { value: '2024-01-20' } });

      // Then select time
      const timeInput = screen.getByTestId('time-input-field');
      fireEvent.change(timeInput, { target: { value: '15:45' } });

      // Should call onChange with combined datetime
      expect(mockOnChange).toHaveBeenCalled();
    });

    it('updates formatted result when both date and time are selected', async () => {
      renderSchedulePicker();

      // Select date
      const dateInput = screen.getByTestId('date-picker-input');
      fireEvent.change(dateInput, { target: { value: '2024-01-20' } });

      // Select time
      const timeInput = screen.getByTestId('time-input-field');
      fireEvent.change(timeInput, { target: { value: '15:45' } });

      // Verify the inputs have the correct values
      expect((dateInput as HTMLInputElement).value).toBe('2024-01-20');
      expect((timeInput as HTMLInputElement).value).toBe('15:45');

      // The result section should be present
      const resultValue = screen.getByTestId('result-value');
      expect(resultValue).toBeInTheDocument();
    });
  });

  describe('Disabled State', () => {
    it('disables date picker when disabled prop is true', () => {
      renderSchedulePicker({ disabled: true });

      const timeInput = screen.getByTestId('time-input-field') as HTMLInputElement;
      const dateTimeWrapper = screen.getByTestId('date-time-wrapper');

      expect(timeInput.disabled).toBe(true);
      expect(dateTimeWrapper).toHaveStyle('opacity: 0.5');
      expect(dateTimeWrapper).toHaveStyle('pointer-events: none');
    });

    it('enables inputs when disabled prop is false', () => {
      renderSchedulePicker({ disabled: false });

      const timeInput = screen.getByTestId('time-input-field') as HTMLInputElement;
      const dateTimeWrapper = screen.getByTestId('date-time-wrapper');

      expect(timeInput.disabled).toBe(false);
      expect(dateTimeWrapper).toHaveStyle('opacity: 1');
      expect(dateTimeWrapper).toHaveStyle('pointer-events: auto');
    });
  });

  describe('Edge Cases', () => {
    it('handles invalid time input gracefully', async () => {
      const user = userEvent.setup();
      renderSchedulePicker({ value: MOCK_DATE });

      const timeInput = screen.getByTestId('time-input-field');

      // Clear and type invalid time
      await user.clear(timeInput);
      await user.type(timeInput, 'invalid');

      // Should not break the component
      expect(
        screen.getByText((content) => {
          return content.includes('scheduleModal.noDateTimeSelected');
        })
      ).toBeInTheDocument();
    });

    it('handles undefined/null values correctly', () => {
      renderSchedulePicker({ value: null });

      expect(
        screen.getByText((content) => {
          return content.includes('scheduleModal.noDateTimeSelected');
        })
      ).toBeInTheDocument();
    });

    it('handles onChange prop being undefined', () => {
      renderSchedulePicker({ onChange: undefined });

      const dateInput = screen.getByTestId('date-picker-input');

      // Should not throw error when onChange is undefined
      expect(() => {
        fireEvent.change(dateInput, { target: { value: '2024-01-20' } });
      }).not.toThrow();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA attributes and labels', () => {
      renderSchedulePicker();

      expect(screen.getByTestId('date-picker')).toBeInTheDocument();
      expect(screen.getByTestId('time-input')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('scheduleModal.timePlaceholder')).toBeInTheDocument();
    });

    it('displays clock icon in time input', () => {
      renderSchedulePicker();

      expect(document.querySelector('.tabler-icon-clock')).toBeInTheDocument();
    });
  });
});
