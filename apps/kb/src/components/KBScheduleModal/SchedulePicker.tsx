import { Box, Stack, Text, rem } from '@mantine/core';
import { DatePicker, TimeInput } from '@mantine/dates';
import { createStyles } from '@mantine/emotion';
import { formatDateTime } from '@resola-ai/ui/utils/dateTime';
import { IconClock } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';

const useStyles = createStyles((theme) => ({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing.md,
    width: '100%',
  },

  dateTimeWrapper: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: rem(16),
    background: '#FFFFFF',
    border: '1px solid #CFCFD7',
    boxShadow: '0px 3px 12px rgba(13, 17, 54, 0.06)',
    borderRadius: rem(8),
    flex: 'none',
    order: 0,
    flexGrow: 0,
    gap: rem(16),
  },
  datePicker: {
    width: '100%',
    display: 'flex',
    justifyContent: 'center',

    '& .mantine-DatePicker-monthCell': {
      padding: `${rem(8)} ${rem(12)}`,
    },

    '& .mantine-DatePicker-day': {
      fontSize: rem(14),
      width: rem(36),
      height: rem(36),
      borderRadius: rem(8),
      border: `${rem(1)} solid transparent`,

      // Selected state (takes precedence over today)
      '&[data-selected]': {
        backgroundColor: theme.colors.decaNavy[4],
        color: theme.colors.decaMono[1],
        border: `${rem(1)} solid ${theme.colors.decaNavy[4]}`,
      },

      // Today state (only applies when not selected)
      '&[data-today]:not([data-selected])': {
        backgroundColor: theme.colors.decaBlue[0],
        color: theme.colors.decaNavy[4],
        border: `${rem(1)} solid ${theme.colors.decaBlue[2]}`,
      },
    },

    '& .mantine-DatePicker-calendarHeader': {
      marginLeft: rem(-40),

      '& .mantine-DatePicker-calendarHeaderLevel': {
        flex: '0.5',
      },
    },
  },
  timeInput: {
    width: '100%',

    '& .mantine-TimeInput-root': {
      width: '100%',
    },
    '& .mantine-TimeInput-input': {
      width: '100%',
    },
  },
  resultText: {
    fontSize: rem(14),
    color: theme.colors.decaGrey[6],
    marginTop: rem(8),
  },
  icon: {
    width: rem(16),
    height: rem(16),
    color: theme.colors.decaBlue[5],
  },
}));

export interface SchedulePickerProps {
  /** The selected date and time value */
  value?: Date | null;
  /** Callback when date/time changes */
  onChange?: (value: Date | null) => void;
  /** Minimum selectable date */
  minDate?: Date;
  /** Maximum selectable date */
  maxDate?: Date;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Additional CSS class name */
  className?: string;
  /** Whether to show the formatted result */
  showResult?: boolean;
}

/**
 * SchedulePicker component that combines DatePicker and TimeInput
 * for selecting both date and time, with Japanese datetime formatting
 */
const SchedulePicker: React.FC<SchedulePickerProps> = ({
  value,
  onChange,
  minDate,
  maxDate,
  disabled = false,
  className,
  showResult = true,
}) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('kb');

  // Internal state for selected date
  const [selectedDate, setSelectedDate] = useState<Date | null>(value || null);

  // Internal state for selected time (separate from date) - initialize from value if available
  const [selectedTime, setSelectedTime] = useState<string>(() => {
    if (value) {
      const hours = value.getHours().toString().padStart(2, '0');
      const minutes = value.getMinutes().toString().padStart(2, '0');
      return `${hours}:${minutes}`;
    }
    return '';
  });

  // Initialize time from value prop
  useEffect(() => {
    if (value) {
      const hours = value.getHours().toString().padStart(2, '0');
      const minutes = value.getMinutes().toString().padStart(2, '0');
      setSelectedTime(`${hours}:${minutes}`);
      setSelectedDate(value);
    } else {
      setSelectedTime('');
      setSelectedDate(null);
    }
  }, [value]);

  // Create combined datetime when date or time changes
  const combinedDateTime = useMemo(() => {
    if (!selectedDate || !selectedTime || !selectedTime.includes(':')) return null;

    const [hours, minutes] = selectedTime.split(':').map(Number);
    if (Number.isNaN(hours) || Number.isNaN(minutes)) return null;

    const newDate = new Date(selectedDate);
    newDate.setHours(hours, minutes, 0, 0);
    return newDate;
  }, [selectedDate, selectedTime]);

  // Handle date picker change
  const handleDateChange = useCallback(
    (date: Date | null) => {
      setSelectedDate(date);

      if (date && selectedTime && selectedTime.includes(':')) {
        const [hours, minutes] = selectedTime.split(':').map(Number);
        if (!Number.isNaN(hours) && !Number.isNaN(minutes)) {
          const newDateTime = new Date(date);
          newDateTime.setHours(hours, minutes, 0, 0);
          onChange?.(newDateTime);
          return;
        }
      }
      onChange?.(null);
    },
    [selectedTime, onChange]
  );

  // Handle time input change
  const handleTimeChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const timeValue = event.target.value;
      setSelectedTime(timeValue);

      if (selectedDate && timeValue && timeValue.includes(':')) {
        const [hours, minutes] = timeValue.split(':').map(Number);
        if (!Number.isNaN(hours) && !Number.isNaN(minutes)) {
          const newDateTime = new Date(selectedDate);
          newDateTime.setHours(hours, minutes, 0, 0);
          onChange?.(newDateTime);
          return;
        }
      }
      onChange?.(null);
    },
    [selectedDate, onChange]
  );

  // Format the result for display
  const formattedResult = useMemo(() => {
    if (!combinedDateTime) return '';
    return formatDateTime(combinedDateTime, 'ja');
  }, [combinedDateTime]);

  return (
    <Box className={cx(classes.wrapper, className)}>
      <Stack gap='md'>
        {/* Date & Time Picker Wrapper */}
        <Box
          className={classes.dateTimeWrapper}
          data-testid='date-time-wrapper'
          style={{ opacity: disabled ? 0.5 : 1, pointerEvents: disabled ? 'none' : 'auto' }}
        >
          <DatePicker
            className={classes.datePicker}
            value={selectedDate}
            onChange={handleDateChange}
            minDate={minDate}
            maxDate={maxDate}
            locale='ja'
            firstDayOfWeek={1}
            size='md'
            highlightToday
            allowDeselect
          />
          <TimeInput
            className={classes.timeInput}
            value={selectedTime}
            onChange={handleTimeChange}
            disabled={disabled}
            leftSection={<IconClock className={classes.icon} />}
            placeholder={t('scheduleModal.timePlaceholder')}
          />
        </Box>

        {/* Result Display */}
        {showResult && (
          <Text className={classes.resultText} data-testid='result-value'>
            {t('scheduleModal.selected')}:{' '}
            {formattedResult || t('scheduleModal.noDateTimeSelected')}
          </Text>
        )}
      </Stack>
    </Box>
  );
};

export default SchedulePicker;
