import { renderWithMantine } from '@/utils/unitTest';
import { fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import ScheduleModal from './ScheduleModal';

// Test constants - use specific timezone to avoid timezone issues
const MOCK_SELECTED_DATE = new Date('2026-01-13T13:00:00.000Z');
const MOCK_MIN_DATE = new Date('2026-01-01T00:00:00.000Z');
const MOCK_MAX_DATE = new Date('2026-12-31T23:59:59.000Z');
const MOCK_INITIAL_VALUE = new Date('2026-01-10T10:00:00.000Z');

const DEFAULT_PROPS = {
  opened: true,
  onClose: vi.fn(),
  onSchedule: vi.fn(),
};

const CUSTOM_PROPS = {
  ...DEFAULT_PROPS,
  title: 'Custom Schedule Title',
  description: 'Custom schedule description for testing',
  cancelText: 'Custom Cancel',
  scheduleText: 'Custom Schedule',
  initialValue: MOCK_INITIAL_VALUE,
  minDate: MOCK_MIN_DATE,
  maxDate: MOCK_MAX_DATE,
};

vi.mock('@tabler/icons-react', async () => {
  const actual = await vi.importActual('@tabler/icons-react');
  return {
    ...actual,
    IconX: (props) => (
      <span data-testid='close-icon' {...props}>
        CloseIcon
      </span>
    ),
    IconCalendar: (props) => (
      <span data-testid='calendar-icon' {...props}>
        CalendarIcon
      </span>
    ),
    IconClock: (props) => (
      <span data-testid='clock-icon' {...props}>
        ClockIcon
      </span>
    ),
  };
});

vi.mock('@resola-ai/ui', () => ({
  DecaButton: ({ children, ...props }) => <button {...props}>{children}</button>,
}));

// Helper function to convert Date to local datetime string
const formatDateForInput = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

// Mock SchedulePicker component
vi.mock('./SchedulePicker', () => ({
  default: ({ value, onChange, minDate, maxDate, disabled, showResult }) => (
    <div data-testid='schedule-picker'>
      <input
        data-testid='mock-datetime-input'
        type='datetime-local'
        value={value ? formatDateForInput(value) : ''}
        onChange={(e) => {
          const newValue = e.target.value ? new Date(e.target.value) : null;
          onChange?.(newValue);
        }}
        min={minDate ? formatDateForInput(minDate) : undefined}
        max={maxDate ? formatDateForInput(maxDate) : undefined}
        disabled={disabled}
      />
      {showResult && (
        <div data-testid='schedule-result'>{value ? value.toISOString() : 'No date selected'}</div>
      )}
    </div>
  ),
}));

describe('ScheduleModal', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render the modal when opened is true', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} />);

      expect(screen.getByTestId('schedule-modal')).toBeInTheDocument();
      expect(screen.getByTestId('modal-title')).toBeInTheDocument();
      expect(screen.getByTestId('modal-description')).toBeInTheDocument();
      expect(screen.getByTestId('schedule-picker')).toBeInTheDocument();
    });

    it('should not render the modal when opened is false', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} opened={false} />);

      // Mantine Modal renders the DOM element even when closed
      // We should check if the modal content is not accessible or check modal state
      const modal = screen.queryByTestId('schedule-modal');
      expect(modal).toBeInTheDocument();

      // Check if modal content is not accessible when closed
      expect(screen.queryByTestId('modal-title')).not.toBeInTheDocument();
      expect(screen.queryByTestId('modal-description')).not.toBeInTheDocument();
    });

    it('should render default title and description', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} />);

      // In test environment, the component uses the translation keys correctly
      expect(screen.getByTestId('modal-title')).toHaveTextContent('scheduleModal.title');
      expect(screen.getByTestId('modal-description')).toHaveTextContent(
        'scheduleModal.description'
      );
    });

    it('should render custom title and description', () => {
      renderWithMantine(<ScheduleModal {...CUSTOM_PROPS} />);

      expect(screen.getByTestId('modal-title')).toHaveTextContent('Custom Schedule Title');
      expect(screen.getByTestId('modal-description')).toHaveTextContent(
        'Custom schedule description for testing'
      );
    });

    it('should render default button texts', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} />);

      expect(screen.getByTestId('cancel-button')).toHaveTextContent('scheduleModal.cancel');
      expect(screen.getByTestId('schedule-button')).toHaveTextContent(
        'scheduleModal.schedulePublish'
      );
    });

    it('should render custom button texts', () => {
      renderWithMantine(<ScheduleModal {...CUSTOM_PROPS} />);

      expect(screen.getByTestId('cancel-button')).toHaveTextContent('Custom Cancel');
      expect(screen.getByTestId('schedule-button')).toHaveTextContent('Custom Schedule');
    });

    it('should render close button', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} />);

      expect(screen.getByTestId('close-button')).toBeInTheDocument();
      // The icon is rendered as SVG, not as our mock component
      expect(screen.getByTestId('close-button')).toHaveAttribute('aria-label', 'Close modal');
    });
  });

  describe('Initial State', () => {
    it('should initialize with null selected datetime when no initialValue', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} />);

      const datetimeInput = screen.getByTestId('mock-datetime-input');
      expect(datetimeInput).toHaveValue('');
    });

    it('should initialize with initialValue when provided', () => {
      renderWithMantine(<ScheduleModal {...CUSTOM_PROPS} />);

      const datetimeInput = screen.getByTestId('mock-datetime-input');
      expect(datetimeInput).toHaveValue(formatDateForInput(MOCK_INITIAL_VALUE));
    });

    it('should pass minDate and maxDate to SchedulePicker', () => {
      renderWithMantine(<ScheduleModal {...CUSTOM_PROPS} />);

      const datetimeInput = screen.getByTestId('mock-datetime-input');
      expect(datetimeInput).toHaveAttribute('min', formatDateForInput(MOCK_MIN_DATE));
      expect(datetimeInput).toHaveAttribute('max', formatDateForInput(MOCK_MAX_DATE));
    });

    it('should disable schedule button when no datetime selected', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} />);

      const scheduleButton = screen.getByTestId('schedule-button');
      expect(scheduleButton).toBeDisabled();
    });
  });

  describe('User Interactions', () => {
    it('should call onClose when close button is clicked', () => {
      const mockOnClose = vi.fn();
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} onClose={mockOnClose} />);

      const closeButton = screen.getByTestId('close-button');
      fireEvent.click(closeButton);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should call onClose when cancel button is clicked', () => {
      const mockOnClose = vi.fn();
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} onClose={mockOnClose} />);

      const cancelButton = screen.getByTestId('cancel-button');
      fireEvent.click(cancelButton);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should update selected datetime when SchedulePicker value changes', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} />);

      const datetimeInput = screen.getByTestId('mock-datetime-input');
      fireEvent.change(datetimeInput, {
        target: { value: formatDateForInput(MOCK_SELECTED_DATE) },
      });

      expect(datetimeInput).toHaveValue(formatDateForInput(MOCK_SELECTED_DATE));
    });

    it('should enable schedule button when datetime is selected', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} />);

      const datetimeInput = screen.getByTestId('mock-datetime-input');
      fireEvent.change(datetimeInput, {
        target: { value: formatDateForInput(MOCK_SELECTED_DATE) },
      });

      const scheduleButton = screen.getByTestId('schedule-button');
      expect(scheduleButton).not.toBeDisabled();
    });

    it('should call onSchedule with selected datetime when schedule button is clicked', () => {
      const mockOnSchedule = vi.fn();
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} onSchedule={mockOnSchedule} />);

      // Select a datetime
      const datetimeInput = screen.getByTestId('mock-datetime-input');
      fireEvent.change(datetimeInput, {
        target: { value: formatDateForInput(MOCK_SELECTED_DATE) },
      });

      // Click schedule button
      const scheduleButton = screen.getByTestId('schedule-button');
      fireEvent.click(scheduleButton);

      expect(mockOnSchedule).toHaveBeenCalledTimes(1);
      // The callback should be called with the date created from the input value
      expect(mockOnSchedule).toHaveBeenCalledWith(expect.any(Date));
    });

    it('should not call onSchedule when schedule button is clicked without selected datetime', () => {
      const mockOnSchedule = vi.fn();
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} onSchedule={mockOnSchedule} />);

      const scheduleButton = screen.getByTestId('schedule-button');
      fireEvent.click(scheduleButton);

      expect(mockOnSchedule).not.toHaveBeenCalled();
    });

    it('should reset selected datetime to initialValue when modal is closed', () => {
      const mockOnClose = vi.fn();
      renderWithMantine(
        <ScheduleModal {...DEFAULT_PROPS} onClose={mockOnClose} initialValue={MOCK_INITIAL_VALUE} />
      );

      // Change the selected datetime
      const datetimeInput = screen.getByTestId('mock-datetime-input');
      fireEvent.change(datetimeInput, {
        target: { value: formatDateForInput(MOCK_SELECTED_DATE) },
      });

      // Close the modal
      const closeButton = screen.getByTestId('close-button');
      fireEvent.click(closeButton);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });
  });

  describe('Loading State', () => {
    it('should disable buttons when loading is true', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} loading={true} />);

      const cancelButton = screen.getByTestId('cancel-button');
      const scheduleButton = screen.getByTestId('schedule-button');

      expect(cancelButton).toBeDisabled();
      expect(scheduleButton).toBeDisabled();
    });

    it('should disable SchedulePicker when loading is true', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} loading={true} />);

      const datetimeInput = screen.getByTestId('mock-datetime-input');
      expect(datetimeInput).toBeDisabled();
    });

    it('should show loading state on schedule button', () => {
      renderWithMantine(
        <ScheduleModal {...DEFAULT_PROPS} loading={true} initialValue={MOCK_SELECTED_DATE} />
      );

      const scheduleButton = screen.getByTestId('schedule-button');
      expect(scheduleButton).toHaveAttribute('data-loading', 'true');
    });
  });

  describe('Accessibility', () => {
    it('should have proper aria-label for close button', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} />);

      const closeButton = screen.getByTestId('close-button');
      expect(closeButton).toHaveAttribute('aria-label', 'Close modal');
    });

    it('should have proper modal role', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} />);

      // The role="dialog" is set on the Modal content, not the root element
      const modalContent = screen.getByRole('dialog');
      expect(modalContent).toBeInTheDocument();
    });
  });

  describe('Props Validation', () => {
    it('should handle undefined onSchedule prop', () => {
      renderWithMantine(<ScheduleModal opened={true} onClose={vi.fn()} />);

      const datetimeInput = screen.getByTestId('mock-datetime-input');
      fireEvent.change(datetimeInput, {
        target: { value: formatDateForInput(MOCK_SELECTED_DATE) },
      });

      const scheduleButton = screen.getByTestId('schedule-button');
      fireEvent.click(scheduleButton);

      // Should not throw error
      expect(scheduleButton).toBeInTheDocument();
    });

    it('should handle null initialValue', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} initialValue={null} />);

      const datetimeInput = screen.getByTestId('mock-datetime-input');
      expect(datetimeInput).toHaveValue('');
    });

    it('should handle undefined optional props', () => {
      const minimalProps = {
        opened: true,
        onClose: vi.fn(),
      };

      renderWithMantine(<ScheduleModal {...minimalProps} />);

      expect(screen.getByTestId('schedule-modal')).toBeInTheDocument();
      expect(screen.getByTestId('modal-title')).toHaveTextContent('scheduleModal.title');
      expect(screen.getByTestId('cancel-button')).toHaveTextContent('scheduleModal.cancel');
      expect(screen.getByTestId('schedule-button')).toHaveTextContent(
        'scheduleModal.schedulePublish'
      );
    });
  });

  describe('Translation Integration', () => {
    it('should use correct translation keys for title and description', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} />);

      expect(screen.getByTestId('modal-title')).toHaveTextContent('scheduleModal.title');
      expect(screen.getByTestId('modal-description')).toHaveTextContent(
        'scheduleModal.description'
      );
    });

    it('should use correct translation keys for button texts', () => {
      renderWithMantine(<ScheduleModal {...DEFAULT_PROPS} />);

      expect(screen.getByTestId('cancel-button')).toHaveTextContent('scheduleModal.cancel');
      expect(screen.getByTestId('schedule-button')).toHaveTextContent(
        'scheduleModal.schedulePublish'
      );
    });
  });
});
