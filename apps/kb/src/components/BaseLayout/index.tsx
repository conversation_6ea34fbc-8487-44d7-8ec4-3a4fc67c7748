import { HEIGHT_OF_HEADER } from '@/constants/ui';
import { AppShell, Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { HeaderContainer, LayoutStructure } from '@resola-ai/ui/components';
import { usePathName } from '@resola-ai/ui/hooks';
import type React from 'react';
import { useMemo } from 'react';

const DEFAULT_LOGO_URL = 'images/DECA-knowledgebase.svg';
const JAPANESE_LOGO_URL = 'images/DECA-knowledgebase-jp.svg';

const useStyles = createStyles((_theme, _, u) => ({
  appWrapper: {
    width: rem('100%'),
    height: `calc(100vh - ${rem(HEIGHT_OF_HEADER)})`,
    marginTop: rem(HEIGHT_OF_HEADER),
  },
  drawerCustomClass: {
    '.mantine-Overlay-root': {
      zIndex: 1099,
    },
    '.mantine-Drawer-inner': {
      zIndex: 1100,
    },
    '.mantine-Drawer-content': {
      flex: `0 0 ${rem(62)}`,
    },
  },
  header: {
    position: 'fixed',
  },
  burgerControlInHome: {
    display: 'none',
  },
  burgerControlInDetail: {
    display: 'none',
    [u.smallerThan('md')]: {
      display: 'block',
    },
  },
}));

interface BaseLayoutProps {
  navigationMobile?: React.ReactNode;
  children: React.ReactNode;
}

const BaseLayout: React.FC<BaseLayoutProps> = ({ navigationMobile, children }) => {
  const { classes } = useStyles();
  const pathName = usePathName();

  const burgerClass = useMemo(
    () => (pathName === '/chatbox' ? classes.burgerControlInHome : classes.burgerControlInDetail),
    [pathName]
  );

  return (
    <LayoutStructure>
      <AppShell header={{ height: HEIGHT_OF_HEADER }}>
        <HeaderContainer
          navigationMobile={navigationMobile}
          logoUrl={DEFAULT_LOGO_URL}
          jaLogoUrl={JAPANESE_LOGO_URL}
          drawerCustomClass={classes.drawerCustomClass}
          burgerCustomClass={burgerClass}
          className={classes.header}
          reloadDocument
        />
      </AppShell>
      <Flex className={classes.appWrapper}>{children}</Flex>
    </LayoutStructure>
  );
};

export default BaseLayout;
