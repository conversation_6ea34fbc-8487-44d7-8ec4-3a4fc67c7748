import { useAuth0 } from '@auth0/auth0-react';
import { Avatar, Box, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTolgee, useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import type React from 'react';
import { useCallback } from 'react';

import { TimeAgo } from '@/components/common';
import { useAppContext } from '@/contexts/AppContext';
import { useCommentContext } from '@/contexts/CommentContext';
import type { Comment, User } from '@/types';
import { getUserName } from '@/utils/article';
import { DecaButton } from '@resola-ai/ui';
import CommentActions from '../CommentActions';

const useStyles = createStyles((theme) => ({
  commentsWrapper: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(12),
    paddingTop: rem(12),
  },
  commentItem: {
    display: 'flex',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    gap: rem(12),
  },
  commentUser: {
    color: theme.colors.decaGrey[9],
    paddingTop: rem(4),
    fontWeight: 500,
    fontSize: theme.fontSizes.sm,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  commentTime: {
    marginLeft: rem(8),
  },
  commentMessage: {
    marginTop: rem(8),
    wordWrap: 'break-word',
    wordBreak: 'break-word',
  },
  commentActions: {
    marginLeft: rem(8),
  },
  commentLoadMore: {
    display: 'flex',
    justifyContent: 'center',
    paddingTop: rem(12),
  },
}));

interface CommentListProps {
  baseId: string;
  articleId: string;
  comments: Comment[];
}

const CommentList: React.FC<CommentListProps> = ({ baseId, articleId, comments = [] }) => {
  const { t } = useTranslate('article');
  const tolgee = useTolgee();
  const { user } = useAuth0();
  const { classes } = useStyles();
  const { pagination, setSelectedComment, deleteComment, loadMoreComments } = useCommentContext();
  const { openConfirmModal, closeConfirmModal } = useAppContext();

  /**
   * Check if the current user is the owner of the comment
   * @param commentUser - User
   * @returns boolean
   */
  const isCommentOwner = useCallback(
    (commentUser: User) => {
      if (!user) return false;
      const { namespace } = user;
      const userId = user[`${namespace}/metadata`]?.user_id;

      return commentUser.id === userId;
    },
    [user]
  );

  /**
   * Handle the edit action for the comment
   * @param comment - Comment
   * @returns void
   * @callback
   * @dependencies setSelectedComment
   */
  const handleEditComment = useCallback(
    (comment: Comment) => {
      setSelectedComment(comment);
    },
    [setSelectedComment]
  );

  /**
   * Handle the delete action for the comment
   * @param commentId - string
   * @returns void
   * @callback
   * @dependencies openConfirmModal, t, deleteComment, closeConfirmModal
   */
  const handleDeleteComment = useCallback(
    (commentId: string) => {
      openConfirmModal({
        onConfirm: async () => {
          await deleteComment(baseId, articleId, commentId);
          closeConfirmModal();
        },
        title: t('comments.deleteConfirmTitle'),
      });
    },
    [baseId, articleId, deleteComment, openConfirmModal, closeConfirmModal]
  );

  /**
   * Handle the load more comments action
   * @returns void
   * @callback
   * @dependencies loadMoreComments, baseId, articleId
   */
  const handleLoadMoreComments = useCallback(() => {
    if (!pagination?.hasPreviousPage) return;
    loadMoreComments(baseId, articleId);
  }, [baseId, articleId, loadMoreComments, pagination?.hasPreviousPage]);

  return (
    <Box className={classes.commentsWrapper}>
      {!isEmpty(comments) ? (
        <>
          {comments.map((comment: Comment) => (
            <Box key={comment.id} className={classes.commentItem}>
              <Avatar radius='xl' src={comment.createdBy?.picture ?? ''} />
              <Box>
                <Text className={classes.commentUser} component='h3'>
                  {getUserName(comment.createdBy, tolgee.getLanguage())}
                  <TimeAgo className={classes.commentTime} dateTime={comment.createdAt} />
                  {isCommentOwner(comment.createdBy) && (
                    <CommentActions
                      className={classes.commentActions}
                      onEdit={() => handleEditComment(comment)}
                      onDelete={() => handleDeleteComment(comment.id)}
                    />
                  )}
                </Text>
                <Text
                  className={classes.commentMessage}
                  component='p'
                  style={{ whiteSpace: 'pre-wrap' }}
                >
                  {comment.text}
                </Text>
              </Box>
            </Box>
          ))}
          {pagination?.hasPreviousPage && (
            <Box className={classes.commentLoadMore}>
              <DecaButton variant='neutral' size='sm' onClick={handleLoadMoreComments}>
                {t('comments.seeMore')}
              </DecaButton>
            </Box>
          )}
        </>
      ) : (
        <Text ta='center' c='dimmed'>
          {t('comments.empty')}
        </Text>
      )}
    </Box>
  );
};

export default CommentList;
