import { useCommentContext } from '@/contexts/CommentContext';
import { useAuth0 } from '@auth0/auth0-react';
import { ActionIcon, Avatar, Box, Loader, Textarea, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCheck, IconSend, IconX } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useEffect, useRef, useState } from 'react';

const useStyles = createStyles((theme) => ({
  commentWrapper: {
    display: 'flex',
    alignItems: 'center',
    gap: rem(12),
    padding: `${rem(12)} 0`,
    borderRadius: rem(12),
  },
  commentBox: {
    display: 'flex',
    alignItems: 'center',
    gap: rem(12),
    width: '100%',
    position: 'relative',
  },
  commentInputWrapper: {
    width: '100%',
  },
  commentInput: {
    width: '100%',
    height: rem(32),
    lineHeight: rem(24),
  },
  button: {
    color: theme.colors.decaNavy[5],
    '&:disabled': {
      backgroundColor: 'transparent',
      borderColor: 'transparent',
    },
  },
  cancelButton: {
    color: theme.colors.decaRed[5],
  },
  buttonActions: {
    display: 'flex',
    gap: rem(8),
    position: 'absolute',
    right: rem(10),
    top: rem(6),
  },
  commentIcon: {
    width: rem(16),
    height: rem(16),
  },
}));

interface CommentBoxProps {
  baseId: string;
  articleId: string;
}

const UpdateCommentButtons = ({ onSave, onCancel }) => {
  const { cx, classes } = useStyles();

  return (
    <>
      <ActionIcon className={classes.button} onClick={onSave}>
        <IconCheck className={classes.commentIcon} />
      </ActionIcon>
      <ActionIcon className={cx(classes.button, classes.cancelButton)} onClick={onCancel}>
        <IconX className={classes.commentIcon} />
      </ActionIcon>
    </>
  );
};

const CommentBox: React.FC<CommentBoxProps> = ({ baseId, articleId }) => {
  const { user } = useAuth0();
  const { t } = useTranslate('article');
  const { classes } = useStyles();
  const [comment, setComment] = useState('');
  const commentInputRef = useRef<HTMLTextAreaElement>(null);
  const { isCommentSending, selectedComment, setSelectedComment, createComment, updateComment } =
    useCommentContext();

  /**
   * Cancel edit comment
   * @returns void
   * @dependencies setSelectedComment, setComment
   */
  const cancelEditComment = useCallback(() => {
    setSelectedComment(undefined);
    setComment('');
  }, [setSelectedComment, setComment]);

  /**
   * Send comment
   * @returns void
   * @dependencies comment, baseId, articleId, createComment
   */
  const sendComment = useCallback(async () => {
    if (!comment) {
      return;
    }

    if (selectedComment) {
      await updateComment(baseId, articleId, selectedComment.id, comment);
      cancelEditComment();
    } else {
      await createComment(baseId, articleId, comment);
      setComment('');
    }
  }, [comment, baseId, articleId, createComment]);

  /**
   * Handle key down and bind Enter key to send comment
   * @param event React.KeyboardEvent<HTMLTextAreaElement>
   * @returns void
   */
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        if (!isCommentSending) sendComment();
      }

      if (event.key === 'Escape' && !event.shiftKey) {
        event.preventDefault();
        if (!isCommentSending && selectedComment) {
          cancelEditComment();
        }
      }
    },
    [sendComment]
  );

  useEffect(() => {
    if (selectedComment) {
      setComment(selectedComment.text);
      commentInputRef.current?.focus();
    }
  }, [selectedComment, setComment, commentInputRef]);

  return (
    <Box className={classes.commentWrapper}>
      <Avatar radius='xl' src={user?.picture} />
      <Box className={classes.commentBox}>
        <Textarea
          ref={commentInputRef}
          classNames={{ root: classes.commentInputWrapper, input: classes.commentInput }}
          value={comment}
          placeholder={t('comments.placeholder')}
          minRows={1}
          maxRows={2}
          autosize
          onChange={(event) => setComment(event.currentTarget.value)}
          onKeyDown={handleKeyDown}
        />
        <Box className={classes.buttonActions}>
          {isCommentSending ? (
            <ActionIcon className={classes.button} disabled>
              <Loader size={16} />
            </ActionIcon>
          ) : selectedComment ? (
            <UpdateCommentButtons onSave={sendComment} onCancel={cancelEditComment} />
          ) : (
            <ActionIcon className={classes.button} onClick={sendComment}>
              <IconSend className={classes.commentIcon} />
            </ActionIcon>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default CommentBox;
