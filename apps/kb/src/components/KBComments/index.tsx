import { useCommentContext } from '@/contexts/CommentContext';
import useKbAccessControl from '@/hooks/useKbAccessControl';
import { Box } from '@mantine/core';
import type React from 'react';
import { useEffect } from 'react';
import CommentBox from './CommentBox';
import CommentList from './CommentList';

interface KBCommentsProps {
  baseId: string;
  articleId: string;
}

const KBComments: React.FC<KBCommentsProps> = ({ baseId, articleId }) => {
  const { comments, getComments } = useCommentContext();
  const { permComment } = useKbAccessControl();

  useEffect(() => {
    getComments(baseId, articleId);
  }, [baseId, articleId]);

  return (
    <Box>
      {permComment.canCreate && <CommentBox baseId={baseId} articleId={articleId} />}
      <CommentList baseId={baseId} articleId={articleId} comments={comments} />
    </Box>
  );
};

export default KBComments;
