import { Box, Menu, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconDots, IconEdit, IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';

const useStyles = createStyles((theme) => ({
  menuTarget: {
    cursor: 'pointer',
    color: theme.colors.decaNavy[5],
    width: rem(20),
  },
  menuItem: {
    color: theme.colors.decaNavy[5],
  },
  menuItemDanger: {
    color: theme.colors.decaRed[5],
  },
}));

interface CommentActionsProps {
  className?: string;
  onEdit?: () => void;
  onDelete?: () => void;
}

const CommentActions: React.FC<CommentActionsProps> = ({ className, onEdit, onDelete }) => {
  const { t } = useTranslate('kb');
  const { classes } = useStyles();

  return (
    <Box className={className}>
      <Menu openDelay={100} closeDelay={400} width={rem(170)}>
        <Menu.Target>
          <IconDots className={classes.menuTarget} width={20} height={20} />
        </Menu.Target>

        <Menu.Dropdown>
          <Menu.Item
            className={classes.menuItem}
            leftSection={<IconEdit width={16} height={16} />}
            onClick={() => onEdit?.()}
          >
            {t('edit')}
          </Menu.Item>
          <Menu.Item
            className={classes.menuItemDanger}
            leftSection={<IconTrash width={16} height={16} />}
            onClick={() => onDelete?.()}
          >
            {t('delete')}
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>
    </Box>
  );
};

export default CommentActions;
