import { Modal, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';

import type { DocumentFile, Folder, KnowledgeBase } from '@/types';
import { isFolder } from '@/utils/tree';
import { KBDetailForm, KBFolderForm } from '../common';

interface KBContextMenuProps {
  opened: boolean;
  item?: KnowledgeBase | Folder | DocumentFile;
  onClose: () => void;
  onSubmitted: () => void;
}

const useStyles = createStyles((theme) => ({
  content: {
    width: rem(600),
    padding: rem(10),
    borderRadius: rem(20),
    flex: 'unset',
  },
  title: {
    color: theme.colors.decaNavy[5],
    fontSize: rem(20),
    fontWeight: 700,
  },
  close: {
    color: theme.colors.decaNavy[5],
    fontSize: rem(20),
  },
  header: {
    marginBottom: rem(20),
  },
}));

const KBContextMenu: React.FC<KBContextMenuProps> = ({ opened, item, onClose, onSubmitted }) => {
  const { classes } = useStyles();
  const { t } = useTranslate('home');

  return item ? (
    <Modal
      centered
      classNames={classes}
      opened={opened}
      onClose={onClose}
      title={isFolder(item) ? t('modal.editFolder') : t('modal.editKnowledgeBase')}
    >
      {isFolder(item) ? (
        <KBFolderForm isEdit folder={item as Folder} onClose={onClose} onSubmitted={onSubmitted} />
      ) : (
        <KBDetailForm
          isEdit
          knowledgeBase={item as KnowledgeBase}
          onCancel={onClose}
          onSubmitted={onSubmitted}
        />
      )}
    </Modal>
  ) : null;
};

export default KBContextMenu;
