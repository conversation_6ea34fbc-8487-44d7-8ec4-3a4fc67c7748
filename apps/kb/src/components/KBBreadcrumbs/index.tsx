import { BREADCRUMB_ZINDEX } from '@/constants/ui';
import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useIntersection } from '@mantine/hooks';
import { type BreadcrumbItem, Breadcrumbs } from '@resola-ai/ui';
import { useMemo } from 'react';

const useStyles = createStyles((theme) => ({
  breadcrumb: {
    height: rem(40),
    alignItems: 'center',
  },
  breadcrumbContainer: {
    width: `calc(100vw - ${rem(136)})`,
    position: 'fixed',
    padding: `${rem(23)} 0 ${rem(10)} ${rem(50)}`,
    backgroundColor: theme.white,
    zIndex: BREADCRUMB_ZINDEX,
    transition: 'border-bottom 0.1s linear',
    borderBottom: `${rem(2)} solid transparent`,
  },
  breadcrumbContainerExpanded: {
    paddingLeft: 0,
  },
  breadcrumbBordered: {
    borderBottomColor: `${theme.colors.decaLight[3]}`,
  },
}));

interface KBBreadcrumbProps {
  items: BreadcrumbItem[];
  expanded?: boolean;
}

const KBBreadcrumbs: React.FC<KBBreadcrumbProps> = ({ items, expanded }) => {
  const { classes, cx } = useStyles();
  const { ref: scrollingTrackerRef, entry } = useIntersection({
    root: null, // Use the document body as the root
    rootMargin: '-40px',
    threshold: 0,
  });

  const showBorder = useMemo(() => !entry?.isIntersecting, [entry?.isIntersecting]);

  return (
    <>
      <Box ref={scrollingTrackerRef} />
      <Box
        className={cx(
          classes.breadcrumbContainer,
          expanded && classes.breadcrumbContainerExpanded,
          showBorder && classes.breadcrumbBordered
        )}
      >
        <Breadcrumbs items={items} className={classes.breadcrumb} />
      </Box>
    </>
  );
};

export default KBBreadcrumbs;
