import { IconArticle, IconDocument, IconFolder, IconS3 } from '@/components/Icons';
import { type IconCustomProps, KB_TYPE, type KnowledgeBaseType } from '@/types';
import { FileIcon } from '..';

interface IllustrationIconProps extends IconCustomProps {
  type: KnowledgeBaseType;
  contentType?: string;
  imageUrl?: string;
  onError?: () => void;
}

const IllustrationIcon: React.FC<IllustrationIconProps> = ({
  type,
  contentType,
  imageUrl,
  ...rest
}) => {
  switch (type) {
    case KB_TYPE.article:
      return <IconArticle {...rest} data-testid='icon-article' />;

    case KB_TYPE.document:
      if (contentType) {
        return <FileIcon contentType={contentType} imageUrl={imageUrl} {...rest} />;
      }
      return <IconDocument {...rest} data-testid='icon-document' />;

    case KB_TYPE.folder:
      return <IconFolder {...rest} data-testid='icon-folder' />;

    case KB_TYPE.dataSource:
      return <IconS3 {...rest} data-testid='icon-s3' />;
    default:
      return null;
  }
};

export default IllustrationIcon;
