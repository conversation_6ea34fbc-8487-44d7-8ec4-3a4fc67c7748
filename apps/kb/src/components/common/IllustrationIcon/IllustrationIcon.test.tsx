import { FILE_CONTENT_TYPE, KB_TYPE, type KnowledgeBaseType } from '@/types';
import { renderWithProviders } from '@/utils/unitTest';
import { screen } from '@testing-library/react';
import { vi } from 'vitest';
import IllustrationIcon from './index';

// Mock the FileIcon component to test props passing
vi.mock('../FileIcon', () => ({
  default: vi.fn((props) => {
    // Call onError if it's provided to verify it works
    if (props.onError && props['data-testid'] === 'mock-trigger-error') {
      props.onError();
    }

    // Destructure custom props that shouldn't be passed to DOM elements
    const { contentType, imageUrl, onError, ...domProps } = props;

    return (
      <div data-testid={`mock-file-icon-${contentType}`} data-image-url={imageUrl} {...domProps} />
    );
  }),
}));

describe('IllustrationIcon', () => {
  it('should render article icon when type is article', () => {
    renderWithProviders(<IllustrationIcon type={KB_TYPE.article as KnowledgeBaseType} />);
    expect(screen.getByTestId('icon-article')).toBeInTheDocument();
  });

  it('should render folder icon when type is folder', () => {
    renderWithProviders(<IllustrationIcon type={KB_TYPE.folder as KnowledgeBaseType} />);
    expect(screen.getByTestId('icon-folder')).toBeInTheDocument();
  });

  it('should render S3 icon when type is from_datasource', () => {
    renderWithProviders(<IllustrationIcon type={KB_TYPE.dataSource as KnowledgeBaseType} />);
    expect(screen.getByTestId('icon-s3')).toBeInTheDocument();
  });

  describe('when type is document', () => {
    it('should render document icon when no contentType is provided', () => {
      renderWithProviders(<IllustrationIcon type={KB_TYPE.document as KnowledgeBaseType} />);
      expect(screen.getByTestId('icon-document')).toBeInTheDocument();
    });

    it('should render PDF icon when contentType is PDF', () => {
      renderWithProviders(
        <IllustrationIcon
          type={KB_TYPE.document as KnowledgeBaseType}
          contentType={FILE_CONTENT_TYPE.pdf}
        />
      );
      expect(screen.getByTestId(`mock-file-icon-${FILE_CONTENT_TYPE.pdf}`)).toBeInTheDocument();
    });

    it('should render image when contentType is image', () => {
      const imageUrl = 'https://example.com/image.jpg';
      renderWithProviders(
        <IllustrationIcon
          type={KB_TYPE.document as KnowledgeBaseType}
          contentType='image/jpeg'
          imageUrl={imageUrl}
        />
      );
      const image = screen.getByTestId('mock-file-icon-image/jpeg');
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute('data-image-url', imageUrl);
    });

    it('should handle image load error with onError callback', () => {
      const onError = vi.fn();
      const imageUrl = 'https://example.com/image.jpg';
      renderWithProviders(
        <IllustrationIcon
          type={KB_TYPE.document as KnowledgeBaseType}
          contentType='image/jpeg'
          imageUrl={imageUrl}
          onError={onError}
          data-testid='mock-trigger-error'
        />
      );

      expect(onError).toHaveBeenCalled();
    });

    it('should not throw when image fails without onError handler', () => {
      const imageUrl = 'https://example.com/image.jpg';
      renderWithProviders(
        <IllustrationIcon
          type={KB_TYPE.document as KnowledgeBaseType}
          contentType='image/jpeg'
          imageUrl={imageUrl}
        />
      );

      const element = screen.getByTestId('mock-file-icon-image/jpeg');
      expect(element).toBeInTheDocument();
    });

    it('should pass onError to FileIcon for non-image documents', () => {
      const onError = vi.fn();
      renderWithProviders(
        <IllustrationIcon
          type={KB_TYPE.document as KnowledgeBaseType}
          contentType={FILE_CONTENT_TYPE.pdf}
          onError={onError}
          data-testid='mock-trigger-error'
        />
      );

      expect(onError).toHaveBeenCalled();
    });
  });

  it('should render nothing for unknown type', () => {
    renderWithProviders(<IllustrationIcon type={KB_TYPE.unknown as KnowledgeBaseType} />);
    expect(screen.queryByTestId('icon-article')).toBeNull();
    expect(screen.queryByTestId('icon-folder')).toBeNull();
    expect(screen.queryByTestId('icon-s3')).toBeNull();
    expect(screen.queryByTestId('icon-document')).toBeNull();
    expect(screen.queryByTestId('mock-file-icon-application/pdf')).toBeNull();
    expect(screen.queryByTestId('mock-file-icon-text/plain')).toBeNull();
    expect(screen.queryByTestId('mock-file-icon-text/csv')).toBeNull();
  });
});
