import { Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { getTimeAgo } from '@resola-ai/ui/utils/dateTime';
import { useTranslate } from '@tolgee/react';
import isNumber from 'lodash/isNumber';
import type React from 'react';
import { useMemo } from 'react';

const useStyles = createStyles((theme) => ({
  root: {
    fontSize: rem(12),
    lineHeight: rem(12),
    color: theme.colors.decaGrey[2],
  },
}));

interface TimeAgoProps {
  dateTime: Date | string;
  className?: string;
}

const TimeAgo: React.FC<TimeAgoProps> = ({ dateTime, className = '' }) => {
  const { t } = useTranslate('common');
  const { classes, cx } = useStyles();

  const timeAgoInfo = useMemo(() => getTimeAgo(dateTime), [dateTime]);

  const timeAgoText = useMemo(
    () =>
      timeAgoInfo
        ? t(
            timeAgoInfo.key,
            isNumber(timeAgoInfo.value) ? { count: timeAgoInfo.value } : { date: timeAgoInfo.value }
          )
        : '',
    [timeAgoInfo]
  );

  return <Text className={cx(classes.root, className)}>{timeAgoText}</Text>;
};

export default TimeAgo;
