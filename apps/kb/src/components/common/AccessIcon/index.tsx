import { ACCESS_LEVEL, type AccessLevel, type IconCustomProps } from '@/types';
import { IconLock, IconWorld } from '@tabler/icons-react';

interface AccessIconProps extends IconCustomProps {
  accessLevel?: AccessLevel;
}

const AccessIcon: React.FC<AccessIconProps> = ({ accessLevel, ...rest }) => {
  switch (accessLevel) {
    case ACCESS_LEVEL.public:
      return <IconWorld {...rest} data-testid='access-icon-public' />;
    case ACCESS_LEVEL.private:
      return <IconLock {...rest} data-testid='access-icon-private' />;
    default:
      return null;
  }
};

export default AccessIcon;
