import { Box, Flex, Text, UnstyledButton, rem } from '@mantine/core';
import { createStyles, getStylesRef } from '@mantine/emotion';
import { IconChevronRight } from '@tabler/icons-react';
import { memo } from 'react';

interface KBActionButtonProps {
  title: string;
  description: string;
  subTitle?: string;
  icon: React.ReactNode;
  background?: string | 'purple' | 'blue' | 'orange' | 'green';
  onClick: () => void;
}

const useStyles = createStyles((theme) => ({
  button: {
    width: '100%',
    minWidth: rem(408),
    height: rem(115),
    borderRadius: rem(12),
    display: 'flex',
    flexDirection: 'column',
    padding: `${rem(12)} ${rem(16)}`,
    gap: rem(4),
  },
  purple: {
    backgroundColor: theme.colors.decaViolet[0],
  },
  blue: {
    backgroundColor: theme.colors.decaBlue[0],
  },
  orange: {
    backgroundColor: theme.colors.decaYellow[0],
    [`& .${getStylesRef('subTitle')}`]: {
      backgroundColor: theme.colors.decaYellow[6],
    },
  },
  green: {
    backgroundColor: theme.colors.decaGreen[0],
    [`& .${getStylesRef('subTitle')}`]: {
      backgroundColor: theme.colors.decaGreen[6],
    },
  },
  subTitle: {
    ref: getStylesRef('subTitle'),
    fontSize: rem(12),
    fontWeight: 500,
    color: theme.white,
    padding: `${rem(4)} ${rem(10)}`,
    paddingTop: rem(2),
    borderRadius: rem(15),
    height: rem(25),
  },
  title: {
    display: 'flex',
    alignItems: 'center',
    gap: rem(6),
    fontSize: theme.fontSizes.sm,
    fontWeight: 700,
    color: theme.colors.decaNavy[5],
    marginBottom: rem(4),
    lineHeight: rem(21.7),
    '& svg': {
      width: rem(14),
      height: rem(14),
    },
  },
  icon: {
    width: rem(36),
    height: rem(36),
    borderRadius: rem(16),
    backgroundColor: theme.white,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: rem(8),
    '& svg': {
      color: theme.colors.decaNavy[5],
    },
  },
  description: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.decaNavy[5],
    lineHeight: rem(18.6),
  },
}));

const KBActionButton: React.FC<KBActionButtonProps> = ({
  title,
  description,
  subTitle,
  icon,
  background = 'purple',
  onClick,
}) => {
  const { classes, cx } = useStyles();

  return (
    <UnstyledButton className={cx(classes.button, classes[background])} onClick={onClick}>
      <Flex justify={'space-between'} w={'100%'}>
        <Box className={classes.icon}>{icon}</Box>
        {subTitle && <Text className={classes.subTitle}>{subTitle}</Text>}
      </Flex>
      <Box className={classes.title}>
        <Text>{title}</Text>
        <IconChevronRight />
      </Box>
      <Text className={classes.description}>{description}</Text>
    </UnstyledButton>
  );
};

export default memo(KBActionButton);
