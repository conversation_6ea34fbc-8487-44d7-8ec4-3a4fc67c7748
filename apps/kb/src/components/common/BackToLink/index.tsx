import { Group, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconArrowLeft } from '@tabler/icons-react';
import { memo } from 'react';
import { Link } from 'react-router-dom';

interface BackToLinkProps {
  to: string;
  text: string;
}

const useStyles = createStyles((theme) => ({
  backLink: {
    fontSize: rem(14),
    fontWeight: 700,
    color: theme.colors.decaNavy[5],
    display: 'flex',
    alignItems: 'center',
    gap: rem(8),
    cursor: 'pointer',
    padding: `${rem(5)} ${rem(10)}`,
    borderRadius: rem(4),
    '&:hover': {
      backgroundColor: theme.colors.decaLight[0],
    },
    '& svg': {
      color: theme.colors.decaNavy[5],
      width: rem(20),
    },
    '& .mantine-Text-root': {
      fontWeight: 700,
    },
  },
}));

const BackToLink: React.FC<BackToLinkProps> = ({ to, text }) => {
  const { classes } = useStyles();

  return (
    <Group>
      <Link to={to} style={{ textDecoration: 'none' }}>
        <Group className={classes.backLink}>
          <IconArrowLeft />
          <Text>{text}</Text>
        </Group>
      </Link>
    </Group>
  );
};

export default memo(BackToLink);
