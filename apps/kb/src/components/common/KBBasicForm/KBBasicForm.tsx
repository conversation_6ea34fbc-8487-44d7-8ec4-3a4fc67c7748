import { Box, Group, Input, Radio, Text } from '@mantine/core';
import { RoundedButton } from '@resola-ai/ui';
import { sanitizeHtml } from '@resola-ai/ui/utils';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useState } from 'react';
import { Controller } from 'react-hook-form';
import { TextInput, Textarea } from 'react-hook-form-mantine';

import { useKbContext } from '@/contexts/KbContext';
import { useBasicFormStyles, useKbBasicForm } from '@/hooks';
import { KB_TYPE, type KnowledgeBase } from '@/types';

interface KBBasicFormProps {
  isEdit?: boolean;
  knowledgeBase?: KnowledgeBase;
  onSubmitted: () => void;
  onCancel: () => void;
}

export const KBBasicForm: React.FC<KBBasicFormProps> = ({
  isEdit = false,
  knowledgeBase,
  onCancel,
  onSubmitted,
}) => {
  const { classes } = useBasicFormStyles();
  const { t } = useTranslate('kb');
  const { saveKnowledgeBase } = useKbContext();
  const [loading, setLoading] = useState(false);

  const { control, handleSubmit, errors, getValues } = useKbBasicForm({
    currentKnowledgeBase: knowledgeBase,
    onSubmitted: async () => {
      setLoading(true);

      const sanitizedValues = {
        ...getValues(),
        description: sanitizeHtml(getValues().description),
      };

      await saveKnowledgeBase({
        ...knowledgeBase,
        ...sanitizedValues,
      } as KnowledgeBase);

      setLoading(false);
      onSubmitted();
    },
  });

  return (
    <form className={classes.root}>
      <Input.Wrapper label={t('kbName')}>
        <Box className={classes.inputInner}>
          <TextInput
            control={control}
            name='name'
            placeholder={t('kbNamePlaceholder')}
            error={errors.name?.message}
          />
        </Box>
      </Input.Wrapper>

      <Input.Wrapper label={t('kbDescription')}>
        <Box className={classes.inputInner}>
          <Textarea
            control={control}
            name='description'
            placeholder={t('kbDescriptionPlaceholder')}
            minRows={4}
            error={errors.description?.message}
          />
        </Box>
      </Input.Wrapper>

      {!isEdit ? (
        <Controller
          name='type'
          key='type'
          control={control}
          render={({ field }) => (
            <Radio.Group
              label={t('kbType')}
              key={field.name}
              error={errors.type?.message}
              {...field}
            >
              <Group mt='xs' className={classes.typeGroup}>
                {[KB_TYPE.qna, KB_TYPE.document].map((type) => (
                  <Radio
                    key={type}
                    value={type}
                    label={
                      <Box>
                        <Text className={classes.typeLabel}>{t(`${type}Label`)}</Text>
                        <Text className={classes.typeDescription}>{t(`${type}Description`)}</Text>
                      </Box>
                    }
                  />
                ))}
              </Group>
            </Radio.Group>
          )}
        />
      ) : null}

      <Group className={classes.buttonGroup}>
        <RoundedButton variant='light' onClick={() => onCancel()}>
          {t('cancel')}
        </RoundedButton>

        <RoundedButton onClick={handleSubmit} variant='filled' loading={loading}>
          {t(isEdit ? 'save' : 'create')}
        </RoundedButton>
      </Group>
    </form>
  );
};
