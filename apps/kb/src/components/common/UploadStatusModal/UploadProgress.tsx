import { type ExtraDataFile, FILE_UPLOAD_STATUS } from '@/types/file';
import { fileSize } from '@/utils/file';
import { Box, Flex, Group, Progress, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { IconAlertTriangle, IconCircleCheck } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useMemo } from 'react';
import { Link } from 'react-router-dom';
import AccessIcon from '../AccessIcon';

const useStyles = createStyles((theme) => ({
  fileGroup: {
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  fileInfo: {
    width: '350px',
    display: 'flex',
    gap: rem(12),
  },
  fileInfoHeader: {
    width: '250px',
  },
  fileName: {
    fontSize: theme.fontSizes.sm,
    fontWeight: 400,
    color: theme.colors.decaNavy[5],
  },
  fileSize: {
    fontSize: theme.fontSizes.xs,
    fontWeight: 400,
    color: theme.colors.decaNavy[3],
  },
  fileProgress: {
    flex: 1,
    display: 'flex',
    gap: rem(4),
    '& .mantine-Progress-root': {
      flex: 1,
    },
  },
  status: {
    fontSize: theme.fontSizes.sm,
    fontWeight: 400,
    display: 'flex',
    alignItems: 'center',
    svg: {
      marginRight: rem(6),
    },
  },
  uploaded: {
    color: theme.colors.decaGreen[6],
    svg: {
      stroke: theme.colors.decaGreen[6],
    },
  },
  failed: {
    color: theme.colors.decaRed[5],
    svg: {
      stroke: theme.colors.decaRed[5],
    },
  },
  icon: {
    width: rem(20),
    height: rem(20),
  },
  fileAccessLevel: {
    borderRadius: theme.radius.xl,
    padding: rem(4),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  fileAccessIcon: {
    width: rem(16),
    height: rem(16),
  },
}));

const UploadProgressBar = ({
  progressId,
  percentage,
}: {
  percentage: number;
  progressId: string;
}) => {
  const { classes } = useStyles();
  return (
    <Box id={`uploader-${progressId}`} className={classes.fileProgress}>
      <Progress value={percentage} mt={rem(6)} color='decaViolet.5' />
      <Text size={rem(14)} component='span' color='decaGrey.1'>
        {percentage}%
      </Text>
    </Box>
  );
};

const StatusIcon = ({ status }: { status: string }) => {
  const { classes } = useStyles();

  switch (status) {
    case FILE_UPLOAD_STATUS.failed:
      return <IconAlertTriangle className={classes.icon} />;

    case FILE_UPLOAD_STATUS.uploaded:
      return <IconCircleCheck className={classes.icon} />;

    default:
      return null;
  }
};

interface UploadProgressProps {
  fileId: string;
  file: File | null;
  status: FILE_UPLOAD_STATUS;
  percentage: number;
  extraDataFile?: ExtraDataFile;
}

const UploadProgress: React.FC<UploadProgressProps> = ({
  fileId,
  file,
  status,
  percentage,
  extraDataFile,
}) => {
  const { t } = useTranslate(['details', 'kb']);
  const { cx, classes } = useStyles();
  const statusClassNames = useMemo(
    () =>
      cx(classes.status, {
        [classes.uploaded]: status === FILE_UPLOAD_STATUS.uploaded,
        [classes.failed]: status === FILE_UPLOAD_STATUS.failed,
      }),
    [status, classes]
  );

  const fileName = useMemo(() => {
    let fileNameText = (
      <Text className={classes.fileName} lineClamp={1}>
        {file?.name}
      </Text>
    );
    if (status === FILE_UPLOAD_STATUS.uploaded) {
      fileNameText = (
        <Link to={`/kb/document/${fileId}`} style={{ textDecoration: 'none' }}>
          {fileNameText}
        </Link>
      );
    }

    return fileNameText;
  }, [status, fileId, file?.name]);

  return (
    <Group className={classes.fileGroup}>
      <Box className={classes.fileInfo}>
        <Box className={classes.fileInfoHeader}>
          {fileName}
          <Text className={classes.fileSize}>{fileSize(file?.size || 0)}</Text>
        </Box>
        {extraDataFile && (
          <DecaButton variant='neutral' size='sm' radius={rem(4)} disabled>
            <Flex align='center' gap={rem(4)}>
              <AccessIcon
                accessLevel={extraDataFile.accessLevel}
                className={classes.fileAccessIcon}
              />
              {t(`accessLevel.${extraDataFile.accessLevel}.label`, { ns: 'kb' })}
            </Flex>
          </DecaButton>
        )}
      </Box>
      {status === FILE_UPLOAD_STATUS.unknown ? (
        <UploadProgressBar progressId={fileId} percentage={percentage} />
      ) : (
        <Text className={statusClassNames}>
          <StatusIcon status={status} />
          {t(`upload.${status}`)}
        </Text>
      )}
    </Group>
  );
};

export default UploadProgress;
