import { FILE_UPLOAD_STATUS, type FileUploaderInstance } from '@/types/file';
import { Dialog, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import get from 'lodash/get';
import type React from 'react';
import { useCallback } from 'react';
import UploadProgress from './UploadProgress';

interface UploadStatusModalProps {
  opened: boolean;
  uploaders: Record<string, FileUploaderInstance>;
  onClose: () => void;
}

const useStyles = createStyles((theme) => ({
  root: {
    width: rem(650),
    boxShadow: `${rem(0)}  ${rem(6)} ${rem(25)} ${rem(0)} ${theme.colors.decaViolet[2]}`,
    '& .mantine-Dialog-closeButton': {
      width: rem(30),
      height: rem(30),
      color: theme.colors.decaNavy[5],
    },
  },
  title: {
    fontSize: theme.fontSizes.md,
    fontWeight: 700,
    color: theme.colors.decaNavy[5],
  },
  files: {
    overflowY: 'auto',
    paddingRight: rem(20),
  },
}));

const UploadStatusModal: React.FC<UploadStatusModalProps> = ({ opened, uploaders, onClose }) => {
  const { classes } = useStyles();
  const { t } = useTranslate('details');

  const renderProgresses = useCallback(
    () =>
      Object.values(uploaders)
        .reverse()
        .map((uploader: FileUploaderInstance) => (
          <UploadProgress
            key={get(uploader, 'fileId', '')}
            fileId={get(uploader, 'fileId', '')}
            file={get(uploader, 'file', null)}
            status={get(uploader, 'status', FILE_UPLOAD_STATUS.unknown)}
            percentage={get(uploader, 'percentage', 0)}
            extraDataFile={get(uploader, 'extraDataFile', undefined)}
          />
        )),
    [uploaders]
  );

  return (
    <Dialog
      opened={opened}
      onClose={onClose}
      size='lg'
      transitionProps={{ transition: 'slide-up', duration: 300 }}
      radius={rem(10)}
      className={classes.root}
      keepMounted
      withCloseButton
    >
      <Text className={classes.title}>{t('uploadDialog.title')}</Text>
      <Flex direction='column' gap={rem(10)} mt={rem(30)} mah={rem(245)} className={classes.files}>
        {uploaders && renderProgresses()}
      </Flex>
    </Dialog>
  );
};

export default UploadStatusModal;
