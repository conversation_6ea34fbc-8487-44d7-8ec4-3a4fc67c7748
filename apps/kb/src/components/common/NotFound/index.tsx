import { AppConfig } from '@/configs';
import { EntityType } from '@/types/entities';
import { Box, Stack, Text, rem } from '@mantine/core';
import { NotFound as UINotFound } from '@resola-ai/ui';
import { T, useTranslate } from '@tolgee/react';
import { useCallback, useMemo } from 'react';
import { Link, useLocation } from 'react-router-dom';

/**
 * Props for the NotFound component
 * @property entityType - Type of entity that wasn't found (folder, document, etc.)
 * @property fromUrl - Original URL that led to the 404 page
 */
interface NotFoundProps {
  className?: string;
  entityType?: EntityType;
  fromUrl?: string;
}

/**
 * NotFound component displays a 404 error page with customized messages based on entity type
 * Features:
 * - Custom messages for different entity types (folder, document, KB, etc.)
 * - Preserves language settings in navigation
 * - Shows original URL if available
 * - Provides navigation back to home
 */
export const NotFound: React.FC<NotFoundProps> = ({
  entityType = EntityType.BASE,
  fromUrl,
  className,
}: NotFoundProps) => {
  const { t } = useTranslate('common');
  const location = useLocation();

  /**
   * Creates a home link that preserves the current language setting
   * Extracts 'lang' parameter from current URL and appends it to the home URL
   */
  const homeLink = useMemo(() => {
    // Ensure we're getting the search parameter correctly
    const langParam = location.search || '';
    const homeUrl = `${AppConfig.BASE_PATH}${langParam}`;

    return (
      <Link to={homeUrl} data-testid='home-link'>
        {t('notFound.goToHome')}
      </Link>
    );
  }, [location.search, t]);

  /**
   * Returns appropriate title and description for the 404 page based on entity type
   * Falls back to generic "page not found" message if entity type is not recognized
   * @param type - The type of entity that wasn't found
   * @returns Object containing translated title and description
   */
  const getNotFoundContent = useCallback(
    (type: EntityType) => {
      const notFoundMessages = {
        [EntityType.FOLDER]: {
          title: t('notFound.folderTitle'),
          description: t('notFound.folderDescription'),
        },
        [EntityType.BASE]: {
          title: t('notFound.kbTitle'),
          description: t('notFound.kbDescription'),
        },
        [EntityType.ARTICLE]: {
          title: t('notFound.articleTitle'),
          description: t('notFound.articleDescription'),
        },
        [EntityType.DOCUMENT]: {
          title: t('notFound.documentTitle'),
          description: t('notFound.documentDescription'),
        },
        [EntityType.JOB]: {
          title: t('notFound.jobTitle'),
          description: t('notFound.jobDescription'),
        },
        [EntityType.TEMPLATE]: {
          title: t('notFound.templateTitle'),
          description: t('notFound.templateDescription'),
        },
      };

      // Fallback to generic message if entity type is not recognized
      return (
        notFoundMessages[type] ?? {
          title: t('notFound.pageTitle'),
          description: t('notFound.pageDescription'),
        }
      );
    },
    [t]
  );

  const content = getNotFoundContent(entityType);

  return (
    <Box data-testid='not-found-container' className={className}>
      <Stack gap={rem(16)} justify='center' align='center'>
        {/* Main 404 content with customized message */}
        <UINotFound title={content.title} description={content.description} />

        {/* Show original URL if available */}
        {fromUrl && (
          <Text size='sm'>
            <T
              keyName='notFound.fromUrl'
              params={{
                FromUrl: <Link to={fromUrl}>{fromUrl}</Link>,
              }}
            />
          </Text>
        )}

        {/* Home link with preserved language setting */}
        <Text size='sm'>{homeLink}</Text>
      </Stack>
    </Box>
  );
};

export default NotFound;
