import { AppConfig } from '@/configs';
import { EntityType } from '@/types/entities';
import { mockLibraries, renderWithProviders } from '@/utils/unitTest';
import { screen } from '@testing-library/react';
import { NotFound } from './index';

// Mock @tolgee/react
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
  T: ({ keyName, params }: { keyName: string; params?: any }) => {
    // Return the key name for simple cases, or handle params if present
    if (params && typeof params === 'object') {
      return (
        <span>
          {keyName}
          {Object.entries(params).map(([key, value]) => (
            <span key={key}>{String(value)}</span>
          ))}
        </span>
      );
    }
    return <span>{keyName}</span>;
  },
}));

// Test constants
const TEST_CONSTANTS = {
  FROM_URL: '/test/original-url',
  LANG_PARAM: '?lang=en',
  HOME_URL: `${AppConfig.BASE_PATH}?lang=en`,
};

// Mock translation keys - the mock returns the key itself, not the translated text
const TEST_TRANSLATIONS = {
  'notFound.folderTitle': 'notFound.folderTitle',
  'notFound.folderDescription': 'notFound.folderDescription',
  'notFound.kbTitle': 'notFound.kbTitle',
  'notFound.kbDescription': 'notFound.kbDescription',
  'notFound.pageTitle': 'notFound.pageTitle',
  'notFound.pageDescription': 'notFound.pageDescription',
  'notFound.fromUrl': 'notFound.fromUrl',
  'notFound.goToHome': 'notFound.goToHome',
  'notFound.articleTitle': 'notFound.articleTitle',
  'notFound.articleDescription': 'notFound.articleDescription',
  'notFound.documentTitle': 'notFound.documentTitle',
  'notFound.documentDescription': 'notFound.documentDescription',
  'notFound.jobTitle': 'notFound.jobTitle',
  'notFound.jobDescription': 'notFound.jobDescription',
  'notFound.templateTitle': 'notFound.templateTitle',
  'notFound.templateDescription': 'notFound.templateDescription',
};

// No need to mock react-router-dom locally - it's handled by unitTest.tsx

describe('NotFound', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLibraries();
  });

  it('renders with default props', () => {
    renderWithProviders(<NotFound />);
    expect(screen.getByTestId('not-found-container')).toBeInTheDocument();
    expect(screen.getByText(TEST_TRANSLATIONS['notFound.kbTitle'])).toBeInTheDocument();
    expect(screen.getByText(TEST_TRANSLATIONS['notFound.kbDescription'])).toBeInTheDocument();
  });

  it('renders with fromUrl prop', () => {
    renderWithProviders(<NotFound fromUrl={TEST_CONSTANTS.FROM_URL} />);
    // The fromUrl prop is passed to the T component, so we verify the component renders without error
    // and the translation key is present
    expect(screen.getByText(TEST_TRANSLATIONS['notFound.fromUrl'])).toBeInTheDocument();
  });

  it('renders home link with preserved language parameter', () => {
    mockLibraries({ search: TEST_CONSTANTS.LANG_PARAM });

    renderWithProviders(<NotFound />);
    const homeLink = screen.getByTestId('home-link');
    expect(homeLink).toBeInTheDocument();
    expect(homeLink).toHaveAttribute('href', TEST_CONSTANTS.HOME_URL);
    expect(homeLink).toHaveTextContent(TEST_TRANSLATIONS['notFound.goToHome']);
  });

  it('renders with different entity types', () => {
    const entityTypes = [
      {
        type: EntityType.FOLDER,
        titleKey: 'notFound.folderTitle',
        descKey: 'notFound.folderDescription',
      },
      { type: EntityType.BASE, titleKey: 'notFound.kbTitle', descKey: 'notFound.kbDescription' },
      {
        type: EntityType.ARTICLE,
        titleKey: 'notFound.articleTitle',
        descKey: 'notFound.articleDescription',
      },
      {
        type: EntityType.DOCUMENT,
        titleKey: 'notFound.documentTitle',
        descKey: 'notFound.documentDescription',
      },
      { type: EntityType.JOB, titleKey: 'notFound.jobTitle', descKey: 'notFound.jobDescription' },
      {
        type: EntityType.TEMPLATE,
        titleKey: 'notFound.templateTitle',
        descKey: 'notFound.templateDescription',
      },
    ];

    entityTypes.forEach(({ type, titleKey, descKey }) => {
      const { unmount } = renderWithProviders(<NotFound entityType={type} />);
      expect(screen.getByText(TEST_TRANSLATIONS[titleKey])).toBeInTheDocument();
      expect(screen.getByText(TEST_TRANSLATIONS[descKey])).toBeInTheDocument();
      unmount();
    });
  });

  it('renders generic not found message for unknown entity type', () => {
    renderWithProviders(<NotFound entityType={'unknown' as EntityType} />);
    expect(screen.getByText(TEST_TRANSLATIONS['notFound.pageTitle'])).toBeInTheDocument();
    expect(screen.getByText(TEST_TRANSLATIONS['notFound.pageDescription'])).toBeInTheDocument();
  });
});
