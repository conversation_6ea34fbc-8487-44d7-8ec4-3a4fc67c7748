import { Flex, Group, Input, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton, RichTextEditor } from '@resola-ai/ui';
import type { EditorEvents, EditorOptions } from '@tiptap/react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { memo, useCallback, useMemo, useState } from 'react';
import { Textarea } from 'react-hook-form-mantine';

import { useKbContext } from '@/contexts/KbContext';
import { useQnAManualForm } from '@/hooks';
import type { QnA, QnAStatus } from '@/types/qna';
import FormMessage from '../FormMessage';

interface QnAManualFormProps {
  isEdit?: boolean;
  kbId: string;
  currentQnA?: QnA | null;
  onCancel: () => void;
  onSubmitted: (qna?: QnA) => void;
}

const useStyles = createStyles((theme) => ({
  root: {
    '.mantine-InputWrapper-root': {
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      marginBottom: theme.spacing.md,
    },
    '.mantine-InputWrapper-label': {
      color: theme.colors.decaNavy[5],
      marginBottom: theme.spacing.xs,
    },
    '.mantine-Input-wrapper': {
      width: '100%',
      marginBottom: 0,
    },
    '.mantine-RichTextEditor-root': {
      borderColor: theme.colors.decaGrey[0],
      'p, [data-placeholder]': {
        fontSize: theme.fontSizes.sm,
      },
    },
    '.mantine-InputWrapper-error': {
      marginTop: rem(0),
    },
    '[contenteditable]': {
      minHeight: rem(240),
      maxHeight: rem(340),
      overflowY: 'auto',
    },
  },
  buttonGroup: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: theme.spacing.md,
  },
  saveAsDraftButton: {
    backgroundColor: theme.white,
    border: `1px solid ${theme.colors.decaNavy[5]}`,
  },
}));

const QnAManualForm: React.FC<QnAManualFormProps> = ({
  isEdit = false,
  kbId,
  currentQnA,
  onCancel,
  onSubmitted,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('kb');
  const { saveQnA } = useKbContext();
  const [loading, setLoading] = useState(false);

  const { control, handleSubmit, errors, setValue, getValues } = useQnAManualForm({
    currentQnA,
    onSubmitted: async () => {
      setLoading(true);

      const updatedQnA = await saveQnA(kbId, getValues());

      setLoading(false);
      onSubmitted(updatedQnA);
    },
  });

  const editorConfig: Partial<EditorOptions> = useMemo(
    () => ({
      onBlur: (props: EditorEvents['blur']) => {
        setValue('answerRaw', props.editor.getHTML());
        setValue('answer', props.editor.getText());
      },
    }),
    []
  );

  const onSubmit = useCallback((status: QnAStatus) => {
    setValue('status', status);
    handleSubmit();
  }, []);

  return (
    <form className={classes.root}>
      <Textarea
        control={control}
        name='question'
        label={t('qnaQuestion')}
        placeholder={t('qnaQuestionPlaceholder')}
        error={errors.question?.message as string}
        minRows={4}
      />

      <Input.Wrapper
        label={t('qnaAnswer')}
        error={
          errors.answer?.message ? (
            <FormMessage message={(errors.answer?.message as string) ?? ''} type='error' />
          ) : null
        }
        required
      >
        <RichTextEditor
          isHideToolbar
          initialContent={currentQnA?.answerRaw || currentQnA?.answer || ''}
          placeholder={t('qnaAnswerPlaceholder')}
          editorConfig={editorConfig as any}
        />
      </Input.Wrapper>

      <Group className={classes.buttonGroup}>
        <DecaButton radius={'xl'} variant='primary_text' onClick={onCancel}>
          {t('cancel')}
        </DecaButton>

        <Flex gap={rem(10)}>
          <DecaButton
            className={classes.saveAsDraftButton}
            variant='primary_text'
            onClick={() => onSubmit('draft')}
            radius={'xl'}
          >
            {t('saveAsDraft')}
          </DecaButton>

          <DecaButton
            variant='primary'
            radius={'xl'}
            onClick={() => onSubmit('published')}
            loading={loading}
            miw={rem(100)}
          >
            {t(isEdit ? 'save' : 'publish')}
          </DecaButton>
        </Flex>
      </Group>
    </form>
  );
};

export default memo(QnAManualForm);
