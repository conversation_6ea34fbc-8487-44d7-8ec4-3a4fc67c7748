import { Button, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type { IPaginationNextPrevious } from '@resola-ai/models';
import { IconChevronLeft, IconChevronRight } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { memo, useMemo } from 'react';

import { DEFAULT_PAGINATION_LIMIT } from '@/constants/api';
import { KBDirectionQueryEnum, type KnowledgeBaseDirectionQuery } from '@/types';
import PageSizeSelection from '../PageSizeSelection';

interface CustomPaginationProps {
  pagination: IPaginationNextPrevious;
  onChange: (direction: KnowledgeBaseDirectionQuery, cursor: string) => void;
  position?: 'left' | 'center' | 'right';
  direction?: KnowledgeBaseDirectionQuery;
  pageSize?: number;
  onPageSizeChange?: (size: number) => void;
}

const useStyles = createStyles((theme) => ({
  container: {
    '&.left': {
      justifyContent: 'flex-start',
    },
    '&.center': {
      justifyContent: 'center',
    },
    '&.right': {
      justifyContent: 'flex-end',
    },
  },

  paginationButton: {
    borderRadius: rem(34),
    color: theme.colors.decaNavy[5],
    backgroundColor: theme.white,
    border: `${rem(1)} solid ${theme.colors.decaLight[5]}`,
    fontWeight: 500,
    minWidth: rem(100),

    '&:hover': {
      backgroundColor: theme.colors.decaNavy[5],
      color: theme.white,
    },

    '&:disabled': {
      backgroundColor: theme.colors.decaLight[1],
      color: theme.colors.decaLight[5],
      borderColor: theme.colors.decaLight[2],
    },
  },
}));

const CustomPagination: React.FC<CustomPaginationProps> = ({
  pagination,
  onChange,
  position = 'center',
  direction = 'backward',
  pageSize = DEFAULT_PAGINATION_LIMIT,
  onPageSizeChange,
}) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('common');

  /**
   * Pagination Controls logic to handle the next and previous page
   * based on the direction pagination
   */
  const paginationControls = useMemo(() => {
    // Backward pagination
    if (direction === KBDirectionQueryEnum.Backward) {
      return {
        hasNextPage: pagination.hasPreviousPage,
        nextPageDirection: KBDirectionQueryEnum.Backward,
        nextPageCursor: pagination.first,
        hasPreviousPage: pagination.hasNextPage,
        previousPageDirection: KBDirectionQueryEnum.Forward,
        previousPageCursor: pagination.last,
      };
    }

    // Forward pagination
    return {
      hasNextPage: pagination.hasNextPage,
      nextPageDirection: KBDirectionQueryEnum.Forward,
      nextPageCursor: pagination.last,
      hasPreviousPage: pagination.hasPreviousPage,
      previousPageDirection: KBDirectionQueryEnum.Backward,
      previousPageCursor: pagination.first,
    };
  }, [direction, pagination]);

  return (
    <Button.Group className={cx(classes.container, position)}>
      <Button
        className={classes.paginationButton}
        leftSection={<IconChevronLeft />}
        disabled={!paginationControls.hasPreviousPage}
        onClick={() =>
          onChange(paginationControls.previousPageDirection, paginationControls.previousPageCursor)
        }
      >
        {t('backBtn')}
      </Button>
      {onPageSizeChange && (
        <PageSizeSelection value={pageSize.toString()} onChange={onPageSizeChange} />
      )}
      <Button
        className={classes.paginationButton}
        rightSection={<IconChevronRight />}
        disabled={!paginationControls.hasNextPage}
        onClick={() =>
          onChange(paginationControls.nextPageDirection, paginationControls.nextPageCursor)
        }
      >
        {t('nextBtn')}
      </Button>
    </Button.Group>
  );
};

export default memo(CustomPagination);
