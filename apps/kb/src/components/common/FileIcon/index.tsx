import {
  IconCsv,
  IconDoc,
  IconDocx,
  IconImage,
  IconMd,
  IconPdf,
  IconTxt,
} from '@/components/Icons';
import { FILE_CONTENT_TYPE, type IconCustomProps } from '@/types';
import { Image } from '@mantine/core';

interface FileIconProps extends IconCustomProps {
  contentType?: string;
  imageUrl?: string;
  onError?: () => void;
}

const FileIcon: React.FC<FileIconProps> = ({ contentType, imageUrl, onError, ...rest }) => {
  if (contentType?.match(FILE_CONTENT_TYPE.image)) {
    if (imageUrl) {
      return (
        <Image src={imageUrl} alt='illustration' onError={onError} {...rest} data-testid='image' />
      );
    }

    return <IconImage data-testid='icon-image' {...rest} />;
  }

  switch (contentType) {
    case FILE_CONTENT_TYPE.pdf:
      return <IconPdf data-testid='icon-pdf' {...rest} />;
    case FILE_CONTENT_TYPE.txt:
      return <IconTxt data-testid='icon-txt' {...rest} />;
    case FILE_CONTENT_TYPE.csv:
      return <IconCsv data-testid='icon-csv' {...rest} />;
    case FILE_CONTENT_TYPE.msword:
      return <IconDoc data-testid='icon-doc' {...rest} />;
    case FILE_CONTENT_TYPE.document:
      return <IconDocx data-testid='icon-docx' {...rest} />;
    case FILE_CONTENT_TYPE.markdown:
      return <IconMd data-testid='icon-md' {...rest} />;
    default:
      return null;
  }
};

export default FileIcon;
