import { FILE_CONTENT_TYPE } from '@/types';
import { renderWithProviders } from '@/utils/unitTest';
import { fireEvent, screen } from '@testing-library/react';
import FileIcon from './index';

describe('FileIcon', () => {
  it('should render image when content type is image', () => {
    const imageUrl = 'https://example.com/image.jpg';
    renderWithProviders(<FileIcon contentType='image/jpeg' imageUrl={imageUrl} />);
    const image = screen.getByTestId('image');
    expect(image).toHaveAttribute('src', imageUrl);
    expect(image).toHaveAttribute('alt', 'illustration');
  });

  it('should render image icon when content type is image', () => {
    renderWithProviders(<FileIcon contentType={FILE_CONTENT_TYPE.image} />);
    expect(screen.getByTestId('icon-image')).toBeInTheDocument();
  });

  it('should render PDF icon for PDF content type', () => {
    renderWithProviders(<FileIcon contentType={FILE_CONTENT_TYPE.pdf} />);
    expect(screen.getByTestId('icon-pdf')).toBeInTheDocument();
  });

  it('should render TXT icon for text content type', () => {
    renderWithProviders(<FileIcon contentType={FILE_CONTENT_TYPE.txt} />);
    expect(screen.getByTestId('icon-txt')).toBeInTheDocument();
  });

  it('should render CSV icon for CSV content type', () => {
    renderWithProviders(<FileIcon contentType={FILE_CONTENT_TYPE.csv} />);
    expect(screen.getByTestId('icon-csv')).toBeInTheDocument();
  });

  it('should render DOC icon for MS Word content type', () => {
    renderWithProviders(<FileIcon contentType={FILE_CONTENT_TYPE.msword} />);
    expect(screen.getByTestId('icon-doc')).toBeInTheDocument();
  });

  it('should render DOCX icon for document content type', () => {
    renderWithProviders(<FileIcon contentType={FILE_CONTENT_TYPE.document} />);
    expect(screen.getByTestId('icon-docx')).toBeInTheDocument();
  });

  it('should render MD icon for markdown content type', () => {
    renderWithProviders(<FileIcon contentType={FILE_CONTENT_TYPE.markdown} />);
    expect(screen.getByTestId('icon-md')).toBeInTheDocument();
  });

  it('should render nothing for unknown content type', () => {
    renderWithProviders(<FileIcon contentType='unknown/type' />);
    expect(screen.queryByTestId('image')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-image')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-pdf')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-txt')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-csv')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-doc')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-docx')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-md')).not.toBeInTheDocument();
  });

  it('should render nothing when no content type is provided', () => {
    renderWithProviders(<FileIcon />);
    expect(screen.queryByTestId('image')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-image')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-pdf')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-txt')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-csv')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-doc')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-docx')).not.toBeInTheDocument();
    expect(screen.queryByTestId('icon-md')).not.toBeInTheDocument();
  });

  it('should call onError when image fails to load', () => {
    const onError = vi.fn();
    const imageUrl = 'https://example.com/image.jpg';
    renderWithProviders(
      <FileIcon contentType='image/jpeg' imageUrl={imageUrl} onError={onError} />
    );

    const image = screen.getByTestId('image');
    fireEvent.error(image);

    expect(onError).toHaveBeenCalled();
  });

  it('should not throw when image fails to load without onError handler', () => {
    const imageUrl = 'https://example.com/image.jpg';
    renderWithProviders(<FileIcon contentType='image/jpeg' imageUrl={imageUrl} />);

    const image = screen.getByTestId('image');
    expect(() => fireEvent.error(image)).not.toThrow();
  });
});
