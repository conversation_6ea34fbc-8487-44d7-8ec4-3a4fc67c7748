import { Select, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronDown } from '@tabler/icons-react';
import { memo, useCallback } from 'react';

interface PageSizeSelectionProps {
  value: string;
  disabled?: boolean;
  options?: { value: string; label: string }[];
  onChange: (value: number) => void;
}

const useStyles = createStyles((theme) => ({
  select: {
    width: rem(70),
    margin: `0 ${rem(4)}`,
    borderColor: theme.colors.decaLight[4],
  },
  input: {
    fontWeight: 500,
    '&:focus-within': {
      borderColor: theme.colors.decaLight[5],
    },
  },
}));

const DEFAULT_PAGE_SIZE_OPTIONS = [
  { value: '10', label: '10' },
  { value: '20', label: '20' },
  { value: '30', label: '30' },
  { value: '40', label: '40' },
  { value: '50', label: '50' },
];

const PageSizeSelection: React.FC<PageSizeSelectionProps> = ({
  value,
  disabled = false,
  options = DEFAULT_PAGE_SIZE_OPTIONS,
  onChange,
}) => {
  const { classes } = useStyles();

  const handleSelectChange = useCallback(
    (value: string | null) => {
      if (value) {
        onChange(Number.parseInt(value));
      }
    },
    [onChange]
  );

  return (
    <Select
      classNames={{ wrapper: classes.select, input: classes.input }}
      rightSection={<IconChevronDown size={16} />}
      disabled={disabled}
      radius='xs'
      withCheckIcon={false}
      value={value}
      onChange={handleSelectChange}
      data={options}
      rightSectionPointerEvents='none'
    />
  );
};

export default memo(PageSizeSelection);
