import { Flex, Group, type ModalOverlayProps, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import { useCallback } from 'react';
import Modal from '../Modal';

// ConfirmModal alignment constants
export const CONFIRM_MODAL_ALIGNMENT = {
  CENTER: 'center',
  LEFT: 'left',
  RIGHT: 'right',
} as const;

export type ConfirmModalAlignmentType =
  (typeof CONFIRM_MODAL_ALIGNMENT)[keyof typeof CONFIRM_MODAL_ALIGNMENT];

export interface ConfirmModalOptions {
  isRemoving?: boolean;
  isShowCancel?: boolean;
  className?: string;
  modalSize?: string;
  isRowReverse?: boolean;
  zIndex?: number;
  overlayProps?: ModalOverlayProps;
}

export interface ConfirmModalProps {
  alignment?: ConfirmModalAlignmentType;
  opened: boolean;
  title: string;
  content: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  options?: ConfirmModalOptions;
}

const useStyles = createStyles((theme) => ({
  confirmModal: {
    '& .mantine-Modal-header': {
      padding: 0,
      minHeight: rem(32),
    },
    textAlign: 'left',
  },
  title: {
    fontSize: rem(18),
    fontWeight: 700,
    lineHeight: rem(28),
    color: theme.colors.decaGrey[9],
    whiteSpace: 'pre-line',
    wordBreak: 'break-word',
  },
  content: {
    fontSize: rem(16),
    fontWeight: 400,
    lineHeight: rem(24),
    color: theme.colors.decaGrey[6],
    whiteSpace: 'pre-line',
    wordBreak: 'break-word',
  },
  confirmActions: {
    marginTop: rem(24),
  },
  button: {
    textTransform: 'capitalize',
    minWidth: rem(100),
  },
  rowReverse: {
    flexDirection: 'row-reverse',
  },
  // Alignment styles
  alignCenter: {
    alignItems: 'center',
    textAlign: 'center',
  },
  alignLeft: {
    alignItems: 'flex-start',
    textAlign: 'left',
  },
  alignRight: {
    alignItems: 'flex-end',
    textAlign: 'right',
  },
  // Actions alignment styles
  actionsCenter: {
    justifyContent: 'center',
  },
  actionsLeft: {
    justifyContent: 'flex-end',
  },
  actionsRight: {
    justifyContent: 'flex-end',
  },
}));

export const ConfirmModal: React.FC<ConfirmModalProps> = ({
  alignment = CONFIRM_MODAL_ALIGNMENT.CENTER,
  opened,
  title,
  content,
  confirmText,
  cancelText,
  onConfirm,
  onCancel,
  options,
}) => {
  const { t } = useTranslate('common');
  const { cx, classes } = useStyles();
  const {
    isRemoving = false,
    isShowCancel = true,
    className,
    modalSize = 'md',
    isRowReverse = false,
    zIndex = 1000,
    overlayProps,
  } = options || {};

  // Handle button text validation with useCallback
  const getButtonText = useCallback((text: string | undefined, defaultText: string) => {
    return isEmpty(text) ? defaultText : text;
  }, []);

  // Mapping alignment to appropriate classes
  const getContentAlignmentClass = () => {
    switch (alignment) {
      case CONFIRM_MODAL_ALIGNMENT.LEFT:
        return classes.alignLeft;
      case CONFIRM_MODAL_ALIGNMENT.RIGHT:
        return classes.alignRight;
      case CONFIRM_MODAL_ALIGNMENT.CENTER:
      default:
        return classes.alignCenter;
    }
  };

  const getActionsAlignmentClass = () => {
    switch (alignment) {
      case CONFIRM_MODAL_ALIGNMENT.LEFT:
        return classes.actionsLeft;
      case CONFIRM_MODAL_ALIGNMENT.RIGHT:
        return classes.actionsRight;
      case CONFIRM_MODAL_ALIGNMENT.CENTER:
      default:
        return classes.actionsCenter;
    }
  };

  return (
    <Modal
      opened={opened}
      onClose={onCancel}
      size={modalSize}
      className={cx(className, classes.confirmModal)}
      zIndex={zIndex}
      overlayProps={overlayProps}
    >
      <Flex direction='column' gap={rem(8)} className={getContentAlignmentClass()}>
        <Text className={classes.title}>{title}</Text>
        <Text className={classes.content}>{content}</Text>
      </Flex>
      <Flex className={cx(classes.confirmActions, getActionsAlignmentClass())} gap={rem(15)}>
        <Group className={cx(isRowReverse ? classes.rowReverse : '')}>
          <DecaButton
            size='md'
            className={classes.button}
            variant={isRemoving ? 'negative' : 'primary'}
            onClick={onConfirm}
            radius={'sm'}
          >
            {getButtonText(confirmText, t('yesBtn'))}
          </DecaButton>
          {isShowCancel && (
            <DecaButton
              size='md'
              className={classes.button}
              variant='neutral'
              onClick={onCancel}
              radius={'sm'}
            >
              {getButtonText(cancelText, t('noBtn'))}
            </DecaButton>
          )}
        </Group>
      </Flex>
    </Modal>
  );
};

export default ConfirmModal;
