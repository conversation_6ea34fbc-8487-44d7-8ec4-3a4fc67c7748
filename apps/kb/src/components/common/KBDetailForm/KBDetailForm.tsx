import { useAppContext, useKnowledgeBaseContext } from '@/contexts';
import { useBasicFormStyles, useKbDetailForm } from '@/hooks';
import { ACCESS_LEVEL, type AccessLevel, KB_TYPE, type KnowledgeBase, PROCESS_TYPE } from '@/types';
import { Badge, Box, Flex, Group, Input, Radio, Text, rem } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { sanitizeHtml } from '@resola-ai/ui/utils';
import { useTranslate } from '@tolgee/react';
import camelCase from 'lodash/camelCase';
import type React from 'react';
import { useCallback, useState } from 'react';
import { Controller } from 'react-hook-form';
import { TextInput, Textarea } from 'react-hook-form-mantine';
import AccessIcon from '../AccessIcon';

interface KBDetailFormProps {
  isEdit?: boolean;
  knowledgeBase?: KnowledgeBase;
  onSubmitted: (data?: any) => void;
  onCancel: () => void;
  parentDirId?: string;
}

// In the near future, we will add DataSource type to the list
const SUPPORTED_KB_TYPES = [KB_TYPE.article];

export const KBDetailForm: React.FC<KBDetailFormProps> = ({
  isEdit = false,
  knowledgeBase,
  onCancel,
  onSubmitted,
  parentDirId,
}) => {
  const { classes } = useBasicFormStyles();
  const { t } = useTranslate('kb');
  const { saveKnowledgeBase } = useKnowledgeBaseContext();
  const { openNoticeModal } = useAppContext();
  const [loading, setLoading] = useState(false);

  const { control, handleSubmit, errors, getValues, watch } = useKbDetailForm({
    currentKnowledgeBase: {
      ...knowledgeBase,
      parentDirId: isEdit ? knowledgeBase?.parentDirId : parentDirId,
    } as KnowledgeBase,
    onSubmitted: async () => {
      setLoading(true);

      const sanitizedValues = {
        ...getValues(),
        description: sanitizeHtml(getValues().description),
      };

      const saveKBResponse = await saveKnowledgeBase({
        ...knowledgeBase,
        ...sanitizedValues,
      } as KnowledgeBase);

      setLoading(false);
      onSubmitted(saveKBResponse);

      const isAccessLevelChanged = isEdit && knowledgeBase?.accessLevel !== getValues().accessLevel;

      if (isAccessLevelChanged) {
        openNoticeModal({
          title: t('accessLevel.changed.title'),
          content: t('accessLevel.changed.content'),
          showLoader: true,
        });
      }
    },
  });

  /**
   * Handle key press event.
   * @param event Keyboard event
   * @returns void
   */
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      if (event.key === 'Enter') {
        event.preventDefault();
        handleSubmit();
      }
    },
    [handleSubmit]
  );

  return (
    <form className={classes.root}>
      <Input.Wrapper label={t('kbName')} withAsterisk>
        <Box className={classes.inputInner}>
          <TextInput
            control={control}
            name='name'
            placeholder={t('kbNamePlaceholder')}
            error={errors.name?.message}
            onKeyDown={handleKeyDown}
          />
        </Box>
      </Input.Wrapper>

      <Input.Wrapper label={t('kbDescription')}>
        <Box className={classes.inputInner}>
          <Textarea
            control={control}
            name='description'
            placeholder={t('kbDescriptionPlaceholder')}
            minRows={4}
            error={errors.description?.message}
            onKeyDown={handleKeyDown}
          />
        </Box>
      </Input.Wrapper>

      {!isEdit ? (
        <Controller
          name='baseType'
          key='baseType'
          control={control}
          render={({ field }) => (
            <Radio.Group
              label={t('kbType')}
              key={field.name}
              error={errors.baseType?.message}
              {...field}
            >
              <Group mt='xs' className={classes.typeGroup}>
                {SUPPORTED_KB_TYPES.map((type) => (
                  <Radio
                    key={type}
                    value={type}
                    label={
                      <Box>
                        <Text className={classes.typeLabel}>{t(`${camelCase(type)}Label`)}</Text>
                        <Text className={classes.typeDescription}>
                          {t(`${camelCase(type)}Description`)}
                        </Text>
                      </Box>
                    }
                  />
                ))}
              </Group>
            </Radio.Group>
          )}
        />
      ) : null}

      {watch('baseType') === KB_TYPE.article && (
        <Controller
          name='accessLevel'
          key='accessLevel'
          control={control}
          render={({ field }) => (
            <Radio.Group
              label={
                <Flex direction={'column'} gap={rem(13)}>
                  <Text fz='md'>{t('accessLevel.label')}</Text>
                  {isEdit && knowledgeBase?.processType === PROCESS_TYPE.processing ? (
                    <Badge maw={rem(80)} color='decaYellow.0' sx={{ textTransform: 'capitalize' }}>
                      <Text fz='md' color='decaYellow.9'>
                        {t('accessLevel.processing')}
                      </Text>
                    </Badge>
                  ) : null}
                </Flex>
              }
              key={field.name}
              error={errors.accessLevel?.message}
              {...field}
            >
              <Group className={classes.typeGroup}>
                <Text color='decaGrey.5'>{t('accessLevel.description')}</Text>
                {[ACCESS_LEVEL.private, ACCESS_LEVEL.public].map((level) => (
                  <Radio
                    classNames={{ inner: classes.accessLevelRadio }}
                    key={level}
                    value={level}
                    disabled={isEdit && knowledgeBase?.processType === PROCESS_TYPE.processing}
                    label={
                      <Box>
                        <Flex gap={rem(9)} align={'center'}>
                          <Box className={classes.iconBox}>
                            <AccessIcon accessLevel={level as AccessLevel} />
                          </Box>
                          <Text fz='md' className={classes.typeLabel}>
                            {t(`accessLevel.${level}.label`)}
                          </Text>
                        </Flex>
                        <Text className={classes.typeDescription}>
                          {t(`accessLevel.${level}.description`)}
                        </Text>
                      </Box>
                    }
                  />
                ))}
              </Group>
            </Radio.Group>
          )}
        />
      )}

      <Group className={classes.buttonGroup}>
        <DecaButton radius={'xl'} variant='neutral' onClick={() => onCancel()}>
          {t('cancel')}
        </DecaButton>

        <DecaButton radius={'xl'} variant='primary' onClick={handleSubmit} loading={loading}>
          {t(isEdit ? 'save' : 'create')}
        </DecaButton>
      </Group>
    </form>
  );
};
