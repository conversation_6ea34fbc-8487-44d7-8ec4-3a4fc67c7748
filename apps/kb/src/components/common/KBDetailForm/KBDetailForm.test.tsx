import { useAppContext } from '@/contexts/AppContext';
import { useKnowledgeBaseContext } from '@/contexts/KnowledgeBaseContext';
import {
  ACCESS_LEVEL,
  type AccessLevel,
  KB_TYPE,
  type KnowledgeBase,
  PROCESS_TYPE,
  type ProcessType,
} from '@/types';
import { renderWithProviders } from '@/utils/unitTest';
import { sanitizeHtml } from '@resola-ai/ui/utils';
import { fireEvent, screen, waitFor, within } from '@testing-library/react';
import type React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { KBDetailForm } from './KBDetailForm';

// Mock the sanitizeHtml utility
vi.mock('@resola-ai/ui/utils', () => ({
  sanitizeHtml: vi.fn((text) => text),
}));

// Mock Tolgee React hooks
vi.mock('@tolgee/react', () => ({
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  useTranslate: () => ({
    t: (key: string, options?: any) => {
      // Handle namespaced keys by stripping the namespace prefix
      let translationKey = key;
      if (key.includes(':')) {
        translationKey = key.split(':')[1];
      }

      if (options && typeof options === 'object') {
        let result = translationKey;
        Object.entries(options).forEach(([optionKey, value]) => {
          result = result.replace(`{{${optionKey}}}`, String(value));
        });
        return result;
      }
      return translationKey;
    },
    i18n: { language: 'en' },
  }),
  useTolgee: () => ({
    t: (key: string, options?: any) => {
      let translationKey = key;
      if (key.includes(':')) {
        translationKey = key.split(':')[1];
      }
      if (options && typeof options === 'object') {
        let result = translationKey;
        Object.entries(options).forEach(([optionKey, value]) => {
          result = result.replace(`{{${optionKey}}}`, String(value));
        });
        return result;
      }
      return translationKey;
    },
    addPlugin: vi.fn(),
    use: vi.fn(),
    init: vi.fn(),
    changeLanguage: vi.fn(),
    getLanguage: vi.fn(() => 'en'),
    isLoaded: vi.fn(() => true),
    isLoading: vi.fn(() => false),
    on: vi.fn(),
    off: vi.fn(),
    emit: vi.fn(),
    addActiveNs: vi.fn(),
  }),
}));

// Mock the contexts
vi.mock('@/contexts/KnowledgeBaseContext', () => ({
  useKnowledgeBaseContext: vi.fn(),
}));

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: vi.fn(),
}));

// Mock the useStyles hook
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...(actual as any),
    useStyles: () => ({ classes: {} }),
    rem: (value: string) => value,
  };
});

// Constants for test data
const TEST_KB_NAME = 'Test Knowledge Base';
const TEST_KB_DESCRIPTION = 'This is a test knowledge base description';
const TEST_PARENT_DIR_ID = 'parent-dir-123';

// Mock data
const mockKnowledgeBase: KnowledgeBase = {
  id: 'kb-123',
  name: TEST_KB_NAME,
  description: TEST_KB_DESCRIPTION,
  baseType: KB_TYPE.article,
  type: KB_TYPE.article,
  accessLevel: ACCESS_LEVEL.private as AccessLevel,
  parentDirId: TEST_PARENT_DIR_ID,
  processType: PROCESS_TYPE.done as ProcessType,
};

// Mock functions
const mockSaveKnowledgeBase = vi.fn().mockResolvedValue(mockKnowledgeBase);
const mockOpenNoticeModal = vi.fn();
const mockOnCancel = vi.fn();
const mockOnSubmitted = vi.fn();

describe('KBDetailForm', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (sanitizeHtml as any).mockImplementation((text) => text);

    (useKnowledgeBaseContext as any).mockReturnValue({
      saveKnowledgeBase: mockSaveKnowledgeBase,
    });

    (useAppContext as any).mockReturnValue({
      openNoticeModal: mockOpenNoticeModal,
    });
  });

  describe('Create mode', () => {
    it('renders the form with empty fields in create mode', () => {
      renderWithProviders(
        <KBDetailForm
          isEdit={false}
          onCancel={mockOnCancel}
          onSubmitted={mockOnSubmitted}
          parentDirId={TEST_PARENT_DIR_ID}
        />
      );

      expect(screen.getByPlaceholderText('kbNamePlaceholder')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('kbDescriptionPlaceholder')).toBeInTheDocument();
      expect(screen.getByText('kbType')).toBeInTheDocument();

      // Check radio options - now only article type should be available
      const radioGroups = screen.getAllByRole('radiogroup');
      const kbTypeRadioGroup = radioGroups[0];
      expect(within(kbTypeRadioGroup).getByDisplayValue('article')).toBeInTheDocument();
      // Verify other types are not present
      expect(
        within(kbTypeRadioGroup).queryByDisplayValue('from_datasource')
      ).not.toBeInTheDocument();
      expect(within(kbTypeRadioGroup).queryByDisplayValue('document')).not.toBeInTheDocument();
    });

    it('shows access level options when article type is selected', async () => {
      renderWithProviders(
        <KBDetailForm
          isEdit={false}
          onCancel={mockOnCancel}
          onSubmitted={mockOnSubmitted}
          parentDirId={TEST_PARENT_DIR_ID}
        />
      );

      // Select article type by value
      const radioGroups = screen.getAllByRole('radiogroup');
      const kbTypeRadioGroup = radioGroups[0];
      const articleRadio = within(kbTypeRadioGroup).getByDisplayValue('article');
      fireEvent.click(articleRadio);

      // Check if access level options are displayed
      await waitFor(() => {
        const updatedRadioGroups = screen.getAllByRole('radiogroup');
        expect(updatedRadioGroups.length).toBeGreaterThan(1); // Now we should have at least two radiogroups

        expect(screen.getByText('accessLevel.label')).toBeInTheDocument();
        expect(screen.getByText('accessLevel.description')).toBeInTheDocument();

        // Get the access level radiogroup
        const accessLevelRadioGroup = updatedRadioGroups[1];
        expect(within(accessLevelRadioGroup).getByDisplayValue('private')).toBeInTheDocument();
        expect(within(accessLevelRadioGroup).getByDisplayValue('public')).toBeInTheDocument();
      });
    });

    it('submits the form with correct data in create mode', async () => {
      renderWithProviders(
        <KBDetailForm
          isEdit={false}
          onCancel={mockOnCancel}
          onSubmitted={mockOnSubmitted}
          parentDirId={TEST_PARENT_DIR_ID}
        />
      );

      // Fill in the form
      const nameInput = screen.getByPlaceholderText('kbNamePlaceholder');
      const descriptionInput = screen.getByPlaceholderText('kbDescriptionPlaceholder');

      fireEvent.change(nameInput, { target: { value: TEST_KB_NAME } });
      fireEvent.change(descriptionInput, { target: { value: TEST_KB_DESCRIPTION } });

      // Select article type
      const radioGroups = screen.getAllByRole('radiogroup');
      const kbTypeRadioGroup = radioGroups[0];
      const articleRadio = within(kbTypeRadioGroup).getByDisplayValue('article');
      fireEvent.click(articleRadio);

      // Select private access level
      await waitFor(() => {
        const updatedRadioGroups = screen.getAllByRole('radiogroup');
        const accessLevelRadioGroup = updatedRadioGroups[1];
        const privateRadio = within(accessLevelRadioGroup).getByDisplayValue('private');
        fireEvent.click(privateRadio);
      });

      // Submit the form
      const submitButton = screen.getByText('create');
      fireEvent.click(submitButton);

      // Verify saveKnowledgeBase was called with correct data
      await waitFor(() => {
        expect(mockSaveKnowledgeBase).toHaveBeenCalledWith(
          expect.objectContaining({
            name: TEST_KB_NAME,
            description: TEST_KB_DESCRIPTION,
            baseType: KB_TYPE.article,
            accessLevel: ACCESS_LEVEL.private,
            parentDirId: TEST_PARENT_DIR_ID,
          })
        );
        expect(mockOnSubmitted).toHaveBeenCalledWith(mockKnowledgeBase);
      });
    });

    it('calls onCancel when cancel button is clicked', () => {
      renderWithProviders(
        <KBDetailForm
          isEdit={false}
          onCancel={mockOnCancel}
          onSubmitted={mockOnSubmitted}
          parentDirId={TEST_PARENT_DIR_ID}
        />
      );

      const cancelButton = screen.getByText('cancel');
      fireEvent.click(cancelButton);

      expect(mockOnCancel).toHaveBeenCalled();
    });

    it('submits the form with sanitized description', async () => {
      const unsanitizedDescription = '<script>alert("test")</script>Test description';
      (sanitizeHtml as any).mockImplementation((text) => `Sanitized: ${text}`);

      renderWithProviders(
        <KBDetailForm
          isEdit={false}
          onCancel={mockOnCancel}
          onSubmitted={mockOnSubmitted}
          parentDirId={TEST_PARENT_DIR_ID}
        />
      );

      // Fill in the form
      const nameInput = screen.getByPlaceholderText('kbNamePlaceholder');
      const descriptionInput = screen.getByPlaceholderText('kbDescriptionPlaceholder');

      fireEvent.change(nameInput, { target: { value: TEST_KB_NAME } });
      fireEvent.change(descriptionInput, { target: { value: unsanitizedDescription } });

      // Select article type
      const radioGroups = screen.getAllByRole('radiogroup');
      const kbTypeRadioGroup = radioGroups[0];
      const articleRadio = within(kbTypeRadioGroup).getByDisplayValue('article');
      fireEvent.click(articleRadio);

      // Submit the form
      const submitButton = screen.getByText('create');
      fireEvent.click(submitButton);

      // Verify sanitizeHtml was called and saveKnowledgeBase received sanitized data
      await waitFor(() => {
        expect(sanitizeHtml).toHaveBeenCalledWith(unsanitizedDescription);
        expect(mockSaveKnowledgeBase).toHaveBeenCalledWith(
          expect.objectContaining({
            description: `Sanitized: ${unsanitizedDescription}`,
          })
        );
      });
    });

    it('shows loading state during form submission', async () => {
      // Make saveKnowledgeBase take some time to resolve
      mockSaveKnowledgeBase.mockImplementation(
        () => new Promise((resolve) => setTimeout(() => resolve(mockKnowledgeBase), 100))
      );

      renderWithProviders(
        <KBDetailForm
          isEdit={false}
          onCancel={mockOnCancel}
          onSubmitted={mockOnSubmitted}
          parentDirId={TEST_PARENT_DIR_ID}
        />
      );

      // Fill in required fields
      const nameInput = screen.getByPlaceholderText('kbNamePlaceholder');
      const descriptionInput = screen.getByPlaceholderText('kbDescriptionPlaceholder');
      fireEvent.change(nameInput, { target: { value: TEST_KB_NAME } });
      fireEvent.change(descriptionInput, { target: { value: TEST_KB_DESCRIPTION } });

      // Select article type
      const radioGroups = screen.getAllByRole('radiogroup');
      const kbTypeRadioGroup = radioGroups[0];
      const articleRadio = within(kbTypeRadioGroup).getByDisplayValue('article');
      fireEvent.click(articleRadio);

      // Select private access level
      await waitFor(() => {
        const updatedRadioGroups = screen.getAllByRole('radiogroup');
        const accessLevelRadioGroup = updatedRadioGroups[1];
        const privateRadio = within(accessLevelRadioGroup).getByDisplayValue('private');
        fireEvent.click(privateRadio);
      });

      // Submit the form
      const submitButton = screen.getByRole('button', { name: 'create' });
      fireEvent.click(submitButton);

      // Wait for saveKnowledgeBase to be called (which means loading should be set)
      await waitFor(() => {
        expect(mockSaveKnowledgeBase).toHaveBeenCalled();
      });

      // Now check that the button is disabled (loading state)
      expect(submitButton).toBeDisabled();

      // Wait for submission to complete and button to be enabled again
      await waitFor(() => {
        expect(submitButton).not.toBeDisabled();
        expect(mockOnSubmitted).toHaveBeenCalledWith(mockKnowledgeBase);
      });
    });
  });

  describe('Edit mode', () => {
    it('renders the form with pre-filled fields in edit mode', () => {
      renderWithProviders(
        <KBDetailForm
          isEdit={true}
          knowledgeBase={mockKnowledgeBase}
          onCancel={mockOnCancel}
          onSubmitted={mockOnSubmitted}
        />
      );

      const nameInput = screen.getByPlaceholderText('kbNamePlaceholder');
      const descriptionInput = screen.getByPlaceholderText('kbDescriptionPlaceholder');

      expect(nameInput).toHaveValue(TEST_KB_NAME);
      expect(descriptionInput).toHaveValue(TEST_KB_DESCRIPTION);
      expect(screen.queryByText('kbType')).not.toBeInTheDocument(); // Type selection should not be visible in edit mode
    });

    it('shows processing badge when KB is in processing state', () => {
      const processingKB: KnowledgeBase = {
        ...mockKnowledgeBase,
        processType: PROCESS_TYPE.processing as ProcessType,
      };

      renderWithProviders(
        <KBDetailForm
          isEdit={true}
          knowledgeBase={processingKB}
          onCancel={mockOnCancel}
          onSubmitted={mockOnSubmitted}
        />
      );

      expect(screen.getByText('accessLevel.processing')).toBeInTheDocument();
    });

    it('disables access level options when KB is in processing state', async () => {
      const processingKB: KnowledgeBase = {
        ...mockKnowledgeBase,
        processType: PROCESS_TYPE.processing as ProcessType,
      };

      renderWithProviders(
        <KBDetailForm
          isEdit={true}
          knowledgeBase={processingKB}
          onCancel={mockOnCancel}
          onSubmitted={mockOnSubmitted}
        />
      );

      // Check if access level options are disabled
      await waitFor(() => {
        const radioGroups = screen.getAllByRole('radiogroup');
        const accessLevelRadioGroup = radioGroups[0]; // In edit mode, there's only the access level radiogroup

        const privateRadio = within(accessLevelRadioGroup).getByDisplayValue('private');
        const publicRadio = within(accessLevelRadioGroup).getByDisplayValue('public');

        expect(privateRadio).toBeDisabled();
        expect(publicRadio).toBeDisabled();
      });
    });

    it('shows notice modal when access level is changed', async () => {
      renderWithProviders(
        <KBDetailForm
          isEdit={true}
          knowledgeBase={mockKnowledgeBase}
          onCancel={mockOnCancel}
          onSubmitted={mockOnSubmitted}
        />
      );

      // Change access level from private to public
      const radioGroups = screen.getAllByRole('radiogroup');
      const accessLevelRadioGroup = radioGroups[0]; // In edit mode, there's only the access level radiogroup
      const publicRadio = within(accessLevelRadioGroup).getByDisplayValue('public');
      fireEvent.click(publicRadio);

      // Submit the form
      const submitButton = screen.getByText('save');
      fireEvent.click(submitButton);

      // Verify notice modal was opened
      await waitFor(() => {
        expect(mockOpenNoticeModal).toHaveBeenCalledWith({
          title: 'accessLevel.changed.title',
          content: 'accessLevel.changed.content',
          showLoader: true,
        });
      });
    });

    it('submits the form with updated data in edit mode', async () => {
      renderWithProviders(
        <KBDetailForm
          isEdit={true}
          knowledgeBase={mockKnowledgeBase}
          onCancel={mockOnCancel}
          onSubmitted={mockOnSubmitted}
        />
      );

      // Update form fields
      const nameInput = screen.getByPlaceholderText('kbNamePlaceholder');
      const descriptionInput = screen.getByPlaceholderText('kbDescriptionPlaceholder');

      const updatedName = 'Updated KB Name';
      const updatedDescription = 'Updated description';

      fireEvent.change(nameInput, { target: { value: updatedName } });
      fireEvent.change(descriptionInput, { target: { value: updatedDescription } });

      // Submit the form
      const submitButton = screen.getByText('save');
      fireEvent.click(submitButton);

      // Verify saveKnowledgeBase was called with updated data
      await waitFor(() => {
        expect(mockSaveKnowledgeBase).toHaveBeenCalledWith(
          expect.objectContaining({
            id: 'kb-123',
            name: updatedName,
            description: updatedDescription,
            baseType: KB_TYPE.article,
            accessLevel: ACCESS_LEVEL.private,
          })
        );
        expect(mockOnSubmitted).toHaveBeenCalledWith(mockKnowledgeBase);
      });
    });

    it('renders access level with icon and description', () => {
      renderWithProviders(
        <KBDetailForm
          isEdit={true}
          knowledgeBase={mockKnowledgeBase}
          onCancel={mockOnCancel}
          onSubmitted={mockOnSubmitted}
        />
      );

      // Verify access level section has all required elements
      expect(screen.getByText('accessLevel.label')).toBeInTheDocument();
      expect(screen.getByText('accessLevel.description')).toBeInTheDocument();

      // Get the access level radio group container
      const radioGroups = screen.getAllByRole('radiogroup');
      const accessLevelRadioGroup = radioGroups[0];

      // Get the parent container of the radio group to find labels and descriptions
      const radioGroupContainer = accessLevelRadioGroup.closest('div');

      // Verify each radio option's label and description are present in the container
      expect(
        within(radioGroupContainer!).getByText('accessLevel.private.label')
      ).toBeInTheDocument();
      expect(
        within(radioGroupContainer!).getByText('accessLevel.private.description')
      ).toBeInTheDocument();
      expect(
        within(radioGroupContainer!).getByText('accessLevel.public.label')
      ).toBeInTheDocument();
      expect(
        within(radioGroupContainer!).getByText('accessLevel.public.description')
      ).toBeInTheDocument();

      // Verify icons are present using their test IDs
      const privateIcon = screen.getByTestId('access-icon-private');
      const publicIcon = screen.getByTestId('access-icon-public');
      expect(privateIcon).toBeInTheDocument();
      expect(publicIcon).toBeInTheDocument();
      // Optionally, check that the icon is rendered somewhere in the same label container as the radio
    });
  });

  describe('Form validation and keyboard events', () => {
    it('submits the form when Enter key is pressed in input fields', async () => {
      renderWithProviders(
        <KBDetailForm
          isEdit={false}
          onCancel={mockOnCancel}
          onSubmitted={mockOnSubmitted}
          parentDirId={TEST_PARENT_DIR_ID}
        />
      );

      // Fill in the form
      const nameInput = screen.getByPlaceholderText('kbNamePlaceholder');
      fireEvent.change(nameInput, { target: { value: TEST_KB_NAME } });

      // Press Enter key
      fireEvent.keyDown(nameInput, { key: 'Enter' });

      // Verify form submission was attempted
      await waitFor(() => {
        expect(mockSaveKnowledgeBase).toHaveBeenCalled();
      });
    });
  });
});
