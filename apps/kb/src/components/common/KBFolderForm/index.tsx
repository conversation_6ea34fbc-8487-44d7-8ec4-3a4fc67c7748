import { zodResolver } from '@hookform/resolvers/zod';
import { Box, Group } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { notifications } from '@mantine/notifications';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useState } from 'react';
import { type SubmitHandler, useForm } from 'react-hook-form';
import { TextInput } from 'react-hook-form-mantine';

import { ROOT_PATH } from '@/constants/folder';
import { useFolderContext } from '@/contexts';
import { folderSchema } from '@/schemas/folder';
import type { Folder } from '@/types/tree';

interface KBFolderFormProps {
  isEdit?: boolean;
  folder?: Folder;
  onClose?: () => void;
  onSubmitted?: () => void;
  parentDirId?: string;
}

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    flexDirection: 'column',
    gap: theme.spacing.md,
  },
  buttonGroup: {
    width: '100%',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingTop: theme.spacing.md,
  },
  error: {
    fontSize: theme.fontSizes.md,
  },
}));

const KBFolderForm: React.FC<KBFolderFormProps> = ({
  folder,
  onClose,
  onSubmitted,
  isEdit,
  parentDirId = ROOT_PATH,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('kb');
  const [loading, setLoading] = useState(false);
  const { createFolder, updateFolder, setShouldRefresh } = useFolderContext();

  const {
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    mode: 'onBlur',
    defaultValues: isEdit ? { ...folder } : { parentDirId },
    resolver: zodResolver(folderSchema(t)),
  });

  const onSubmit: SubmitHandler<Partial<Folder>> = async (data) => {
    setLoading(true);
    try {
      const response = isEdit ? await updateFolder(data) : await createFolder(data);

      if (response) {
        setShouldRefresh(true);
        onSubmitted?.();
      }
    } catch (error: any) {
      notifications.show({
        title: t('folder.createError'),
        message: error.message,
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <form>
      <Box className={classes.root}>
        <TextInput
          classNames={{ error: classes.error }}
          w={'100%'}
          name='name'
          placeholder={t('folder.pathPlaceholder')}
          control={control}
          withAsterisk
          error={errors.name?.message}
        />
        <Group className={classes.buttonGroup}>
          <DecaButton radius={'xl'} variant='neutral' onClick={onClose}>
            {t('cancel')}
          </DecaButton>

          <DecaButton
            radius={'xl'}
            variant='primary'
            disabled={Boolean(errors.path)}
            onClick={handleSubmit(onSubmit)}
            loading={loading}
          >
            {t(isEdit ? 'save' : 'create')}
          </DecaButton>
        </Group>
      </Box>
    </form>
  );
};

export default KBFolderForm;
