import { Stepper, type StepperStepProps, rem } from '@mantine/core';
import { createStyles, getStylesRef } from '@mantine/emotion';
import { IconCheck } from '@tabler/icons-react';

interface CustomStepperColorProps {
  activeColor?: string;
}

interface CustomStepperProps extends CustomStepperColorProps {
  align?: 'right' | 'left' | 'bottom' | 'top';
  orientation?: 'vertical' | 'horizontal';
  steps: StepperStepProps[];
  className?: string;
  activeStep?: number;
  setActiveStep?: (step: number) => void;
}

const useStyles = createStyles((theme, { activeColor }: CustomStepperColorProps) => ({
  root: {
    minHeight: rem(85),
  },
  step: {
    padding: 0,
  },
  stepIcon: {
    width: rem(32),
    height: rem(32),
    minWidth: rem(32),
    fontSize: rem(19),
    fontWeight: 500,
    color: theme.colors.decaGrey[0],
    border: `2px solid ${theme.colors.decaGrey[0]}`,
    '&[data-progress], &[data-completed]': {
      backgroundColor: activeColor ?? theme.colors.decaViolet[4],
      borderColor: activeColor ?? theme.colors.decaViolet[4],
      color: theme.white,
    },
  },
  stepLabel: {
    width: rem(155),
    color: theme.colors.decaNavy[5],
    fontSize: theme.fontSizes.sm,
    fontWeight: 700,
    lineHeight: rem(21.7),
    textAlign: 'center',
    whiteSpace: 'pre-line',
  },
  separator: {
    ref: getStylesRef('separator'),
    marginLeft: rem(-2),
    marginRight: rem(-2),
    height: rem(2),
    backgroundColor: theme.colors.decaGrey[0],
  },
  separatorActive: {
    ref: getStylesRef('separatorActive'),
    backgroundColor: activeColor ?? theme.colors.decaViolet[4],
  },
  bottom: {
    flexDirection: 'column',
    justifyContent: 'center',
    gap: rem(10),
    position: 'relative',

    '& .mantine-Stepper-stepBody': {
      marginLeft: 0,
      maxWidth: rem(155),
      position: 'absolute',
      top: rem(40),
    },
  },
  icon: {
    width: rem(18),
    height: rem(18),
  },
}));

const CustomStepper: React.FC<CustomStepperProps> = ({
  align = 'bottom',
  orientation = 'horizontal',
  steps,
  className,
  activeStep = 0,
  activeColor,
}) => {
  const { classes, cx } = useStyles({ activeColor });

  return (
    <Stepper
      classNames={classes}
      orientation={orientation}
      className={cx(className, align)}
      active={activeStep}
      completedIcon={<IconCheck className={classes.icon} />}
    >
      {steps.map((step, index) => (
        <Stepper.Step
          key={
            typeof step.label === 'string'
              ? step.label
              : typeof step.title === 'string'
                ? step.title
                : index
          }
          {...step}
          className={classes[align]}
        />
      ))}
    </Stepper>
  );
};

export default CustomStepper;
