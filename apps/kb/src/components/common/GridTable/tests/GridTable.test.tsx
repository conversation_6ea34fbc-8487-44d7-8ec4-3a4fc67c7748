import { renderWithProviders } from '@/utils/unitTest';
import { fireEvent, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import GridTable from '../GridTable';

// Constants for test data
const TEST_DATA = {
  COLUMNS: [
    { title: 'ID', key: 'id', size: 2 },
    { title: 'Name', key: 'name', size: 4 },
    { title: 'Email', key: 'email', size: 6 },
  ],
  ROWS: [
    {
      data: {
        id: '1',
        name: '<PERSON>',
        email: '<EMAIL>',
      },
    },
    {
      data: {
        id: '2',
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      props: {
        activated: true,
      },
    },
  ],
  ALL_IDS: ['1', '2'],
};

const MOCK_HANDLERS = {
  onSelect: vi.fn(),
  onDeselect: vi.fn(),
  onSelectAll: vi.fn(),
  onDeselectAll: vi.fn(),
};

describe('GridTable', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('handles row activation styling', () => {
    const { container } = renderWithProviders(
      <GridTable
        columns={TEST_DATA.COLUMNS}
        rows={[
          {
            data: { id: '1', name: 'Test', email: '<EMAIL>' },
            props: { activated: true },
          },
        ]}
      />
    );

    const activatedRow = container.querySelector('[data-activated="true"]');
    expect(activatedRow).not.toBeNull();
  });

  it('applies activated styling to the correct row', () => {
    renderWithProviders(<GridTable columns={TEST_DATA.COLUMNS} rows={TEST_DATA.ROWS} />);

    // Find the row containing "Jane Smith" (which should be activated)
    const janeRow = screen.getByText('Jane Smith').closest('[data-activated="true"]');
    expect(janeRow).not.toBeNull();

    // Find the row containing "John Doe" (which should not be activated)
    const johnRow = screen.getByText('John Doe').closest('[data-activated="true"]');
    expect(johnRow).toBeNull();
  });

  it('renders table headers correctly', () => {
    renderWithProviders(<GridTable columns={TEST_DATA.COLUMNS} rows={TEST_DATA.ROWS} />);

    TEST_DATA.COLUMNS.forEach((column) => {
      expect(screen.getByText(column.title!)).toBeInTheDocument();
    });
  });

  it('renders table rows correctly', () => {
    renderWithProviders(<GridTable columns={TEST_DATA.COLUMNS} rows={TEST_DATA.ROWS} />);

    TEST_DATA.ROWS.forEach((row) => {
      expect(screen.getByText(row.data.name)).toBeInTheDocument();
      expect(screen.getByText(row.data.email)).toBeInTheDocument();
    });
  });

  it('displays empty message when no rows', () => {
    renderWithProviders(<GridTable columns={TEST_DATA.COLUMNS} rows={[]} />);

    expect(screen.getByText('emptyMessage')).toBeInTheDocument();
  });

  describe('Selection functionality', () => {
    it('renders checkboxes when selection handlers are provided', () => {
      renderWithProviders(
        <GridTable
          columns={TEST_DATA.COLUMNS}
          rows={TEST_DATA.ROWS}
          allAvailableIds={TEST_DATA.ALL_IDS}
          selectedIds={[]}
          selectionHandlers={MOCK_HANDLERS}
        />
      );

      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes).toHaveLength(TEST_DATA.ROWS.length + 1);
    });

    it('handles individual row selection', () => {
      renderWithProviders(
        <GridTable
          columns={TEST_DATA.COLUMNS}
          rows={TEST_DATA.ROWS}
          allAvailableIds={TEST_DATA.ALL_IDS}
          selectedIds={[]}
          selectionHandlers={MOCK_HANDLERS}
        />
      );

      const checkboxes = screen.getAllByRole('checkbox');
      fireEvent.click(checkboxes[1]); // First row checkbox

      expect(MOCK_HANDLERS.onSelect).toHaveBeenCalledWith('1');
    });

    it('handles "select all" functionality', () => {
      renderWithProviders(
        <GridTable
          columns={TEST_DATA.COLUMNS}
          rows={TEST_DATA.ROWS}
          allAvailableIds={TEST_DATA.ALL_IDS}
          selectedIds={[]}
          selectionHandlers={MOCK_HANDLERS}
        />
      );

      const headerCheckbox = screen.getAllByRole('checkbox')[0];
      fireEvent.click(headerCheckbox);

      expect(MOCK_HANDLERS.onSelectAll).toHaveBeenCalled();
    });

    it('shows correct selection state', () => {
      renderWithProviders(
        <GridTable
          columns={TEST_DATA.COLUMNS}
          rows={TEST_DATA.ROWS}
          allAvailableIds={TEST_DATA.ALL_IDS}
          selectedIds={['1']}
          selectionHandlers={MOCK_HANDLERS}
        />
      );

      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes[1]).toBeChecked(); // First row should be checked
      expect(checkboxes[2]).not.toBeChecked(); // Second row should not be checked
    });
  });
});
