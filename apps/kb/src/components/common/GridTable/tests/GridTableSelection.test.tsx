import { mockLibraries, renderWithProviders } from '@/utils/unitTest';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { GridTableSelection } from '../GridTableSelection';

// Mock the useTranslate hook to return the expected translation
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, options?: any) => {
      // Handle the table.selectionInfo key specifically
      if (key === 'table.selectionInfo') {
        if (options && typeof options === 'object') {
          const { selected, total } = options;
          return `${selected} items selected / ${total} items`;
        }
        return 'table.selectionInfo';
      }

      // Handle namespaced keys by stripping the namespace prefix
      let translationKey = key;
      if (key.includes(':')) {
        translationKey = key.split(':')[1];
      }

      if (options && typeof options === 'object') {
        let result = translationKey;
        Object.entries(options).forEach(([optionKey, value]) => {
          result = result.replace(`{{${optionKey}}}`, String(value));
        });
        return result;
      }
      return translationKey;
    },
    i18n: { language: 'en' },
  }),
}));

// Test constants
const TEST_CASES = {
  EMPTY: {
    selected: 0,
    total: 0,
  },
  PARTIAL_SELECTION: {
    selected: 3,
    total: 10,
  },
  ALL_SELECTED: {
    selected: 5,
    total: 5,
  },
} as const;

describe('GridTableSelection', () => {
  mockLibraries();

  it('renders with zero selection', () => {
    renderWithProviders(
      <GridTableSelection
        selectedCount={TEST_CASES.EMPTY.selected}
        totalCount={TEST_CASES.EMPTY.total}
      />
    );

    expect(screen.getByText('0 items selected / 0 items')).toBeInTheDocument();
  });

  it('renders with partial selection', () => {
    renderWithProviders(
      <GridTableSelection
        selectedCount={TEST_CASES.PARTIAL_SELECTION.selected}
        totalCount={TEST_CASES.PARTIAL_SELECTION.total}
      />
    );

    expect(screen.getByText('3 items selected / 10 items')).toBeInTheDocument();
  });

  it('renders with all items selected', () => {
    renderWithProviders(
      <GridTableSelection
        selectedCount={TEST_CASES.ALL_SELECTED.selected}
        totalCount={TEST_CASES.ALL_SELECTED.total}
      />
    );

    expect(screen.getByText('5 items selected / 5 items')).toBeInTheDocument();
  });

  it('renders with default props when no values provided', () => {
    renderWithProviders(<GridTableSelection selectedCount={0} totalCount={0} />);
    expect(screen.getByText('0 items selected / 0 items')).toBeInTheDocument();
  });
});
