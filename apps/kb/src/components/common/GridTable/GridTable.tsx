import { Box, Checkbox, Grid, type GridProps, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useId } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { GridTableSelection } from './GridTableSelection';

const useStyles = createStyles((theme) => ({
  wrapper: {
    width: rem('100%'),
    fontSize: rem(14),
    lineHeight: rem(22),
  },
  row: {
    borderRadius: rem(10),
    border: `${rem(1)} solid ${theme.colors.decaLight[2]}`,
    background: theme.colors.decaLight[0],
    padding: `${rem(10)} ${rem(16)}`,
    width: rem('100%'),
    margin: `0 0 ${theme.spacing.sm}`,
  },
  activatedRow: {
    background: theme.colors.decaViolet[0],
  },
  bodyRow: {
    '&:hover': {
      background: theme.colors.decaViolet[0],
    },
  },
  column: {
    color: theme.colors.decaNavy[5],
    padding: theme.spacing.xs,
    fontWeight: 500,
    display: 'flex',
    alignItems: 'center',
    svg: {
      color: theme.colors.decaNavy[5],
    },
  },
  checkboxColumn: {
    maxWidth: rem(40),
  },
  checkbox: {
    '& .mantine-Checkbox-input': {
      cursor: 'pointer',
    },
  },
  headerColumn: {
    fontWeight: 700,
  },
  tableContainer: {
    position: 'relative',
    width: '100%',
  },
}));

export type GridTableColumn = {
  title?: string;
  key: string;
  className?: string;
  // 'number' means grid cols: 1 - 12, 'auto' means the column width will be determined by the content, 'content' means the column width will be determined by the content
  size?: number | 'auto' | 'content';
};

export type GridTableRow = {
  data: Record<string, any>;
  props?: Omit<GridProps, 'children'> & { activated?: boolean };
};

interface GridTableProps {
  classNames?: {
    wrapper?: string;
    header?: string;
    body?: string;
    row?: string;
    column?: string;
  };
  rows: GridTableRow[];
  columns: GridTableColumn[];
  allAvailableIds?: string[];
  selectedIds?: string[];
  selectionHandlers?: {
    onSelect?: (rowId: string) => void;
    onDeselect?: (rowId: string) => void;
    onSelectAll?: () => void;
    onDeselectAll?: () => void;
  };
}

const CheckBoxColumn: React.FC<{
  rowId: string;
  selectedIds: string[];
  selectionHandlers: GridTableProps['selectionHandlers'];
}> = ({ rowId, selectedIds, selectionHandlers }) => {
  const { classes } = useStyles();
  const [checked, setChecked] = useState(false);

  const handleSelect = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      if (event.target.checked) {
        selectionHandlers?.onSelect?.(rowId);
      } else {
        selectionHandlers?.onDeselect?.(rowId);
      }
    },
    [rowId, selectionHandlers]
  );

  const preventRowClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    event.stopPropagation();
  }, []);

  useEffect(() => {
    setChecked(selectedIds.includes(rowId));
  }, [selectedIds, rowId]);

  return (
    <Grid.Col className={classes.checkboxColumn}>
      <Checkbox
        className={classes.checkbox}
        checked={checked}
        onClick={preventRowClick}
        onChange={handleSelect}
      />
    </Grid.Col>
  );
};

const CheckAllColumn: React.FC<{
  allIds: string[];
  selectedIds: string[];
  selectionHandlers: GridTableProps['selectionHandlers'];
}> = ({ allIds, selectedIds, selectionHandlers }) => {
  const { classes } = useStyles();
  const [checked, setChecked] = useState(false);
  const [indeterminate, setIndeterminate] = useState(false);

  const handleSelect = useCallback(() => {
    if (checked || indeterminate) {
      selectionHandlers?.onDeselectAll?.();
    } else {
      selectionHandlers?.onSelectAll?.();
    }
  }, [selectionHandlers, checked, indeterminate]);

  useEffect(() => {
    if (!allIds.length) return;

    const allSelected = selectedIds.length === allIds.length;
    const someSelected = selectedIds.length > 0 && selectedIds.length < allIds.length;

    setChecked(allSelected);
    setIndeterminate(someSelected);
  }, [selectedIds, allIds]);

  return (
    <Grid.Col className={classes.checkboxColumn}>
      <Checkbox
        className={classes.checkbox}
        checked={checked}
        indeterminate={indeterminate}
        onChange={handleSelect}
      />
    </Grid.Col>
  );
};

const GridTable: React.FC<GridTableProps> = ({
  classNames,
  columns,
  rows,
  allAvailableIds = [],
  selectedIds = [],
  selectionHandlers,
}) => {
  const { t } = useTranslate('common');
  const { cx, classes } = useStyles();
  const checkboxId = useId();

  /**
   * Table header rendering
   * @returns {JSX.Element[]}
   * @dependencies columns
   */
  const tableHeader = useMemo(() => {
    const headerColumns = columns.map((column: GridTableColumn, columnIndex: number) => (
      <Grid.Col
        key={`header-${column.key}${columnIndex}`}
        className={cx(classes.column, classes.headerColumn, classNames?.column)}
        span={column.size ?? 'auto'}
      >
        {column.title ?? ''}
      </Grid.Col>
    ));

    if (selectionHandlers) {
      headerColumns.unshift(
        <CheckAllColumn
          key={`checkbox-all-${checkboxId}`}
          allIds={allAvailableIds}
          selectedIds={selectedIds}
          selectionHandlers={selectionHandlers}
        />
      );
    }

    return headerColumns;
  }, [columns, allAvailableIds, selectionHandlers, classNames, checkboxId, selectedIds]);

  /**
   * Table body rendering
   * @returns {JSX.Element[]}
   * @dependencies rows, columns
   */
  const tableBody = useMemo(() => {
    const bodyRows = rows.map((row: GridTableRow, rowIndex: number) => {
      const { activated, ...others } = row.props ?? {};

      return (
        <Grid
          key={`row-${rowIndex}-${row.data.id}`}
          className={cx(
            classes.row,
            classes.bodyRow,
            { [classes.activatedRow]: activated },
            classNames?.row
          )}
          data-activated={activated || undefined}
          {...others}
        >
          {selectionHandlers && (
            <CheckBoxColumn
              rowId={row.data.id}
              selectedIds={selectedIds}
              selectionHandlers={selectionHandlers}
            />
          )}
          {columns.map((column, columnIndex) => (
            <Grid.Col
              key={`body-${column.key}${columnIndex}`}
              className={cx(classes.column, classNames?.column)}
              span={column.size ?? 'auto'}
            >
              {row.data[column.key]}
            </Grid.Col>
          ))}
        </Grid>
      );
    });

    return bodyRows;
  }, [rows, columns, selectionHandlers, classNames, selectedIds]);

  return (
    <Box className={cx(classes.wrapper, classNames?.wrapper)}>
      <Box className={classes.tableContainer}>
        {selectionHandlers && (
          <GridTableSelection
            selectedCount={selectedIds.length}
            totalCount={allAvailableIds.length}
          />
        )}
        <Box className={classNames?.header}>
          <Grid className={cx(classes.row, classNames?.row)}>{tableHeader}</Grid>
        </Box>
        <Box className={classNames?.body}>
          {rows.length > 0 ? (
            tableBody
          ) : (
            <Text c='dimmed' ta='center'>
              {t('emptyMessage')}
            </Text>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default memo(GridTable);
