import { Paper, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';

const useStyles = createStyles((theme) => ({
  selectionInfo: {
    marginLeft: 'auto',
    marginBottom: theme.spacing.md,
    padding: `${theme.spacing.xs} ${theme.spacing.md}`,
    background: theme.colors.decaLight[0],
    border: `${rem(1)} solid ${theme.colors.decaLight[2]}`,
    borderRadius: theme.radius.md,
    fontSize: theme.fontSizes.sm,
    color: theme.colors.decaNavy[5],
    fontWeight: 500,
  },
}));

interface SelectionInfoProps {
  selectedCount: number;
  totalCount: number;
}

export const GridTableSelection: React.FC<SelectionInfoProps> = ({
  selectedCount = 0,
  totalCount = 0,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('common');

  return (
    <Paper className={classes.selectionInfo}>
      {t('table.selectionInfo', {
        selected: selectedCount,
        total: totalCount,
      })}
    </Paper>
  );
};

export default GridTableSelection;
