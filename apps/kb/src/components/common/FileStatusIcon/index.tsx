import { FILE_UPLOAD_STATUS, type IconCustomProps } from '@/types';
import { Group, Text, rem } from '@mantine/core';
import { createStyles, keyframes } from '@mantine/emotion';
import { IconAlertTriangle, IconCheck, IconLoader } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';

interface FileStatusIconProps extends IconCustomProps {
  status?: FILE_UPLOAD_STATUS;
  showLabel?: boolean;
}

const spin = keyframes({
  '0%': { transform: 'rotate(0deg)' },
  '100%': { transform: 'rotate(359deg)' },
});

const STATUS_CONFIG = {
  [FILE_UPLOAD_STATUS.unknown]: {
    icon: IconLoader,
    color: 'decaYellow',
    hasAnimation: true,
  },
  [FILE_UPLOAD_STATUS.uploaded]: {
    icon: IconCheck,
    color: 'decaGreen',
    hasAnimation: false,
  },
  [FILE_UPLOAD_STATUS.failed]: {
    icon: IconAlertTriangle,
    color: 'decaRed',
    hasAnimation: false,
  },
};

const useStyles = createStyles((theme) => {
  const createColorVariants = (
    status: string,
    config: (typeof STATUS_CONFIG)[keyof typeof STATUS_CONFIG]
  ) => ({
    [`${status}Icon`]: {
      color: theme.colors[config.color][4],
    },
    [`${status}Label`]: {
      color: theme.colors[config.color][9],
    },
    [`${status}Container`]: {
      backgroundColor: theme.colors[config.color][1],
    },
  });

  return {
    group: {
      gap: rem(10),
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
      flexWrap: 'nowrap',
      width: 'fit-content',
      height: 'fit-content',
      padding: `${rem(4)} ${rem(15)}`,
      borderRadius: rem(20),
    },
    statusText: {
      textTransform: 'capitalize',
    },
    // Add separate animation class
    animatedIcon: {
      animation: `${spin} 5s linear infinite`,
    },
    // Generate color variants for each status
    ...Object.entries(STATUS_CONFIG).reduce((acc, [status, config]) => {
      const variants = createColorVariants(status, config);
      Object.assign(acc, variants);
      return acc;
    }, {}),
  };
});

const FileStatusIcon: React.FC<FileStatusIconProps> = ({ status, showLabel = false, ...rest }) => {
  const { classes } = useStyles();
  const { t } = useTranslate('details');
  const currentStatus = status || FILE_UPLOAD_STATUS.unknown;
  const config = STATUS_CONFIG[currentStatus] || STATUS_CONFIG[FILE_UPLOAD_STATUS.unknown];
  const Icon = config.icon;

  if (!Icon) return null;

  const colorMode = showLabel ? 'Label' : 'Icon';
  const icon = (
    <Icon
      {...rest}
      className={`
        ${classes[`${currentStatus}${colorMode}`]}
        ${config.hasAnimation ? classes.animatedIcon : ''}
      `}
    />
  );

  if (!showLabel) {
    return icon;
  }

  return (
    <Group className={`${classes.group} ${classes[`${currentStatus}Container`]}`}>
      {icon}
      <Text fz='md' className={`${classes.statusText} ${classes[`${currentStatus}Label`]}`}>
        {t(`upload.${currentStatus}`)}
      </Text>
    </Group>
  );
};

export default FileStatusIcon;
