import { Box, Input, Loader, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconSearch, IconX } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useSearchParams } from 'react-router-dom';

interface SearchBoxProps {
  className?: string;
  placeholder?: string;
  loading?: boolean;
  onSearch?: (query: string) => void;
}

const useStyles = createStyles((theme) => ({
  input: {
    border: `${rem(1)} ${theme.colors.decaLight[5]}`,
    borderRadius: rem(8),
    width: '100%',
    color: theme.colors.decaLight[5],
  },
  icon: {
    width: rem(16),
    strokeWidth: rem(2),
    stroke: theme.colors.decaLight[5],
  },
  iconX: {
    width: rem(16),
    strokeWidth: rem(2),
    stroke: theme.colors.decaLight[5],
    cursor: 'pointer',
    '&:hover': {
      stroke: theme.colors.decaLight[4],
    },
  },
}));

const SearchBox: React.FC<SearchBoxProps> = ({
  className,
  placeholder,
  loading = false,
  onSearch,
}) => {
  const { t } = useTranslate('common');
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [query, setQuery] = useState<string>('');
  const { classes } = useStyles();

  /**
   * Handle search
   * @returns void
   * @dependencies query, onSearch
   */
  const handleSearch = useCallback(() => {
    onSearch?.(query);
  }, [onSearch, query]);

  /**
   * Clear search
   * @returns void
   * @dependencies onSearch
   */
  const clearSearch = useCallback(() => {
    setQuery('');
    onSearch?.('');
  }, [onSearch, setQuery]);

  /**
   * Handle on blur and reset query if it's empty
   * @returns void
   * @dependencies query, onSearch
   */
  const handleOnBlur = useCallback(() => {
    if (!query) {
      onSearch?.('');
    }
  }, [query]);

  /**
   * Handle key down and bind Enter key to search
   * @param event React.KeyboardEvent<HTMLInputElement>
   * @returns void
   */
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSearch();
      }
      if (event.key === 'Escape') {
        clearSearch();
      }
    },
    [handleSearch, clearSearch]
  );

  /**
   * Right section of input
   * @returns React.ReactNode
   */
  const rightSection = useMemo(
    () =>
      loading ? (
        <Loader size={20} />
      ) : (
        query && <IconX className={classes.iconX} onClick={clearSearch} />
      ),
    [loading, query]
  );

  useEffect(() => {
    if (query && searchParams.get('query') !== query) {
      clearSearch();
    }
  }, [location, searchParams]);

  return (
    <Box className={className}>
      <Input
        className={classes.input}
        leftSection={<IconSearch className={classes.icon} />}
        rightSection={rightSection}
        rightSectionPointerEvents='auto'
        value={query}
        onChange={(event) => setQuery(event.currentTarget.value)}
        onKeyDown={handleKeyDown}
        onBlur={handleOnBlur}
        placeholder={placeholder ?? t('searchPlaceholder')}
      />
    </Box>
  );
};

export default SearchBox;
