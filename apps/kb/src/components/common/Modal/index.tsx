import { Modal as MantineModal, type ModalOverlayProps, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconX } from '@tabler/icons-react';
import type React from 'react';

type ModalProps = {
  title?: string;
  opened: boolean;
  children: React.ReactNode;
  className?: string;
  size?: string;
  onClose: () => void;
  zIndex?: number;
  overlayProps?: ModalOverlayProps;
};

const useStyles = createStyles((theme, { size }: ModalProps) => ({
  root: {
    [`@media (max-height: ${rem(800)})`]: {
      '& .mantine-Modal-inner': {
        paddingTop: rem(100),
      },
    },
  },
  modalContent: {
    minWidth: size ? 'unset' : rem(656),
    padding: rem(16),
    borderRadius: rem(12),
    boxShadow: `${rem(0)} ${rem(6)} ${rem(26)} ${rem(0)} ${theme.colors.decaDark[1]}`,
  },
  modalBody: {
    paddingTop: `${rem(16)} !important`,
  },
  closeButton: {
    width: rem(28),
    height: rem(28),
    svg: {
      stroke: theme.colors.decaGrey[4],
      strokeWidth: rem(2),
      width: rem(20),
      height: rem(20),
      '&:not(.tabler-icon)': {
        display: 'none',
      },
    },
  },
  title: {
    fontWeight: 700,
    fontSize: rem(20),
    lineHeight: rem(31),
    color: theme.colors.decaNavy[5],
  },
}));

/**
 * Custom close button for modal
 * @param onClose - Callback function to close the modal
 * @returns React.FC
 */
export const ModalCloseButton: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const { classes } = useStyles({ size: 'md' } as ModalProps);
  const { CloseButton } = MantineModal;

  return (
    <CloseButton className={classes.closeButton} onClick={onClose}>
      <IconX />
    </CloseButton>
  );
};

/**
 * Custom title with close button for modal use for Modals Manager
 * @param title - Title of the modal
 * @param onClose - Callback function to close the modal
 * @returns React.FC
 */
export const ModalTitleWithCloseButton: React.FC<{ title: string; onClose: () => void }> = ({
  title,
  onClose,
}) => {
  return (
    <>
      {title}
      <ModalCloseButton onClose={onClose} />
    </>
  );
};

const Modal: React.FC<ModalProps> = ({
  title,
  opened,
  children,
  className,
  onClose,
  size,
  zIndex,
  overlayProps,
}) => {
  const { classes, cx } = useStyles({ size } as ModalProps);
  const { Root, Overlay, Content, Header, Title, Body } = MantineModal;

  return (
    <Root
      className={cx(classes.root, className)}
      opened={opened}
      onClose={onClose}
      centered
      size={size}
      zIndex={zIndex}
    >
      <Overlay {...overlayProps} />
      <Content className={classes.modalContent}>
        <Header>
          {title && <Title className={classes.title}>{title}</Title>}
          <ModalCloseButton onClose={onClose} />
        </Header>
        <Body className={classes.modalBody}>{children}</Body>
      </Content>
    </Root>
  );
};

export default Modal;
