import useKbAccessControl from '@/hooks/useKbAccessControl';
import { ActionIcon, Menu, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import {
  IconDots,
  IconDownload,
  IconEdit,
  IconFileExport,
  IconTrash,
  IconUpload,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { memo } from 'react';

interface KBMenuProps {
  onEdit?: () => void;
  onDelete?: () => void;
  onMove?: () => void;
  onDownload?: () => void;
  onExport?: () => void;
  exportTitle?: string;
}

const useStyles = createStyles((theme) => ({
  menuTarget: {
    cursor: 'pointer',
    width: rem(24),
    height: rem(24),
    '&:hover': {
      backgroundColor: theme.colors.decaViolet[1],
    },
  },
  menuIcon: {
    color: theme.colors.decaNavy[5],
    width: rem(20),
    height: rem(20),
  },
  dropdown: {
    backgroundColor: theme.white,
    border: `1px solid ${theme.colors.decaLight[4]}`,
    borderRadius: rem(10),
    boxShadow: theme.shadows.sm,
  },
  item: {
    padding: rem(10),
    width: rem(220),
    color: theme.colors.decaNavy[5],
    fontSize: rem(14),
    fontWeight: 500,
    borderRadius: 0,
    '&:hover': {
      backgroundColor: theme.colors.decaLight[0],
    },
  },
  icon: {
    width: rem(20),
    height: rem(20),
  },
}));

const KBMenu: React.FC<KBMenuProps> = ({
  onEdit,
  onMove,
  onDelete,
  onDownload,
  onExport,
  exportTitle,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('home');
  const { permKb, permFolder, permDocument } = useKbAccessControl();

  if (!permKb.canUpdate && !permFolder.canUpdate && !permDocument.canUpdate) return null;

  return (
    <Menu position='right-start' classNames={classes}>
      <Menu.Target>
        <ActionIcon className={classes.menuTarget}>
          <IconDots className={classes.menuIcon} data-testid='icon-dots' />
        </ActionIcon>
      </Menu.Target>
      <Menu.Dropdown>
        {onDownload && (
          <Menu.Item
            leftSection={<IconDownload className={classes.menuIcon} data-testid='icon-download' />}
            onClick={onDownload}
          >
            {t('menu.download')}
          </Menu.Item>
        )}
        {onEdit && (
          <Menu.Item
            leftSection={<IconEdit className={classes.menuIcon} data-testid='icon-edit' />}
            onClick={onEdit}
          >
            {t('menu.edit')}
          </Menu.Item>
        )}
        {onMove && (
          <Menu.Item
            leftSection={
              <IconFileExport className={classes.menuIcon} data-testid='icon-file-export' />
            }
            onClick={onMove}
          >
            {t('menu.move')}
          </Menu.Item>
        )}
        {onExport && (
          <Menu.Item
            leftSection={<IconUpload className={classes.menuIcon} data-testid='icon-upload' />}
            onClick={onExport}
          >
            {exportTitle || t('menu.export')}
          </Menu.Item>
        )}
        {onDelete && (
          <Menu.Item
            leftSection={<IconTrash className={classes.menuIcon} data-testid='icon-trash' />}
            onClick={onDelete}
          >
            {t('menu.delete')}
          </Menu.Item>
        )}
      </Menu.Dropdown>
    </Menu>
  );
};

export default memo(KBMenu);
