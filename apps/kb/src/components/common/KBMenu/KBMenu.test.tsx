import { renderWithMantine } from '@/utils/unitTest';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import KBMenu from './index';

// Mock constants
const DEFAULT_PERMISSIONS = {
  permKb: {
    canView: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
  },
  permFolder: {
    canView: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
  },
  permDocument: {
    canView: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canEditAccessLevel: true,
  },
};

const NO_UPDATE_PERMISSIONS = {
  permKb: {
    canView: true,
    canCreate: true,
    canUpdate: false,
    canDelete: false,
  },
  permFolder: {
    canView: true,
    canCreate: true,
    canUpdate: false,
    canDelete: false,
  },
  permDocument: {
    canView: true,
    canCreate: true,
    canUpdate: false,
    canDelete: false,
    canEditAccessLevel: false,
  },
};

// Mock external dependencies
const mockUseKbAccessControl = vi.fn();

vi.mock('@/hooks/useKbAccessControl', () => ({
  default: () => mockUseKbAccessControl(),
}));

// Mock IconEdit specifically to render with data-testid
vi.mock('@tabler/icons-react', async () => {
  const actual = await vi.importActual('@tabler/icons-react');
  return {
    ...actual,
    IconEdit: (props) => (
      <svg data-testid='icon-edit' {...props}>
        <title>Edit Icon</title>
        <path d='mock-edit-icon' />
      </svg>
    ),
  };
});

describe('KBMenu', () => {
  // Mock functions for testing callbacks
  const mockOnEdit = vi.fn();
  const mockOnDelete = vi.fn();
  const mockOnMove = vi.fn();
  const mockOnDownload = vi.fn();
  const mockOnExport = vi.fn();

  beforeEach(() => {
    vi.resetAllMocks();
    mockUseKbAccessControl.mockReturnValue(DEFAULT_PERMISSIONS);
  });

  describe('Component Rendering', () => {
    it('should render menu trigger button', () => {
      renderWithMantine(<KBMenu />);

      // Look for the ActionIcon button
      const menuButton = screen.getByRole('button');
      expect(menuButton).toBeInTheDocument();
    });

    it('should render all menu items when all callbacks are provided', async () => {
      renderWithMantine(
        <KBMenu
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onMove={mockOnMove}
          onDownload={mockOnDownload}
          onExport={mockOnExport}
        />
      );

      // Click the menu trigger to open the dropdown
      const menuButton = screen.getByRole('button');
      fireEvent.click(menuButton);

      await waitFor(() => {
        expect(screen.getByText('menu.download')).toBeInTheDocument();
        expect(screen.getByText('menu.edit')).toBeInTheDocument();
        expect(screen.getByText('menu.move')).toBeInTheDocument();
        expect(screen.getByText('menu.export')).toBeInTheDocument();
        expect(screen.getByText('menu.delete')).toBeInTheDocument();
      });
    });

    it('should render custom export title when provided', async () => {
      const customExportTitle = 'Custom Export';
      renderWithMantine(<KBMenu onExport={mockOnExport} exportTitle={customExportTitle} />);

      const menuButton = screen.getByRole('button');
      fireEvent.click(menuButton);

      await waitFor(() => {
        expect(screen.getByText(customExportTitle)).toBeInTheDocument();
        expect(screen.queryByText('menu.export')).not.toBeInTheDocument();
      });
    });

    it('should only render menu items for provided callbacks', async () => {
      renderWithMantine(<KBMenu onEdit={mockOnEdit} onDownload={mockOnDownload} />);

      fireEvent.click(screen.getByTestId('icon-dots').closest('button')!);

      await waitFor(() => {
        expect(screen.getByText('menu.download')).toBeInTheDocument();
        expect(screen.getByText('menu.edit')).toBeInTheDocument();
        expect(screen.queryByText('menu.move')).not.toBeInTheDocument();
        expect(screen.queryByText('menu.export')).not.toBeInTheDocument();
        expect(screen.queryByText('menu.delete')).not.toBeInTheDocument();
      });
    });
  });

  describe('Permission-based Rendering', () => {
    it('should not render component when user has no update permissions', () => {
      mockUseKbAccessControl.mockReturnValue(NO_UPDATE_PERMISSIONS);

      renderWithMantine(<KBMenu onEdit={mockOnEdit} />);

      // Check if the actual component content is not rendered (ignoring Mantine styles)
      expect(screen.queryByTestId('icon-dots')).not.toBeInTheDocument();
    });

    it('should render component when user has KB update permissions', () => {
      mockUseKbAccessControl.mockReturnValue({
        ...NO_UPDATE_PERMISSIONS,
        permKb: { ...NO_UPDATE_PERMISSIONS.permKb, canUpdate: true },
      });

      renderWithMantine(<KBMenu onEdit={mockOnEdit} />);

      expect(screen.getByTestId('icon-dots')).toBeInTheDocument();
    });

    it('should render component when user has folder update permissions', () => {
      mockUseKbAccessControl.mockReturnValue({
        ...NO_UPDATE_PERMISSIONS,
        permFolder: { ...NO_UPDATE_PERMISSIONS.permFolder, canUpdate: true },
      });

      renderWithMantine(<KBMenu onEdit={mockOnEdit} />);

      expect(screen.getByTestId('icon-dots')).toBeInTheDocument();
    });

    it('should render component when user has document update permissions', () => {
      mockUseKbAccessControl.mockReturnValue({
        ...NO_UPDATE_PERMISSIONS,
        permDocument: { ...NO_UPDATE_PERMISSIONS.permDocument, canUpdate: true },
      });

      renderWithMantine(<KBMenu onEdit={mockOnEdit} />);

      expect(screen.getByTestId('icon-dots')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('should call onDownload when download menu item is clicked', async () => {
      renderWithMantine(<KBMenu onDownload={mockOnDownload} />);

      fireEvent.click(screen.getByTestId('icon-dots').closest('button')!);

      await waitFor(() => {
        expect(screen.getByText('menu.download')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('menu.download'));
      expect(mockOnDownload).toHaveBeenCalledTimes(1);
    });

    it('should call onEdit when edit menu item is clicked', async () => {
      renderWithMantine(<KBMenu onEdit={mockOnEdit} />);

      fireEvent.click(screen.getByTestId('icon-dots').closest('button')!);

      await waitFor(() => {
        expect(screen.getByText('menu.edit')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('menu.edit'));
      expect(mockOnEdit).toHaveBeenCalledTimes(1);
    });

    it('should call onMove when move menu item is clicked', async () => {
      renderWithMantine(<KBMenu onMove={mockOnMove} />);

      fireEvent.click(screen.getByTestId('icon-dots').closest('button')!);

      await waitFor(() => {
        expect(screen.getByText('menu.move')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('menu.move'));
      expect(mockOnMove).toHaveBeenCalledTimes(1);
    });

    it('should call onExport when export menu item is clicked', async () => {
      renderWithMantine(<KBMenu onExport={mockOnExport} />);

      fireEvent.click(screen.getByTestId('icon-dots').closest('button')!);

      await waitFor(() => {
        expect(screen.getByText('menu.export')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('menu.export'));
      expect(mockOnExport).toHaveBeenCalledTimes(1);
    });

    it('should call onDelete when delete menu item is clicked', async () => {
      renderWithMantine(<KBMenu onDelete={mockOnDelete} />);

      fireEvent.click(screen.getByTestId('icon-dots').closest('button')!);

      await waitFor(() => {
        expect(screen.getByText('menu.delete')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('menu.delete'));
      expect(mockOnDelete).toHaveBeenCalledTimes(1);
    });

    it('should call multiple callbacks when multiple menu items are clicked', async () => {
      renderWithMantine(<KBMenu onEdit={mockOnEdit} onDelete={mockOnDelete} />);

      // Click the menu trigger to open the dropdown
      const menuTrigger = screen.getByTestId('icon-dots').closest('button')!;

      // Click edit
      fireEvent.click(menuTrigger);

      await waitFor(() => {
        expect(screen.getByText('menu.edit')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('menu.edit'));

      // Click delete (need to reopen menu after first click closes it)
      fireEvent.click(menuTrigger);

      await waitFor(() => {
        expect(screen.getByText('menu.delete')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText('menu.delete'));

      expect(mockOnEdit).toHaveBeenCalledTimes(1);
      expect(mockOnDelete).toHaveBeenCalledTimes(1);
    });
  });

  describe('Icon Rendering', () => {
    it('should render correct icons for each menu item', async () => {
      renderWithMantine(
        <KBMenu
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onMove={mockOnMove}
          onDownload={mockOnDownload}
          onExport={mockOnExport}
        />
      );

      fireEvent.click(screen.getByTestId('icon-dots').closest('button')!);

      await waitFor(() => {
        expect(screen.getByTestId('icon-download')).toBeInTheDocument();
        // IconEdit is mocked globally as a span, so check for the span text instead
        expect(screen.getByText('EditIcon')).toBeInTheDocument();
        expect(screen.getByTestId('icon-file-export')).toBeInTheDocument();
        expect(screen.getByTestId('icon-upload')).toBeInTheDocument();
        expect(screen.getByTestId('icon-trash')).toBeInTheDocument();
      });
    });
  });

  describe('Translation Integration', () => {
    it('should display menu item labels using translation keys', async () => {
      renderWithMantine(
        <KBMenu
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onMove={mockOnMove}
          onDownload={mockOnDownload}
          onExport={mockOnExport}
        />
      );

      fireEvent.click(screen.getByTestId('icon-dots').closest('button')!);

      await waitFor(() => {
        // Verify that the component renders the expected translation keys
        expect(screen.getByText('menu.download')).toBeInTheDocument();
        expect(screen.getByText('menu.edit')).toBeInTheDocument();
        expect(screen.getByText('menu.move')).toBeInTheDocument();
        expect(screen.getByText('menu.export')).toBeInTheDocument();
        expect(screen.getByText('menu.delete')).toBeInTheDocument();
      });
    });

    it('should properly handle custom export title', async () => {
      const customTitle = 'Custom Export Title';
      renderWithMantine(<KBMenu onExport={mockOnExport} exportTitle={customTitle} />);

      fireEvent.click(screen.getByTestId('icon-dots').closest('button')!);

      await waitFor(() => {
        // Should use custom title instead of translation key
        expect(screen.getByText(customTitle)).toBeInTheDocument();
        expect(screen.queryByText('menu.export')).not.toBeInTheDocument();
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle component with no props', () => {
      renderWithMantine(<KBMenu />);

      expect(screen.getByTestId('icon-dots')).toBeInTheDocument();
    });

    it('should handle empty export title', async () => {
      renderWithMantine(<KBMenu onExport={mockOnExport} exportTitle='' />);

      fireEvent.click(screen.getByTestId('icon-dots').closest('button')!);

      await waitFor(() => {
        // Should fall back to default translation
        expect(screen.getByText('menu.export')).toBeInTheDocument();
      });
    });
  });

  describe('Menu Positioning and Styling', () => {
    it('should apply correct menu position', () => {
      renderWithMantine(<KBMenu onEdit={mockOnEdit} />);

      // Menu component should be rendered (specific positioning is handled by Mantine)
      expect(screen.getByTestId('icon-dots')).toBeInTheDocument();
    });
  });
});
