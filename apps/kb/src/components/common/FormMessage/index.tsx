import { Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type React from 'react';

interface FormMessageProps {
  className?: string;
  message: string;
  type: 'error' | 'info';
}

const useStyles = createStyles((theme) => ({
  root: {
    color: theme.colors.decaRed?.[5],
    fontSize: theme.fontSizes.sm,
  },
  error: {
    color: theme.colors.decaRed?.[5],
  },
  info: {
    color: theme.colors.decaGrey?.[5],
  },
}));

const FormMessage: React.FC<FormMessageProps> = ({ className, message, type = 'error' }) => {
  const { cx, classes } = useStyles();

  return <Text className={cx(classes.root, classes[type], className)}>{message}</Text>;
};

export default FormMessage;
