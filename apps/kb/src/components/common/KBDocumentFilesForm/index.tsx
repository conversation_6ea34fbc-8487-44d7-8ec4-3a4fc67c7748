import IconSwitchCross from '@/components/Icons/IconSwitchCross';
import type { ExtraDataFile } from '@/types';
import { fileSize } from '@/utils';
import { Box, Flex, Group, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useState } from 'react';
import AccessIcon from '../AccessIcon';
import SwitchButton from '../SwitchButton';

interface KBDocumentFilesFormProps {
  onClose?: () => void;
  onSubmitted?: (files: File[], extraDataFiles: ExtraDataFile[]) => Promise<void>;
  files: File[];
}

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    flexDirection: 'column',
    gap: theme.spacing.md,
  },
  buttonGroup: {
    width: '100%',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingTop: theme.spacing.md,
  },
  error: {
    fontSize: theme.fontSizes.md,
  },
  typeDescription: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.decaGrey[6],
  },
  switchGroup: {
    width: '100%',
  },
  switchIcon: {
    width: rem(16),
    height: rem(16),
    marginRight: rem(5),
  },
  filesContainer: {
    width: '100%',
    maxHeight: rem(310),
    borderRadius: theme.radius.md,
    backgroundColor: theme.colors.decaLight[0],
    overflowY: 'auto',
    padding: theme.spacing.md,
  },
  fileItem: {
    marginBottom: theme.spacing.md,
  },
  fileName: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.decaNavy[5],
    wordBreak: 'break-all',
  },
  fileSize: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.decaNavy[3],
  },
}));

const KBDocumentFilesForm: React.FC<KBDocumentFilesFormProps> = ({
  onClose,
  onSubmitted,
  files,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('kb');
  const [loading, setLoading] = useState(false);

  const [checkedFiles, setCheckedFiles] = useState<ExtraDataFile[]>([]);
  const handleChangeAccessLevel = useCallback((file: ExtraDataFile) => {
    setCheckedFiles((prev) => prev.map((f) => (f.name === file.name ? file : f)));
  }, []);

  const handleSwitchAll = useCallback(() => {
    setCheckedFiles((prev) => {
      return prev.map((f) => ({
        ...f,
        accessLevel: f.accessLevel === 'public' ? 'private' : 'public',
      }));
    });
  }, []);

  const handleUpload = useCallback(async () => {
    setLoading(true);
    await onSubmitted?.(files, checkedFiles);
  }, [files, checkedFiles, onSubmitted]);

  const labelOn = (
    <Flex align='center' gap={rem(3)} justify='space-between'>
      <AccessIcon accessLevel='public' className={classes.switchIcon} />
      {t('accessLevel.public.label')}
    </Flex>
  );
  const labelOff = (
    <Flex align='center' gap={rem(3)} justify='space-between'>
      <AccessIcon accessLevel='private' className={classes.switchIcon} />
      {t('accessLevel.private.label')}
    </Flex>
  );

  useEffect(() => {
    setCheckedFiles(
      files.map((file) => ({ name: file.name, size: file.size, accessLevel: 'public' }))
    );
  }, [files]);

  return (
    <Box className={classes.root}>
      <Flex justify='space-between' align='center' className={classes.switchGroup} gap={rem(10)}>
        <Text className={classes.typeDescription}>{t('modal.uploadingFilesDescription')}</Text>
        <DecaButton variant='neutral' size='sm' radius={'xs'} onClick={handleSwitchAll}>
          <IconSwitchCross className={classes.switchIcon} />
          {t('modal.switchAll')}
        </DecaButton>
      </Flex>
      <Box className={classes.filesContainer}>
        {checkedFiles.map((file) => (
          <Flex key={file.name} justify='space-between' align='center' className={classes.fileItem}>
            <div>
              <Text className={classes.fileName} lineClamp={1}>
                {file.name}
              </Text>
              <Text className={classes.fileSize}>{fileSize(file.size)}</Text>
            </div>
            <Box>
              <SwitchButton
                labelOn={labelOn}
                labelOff={labelOff}
                checked={file.accessLevel === 'public'}
                onChange={(checked) => {
                  handleChangeAccessLevel({ ...file, accessLevel: checked ? 'public' : 'private' });
                }}
              />
            </Box>
          </Flex>
        ))}
      </Box>
      <Group className={classes.buttonGroup}>
        <DecaButton radius={'xl'} variant='neutral' onClick={onClose}>
          {t('cancel')}
        </DecaButton>
        <DecaButton
          radius={'xl'}
          variant='primary'
          loading={loading}
          onClick={handleUpload}
          disabled={loading}
        >
          {t('upload')}
        </DecaButton>
      </Group>
    </Box>
  );
};

export default KBDocumentFilesForm;
