import { Flex, Group, Text, Title, rem } from '@mantine/core';
import { createStyles, keyframes } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { IconLoader } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';

import Modal from '../Modal';

export interface NoticeModalProps {
  opened: boolean;
  title: string;
  content: string;
  cancelText?: string;
  showLoader?: boolean;
  onCancel: () => void;
}

const spin = keyframes({
  '0%': {
    transform: 'rotate(0deg)',
  },
  '100%': {
    transform: 'rotate(359deg)',
  },
});

const useStyles = createStyles((theme) => ({
  noticeModal: {
    '& .mantine-Modal-header': {
      display: 'none',
    },
    textAlign: 'center',
  },
  loadingIcon: {
    color: theme.colors.decaNavy[5],
    animation: `${spin} 1s linear infinite`,
    width: rem(30),
    height: rem(30),
  },
  modalTitle: {
    color: theme.colors.decaNavy[5],
    fontWeight: 700,
  },
  modalContent: {
    color: theme.colors.decaNavy[5],
    fontWeight: 500,
  },
}));

const NoticeModal: React.FC<NoticeModalProps> = ({
  opened,
  title,
  content,
  cancelText,
  showLoader,
  onCancel,
}) => {
  const { t } = useTranslate('common');
  const { classes } = useStyles();

  return (
    <Modal opened={opened} onClose={onCancel} size='md' className={classes.noticeModal}>
      <Flex direction={'column'} justify={'center'} gap={rem(15)} align={'center'}>
        {showLoader && <IconLoader className={classes.loadingIcon} />}
        <Title order={4} className={classes.modalTitle}>
          {title}
        </Title>
        <Text size='lg' className={classes.modalContent}>
          {content}
        </Text>
        <Group>
          <DecaButton
            size='md'
            variant='neutral'
            onClick={onCancel}
            radius={'xl'}
            tt='capitalize'
            miw={rem(100)}
          >
            {cancelText || t('closeBtn')}
          </DecaButton>
        </Group>
      </Flex>
    </Modal>
  );
};

export default NoticeModal;
