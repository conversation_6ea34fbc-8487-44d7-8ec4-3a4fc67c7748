import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { memo } from 'react';

const useStyles = createStyles(() => ({
  formActions: {
    display: 'flex',
    gap: rem(12),
  },
  button: {
    minWidth: rem(120),
    fontSize: rem(14),
    lineHeight: rem(22),
  },
}));

interface EditorActionsProps {
  disabled?: boolean;
  className?: string;
  onSave: () => void;
  onCancel: () => void;
}

const EditorActions: React.FC<EditorActionsProps> = ({ disabled, className, onSave, onCancel }) => {
  const { t } = useTranslate('kb');
  const { classes, cx } = useStyles();

  return (
    <Box className={cx(className, classes.formActions)}>
      <DecaButton className={classes.button} radius={'xl'} variant='neutral' onClick={onCancel}>
        {t('cancel')}
      </DecaButton>
      <DecaButton
        className={classes.button}
        radius={'xl'}
        variant='primary'
        onClick={onSave}
        disabled={disabled}
      >
        {t('save')}
      </DecaButton>
    </Box>
  );
};

export default memo(EditorActions);
