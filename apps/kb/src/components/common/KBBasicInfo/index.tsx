import type { KnowledgeBaseType } from '@/types';
import { Box, Flex, Group, Text, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { stripHtml } from '@resola-ai/ui/utils';
import { IconEdit } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { memo } from 'react';
import IllustrationIcon from '../IllustrationIcon';

interface KBBasicInfoProps {
  name: string;
  description: string;
  type?: KnowledgeBaseType;
  className?: string;
  onEdit?: () => void;
}

const useStyles = createStyles((theme) => ({
  container: {
    margin: `${rem(30)} 0`,
    marginTop: rem(100),
  },
  title: {
    fontWeight: 700,
    color: theme.colors.decaNavy[5],
    marginBottom: rem(10),
    gap: rem(15),
    whiteSpace: 'break-spaces',
    wordBreak: 'break-word',
  },
  description: {
    fontSize: theme.fontSizes.md,
    fontWeight: 500,
    color: theme.colors.decaNavy[5],
    whiteSpace: 'pre-line',
  },
  icon: {
    cursor: 'pointer',
  },
}));

const KBBasicInfo: React.FC<KBBasicInfoProps> = ({
  name,
  description,
  type,
  className,
  onEdit,
}) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('details');

  return (
    <Box className={cx(classes.container, className)}>
      <Flex justify='space-between' align='center' gap={rem(20)}>
        <Box>
          <Group className={classes.title}>
            <Title order={2}>{name}</Title>
            {onEdit && <IconEdit className={classes.icon} onClick={onEdit} />}
          </Group>
          <Text component='p' className={classes.description}>
            {t('description')}: {stripHtml(description)}
          </Text>
        </Box>
        {type && <IllustrationIcon type={type} />}
      </Flex>
    </Box>
  );
};

export default memo(KBBasicInfo);
