import { Box, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { memo } from 'react';

import { KBCreateModal } from '@/components';
import { IconKnowledgeBases } from '@/components/Icons';

interface GridEmptyProps {
  isNewVersion?: boolean;
  parentDirId?: string;
  onFetch?: () => void;
  folderLevel?: number;
}

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: rem(20),
    minHeight: rem(374),
    height: '70%',
    borderRadius: rem(10),
    border: `${rem(1)} solid ${theme.colors.decaLight[2]}`,
    backgroundColor: theme.colors.decaLight[0],
    marginBottom: rem(20),
  },
  iconBox: {
    marginBottom: rem(20),
    fontSize: rem(40),
  },
  icon: {
    width: rem(200),
  },
  contentBox: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: theme.fontSizes.lg,
    lineHeight: rem(29),
    fontWeight: 700,
    p: {
      marginBottom: rem(24),
      textAlign: 'center',
      color: theme.colors.decaGrey[4],
    },
  },
}));

const GridEmpty: React.FC<GridEmptyProps> = ({
  isNewVersion = false,
  parentDirId,
  onFetch,
  folderLevel = 0,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('home');

  return (
    <Box className={classes.root}>
      <Box className={classes.iconBox}>
        <IconKnowledgeBases className={classes.icon} />
      </Box>
      <Box className={classes.contentBox}>
        <Text component='p'>
          {t('grid.empty')}
          <br />
          {t('grid.emptyMessage')}
        </Text>
        <KBCreateModal
          isNewVersion={isNewVersion}
          parentDirId={parentDirId}
          onClose={onFetch}
          folderLevel={folderLevel}
        />
      </Box>
    </Box>
  );
};

export default memo(GridEmpty);
