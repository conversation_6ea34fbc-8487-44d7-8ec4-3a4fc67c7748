import { Box, type BoxProps } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import DOMPurify from 'dompurify';
import type React from 'react';

interface KBDescriptionProps extends Omit<BoxProps, 'children' | 'dangerouslySetInnerHTML'> {
  /** The HTML content to render */
  content: string;
  /** Number of lines to show before truncating with ellipsis */
  lines?: number;
  /** Additional CSS class name */
  className?: string;
}

interface StylesParams {
  lines: number;
}

const useStyles = createStyles((theme, { lines }: StylesParams) => ({
  description: {
    display: '-webkit-box',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    WebkitLineClamp: lines,
    WebkitBoxOrient: 'vertical',
    wordBreak: 'break-word',
    whiteSpace: 'pre-line',

    // Style HTML elements within the description
    '& p': {
      margin: 0,
      padding: 0,
    },
    '& br': {
      lineHeight: 'inherit',
    },
    '& strong, & b': {
      fontWeight: 600,
    },
    '& i': {
      fontStyle: 'italic',
    },
    '& em': {
      fontStyle: 'normal',
    },
    '& u': {
      textDecoration: 'underline',
    },
    '& a': {
      color: theme.colors.decaBlue?.[5] || theme.colors.blue[6],
      textDecoration: 'none',
      '&:hover': {
        textDecoration: 'underline',
      },
    },
    '& ul, & ol': {
      margin: 0,
      paddingLeft: theme.spacing.md,
    },
    '& li': {
      margin: 0,
    },
  },
}));

/**
 * KBDescription Component
 * Renders HTML content with ellipsis support for long text
 * @param {KBDescriptionProps} props
 * @returns {JSX.Element}
 */
const KBDescription: React.FC<KBDescriptionProps> = ({
  content,
  lines = 1,
  className,
  ...rest
}) => {
  const { classes, cx } = useStyles({ lines });

  if (!content) {
    return null;
  }

  return (
    <Box
      className={cx(classes.description, className)}
      // biome-ignore lint/security/noDangerouslySetInnerHtml: Content is sanitized with DOMPurify
      dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(content) }}
      {...rest}
    />
  );
};

export default KBDescription;
