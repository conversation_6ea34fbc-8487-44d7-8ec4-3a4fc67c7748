import { renderWithMantine } from '@/utils/unitTest';
import { screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import KBDescription from './index';

// Test constants
const SIMPLE_TEXT = 'This is a simple text description';
const HTML_WITH_TAGS = 'This is <strong>bold</strong> and <em>italic</em> text';
const HTML_WITH_LINK = 'Visit <a href="https://example.com">this link</a> for more info';
const HTML_WITH_LIST = 'Items: <ul><li>First item</li><li>Second item</li></ul>';
const LONG_TEXT =
  'This is a very long text that should be truncated with ellipsis when it exceeds the specified number of lines. Lorem ipsum dolor sit amet, consectetur adipiscing elit.';
const HTML_WITH_PARAGRAPH = '<p>First paragraph</p><p>Second paragraph</p>';

describe('KBDescription', () => {
  describe('Rendering', () => {
    it('should render simple text content', () => {
      renderWithMantine(<KBDescription content={SIMPLE_TEXT} />);

      expect(screen.getByText(SIMPLE_TEXT)).toBeInTheDocument();
    });

    it('should render HTML content with formatting tags', () => {
      renderWithMantine(<KBDescription content={HTML_WITH_TAGS} />);

      // Check that HTML is rendered (not as plain text)
      expect(screen.getByText('bold')).toBeInTheDocument();
      expect(screen.getByText('italic')).toBeInTheDocument();

      // Verify HTML structure is preserved
      const container = screen.getByText('bold').closest('div');
      expect(container?.innerHTML).toContain('<strong>bold</strong>');
      expect(container?.innerHTML).toContain('<em>italic</em>');
    });

    it('should render HTML links correctly', () => {
      renderWithMantine(<KBDescription content={HTML_WITH_LINK} />);

      const link = screen.getByRole('link', { name: 'this link' });
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', 'https://example.com');
    });

    it('should render HTML lists correctly', () => {
      renderWithMantine(<KBDescription content={HTML_WITH_LIST} />);

      expect(screen.getByText('First item')).toBeInTheDocument();
      expect(screen.getByText('Second item')).toBeInTheDocument();

      // Check list structure
      const container = screen.getByText('First item').closest('div');
      expect(container?.innerHTML).toContain('<ul>');
      expect(container?.innerHTML).toContain('<li>');
    });

    it('should return null when content is empty', () => {
      const { container } = renderWithMantine(<KBDescription content='' />);

      // Should not render the component div, only Mantine styles
      const componentDiv = container.querySelector('div:not([data-mantine-styles])');
      expect(componentDiv).toBeNull();
    });

    it('should return null when content is not provided', () => {
      const { container } = renderWithMantine(<KBDescription content={null as any} />);

      // Should not render the component div, only Mantine styles
      const componentDiv = container.querySelector('div:not([data-mantine-styles])');
      expect(componentDiv).toBeNull();
    });
  });

  describe('Ellipsis Functionality', () => {
    it('should apply ellipsis styles with default 1 line', () => {
      renderWithMantine(<KBDescription content={LONG_TEXT} />);

      const element = screen.getByText(LONG_TEXT);
      const computedStyle = window.getComputedStyle(element);

      expect(computedStyle.display).toBe('-webkit-box');
      expect(computedStyle.overflow).toBe('hidden');
      expect(computedStyle.textOverflow).toBe('ellipsis');
      expect(computedStyle.webkitLineClamp).toBe('1');
    });

    it('should apply ellipsis styles with custom number of lines', () => {
      renderWithMantine(<KBDescription content={LONG_TEXT} lines={3} />);

      const element = screen.getByText(LONG_TEXT);
      const computedStyle = window.getComputedStyle(element);

      expect(computedStyle.webkitLineClamp).toBe('3');
    });

    it('should handle ellipsis for HTML content', () => {
      renderWithMantine(<KBDescription content={HTML_WITH_PARAGRAPH} lines={1} />);

      const container = screen.getByText('First paragraph').closest('div');
      const computedStyle = window.getComputedStyle(container!);

      expect(computedStyle.display).toBe('-webkit-box');
      expect(computedStyle.webkitLineClamp).toBe('1');
    });
  });

  describe('Styling', () => {
    it('should apply custom className', () => {
      const customClass = 'custom-description-class';
      renderWithMantine(<KBDescription content={SIMPLE_TEXT} className={customClass} />);

      const element = screen.getByText(SIMPLE_TEXT);
      expect(element).toHaveClass(customClass);
    });

    it('should accept additional Box props', () => {
      const testId = 'test-description';
      renderWithMantine(<KBDescription content={SIMPLE_TEXT} data-testid={testId} />);

      expect(screen.getByTestId(testId)).toBeInTheDocument();
    });

    it('should apply theme styles to HTML elements', () => {
      renderWithMantine(<KBDescription content={HTML_WITH_TAGS} />);

      const strongElement = screen.getByText('bold');
      const emElement = screen.getByText('italic');

      // Check that elements exist (styling is applied via CSS classes)
      expect(strongElement.tagName).toBe('STRONG');
      expect(emElement.tagName).toBe('EM');
    });

    it('should apply background color to em tags', () => {
      renderWithMantine(<KBDescription content={HTML_WITH_TAGS} />);

      const emElement = screen.getByText('italic');

      // Verify it's an em tag and exists in the document
      expect(emElement.tagName).toBe('EM');
      expect(emElement).toBeInTheDocument();

      // The background color is applied via CSS class, so we verify the element is present
      // and can be styled (actual color testing would require more complex setup)
    });
  });

  describe('Edge Cases', () => {
    it('should handle content with only whitespace', () => {
      const whitespaceContent = '   ';
      renderWithMantine(<KBDescription content={whitespaceContent} />);

      // Should render but with whitespace content
      const container = document.querySelector('[data-testid]') || document.body.firstChild;
      expect(container).toBeTruthy();
    });

    it('should handle malformed HTML gracefully', () => {
      const malformedHtml = '<strong>Bold text without closing tag';
      renderWithMantine(<KBDescription content={malformedHtml} />);

      // Browser should handle malformed HTML gracefully
      expect(screen.getByText('Bold text without closing tag')).toBeInTheDocument();
    });

    it('should handle special characters in HTML', () => {
      const specialChars = 'Text with &amp; &lt; &gt; &quot; &#39; characters';
      renderWithMantine(<KBDescription content={specialChars} />);

      // HTML entities should be decoded
      const container = screen.getByText(/Text with/).closest('div');
      expect(container?.textContent).toContain('&');
      expect(container?.textContent).toContain('<');
      expect(container?.textContent).toContain('>');
    });

    it('should handle very long HTML content', () => {
      const longHtmlContent = `<p>${LONG_TEXT}</p><p>${LONG_TEXT}</p>`;
      renderWithMantine(<KBDescription content={longHtmlContent} lines={2} />);

      // Use getAllByText since there are multiple elements with the same text
      const elements = screen.getAllByText(LONG_TEXT);
      expect(elements).toHaveLength(2);

      // Get the parent container that has the ellipsis styles
      const container = elements[0].closest('div');
      const computedStyle = window.getComputedStyle(container!);

      expect(computedStyle.webkitLineClamp).toBe('2');
      expect(computedStyle.overflow).toBe('hidden');
    });

    it('should handle zero lines parameter', () => {
      renderWithMantine(<KBDescription content={SIMPLE_TEXT} lines={0} />);

      const element = screen.getByText(SIMPLE_TEXT);
      const computedStyle = window.getComputedStyle(element);

      expect(computedStyle.webkitLineClamp).toBe('0');
    });
  });
});
