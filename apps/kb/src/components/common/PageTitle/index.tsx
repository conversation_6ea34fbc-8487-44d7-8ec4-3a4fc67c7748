import { Box, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

interface PageTitleProps {
  readonly title: React.ReactNode | string;
  readonly className?: string;
  readonly order?: 1 | 2 | 3 | 4 | 5 | 6;
  readonly rightSection?: React.ReactNode;
}

const useStyles = createStyles((theme) => ({
  title: {
    color: theme.colors.decaNavy[5],
    fontWeight: 700,
    fontSize: rem(32),
    lineHeight: rem(48),
    '&[data-order="1"]': {
      fontSize: rem(32),
      lineHeight: rem(48),
    },
    '&[data-order="2"]': {
      fontSize: rem(28),
      lineHeight: rem(36),
    },
    '&[data-order="3"]': {
      fontSize: rem(24),
      lineHeight: rem(36),
    },
    '&[data-order="4"]': {
      fontSize: rem(20),
      lineHeight: rem(30),
    },
    '&[data-order="5"]': {
      fontSize: rem(16),
      lineHeight: rem(24),
    },
    '&[data-order="6"]': {
      fontSize: rem(14),
      lineHeight: rem(21),
    },
  },
  container: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
}));

const PageTitle = ({ title, className, order = 1, rightSection }: PageTitleProps) => {
  const { classes, cx } = useStyles();

  return (
    <Box className={classes.container}>
      <Title order={order} className={cx(classes.title, className)}>
        {title}
      </Title>
      {rightSection && <Box>{rightSection}</Box>}
    </Box>
  );
};

export default PageTitle;
