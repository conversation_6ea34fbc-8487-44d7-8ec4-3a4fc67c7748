import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { formatDateTime } from '@resola-ai/ui/utils/dateTime';
import type React from 'react';

interface DateTimeProps {
  label?: string;
  dateTime: Date | string;
}

const useStyles = createStyles((theme) => ({
  root: {
    fontWeight: 500,
    fontSize: rem(12),
    lineHeight: rem(19),
    color: theme.colors.decaLight[8],
  },
  label: {
    marginLeft: rem(8),
  },
}));

const DateTime: React.FC<DateTimeProps> = ({ label = '', dateTime = '' }) => {
  const { classes } = useStyles();

  return (
    <p className={classes.root}>
      <span>{formatDateTime(dateTime)}</span>
      {label && <span className={classes.label}>{label}</span>}
    </p>
  );
};

export default DateTime;
