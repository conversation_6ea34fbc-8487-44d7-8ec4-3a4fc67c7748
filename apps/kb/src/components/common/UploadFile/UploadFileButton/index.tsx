import { But<PERSON>, <PERSON>Button, Loader, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconFileUpload } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { memo } from 'react';

const useStyles = createStyles((theme) => ({
  button: {
    minWidth: rem(211),
    height: rem(42),
    borderRadius: rem(32),
    padding: `${rem(1)} ${rem(22)}`,
    backgroundColor: theme.colors.decaNavy[5],
    color: theme.white,
    fontSize: theme.fontSizes.md,
    lineHeight: rem(40),
    '& span.mantine-Button-label': {
      position: 'relative',
      top: rem(-2),
    },
  },
  uploading: {
    backgroundColor: theme.colors.decaNavy[0],
    color: theme.colors.decaNavy[5],
    pointerEvents: 'none',
  },
}));

interface UploadFileButtonProps {
  accept?: string;
  isUploading?: boolean;
  isDisabled?: boolean;
  onChange: (files: File[] | null) => void;
}

const UploadFileButton: React.FC<UploadFileButtonProps> = ({
  isUploading = true,
  isDisabled = false,
  accept = '',
  onChange,
}) => {
  const { cx, classes } = useStyles();
  const { t } = useTranslate('details');

  return (
    <FileButton onChange={onChange} accept={accept} multiple>
      {(props) => (
        <Button
          {...props}
          className={cx(classes.button, isUploading ? classes.uploading : '')}
          leftSection={isUploading ? <Loader size={20} /> : <IconFileUpload />}
          disabled={isDisabled}
        >
          {t('uploadDoc')}
        </Button>
      )}
    </FileButton>
  );
};

export default memo(UploadFileButton);
