import { Box, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconFileDescription } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';
import UploadFileButton from './UploadFileButton';

interface UploadFileProps {
  accept?: string;
  isUploading?: boolean;
  isDisabled?: boolean;
  minimize?: boolean;
  onChange?: (files: File[] | null) => void;
}

const useStyles = createStyles((theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: rem(20),
    width: '100%',
    height: rem(400),
    border: `1px solid ${theme.colors.dark[0]}`,
    borderRadius: rem(10),
  },
  icon: {
    width: rem(49.33),
    height: rem(61.67),
    color: theme.colors.decaViolet[4],
  },
  description: {
    fontSize: theme.fontSizes.md,
    fontWeight: 700,
    lineHeight: rem(29),
    textAlign: 'center',
  },
  note: {
    color: theme.colors.decaGrey[5],
  },
}));

const UploadFile: React.FC<UploadFileProps> = ({
  accept = '',
  isUploading = false,
  isDisabled = false,
  minimize = false,
  onChange,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('details');
  const handleChange = useCallback(
    (inputFiles: File[] | null) => {
      onChange?.(inputFiles);
    },
    [onChange]
  );

  return (
    <>
      {minimize ? (
        <UploadFileButton
          accept={accept}
          isUploading={isUploading}
          isDisabled={isDisabled}
          onChange={handleChange}
        />
      ) : (
        <Box className={classes.container}>
          <IconFileDescription className={classes.icon} />
          <Box className={classes.description}>
            <Text>{t('noDocument')}</Text>
            <Text>{t('uploadDocuments')}</Text>
          </Box>
          <UploadFileButton
            accept={accept}
            isUploading={isUploading}
            isDisabled={isDisabled}
            onChange={handleChange}
          />
          <Text className={classes.note}>{t('uploadNote')}</Text>
        </Box>
      )}
    </>
  );
};

export default UploadFile;
