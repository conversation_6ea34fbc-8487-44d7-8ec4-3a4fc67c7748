import { Box, Button, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useEffect, useState } from 'react';

interface SwitchButtonProps {
  labelOn: React.ReactNode;
  labelOff: React.ReactNode;
  checked: boolean;
  onChange: (checked: boolean) => void;
}

const useStyles = createStyles((theme) => ({
  button: {
    width: rem(100),
    borderRadius: theme.radius.sm,
    backgroundColor: theme.colors.silverFox[0],
    color: theme.colors.decaNavy[5],
    padding: `${rem(4)} ${rem(8)}`,
    '&:hover': {
      backgroundColor: theme.colors.decaLight[0],
      color: theme.colors.decaNavy[5],
    },
    '&.active': {
      backgroundColor: theme.colors.decaNavy[5],
      color: theme.colors.silverFox[0],
      '&:hover': {
        backgroundColor: theme.colors.decaNavy[4],
      },
    },
  },
  switchButtonContainer: {
    display: 'flex',
    padding: `${rem(4)} ${rem(8)}`,
    gap: rem(10),
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    border: `${rem(1)} solid #EEEEF1`,
    borderRadius: theme.radius.sm,
    backgroundColor: theme.colors.silverFox[0],
  },
}));

const SwitchButton: React.FC<SwitchButtonProps> = ({ labelOn, labelOff, checked, onChange }) => {
  const { classes } = useStyles();
  const [active, setActive] = useState(checked);
  useEffect(() => {
    setActive(checked);
  }, [checked]);

  const handleChange = () => {
    setActive(!active);
    onChange(!active);
  };

  const buttonClass = active ? 'active' : '';
  const buttonClassInactive = !active ? 'active' : '';

  return (
    <Box className={classes.switchButtonContainer}>
      <Button
        size='sm'
        radius={'xs'}
        className={`${classes.button} ${buttonClass}`}
        onClick={handleChange}
      >
        {labelOn}
      </Button>
      <Button
        size='sm'
        radius={'xs'}
        className={`${classes.button} ${buttonClassInactive}`}
        onClick={handleChange}
      >
        {labelOff}
      </Button>
    </Box>
  );
};

export default SwitchButton;
