import { ActionIcon, Flex, Tooltip, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconFileSymlink } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React, { memo } from 'react';

const useStyles = createStyles((theme) => ({
  actionButton: {
    color: theme.colors.gray[6],
    '&:hover': {
      color: theme.colors.blue[6],
    },
  },
  actionIcon: {
    cursor: 'pointer',
    width: rem(18),
    height: rem(18),
  },
}));

type JobArticleActionsProps = {
  onOpen: () => void;
};

const JobArticleActions: React.FC<JobArticleActionsProps> = ({ onOpen }) => {
  const { t } = useTranslate('common');
  const { classes } = useStyles();

  const handleClick = React.useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  return (
    <Flex gap='xs' onClick={handleClick}>
      <Tooltip label={t('actions.view')} withinPortal>
        <ActionIcon
          className={classes.actionButton}
          onClick={onOpen}
          size='sm'
          aria-label={t('actions.view')}
        >
          <IconFileSymlink className={classes.actionIcon} />
        </ActionIcon>
      </Tooltip>
    </Flex>
  );
};

export default memo(JobArticleActions);
