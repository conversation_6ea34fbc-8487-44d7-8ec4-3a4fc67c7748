import { EditorActions } from '@/components';
import { useKbAccessControl } from '@/hooks/useKbAccessControl';
import { Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton, ThreeDotsMenu } from '@resola-ai/ui';
import { IconEdit, IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { memo, useMemo } from 'react';

const useStyles = createStyles((theme) => ({
  actionButton: {
    color: theme.colors.gray[6],
    '&:hover': {
      color: theme.colors.blue[6],
    },
  },
  actionIcon: {
    cursor: 'pointer',
    width: rem(18),
    height: rem(18),
  },
}));

interface JobArticleDetailActionsProps {
  className?: string;
  editing?: boolean;
  disabledContentSaving?: boolean;
  onSave: () => void;
  onSaveTo?: () => void;
  toggleEdit?: (editing: boolean) => void;
  onDelete?: () => void;
}

const JobArticleDetailActions: React.FC<JobArticleDetailActionsProps> = ({
  className,
  editing,
  disabledContentSaving,
  onSave,
  onSaveTo,
  toggleEdit,
  onDelete,
}) => {
  const { t } = useTranslate(['job', 'common']);
  const { classes } = useStyles();
  const { permJob } = useKbAccessControl();
  const menuItems = useMemo(
    () => [
      ...(onDelete
        ? [
            {
              icon: <IconTrash className={classes.actionIcon} />,
              label: t('actions.delete', { ns: 'common' }),
              onClick: onDelete,
            },
          ]
        : []),
    ],
    [onDelete, t, classes.actionIcon]
  );

  return (
    <Flex className={className} gap='sm'>
      {editing ? (
        <EditorActions
          disabled={disabledContentSaving}
          onSave={onSave}
          onCancel={() => toggleEdit?.(false)}
        />
      ) : (
        <>
          {permJob.canEditGenArticle && (
            <DecaButton
              radius='xl'
              variant='neutral'
              leftSection={<IconEdit size={20} />}
              onClick={() => toggleEdit?.(true)}
            >
              {t('actions.edit', { ns: 'common' })}
            </DecaButton>
          )}
          {permJob.canSaveGenArticle && (
            <DecaButton radius={'xl'} variant='primary' onClick={onSaveTo}>
              {t('button.saveToKnowledgeBase')}
            </DecaButton>
          )}
          {menuItems.length > 0 && <ThreeDotsMenu items={menuItems} />}
        </>
      )}
    </Flex>
  );
};

export default memo(JobArticleDetailActions);
