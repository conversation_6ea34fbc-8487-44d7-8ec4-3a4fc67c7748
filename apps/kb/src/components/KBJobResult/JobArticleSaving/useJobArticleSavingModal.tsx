import { modals } from '@mantine/modals';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';

import { useKBSelectionModal } from '@/components';
import { useAppContext } from '@/contexts/AppContext';
import { useModalManager, useNotifications } from '@/hooks';
import { GenArticleAPI, KbAPI } from '@/services/api/v2';

interface UseJobArticleSavingModalProps {
  jobArticleIds: string[];
  defaultBaseId?: string;
  afterSaving?: () => void;
}

export const useJobArticleSavingModal = ({
  jobArticleIds,
  defaultBaseId = '',
  afterSaving,
}: UseJobArticleSavingModalProps) => {
  const { t } = useTranslate(['job', 'kb', 'common']);
  const { modalClasses } = useModalManager();
  const { notifyMessage } = useNotifications(t);
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const { openKBSelectionModal, closeKBSelectionModal } = useKBSelectionModal({
    title: t('articleSavingModal.title'),
    description: t('articleSavingModal.description'),
  });

  const saveJobArticle = useCallback(
    async (baseId: string, articleIds: string[]) => {
      const updatedResponse = await GenArticleAPI.saveToKB(baseId, articleIds);

      if (updatedResponse?.status === 'success') {
        notifyMessage(t('articleSavingModal.successTitle'), t('articleSavingModal.successMessage'));
        modals.closeAll();
        afterSaving?.();
      }
    },
    [afterSaving, notifyMessage, t]
  );

  const confirmAndSaveArticleToKB = useCallback(
    async (baseId: string, articleIds: string[], savingCallback?: () => void) => {
      if (!baseId) return;

      const { data: base } = await KbAPI.get(baseId);

      if (!base) return;

      const saveHandler = async () => {
        closeConfirmModal();
        await saveJobArticle(baseId, articleIds);
      };

      return new Promise<void>((resolve) => {
        openConfirmModal({
          onConfirm: async () => {
            await saveHandler();
            savingCallback?.();
            resolve();
          },
          onCancel: () => {
            resolve();
          },
          title: t('articleSavingModal.confirmSavingMessage', {
            toKBName: base.data?.name || t('unknownKBName', { ns: 'kb' }),
          }),
          confirmText: t('actions.save', { ns: 'common' }),
          cancelText: t('actions.cancel', { ns: 'common' }),
          options: {
            className: modalClasses.confirmModal,
            modalSize: '465px',
          },
        });
      });
    },
    [
      modalClasses,
      t,
      closeConfirmModal,
      openConfirmModal,
      saveJobArticle,
      notifyMessage,
      afterSaving,
    ]
  );

  return {
    openJobArticleSavingModal: ({ savingCallback }: { savingCallback?: () => void }) => {
      openKBSelectionModal({
        currentBaseId: defaultBaseId,
        onSelect: (baseId) => confirmAndSaveArticleToKB(baseId, jobArticleIds, savingCallback),
      });
    },
    closeJobArticleSavingModal: () => {
      closeKBSelectionModal();
    },
  };
};
