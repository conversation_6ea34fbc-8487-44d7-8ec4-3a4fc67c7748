import { JOB_ARTICLE_DOWNLOAD_FORMATS } from '@/constants/file';
import type { JobArticleDownloadFormat } from '@/types/job';
import { Menu } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { IconFileDownload } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback, useState } from 'react';

interface JobArticleDownloadProps {
  jobId: string;
  jobTitle: string;
  onDownload: (jobId: string, jobTitle: string, format: JobArticleDownloadFormat) => void;
}

export const JobArticleDownload = ({ jobId, jobTitle, onDownload }: JobArticleDownloadProps) => {
  const { t } = useTranslate('job');
  const [opened, setOpened] = useState(false);

  const handleDownload = useCallback(
    (format: JobArticleDownloadFormat) => {
      onDownload(jobId, jobTitle, format);
    },
    [jobId, jobTitle, onDownload]
  );

  return (
    <Menu opened={opened} onChange={setOpened} width={250}>
      <Menu.Target>
        <DecaButton
          disabled={!jobId}
          leftSection={<IconFileDownload size={20} />}
          variant='neutral'
          onClick={() => setOpened((opened) => !opened)}
        >
          {t('button.downloadGeneratedArticles')}
        </DecaButton>
      </Menu.Target>

      <Menu.Dropdown>
        <Menu.Item fz='md' onClick={() => handleDownload(JOB_ARTICLE_DOWNLOAD_FORMATS.csv.type)}>
          {t('button.generatedArticlesCSV')}
        </Menu.Item>
        <Menu.Item fz='md' onClick={() => handleDownload(JOB_ARTICLE_DOWNLOAD_FORMATS.excel.type)}>
          {t('button.generatedArticlesExcel')}
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
};

export default JobArticleDownload;
