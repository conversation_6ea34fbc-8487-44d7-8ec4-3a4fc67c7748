import type { ArticleGeneratorActivity } from '@/types';
import { Box, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { Activities, type ActivityItem } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useMemo } from 'react';
import JobArticleActivityContent from './JobArticleActivityContent';

const useStyles = createStyles((theme) => ({
  container: {
    padding: rem(12),
  },
  title: {
    fontSize: theme.fontSizes.lg,
    fontWeight: 700,
    color: theme.colors.decaGrey[9],
    marginBottom: rem(24),
  },
  activities: {
    paddingTop: rem(12),
    paddingLeft: rem(12),
  },
}));

interface JobArticleActivitiesProps {
  activities: ArticleGeneratorActivity[];
}

export const JobArticleActivities: React.FC<JobArticleActivitiesProps> = ({ activities }) => {
  const { t } = useTranslate('job');
  const { classes } = useStyles();

  const activityItems: ActivityItem[] = useMemo(() => {
    if (!activities?.length) {
      return [];
    }

    return activities.map((activity) => ({
      content: <JobArticleActivityContent activity={activity} />,
      date: activity.timestamp,
    }));
  }, [activities]);

  if (!activityItems?.length) return null;

  return (
    <Box className={classes.container}>
      <Title order={3} className={classes.title}>
        {t('activity.title')}
      </Title>
      <Activities className={classes.activities} items={activityItems} />
    </Box>
  );
};

export default JobArticleActivities;
