import type { ArticleGeneratorActivity } from '@/types';
import { Avatar, Badge, Box, Group, type MantineTheme, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { formatDateTime } from '@resola-ai/ui/utils';
import { T } from '@tolgee/react';
import camelCase from 'lodash/camelCase';
import type React from 'react';
import { Link } from 'react-router-dom';

const useStyles = createStyles((theme: MantineTheme) => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing.sm,
    padding: theme.spacing.md,
    paddingBottom: theme.spacing.sm,
    marginTop: rem(-28),
  },
  activityBadge: {
    padding: `${rem(4)} ${rem(6)}`,
    borderRadius: rem(4),
    boxSizing: 'content-box',
    border: `${rem(1)} solid ${theme.colors.decaLight[1]}`,
    backgroundColor: theme.colors.decaViolet[0],
    color: theme.colors.decaGray[5],
    fontSize: theme.fontSizes.md,
    textTransform: 'none',
    fontWeight: 400,
  },
  userContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing.sm,
  },
  userInfo: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: theme.spacing.xs,
    color: theme.colors.decaGray[5],
  },
  kbLink: {
    color: theme.colors.decaGray[5],
    fontWeight: 500,
  },
}));

interface JobArticleActivityContentProps {
  activity: ArticleGeneratorActivity;
}

export const JobArticleActivityContent: React.FC<JobArticleActivityContentProps> = ({
  activity: { action, kbId, kbName, userAvatar, userName, userEmail, timestamp },
}) => {
  const { classes } = useStyles();

  return (
    <Box className={classes.root}>
      <Badge className={classes.activityBadge} variant='light' color='blue'>
        <T
          keyName={`activity.types.${camelCase(action)}`}
          params={{
            KBName: (
              <Link to={`/kb/${kbId}`} target='_blank' className={classes.kbLink}>
                {kbName}
              </Link>
            ),
          }}
        />
      </Badge>
      <Group className={classes.userContainer}>
        <Avatar
          src={userAvatar}
          radius='xl'
          size='sm'
          alt={`Avatar for ${userName || userEmail}`}
        />
        <Box className={classes.userInfo}>
          <Text>{userName || userEmail}</Text>
          <Text>{formatDateTime(timestamp)}</Text>
        </Box>
      </Group>
    </Box>
  );
};

export default JobArticleActivityContent;
