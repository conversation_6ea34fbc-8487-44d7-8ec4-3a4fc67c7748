import { GenArticleAPI } from '@/services/api/v2/genArticle';
import type { JobArticle } from '@/types';
import { renderWithMantine } from '@/utils/unitTest';
import { useDisclosure, useToggle } from '@mantine/hooks';
import { act, fireEvent, screen } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import JobArticleViewer, { type JobArticleViewerRef } from './JobArticleViewer';

// Mock dependencies
vi.mock('@mantine/hooks', () => ({
  useDisclosure: vi.fn(),
  useToggle: vi.fn(),
}));

vi.mock('@/services/api/v2/genArticle', () => ({
  GenArticleAPI: {
    update: vi.fn(),
    getHistory: vi.fn(),
  },
}));

// Mock JobArticleSaving
const openJobArticleSavingModalMock = vi.fn();
vi.mock('../JobArticleSaving', () => ({
  useJobArticleSavingModal: () => ({
    openJobArticleSavingModal: openJobArticleSavingModalMock,
  }),
}));

// Mock JobArticleDetail
vi.mock('./JobArticleDetail', () => ({
  default: ({ jobArticle, editing }) => (
    <div data-testid='job-article-detail'>
      <div data-testid='job-article-title'>{jobArticle?.title || ''}</div>
      <div data-testid='job-article-editing'>{editing?.toString() || ''}</div>
    </div>
  ),
}));

// Mock JobArticleDetailActions
vi.mock('../JobArticleDetailActions', () => ({
  default: ({ editing, onSave, onSaveTo, toggleEdit }) => (
    <div data-testid='job-article-actions'>
      {editing ? (
        <>
          <button type='button' data-testid='save-button' onClick={onSave}>
            Save
          </button>
          <button type='button' data-testid='cancel-button' onClick={toggleEdit}>
            Cancel
          </button>
        </>
      ) : (
        <>
          <button type='button' data-testid='edit-button' onClick={toggleEdit}>
            Edit
          </button>
          <button type='button' data-testid='save-to-button' onClick={onSaveTo}>
            Save To
          </button>
        </>
      )}
    </div>
  ),
}));

// Mock the styles hook
vi.mock('@/hooks/useArticleStyles', () => ({
  useArticleViewerStyles: () => ({
    classes: {
      articleDrawer: 'mock-article-drawer',
      drawerFullView: 'mock-drawer-full-view',
      bodyFullView: 'mock-body-full-view',
    },
    cx: (...args) => args.filter(Boolean).join(' '),
  }),
}));

// Mock GenArticleContext
vi.mock('@/contexts/GenArticleContext', () => {
  const mockContext = {
    updateGenArticle: vi.fn().mockResolvedValue({
      status: 'success',
      data: {
        id: 'article-123',
        title: 'Updated Article',
        content: 'Updated content',
        contentRaw: '<p>Updated content</p>',
      },
    }),
    editingGenArticle: {
      id: 'article-123',
      title: 'Test Article',
      content: 'Test content',
      contentRaw: '<p>Test content</p>',
    },
    articleErrors: {},
    getArticleActivities: vi.fn(),
  };

  return {
    GenArticleProvider: ({ children }) => <div data-testid='gen-article-provider'>{children}</div>,
    GenArticleContext: {
      Consumer: ({ children }) => children(mockContext),
    },
  };
});

// Mock react-i18next

// Mock Box from @mantine/core
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    Box: ({ children, className }) => <div className={className}>{children}</div>,
  };
});

// Mock DynamicDrawer component
vi.mock('@resola-ai/ui', () => ({
  DynamicDrawer: ({ children, opened, rightAction, onClose, onResize }) => (
    <div data-testid='dynamic-drawer' data-opened={opened}>
      <div data-testid='drawer-right-action'>{rightAction}</div>
      <div data-testid='drawer-content'>{opened ? children : null}</div>
      <button type='button' data-testid='drawer-close-button' onClick={onClose}>
        Close
      </button>
      <button type='button' data-testid='drawer-resize-button' onClick={() => onResize?.(true)}>
        Resize
      </button>
    </div>
  ),
}));

// Constants
const MOCK_ARTICLE: JobArticle = {
  id: 'article-123',
  jobId: 'job-123',
  title: 'Test Article',
  content: 'Test content',
  contentRaw: '<p>Test content</p>',
  createdAt: new Date('2023-01-01T00:00:00Z'),
  updatedAt: new Date('2023-01-01T00:00:00Z'),
  orgId: 'org-123',
  document: { id: 'doc-1', name: 'Document 1' },
  metadata: {
    references: [{ documentId: 'doc-1', documentName: 'Document 1' }],
  },
};

const UPDATED_ARTICLE = {
  ...MOCK_ARTICLE,
  title: 'Updated Article',
  content: 'Updated content',
  contentRaw: '<p>Updated content</p>',
  data: {
    ...MOCK_ARTICLE,
    title: 'Updated Article',
    content: 'Updated content',
    contentRaw: '<p>Updated content</p>',
  },
};

describe('JobArticleViewer', () => {
  // Setup mocks for hooks
  const mockOpen = vi.fn();
  const mockClose = vi.fn();
  const mockToggleEditing = vi.fn();

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Setup useDisclosure mock
    (useDisclosure as unknown as ReturnType<typeof vi.fn>).mockReturnValue([
      false,
      { open: mockOpen, close: mockClose },
    ]);

    // Setup useToggle mock
    (useToggle as unknown as ReturnType<typeof vi.fn>).mockReturnValue([false, mockToggleEditing]);

    // Setup API mocks
    (GenArticleAPI.update as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(
      UPDATED_ARTICLE
    );
    (GenArticleAPI.getHistory as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({
      data: [],
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should render correctly when closed', () => {
    const { container } = renderWithMantine(<JobArticleViewer />);
    expect(container).toBeInTheDocument();
    // Drawer should not be visible when closed
    expect(screen.getByTestId('dynamic-drawer')).toBeInTheDocument();
    expect(screen.getByTestId('dynamic-drawer').getAttribute('data-opened')).toBe('false');
  });

  it('should open and display article details', async () => {
    // Mock the drawer as open
    (useDisclosure as unknown as ReturnType<typeof vi.fn>).mockReturnValue([
      true,
      { open: mockOpen, close: mockClose },
    ]);

    const ref = { current: null as JobArticleViewerRef | null };

    renderWithMantine(<JobArticleViewer ref={ref} />);

    // Set data via ref within act
    await act(async () => {
      if (ref.current) {
        ref.current.setData({ jobArticle: MOCK_ARTICLE });
      }
    });

    // Wait for component to update
    expect(screen.getByTestId('dynamic-drawer')).toBeInTheDocument();
    expect(screen.getByTestId('dynamic-drawer').getAttribute('data-opened')).toBe('true');
  });

  it('should toggle editing mode', async () => {
    // Mock the drawer as open and editing as true
    (useDisclosure as unknown as ReturnType<typeof vi.fn>).mockReturnValue([
      true,
      { open: mockOpen, close: mockClose },
    ]);
    (useToggle as unknown as ReturnType<typeof vi.fn>).mockReturnValue([true, mockToggleEditing]);

    const ref = { current: null as JobArticleViewerRef | null };
    renderWithMantine(<JobArticleViewer ref={ref} />);

    // Set data via ref within act
    await act(async () => {
      if (ref.current) {
        ref.current.setData({ jobArticle: MOCK_ARTICLE });
      }
    });

    // Component should be rendered
    expect(screen.getByTestId('dynamic-drawer')).toBeInTheDocument();
    expect(screen.getByTestId('dynamic-drawer').getAttribute('data-opened')).toBe('true');
  });

  it('should call update API when saving article', async () => {
    // Mock the drawer as open
    (useDisclosure as unknown as ReturnType<typeof vi.fn>).mockReturnValue([
      true,
      { open: mockOpen, close: mockClose },
    ]);

    // Mock editing state
    (useToggle as unknown as ReturnType<typeof vi.fn>).mockReturnValue([true, mockToggleEditing]);

    const onUpdatedMock = vi.fn();

    const ref = { current: null as JobArticleViewerRef | null };
    renderWithMantine(<JobArticleViewer ref={ref} onUpdated={onUpdatedMock} />);

    // Set data via ref within act
    await act(async () => {
      if (ref.current) {
        ref.current.setData({ jobArticle: MOCK_ARTICLE });
      }
    });

    // Drawer should be open
    expect(screen.getByTestId('dynamic-drawer')).toBeInTheDocument();
    expect(screen.getByTestId('dynamic-drawer').getAttribute('data-opened')).toBe('true');

    // Right action should be present
    expect(screen.getByTestId('drawer-right-action')).toBeInTheDocument();
  });

  it('should close the drawer and call onClosed callback', async () => {
    // Mock the drawer as open
    (useDisclosure as unknown as ReturnType<typeof vi.fn>).mockReturnValue([
      true,
      { open: mockOpen, close: mockClose },
    ]);

    const onClosedMock = vi.fn();

    const ref = { current: null as JobArticleViewerRef | null };
    renderWithMantine(<JobArticleViewer ref={ref} onClosed={onClosedMock} />);

    // Call close via ref within act
    await act(async () => {
      if (ref.current) {
        ref.current.close();
      }
    });

    // Verify close was called
    expect(mockClose).toHaveBeenCalled();
    expect(onClosedMock).toHaveBeenCalled();
  });

  it('should handle resize events', async () => {
    // Mock the drawer as open
    (useDisclosure as unknown as ReturnType<typeof vi.fn>).mockReturnValue([
      true,
      { open: mockOpen, close: mockClose },
    ]);

    const ref = { current: null as JobArticleViewerRef | null };
    renderWithMantine(<JobArticleViewer ref={ref} />);

    // Set data via ref within act
    await act(async () => {
      if (ref.current) {
        ref.current.setData({ jobArticle: MOCK_ARTICLE });
      }
    });

    // Find resize button
    expect(screen.getByTestId('drawer-resize-button')).toBeInTheDocument();

    // Click resize button within act
    await act(async () => {
      fireEvent.click(screen.getByTestId('drawer-resize-button'));
    });

    // Verify component doesn't crash
    expect(screen.getByTestId('dynamic-drawer')).toBeInTheDocument();
  });
});
