import { DEFAULT_EMPTY_ARTICLE_CONTENT } from '@/constants/kb';
import type { JobArticle } from '@/types/job';
import { renderWithMantine } from '@/utils/unitTest';
import { act, fireEvent, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import JobArticleContentForm from './JobArticleContentForm';

// Mock Tolgee hooks
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key, // Simple mock that returns the key
  }),
  useTolgee: () => ({
    getLanguage: () => 'en',
  }),
}));

// Note: @mantine/core and @resola-ai/ui are already mocked globally in unitTest.tsx

vi.mock('@resola-ai/blocknote-editor', () => ({
  BlockNoteEditor: ({
    onChange,
    initialHTML,
    isBordered,
    isMarkdown,
    className,
    language,
    ...props
  }) => (
    <div data-testid='block-note-editor' className={className} {...props}>
      <textarea
        data-testid='editor-content'
        defaultValue={initialHTML}
        onChange={(e) => onChange?.(e.target.value, e.target.value)}
      />
    </div>
  ),
  BlockNoteViewer: ({ initialHTML, isMarkdown, className, ...props }) => (
    <div data-testid='block-note-viewer' className={className} {...props}>
      {initialHTML}
    </div>
  ),
  isBlockNoteMarkdownContent: vi.fn(() => false),
}));

vi.mock('@/hooks/useArticleStyles', () => ({
  useArticleDetailStyles: vi.fn(() => ({
    classes: {
      articleFormSection: 'articleFormSection',
      articleFormField: 'articleFormField',
      articleFormFieldLabel: 'articleFormFieldLabel',
      articleLabel: 'articleLabel',
      articleErrorMessage: 'articleErrorMessage',
      articleInputField: 'articleInputField',
      articleTitle: 'articleTitle',
      articleEditorField: 'articleEditorField',
    },
  })),
}));

// Mock the context
const validateArticleContentMock = vi.fn();
const mockArticleErrors = { title: '', content: '' };

vi.mock('@/contexts/GenArticleContext', () => ({
  useGenArticleContext: vi.fn(() => ({
    validateArticleContent: validateArticleContentMock,
    articleErrors: mockArticleErrors,
  })),
}));

// Use renderWithMantine which includes necessary providers
const customRender = renderWithMantine;

describe('JobArticleContentForm', () => {
  const mockArticle: JobArticle = {
    id: 'test-id',
    jobId: 'job-123',
    title: 'Test Article Title',
    content: 'Test article content',
    contentRaw: 'Test article content',
    createdAt: new Date('2023-01-01T00:00:00Z'),
    updatedAt: new Date('2023-01-01T00:00:00Z'),
    orgId: 'org-123',
    document: {
      id: 'doc-123',
      name: 'Test Document',
    },
    metadata: {
      references: [],
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockArticleErrors.title = '';
    mockArticleErrors.content = '';
    validateArticleContentMock.mockReturnValue({
      hasEmptyTitle: false,
      hasEmptyContent: false,
      hasTitleLengthError: false,
      hasContentLengthError: false,
    });
  });

  it('renders view mode correctly', () => {
    customRender(<JobArticleContentForm jobArticle={mockArticle} editing={false} />);

    expect(screen.getByTestId('article-title')).toHaveTextContent(mockArticle.title);
    expect(screen.getByTestId('block-note-viewer')).toBeInTheDocument();
    expect(screen.queryByTestId('block-note-editor')).not.toBeInTheDocument();
  });

  it('renders edit mode correctly', () => {
    customRender(<JobArticleContentForm jobArticle={mockArticle} editing={true} />);

    expect(screen.getByTestId('text-input')).toHaveValue(mockArticle.title);
    expect(screen.getByTestId('block-note-editor')).toBeInTheDocument();
    expect(screen.queryByTestId('block-note-viewer')).not.toBeInTheDocument();
  });

  it('handles title changes correctly', async () => {
    const onSave = vi.fn();
    const user = userEvent.setup();

    customRender(<JobArticleContentForm jobArticle={mockArticle} editing={true} onSave={onSave} />);

    const titleInput = screen.getByTestId('text-input');

    await act(async () => {
      await user.clear(titleInput);
      await user.type(titleInput, 'New Title');
      fireEvent.blur(titleInput);
    });

    expect(onSave).toHaveBeenCalledWith({
      ...mockArticle,
      title: 'New Title',
      content: mockArticle.content,
      contentRaw: mockArticle.contentRaw,
    });
  });

  it('handles editor content changes', async () => {
    const onSave = vi.fn();

    customRender(<JobArticleContentForm jobArticle={mockArticle} editing={true} onSave={onSave} />);

    const editorContent = screen.getByTestId('editor-content');

    await act(async () => {
      fireEvent.change(editorContent, { target: { value: 'New content' } });
    });

    expect(onSave).toHaveBeenCalledWith({
      ...mockArticle,
      title: mockArticle.title,
      content: 'New content',
      contentRaw: 'New content',
    });
  });

  it('validates article content when entering edit mode', () => {
    customRender(<JobArticleContentForm jobArticle={mockArticle} editing={true} />);

    expect(validateArticleContentMock).toHaveBeenCalledWith({
      title: mockArticle.title,
      content: mockArticle.content,
      contentRaw: mockArticle.contentRaw,
    });
  });

  it('shows error messages when validation fails', () => {
    mockArticleErrors.title = 'Title is required';
    mockArticleErrors.content = 'Content is too long';

    customRender(<JobArticleContentForm jobArticle={mockArticle} editing={true} />);

    expect(screen.getAllByRole('alert')[0]).toHaveTextContent('Title is required');
    expect(screen.getAllByRole('alert')[1]).toHaveTextContent('Content is too long');
  });

  it('does not save when validation fails', async () => {
    const onSave = vi.fn();
    validateArticleContentMock.mockReturnValue({
      hasEmptyTitle: true,
      hasEmptyContent: false,
      hasTitleLengthError: false,
      hasContentLengthError: false,
    });

    customRender(
      <JobArticleContentForm
        jobArticle={{ ...mockArticle, title: '' }}
        editing={true}
        onSave={onSave}
      />
    );

    const titleInput = screen.getByTestId('text-input');
    fireEvent.blur(titleInput);

    expect(onSave).not.toHaveBeenCalled();
  });

  it('handles empty content and sets default value', async () => {
    const onSave = vi.fn();

    customRender(<JobArticleContentForm jobArticle={mockArticle} editing={true} onSave={onSave} />);

    const editorContent = screen.getByTestId('editor-content');

    await act(async () => {
      fireEvent.change(editorContent, { target: { value: '' } });
    });

    expect(onSave).toHaveBeenCalledWith({
      ...mockArticle,
      title: mockArticle.title,
      content: DEFAULT_EMPTY_ARTICLE_CONTENT,
      contentRaw: '',
    });
  });

  it('resets form values when editing mode changes', async () => {
    const { rerender } = customRender(
      <JobArticleContentForm jobArticle={mockArticle} editing={false} />
    );

    // Change to editing mode
    rerender(<JobArticleContentForm jobArticle={mockArticle} editing={true} />);

    expect(screen.getByTestId('text-input')).toHaveValue(mockArticle.title);
    expect(validateArticleContentMock).toHaveBeenCalledWith({
      title: mockArticle.title,
      content: mockArticle.content,
      contentRaw: mockArticle.contentRaw,
    });
  });
});
