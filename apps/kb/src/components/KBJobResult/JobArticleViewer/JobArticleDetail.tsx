import { useGenArticleContext } from '@/contexts/GenArticleContext';
import type { JobArticle } from '@/types';
import { Divider, LoadingOverlay } from '@mantine/core';
import type React from 'react';
import { useCallback, useEffect, useMemo } from 'react';
import { JobArticleActivities } from '../JobArticleActivities';
import JobDocumentSource from '../JobDocumentSource';
import JobArticleContentForm from './JobArticleContentForm';

interface JobArticleDetailProps {
  jobArticle: JobArticle;
  editing: boolean;
}

const JobArticleDetail: React.FC<JobArticleDetailProps> = ({ jobArticle, editing }) => {
  const {
    setEditingGenArticle,
    updating,
    activitiesLoading,
    getArticleActivities,
    articleActivities,
    articleErrors,
  } = useGenArticleContext();

  const referenceDocuments = useMemo(
    () => jobArticle?.metadata?.references || [],
    [jobArticle?.metadata]
  );

  const handleUpdateArticle = useCallback(
    (data: JobArticle, callback?: () => void) => {
      if (articleErrors.title || articleErrors.content) return;

      setEditingGenArticle(data);
      callback?.();
    },
    [setEditingGenArticle, jobArticle, articleErrors]
  );

  useEffect(() => {
    if (jobArticle?.id) {
      setEditingGenArticle(jobArticle);
      getArticleActivities(jobArticle.id);
    }
  }, [jobArticle]);

  if (!jobArticle?.id) return null;

  return (
    <>
      <LoadingOverlay visible={updating || activitiesLoading} />
      <JobArticleContentForm
        jobArticle={jobArticle}
        onSave={handleUpdateArticle}
        editing={editing}
      />
      {referenceDocuments.length > 0 && (
        <>
          <Divider mt='sm' mb='lg' />
          <JobDocumentSource documents={referenceDocuments} />
        </>
      )}
      <JobArticleActivities activities={articleActivities || []} />
    </>
  );
};

export default JobArticleDetail;
