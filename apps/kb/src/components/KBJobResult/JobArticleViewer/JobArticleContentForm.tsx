import { DEFAULT_EMPTY_ARTICLE_CONTENT } from '@/constants/kb';
import { useGenArticleContext } from '@/contexts/GenArticleContext';
import { useArticleDetailStyles } from '@/hooks/useArticleStyles';
import type { JobArticle } from '@/types/job';
import { Box, Text, TextInput, Title } from '@mantine/core';
import {
  BlockNoteEditor,
  BlockNoteViewer,
  isBlockNoteMarkdownContent,
} from '@resola-ai/blocknote-editor';
import { ErrorMessage } from '@resola-ai/ui';
import { useTolgee, useTranslate } from '@tolgee/react';
import React, { useEffect, useState, useCallback, useMemo } from 'react';

/**
 * Props for the JobArticleContentForm component
 */
interface JobArticleContentFormProps {
  /** The job article data to display or edit */
  jobArticle: JobArticle;
  /** Whether the form is in edit mode */
  editing: boolean;
  /** Optional callback function called when article content is saved */
  onSave?: (data: JobArticle) => void;
}

/**
 * Component for displaying and editing job article content
 *
 * This component handles both view and edit modes for article title and content.
 * In view mode, it displays the title and content using BlockNoteViewer.
 * In edit mode, it provides input fields for title and content using BlockNoteEditor.
 */
const JobArticleContentForm: React.FC<JobArticleContentFormProps> = ({
  jobArticle,
  editing,
  onSave,
}) => {
  const { t } = useTranslate(['article', 'common']);
  const tolgee = useTolgee();
  const { classes } = useArticleDetailStyles();
  const { validateArticleContent, articleErrors } = useGenArticleContext();

  // Local state for form values
  const [jobArticleTitle, setJobArticleTitle] = useState<string>(jobArticle.title);
  const [content, setContent] = useState<string>(jobArticle.content);
  const [contentRaw, setContentRaw] = useState<string>(jobArticle.contentRaw);

  /**
   * Checks if the article is invalid based on title and content
   *
   * @param title - The article title
   * @param content - The processed article content
   * @param contentRaw - The raw article content
   * @returns True if the article is invalid, false otherwise
   */
  const isInvalidArticle = useCallback(
    (title: string, content: string, contentRaw: string) => {
      const { hasEmptyTitle, hasEmptyContent, hasTitleLengthError, hasContentLengthError } =
        validateArticleContent({ title, content, contentRaw });

      return hasEmptyTitle ?? hasEmptyContent ?? hasTitleLengthError ?? hasContentLengthError;
    },
    [validateArticleContent]
  );

  /**
   * Creates updated article data and calls the onSave callback if validation passes
   * @param data - The current article data
   */
  const handleEditorSaving = useCallback(
    (data: JobArticle) => {
      const { title, content, contentRaw } = data;
      // Skip saving if validation fails
      if (isInvalidArticle(title, content, contentRaw)) return;

      onSave?.({
        ...jobArticle,
        title,
        contentRaw,
        content,
      });
    },
    [jobArticle, isInvalidArticle, onSave]
  );

  /**
   * Handles changes in the editor content
   *
   * Updates local state and saves changes if valid
   *
   * @param contentRaw - The raw editor content
   * @param content - The processed editor content
   */
  const handleEditorChange = useCallback(
    (contentRaw: string, content: string) => {
      // Update local state
      setContentRaw(contentRaw);
      // Use default content if empty
      setContent(content || DEFAULT_EMPTY_ARTICLE_CONTENT);

      // Save changes
      handleEditorSaving({
        ...jobArticle,
        title: jobArticleTitle, // Include current title to ensure complete data
        contentRaw,
        content: content || DEFAULT_EMPTY_ARTICLE_CONTENT,
      });
    },
    [jobArticle, jobArticleTitle, handleEditorSaving]
  );

  /**
   * Handles changes to the title input
   */
  const handleTitleChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setJobArticleTitle(event?.currentTarget.value);
  }, []);

  /**
   * Saves the title when the input loses focus
   */
  const handleTitleBlur = useCallback(() => {
    // Skip saving if validation fails
    if (isInvalidArticle(jobArticleTitle, content, contentRaw)) return;

    onSave?.({
      ...jobArticle,
      title: jobArticleTitle,
      content, // Include current content to ensure complete data
      contentRaw, // Include current raw content to ensure complete data
    });
  }, [jobArticle, jobArticleTitle, content, contentRaw, isInvalidArticle, onSave]);

  // Memoize the editor props to avoid unnecessary re-renders
  const editorProps = useMemo(
    () => ({
      className: classes.articleEditorField,
      isMarkdown: isBlockNoteMarkdownContent(jobArticle.contentRaw),
      initialHTML: jobArticle.contentRaw,
      language: tolgee.getLanguage(),
      isBordered: false,
    }),
    [classes.articleEditorField, jobArticle.contentRaw, tolgee]
  );

  // Reset form values and validate when editing mode changes
  useEffect(() => {
    if (editing) {
      // Reset to original values when entering edit mode
      setJobArticleTitle(jobArticle.title);
      setContent(jobArticle.content);
      setContentRaw(jobArticle.contentRaw);

      // Validate the current content
      validateArticleContent({
        title: jobArticle.title,
        content: jobArticle.content,
        contentRaw: jobArticle.contentRaw,
      });
    }
  }, [editing, jobArticle]);

  // Render different UI based on editing mode
  return (
    <Box className={classes.articleFormSection}>
      {/* Title Section */}
      <Box className={classes.articleFormField}>
        {editing ? (
          <>
            <Box className={classes.articleFormFieldLabel}>
              <Text className={classes.articleLabel}>{t('articleCollection.title')}</Text>
              {articleErrors.title && (
                <ErrorMessage
                  className={classes.articleErrorMessage}
                  message={articleErrors.title}
                />
              )}
            </Box>
            <TextInput
              placeholder={t('articleCollection.title')}
              className={classes.articleInputField}
              value={jobArticleTitle}
              onChange={handleTitleChange}
              onBlur={handleTitleBlur}
            />
          </>
        ) : (
          <Title className={classes.articleTitle} order={2} data-testid='article-title'>
            {jobArticle.title}
          </Title>
        )}
      </Box>

      {/* Content Section */}
      <Box className={classes.articleFormField}>
        {editing ? (
          <>
            <Box className={classes.articleFormFieldLabel}>
              <Text className={classes.articleLabel}>{t('articleCollection.content')}</Text>
              {articleErrors.content && (
                <ErrorMessage
                  className={classes.articleErrorMessage}
                  message={articleErrors.content}
                />
              )}
            </Box>

            <BlockNoteEditor {...editorProps} onChange={handleEditorChange} />
          </>
        ) : (
          <BlockNoteViewer
            isMarkdown={isBlockNoteMarkdownContent(jobArticle.contentRaw)}
            className={classes.articleEditorField}
            initialHTML={jobArticle.contentRaw}
            data-testid='block-note-viewer'
          />
        )}
      </Box>
    </Box>
  );
};

export default React.memo(JobArticleContentForm);
