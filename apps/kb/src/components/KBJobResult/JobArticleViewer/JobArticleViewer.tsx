import { Box } from '@mantine/core';
import { useDisclosure, useToggle } from '@mantine/hooks';
import { forwardRef, useCallback, useImperativeHandle, useMemo, useState } from 'react';

import { DEFAULT_DRAWER_OFFSET, DRAWER_WIDTH_WITHOUT_SIDEBAR } from '@/constants/ui';
import {
  GenArticleContext,
  type GenArticleContextType,
  GenArticleProvider,
} from '@/contexts/GenArticleContext';
import { useArticleViewerStyles } from '@/hooks/useArticleStyles';
import type { JobArticle } from '@/types';
import { DynamicDrawer } from '@resola-ai/ui';
import JobArticleDetailActions from '../JobArticleDetailActions';
import { useJobArticleSavingModal } from '../JobArticleSaving';
import JobArticleDetail from './JobArticleDetail';

interface JobArticleViewerProps {
  className?: string;
  backTitle?: string;
  onUpdated?: () => void;
  onClosed?: () => void;
}

interface JobArticleViewerData {
  jobArticle: JobArticle | null;
}

export type JobArticleViewerRef = Partial<HTMLDivElement> & {
  open: () => void;
  close: () => void;
  setData: (data: JobArticleViewerData) => void;
};

const JobArticleViewer = forwardRef<JobArticleViewerRef, JobArticleViewerProps>(
  function JobArticleViewerHandler(
    { className = '', backTitle = '', onUpdated, onClosed }: JobArticleViewerProps,
    ref
  ): JSX.Element {
    const { classes, cx } = useArticleViewerStyles({
      drawerWidth: DRAWER_WIDTH_WITHOUT_SIDEBAR,
      withSidebar: false,
    });

    const [jobArticle, setJobArticle] = useState<JobArticle | null>(null);
    const [isFullView, setIsFullView] = useState<boolean>(false);

    const [opened, { open, close }] = useDisclosure(false);
    const [editing, toggleEditing] = useToggle();

    const { openJobArticleSavingModal } = useJobArticleSavingModal({
      jobArticleIds: jobArticle ? [jobArticle.id] : [],
    });

    /**
     * Handle resize event when maximize/minimize drawer
     * @param {boolean} isMaximize - Whether drawer is maximized
     * @returns {void}
     */
    const onResize = useCallback((isMaximize: boolean) => {
      setIsFullView(isMaximize);
    }, []);

    /**
     * Handle closing the job article viewer drawer
     * Calls the close function and triggers the onClosed callback if provided
     * @returns {void}
     */
    const closeJobArticleViewer = useCallback(() => {
      close();
      onClosed?.();
    }, [onClosed, close]);

    /**
     * Handle saving the job article with the updated content
     * @param {GenArticleContextType} context - The context containing the editingGenArticle
     * @returns {void}
     */
    const handleSave = useCallback(
      async (context: GenArticleContextType) => {
        if (!context.editingGenArticle) return;

        const { id, title, contentRaw, content } = context.editingGenArticle;
        const { articleErrors } = context;

        // If there are validation errors, do not update the article
        if (articleErrors.title || articleErrors.content) return;

        const updatedGenArticle = await context.updateGenArticle({
          id,
          title,
          contentRaw,
          content,
        });

        if (updatedGenArticle?.status === 'success') {
          setJobArticle(updatedGenArticle.data);
          toggleEditing(false);
          onUpdated?.();
        }
      },
      [setJobArticle, toggleEditing, onUpdated]
    );

    /**
     * Renders article detail actions based on context and editing state
     */
    const renderArticleActions = useCallback(
      (
        context: GenArticleContextType,
        editing: boolean,
        handleSave: (context: GenArticleContextType) => void,
        openSavingModal: () => void,
        toggleEdit: () => void
      ) => {
        const hasErrors = Boolean(context.articleErrors.title ?? context.articleErrors.content);

        return (
          <JobArticleDetailActions
            editing={editing}
            onSave={() => handleSave(context)}
            onSaveTo={openSavingModal}
            disabledContentSaving={hasErrors}
            toggleEdit={toggleEdit}
          />
        );
      },
      [] // Empty dependency array since all dependencies are passed as parameters
    );

    /**
     * Memoized drawer header right action component containing save/cancel buttons
     */
    const drawerHeaderRightAction = useMemo(() => {
      return (
        <GenArticleContext.Consumer>
          {(context) => {
            if (!context) return null;

            const handleSavingCallback = () => {
              if (jobArticle?.id) {
                context.getArticleActivities(jobArticle.id);
              }
            };

            const handleOpenSavingModal = () => {
              openJobArticleSavingModal({
                savingCallback: handleSavingCallback,
              });
            };

            return renderArticleActions(
              context,
              editing,
              handleSave,
              handleOpenSavingModal,
              toggleEditing
            );
          }}
        </GenArticleContext.Consumer>
      );
    }, [
      jobArticle?.id,
      editing,
      handleSave,
      openJobArticleSavingModal,
      toggleEditing,
      renderArticleActions,
    ]);

    /**
     * Memoized article detail component
     * Renders JobArticleDetail if jobArticle exists
     * Returns null if no jobArticle is present
     * @returns {JSX.Element | null} Memoized JobArticleDetail component or null
     */
    const articleDetail = useMemo(() => {
      if (!jobArticle) return null;

      return <JobArticleDetail jobArticle={jobArticle} editing={editing} />;
    }, [jobArticle, editing]);

    /**
     * Exposes imperative methods to control the drawer from outside the component
     * Provides open(), close(), and setData() methods that can be called via a ref
     * @returns {Object} Object containing open, close and setData methods
     */
    useImperativeHandle<
      JobArticleViewerRef,
      Pick<JobArticleViewerRef, 'open' | 'close' | 'setData'>
    >(ref, () => ({
      open,
      close: closeJobArticleViewer,
      setData: ({ jobArticle }: JobArticleViewerData) => {
        setJobArticle(jobArticle);
      },
    }));

    return (
      <GenArticleProvider>
        <DynamicDrawer
          className={cx(classes.articleDrawer, className, isFullView ? classes.drawerFullView : '')}
          offset={DEFAULT_DRAWER_OFFSET}
          opened={opened}
          backTitle={backTitle}
          withOverlay={false}
          closeOnEscape={false}
          lockScroll={false}
          zIndex={100}
          rightAction={drawerHeaderRightAction}
          onResize={onResize}
          onClose={closeJobArticleViewer}
        >
          <Box className={isFullView ? classes.bodyFullView : ''}>{articleDetail}</Box>
        </DynamicDrawer>
      </GenArticleProvider>
    );
  }
);

export default JobArticleViewer;
