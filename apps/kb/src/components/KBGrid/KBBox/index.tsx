import { Box, Grid, Group, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type { IPaginationNextPrevious } from '@resola-ai/models';
import { Link } from 'react-router-dom';

import { CustomPagination, KBCreateModal } from '@/components';
import type { KnowledgeBase, KnowledgeBaseDirectionQuery } from '@/types';
import KBCard from '../KBCard';

interface KBBoxProps {
  kbList: KnowledgeBase[];
  pagination?: IPaginationNextPrevious;
  onFetchKbList: (direction: KnowledgeBaseDirectionQuery, cursor: string) => void;
}

const useStyles = createStyles(() => ({
  header: {
    marginBottom: rem(40),
  },
  pagination: {
    marginTop: rem(30),
    marginBottom: rem(30),
  },
}));
const KBBox: React.FC<KBBoxProps> = ({ kbList, pagination, onFetchKbList }) => {
  const { classes } = useStyles();

  return (
    <>
      <Group className={classes.header}>
        <Box />
        <KBCreateModal />
      </Group>
      <Grid gutter='lg'>
        {kbList.map((item: KnowledgeBase) => (
          <Grid.Col key={item.id} span={{ xs: 6, sm: 6, md: 4, lg: 3 }}>
            <Link to={`/kb/qna/${item.id}`} style={{ textDecoration: 'none' }}>
              <KBCard data={item} />
            </Link>
          </Grid.Col>
        ))}
      </Grid>
      {pagination && (
        <Box className={classes.pagination}>
          <CustomPagination pagination={pagination} onChange={onFetchKbList} />
        </Box>
      )}
    </>
  );
};

export default KBBox;
