import { Card, Group, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { TextEllipsis } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import type React from 'react';

import { DateTime } from '@/components';
import { type KnowledgeBase, QNA_JOBS_STATUS } from '@/types';
import { getQnAJobStatusMapping } from '@/utils/qna';
import KBStatus from '../KBStatus';
import KBCategoryIcon from '../KBTypeIcon';

interface KBCardProps {
  className?: string;
  data: KnowledgeBase;
}

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
    border: `${rem(1)} solid ${theme.colors.decaLight[8]}`,
    borderRadius: rem(12),
    minHeight: rem(298),
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: theme.colors.decaViolet[0],
      borderColor: theme.colors.decaViolet[1],
      borderWidth: rem(2),
    },
  },
  section: {
    padding: rem(20),
  },
  headingGroup: {
    marginBottom: rem(12),
  },
  title: {
    fontWeight: 700,
    fontSize: rem(16),
    color: theme.colors.decaNavy[5],
    lineHeight: rem(24),
    marginBottom: rem(12),
  },
  description: {
    fontSize: rem(14),
    fontWeight: 400,
    color: theme.colors.decaNavy[5],
    lineHeight: rem(19),
    marginBottom: rem(16),
  },
  orange: {
    backgroundColor: theme.colors.decaYellow[0],
    '&:hover': {
      borderColor: theme.colors.decaYellow[1],
    },
  },
  green: {
    backgroundColor: theme.colors.decaGreen[0],
    '&:hover': {
      borderColor: theme.colors.decaGreen[1],
    },
  },
}));

const KBCard: React.FC<KBCardProps> = ({ className, data }) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('common');
  const { name, description, baseType, currentQaGenStatus, createdAt = new Date() } = data;
  const { actionColor } = getQnAJobStatusMapping(currentQaGenStatus || QNA_JOBS_STATUS.idle);

  return (
    <Card className={cx(classes.root, classes[actionColor], className)}>
      <Card.Section className={classes.section}>
        <Group className={classes.headingGroup}>
          <KBCategoryIcon
            type={baseType}
            isGenQnASucceed={currentQaGenStatus === QNA_JOBS_STATUS.succeed}
          />
          {currentQaGenStatus && <KBStatus status={currentQaGenStatus} />}
        </Group>
        <TextEllipsis className={classes.title}>{name}</TextEllipsis>
        <TextEllipsis className={classes.description} lines={5}>
          {description}
        </TextEllipsis>
      </Card.Section>
      <DateTime dateTime={createdAt} label={t('created')} />
    </Card>
  );
};

export default KBCard;
