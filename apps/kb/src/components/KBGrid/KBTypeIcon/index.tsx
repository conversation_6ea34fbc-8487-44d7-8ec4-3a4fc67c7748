import { KB_TYPE, type KnowledgeBaseType } from '@/types';
import { Badge, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import {
  IconFileDatabase,
  IconFileDescription,
  IconFileText,
  IconMessages,
} from '@tabler/icons-react';
import type React from 'react';
import { useMemo } from 'react';

type KBTypeIconProps = {
  className?: string;
  type: KnowledgeBaseType;
  isGenQnASucceed?: boolean;
};

const KBTypeIcons = {
  [KB_TYPE.document]: <IconFileText />,
  [KB_TYPE.qna]: <IconMessages />,
  [KB_TYPE.article]: <IconFileDescription />,
  [KB_TYPE.dataSource]: <IconFileDatabase />,
};

const useStyles = createStyles((theme) => ({
  root: {
    width: rem(34),
    height: rem(34),
    borderRadius: rem(17),
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 0,
    lineHeight: rem(1),
    backgroundColor: theme.colors.decaViolet[0],
    svg: {
      width: rem(18),
      height: rem(18),
    },
  },
  qna: {
    color: theme.colors.decaViolet[5],
    background: theme.colors.decaViolet[0],
  },
  document: {
    color: theme.colors.decaBlue[5],
    background: theme.colors.decaBlue[0],
  },
  succeed: {
    color: theme.colors.decaYellow[5],
    background: theme.colors.decaYellow[0],
  },
}));

const KBTypeIcon: React.FC<KBTypeIconProps> = ({ className, type, isGenQnASucceed = false }) => {
  const { classes, cx } = useStyles();
  const Icon: React.ReactNode = useMemo(() => KBTypeIcons[type] || null, [type]);

  return (
    <Badge
      className={cx(classes.root, classes[type], isGenQnASucceed && classes.succeed, className)}
    >
      {Icon}
    </Badge>
  );
};

export default KBTypeIcon;
