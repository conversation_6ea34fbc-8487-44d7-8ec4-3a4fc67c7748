import type { IPaginationNextPrevious } from '@resola-ai/models';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';

import { GridEmpty } from '@/components';
import { useKbContext } from '@/contexts/KbContext';
import type { KnowledgeBase, KnowledgeBaseDirectionQuery } from '@/types';
import { sortByCreatedAt } from '@/utils';
import KBBox from './KBBox';

const KBGrid: React.FC = () => {
  const [pagination, setPagination] = useState<IPaginationNextPrevious>();
  const [kbList, setKbList] = useState<KnowledgeBase[]>();
  const { getKnowledgeBaseList, isKbListForceUpdate } = useKbContext();

  const fetchKbList = useCallback(
    async (direction?: KnowledgeBaseDirectionQuery, cursor = '') => {
      const res = await getKnowledgeBaseList(cursor, direction);

      setKbList(res?.data && sortByCreatedAt(res?.data));
      setPagination(res?.pagination);
    },
    [getKnowledgeBaseList]
  );

  useEffect(() => {
    fetchKbList();
  }, []);

  useEffect(() => {
    if (isKbListForceUpdate) {
      fetchKbList('backward', '');
    }
  }, [fetchKbList, isKbListForceUpdate]);

  if (!kbList) {
    return null;
  }

  return (
    <>
      {kbList.length === 0 ? (
        <GridEmpty />
      ) : (
        <KBBox kbList={kbList} pagination={pagination} onFetchKbList={fetchKbList} />
      )}
    </>
  );
};

export default KBGrid;
