import type { QnAJobStatus } from '@/types';
import { getQnAJobStatusMapping } from '@/utils/qna';
import { Badge, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';

interface KBStatusProps {
  status: QnAJobStatus;
}

const useStyles = createStyles((theme) => ({
  root: {
    color: theme.white,
    padding: `${rem(2)} ${rem(10)} ${rem(4)} ${rem(10)}`,
    textTransform: 'none',
    fontWeight: 500,
    fontSize: rem(12),
  },
  orange: {
    backgroundColor: theme.colors.decaYellow[5],
  },
  green: {
    backgroundColor: theme.colors.decaGreen[5],
  },
}));

const KBStatus: React.FC<KBStatusProps> = ({ status }) => {
  const { t } = useTranslate('kb');
  const { classes, cx } = useStyles();
  const { actionColor, actionText } = getQnAJobStatusMapping(status);

  // eslint-disable-next-line no-extra-boolean-cast
  if (!actionText) {
    return null;
  }

  return (
    <Badge className={cx(classes.root, status && classes[actionColor])}>
      {t(actionText || '')}
    </Badge>
  );
};

export default KBStatus;
