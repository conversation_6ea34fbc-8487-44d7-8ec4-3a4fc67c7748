import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconDownload, IconFileSymlink } from '@tabler/icons-react';
import type React from 'react';
import { useCallback } from 'react';

const useStyles = createStyles((theme) => ({
  actions: {
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: rem(20),
  },
  button: {
    minWidth: rem(170),
    // TODO: Need to check why the font-size is not override the style from DecaButton with no important
    fontSize: `${theme.fontSizes.sm} !important`,
    lineHeight: rem(22),
  },
  icon: {
    width: rem(24),
    height: rem(24),
    cursor: 'pointer',
  },
}));

interface SearchRowActionsProps {
  className?: string;
  onOpen: () => void;
  onDownload?: () => void;
}

const SearchRowActions: React.FC<SearchRowActionsProps> = ({ className, onOpen, onDownload }) => {
  const { cx, classes } = useStyles();

  /**
   * Handle Open Click
   * @param {React.MouseEvent<HTMLElement>} event
   * @returns {void}
   */
  const onOpenClick = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      event.stopPropagation();
      onOpen();
    },
    [onOpen]
  );

  const onDownloadClick = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      event.stopPropagation();
      onDownload?.();
    },
    [onDownload]
  );

  return (
    <Box className={cx(classes.actions, className)}>
      {onDownload && (
        <Box onClick={onDownloadClick}>
          <IconDownload className={classes.icon} />
        </Box>
      )}
      <Box onClick={onOpenClick}>
        <IconFileSymlink className={classes.icon} />
      </Box>
    </Box>
  );
};

export default SearchRowActions;
