import { IconSearch } from '@/components/Icons';
import { useEmptyStyles } from '@/hooks/useEmptyStyles';
import { Box, Text } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { memo } from 'react';

const SearchEmpty: React.FC<{ className?: string }> = ({ className }) => {
  const { cx, classes } = useEmptyStyles();
  const { t } = useTranslate('kb');

  return (
    <Box className={cx(classes.root, className)}>
      <Box className={classes.iconBox}>
        <IconSearch className={classes.icon} />
      </Box>
      <Box className={classes.contentBox}>
        <Text component='p'>{t('search.emptyMessage')}</Text>
      </Box>
    </Box>
  );
};

export default memo(SearchEmpty);
