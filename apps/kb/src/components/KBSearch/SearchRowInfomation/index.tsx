import { KBDescription } from '@/components/common';
import type { SearchResult } from '@/types';
import { normalizeSearchResult } from '@/utils/search';
import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { Breadcrumbs, TextEllipsis } from '@resola-ai/ui';
import { IconChevronRight } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';

const FEEDBACK_WIDTH = 140;

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(8),
    width: `calc(100% - ${FEEDBACK_WIDTH}px)`,
  },
  breadcrumbs: {
    '& .mantine-Breadcrumbs-root': {
      width: rem('100%'),
    },
    '& .mantine-Text-root, & .mantine-Text-root.active': {
      fontSize: rem(12),
      lineHeight: rem(19),
      color: theme.colors.decaGrey[3],
    },
    '& .mantine-Breadcrumbs-breadcrumb': {
      maxWidth: rem('30%'),
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
  },
  informationBox: {
    display: 'flex',
    alignItems: 'flex-start',
    justifyContent: 'flex-start',
    svg: {
      marginRight: theme.spacing.xs,
      minWidth: rem(24),
    },
  },
  title: {
    fontSize: rem(16),
    lineHeight: rem(24),
    fontWeight: 500,
    wordWrap: 'break-word',
    color: theme.colors.decaNavy[5],
  },
  description: {
    fontSize: rem(12),
    lineHeight: rem(18),
    fontWeight: 400,
    color: theme.colors.decaGrey[9],
    wordWrap: 'break-word',
  },
  separatorIcon: {
    width: rem(14),
    height: rem(14),
  },
}));

/**
 * Search Row Infomation Component
 * @param {SearchRowInfomationProps} props
 * @returns {JSX.Element}
 * @dependencies searchResult: SearchResult
 */
interface SearchRowInfomationProps {
  searchResult: SearchResult;
}

const SearchRowInfomation: React.FC<SearchRowInfomationProps> = ({ searchResult }) => {
  const { classes } = useStyles();
  const { t } = useTranslate('home');
  const { breadcrumbs, title, description, icon } = normalizeSearchResult(searchResult, t);

  return (
    <Box className={classes.root}>
      <Breadcrumbs
        className={classes.breadcrumbs}
        items={breadcrumbs}
        separator={<IconChevronRight className={classes.separatorIcon} />}
      />
      <Box className={classes.informationBox}>
        {icon}
        <TextEllipsis className={classes.title} lines={2}>
          {title}
        </TextEllipsis>
      </Box>
      {description && (
        <KBDescription content={description} className={classes.description} lines={1} />
      )}
    </Box>
  );
};

export default SearchRowInfomation;
