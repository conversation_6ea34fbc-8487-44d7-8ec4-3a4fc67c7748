import { useSearchContext } from '@/contexts';
import { SearchEngine, type SearchEngineLanguage } from '@/services/SearchEngine';
import { Box, LoadingOverlay, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTolgee, useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import isNil from 'lodash/isNil';
import type React from 'react';
import { useCallback, useEffect } from 'react';
import SearchEmpty from './SearchEmpty';
import SearchTable from './SearchTable';

const useStyles = createStyles((theme) => ({
  root: {
    position: 'relative',
    padding: `0 0 ${rem(20)}`,
    minHeight: rem(600),
  },
  label: {
    fontWeight: 500,
    fontSize: rem(20),
    lineHeight: rem(30),
    color: theme.colors.decaNavy[5],
    marginBottom: rem(24),
  },
  count: {
    color: theme.colors.decaGrey[5],
  },
  emptyBlock: {
    minHeight: rem(460),
  },
}));

interface KBSearchProps {
  keyword?: string | undefined;
  searchFilter?: string | undefined;
  folderId?: string;
}

const KBSearch: React.FC<KBSearchProps> = ({
  keyword = undefined,
  searchFilter = undefined,
  folderId = '',
}) => {
  const { t } = useTranslate('kb');
  const tolgee = useTolgee();
  const { classes } = useStyles();

  const { isSearching, searchResults, search, searchByFilters } = useSearchContext();

  const handleSearch = useCallback(
    (showLoader = true) => {
      if (isNil(keyword)) return;

      if (!isNil(searchFilter)) {
        const searchEngine = new SearchEngine(tolgee.getLanguage() as SearchEngineLanguage);
        const filters = searchEngine.getFilters(searchFilter, keyword);
        const entities = searchEngine.getEntities(searchFilter);

        searchByFilters({
          query: keyword,
          folderId,
          showLoader,
          filters,
          entities,
        });

        return;
      }

      search(keyword, folderId, showLoader);
    },
    [keyword, folderId, searchFilter]
  );

  useEffect(() => {
    handleSearch();
  }, [handleSearch]);

  return (
    <Box className={classes.root}>
      <LoadingOverlay visible={isSearching} overlayProps={{ blur: 2 }} />
      {!isEmpty(searchResults) && (
        <>
          <Text className={classes.label} component='h4'>
            {t('search.searchResuls')}
            <span className={classes.count}>({searchResults.length})</span>
          </Text>
          <SearchTable searchResults={searchResults} onUpdated={() => handleSearch(false)} />
        </>
      )}
      {!isSearching && isEmpty(searchResults) && <SearchEmpty className={classes.emptyBlock} />}
    </Box>
  );
};

export default KBSearch;
