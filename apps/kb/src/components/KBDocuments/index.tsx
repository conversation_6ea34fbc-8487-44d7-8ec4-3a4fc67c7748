import { DOCUMENT_ACCEPT_TYPES } from '@/constants/file';
import { ROOT_PATH } from '@/constants/folder';
import { useAppContext } from '@/contexts/AppContext';
import { useKbContext } from '@/contexts/KbContext';
import useFileUpload from '@/hooks/useFileUpload';
import { FileAPI } from '@/services/api/v1';
import {
  type DocumentFile,
  type DocumentFileV1,
  type FileContentType,
  KB_TYPE,
  type KnowledgeBase,
  type KnowledgeBaseDirectionQuery,
  type KnowledgeBaseType,
  type PresignedURLResult,
  type PresignedURLResultV1,
} from '@/types';
import { sortByCreatedAt } from '@/utils/dateTime';
import { Box, LoadingOverlay, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import type { IPaginationNextPrevious } from '@resola-ai/models';
import { useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import toStringLodash from 'lodash/toString';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { CustomPagination, UploadFile, UploadStatusModal } from '../common';
import DocumentsTable from './DocumentsTable';

interface KBDocumentsProps {
  kb: KnowledgeBase;
}

const useStyles = createStyles(() => ({
  container: {
    paddingBottom: rem(30),
  },
  content: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(20),
  },
  toolbar: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: rem(20),
  },
  tableWrapper: {
    overflowX: 'auto',
    minHeight: rem(400),
    position: 'relative',
  },
  pagination: {
    marginTop: rem(10),
  },
}));

const KBDocuments: React.FC<KBDocumentsProps> = ({ kb }) => {
  const { classes } = useStyles();
  const { t } = useTranslate('details');
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const { isLoading, getDocumentList, getDocumentLink, removeDocument } = useKbContext();
  const { isUploading, uploadFiles, uploaders } = useFileUpload();
  const [files, setFiles] = useState<DocumentFileV1[]>();
  const [pagination, setPagination] = useState<IPaginationNextPrevious>();
  const [isDisabled, setIsDisabled] = useState<boolean>(false);
  const [opened, { toggle, close }] = useDisclosure(false);
  const acceptTypes = useMemo(() => DOCUMENT_ACCEPT_TYPES.join(','), [DOCUMENT_ACCEPT_TYPES]);

  const fetchDocumentList = useCallback(
    async (direction: KnowledgeBaseDirectionQuery = 'backward', cursor = '') => {
      if (kb.id) {
        const res = await getDocumentList(kb.id, direction, cursor);

        if (res && res.status === 'success') {
          setFiles(sortByCreatedAt(res?.data || []));
          setPagination(res?.pagination);
        }

        /**
         * TODO: Handle 404 error for the new KB ver 2 data that doesn't supported yet
         * Remove this block when the new KB Document ver 2 data is supported
         */
        if (res && toStringLodash(res.status) === '404') {
          setIsDisabled(true);
          setFiles([]);
          setPagination(undefined);
        }
      }
    },
    [kb.id, setFiles, setPagination]
  );

  const onUploadCompleted = useCallback(async () => {
    await fetchDocumentList();
    notifications.show({
      title: t('upload.uploadSuccessTitle'),
      message: t('upload.uploadSuccessMessage'),
      color: 'teal',
    });
  }, [fetchDocumentList]);

  const getPresignedUrl = useCallback(
    async (files: File[]): Promise<PresignedURLResult[]> => {
      const { results } =
        (await FileAPI.getPresignedUrl(
          kb.id,
          files.map((file) => ({
            fileName: file.name,
            contentLength: file.size,
            contentType: file.type as FileContentType,
          }))
        )) ?? ({ results: [] } as any);

      if (!results || !results.length) {
        return [];
      }

      const convertedResults: PresignedURLResult[] = results.map((result: PresignedURLResultV1) => {
        const fileRecord: DocumentFile = {
          id: result.fileRecord.id,
          orgId: result.fileRecord.orgId,
          folderId: ROOT_PATH,
          metadata: {
            name: result.fileRecord.metadata.fileName,
            contentLength: result.fileRecord.metadata.contentLength,
            contentType: result.fileRecord.metadata.contentType,
            uploadUrl: result.fileRecord.uploadUrl,
            downloadUrl: result.fileRecord.downloadUrl,
            downloadUrlExpires: result.fileRecord.downloadUrlExpires,
            tokenUsage: result.fileRecord.tokenUsage,
            createdBy: result.fileRecord.creator,
          },
          type: KB_TYPE.document as KnowledgeBaseType,
          parentDirBreadcrumbArray: [],
          parentDirPath: ROOT_PATH,
          createdAt: result.fileRecord.createdAt,
          updatedAt: result.fileRecord.updatedAt,
        };
        return { fileRecord };
      });

      return convertedResults;
    },
    [kb.id]
  );
  const onUploadDocument = useCallback(
    async (inputFiles: File[] | null) => {
      if (kb.id && inputFiles) {
        if (!opened) toggle();

        await uploadFiles({
          files: inputFiles,
          getPresignedUrl,
          onCompleted: onUploadCompleted,
        });
      }
    },
    [kb?.id, toggle, opened, uploadFiles, onUploadCompleted]
  );

  const onViewDocument = useCallback(
    async (fileId: string) => {
      if (kb.id) {
        const documentLinkObj = await getDocumentLink(kb.id, fileId);

        if (documentLinkObj?.status === 'success' && documentLinkObj?.downloadUrl) {
          window.open(documentLinkObj.downloadUrl, '_blank');
        } else {
          notifications.show({
            title: t('upload.fileNotFound'),
            message: t('upload.fileNotFoundMessage'),
            color: 'red',
          });
        }
      }
    },
    [kb.id, getDocumentLink]
  );

  const handleRemoveConfirm = useCallback(
    async (id: string) => {
      await removeDocument(kb.id, id);
      setFiles((prev) => {
        if (!prev) return [];
        return prev.filter((file) => file.id !== id);
      });
      closeConfirmModal();
      await fetchDocumentList();
    },
    [setFiles, closeConfirmModal, fetchDocumentList, kb.id, removeDocument]
  );

  const onRemoveDocument = useCallback(
    (id: string) => {
      openConfirmModal({
        onConfirm: () => handleRemoveConfirm(id),
        title: t('deleteDocumentConfirmTitle'),
      });
    },
    [openConfirmModal, handleRemoveConfirm, t]
  );

  useEffect(() => {
    fetchDocumentList();
  }, [kb.id]);

  return (
    <Box className={classes.container}>
      {files && !isEmpty(files) && (
        <Box className={classes.content}>
          <Box className={classes.toolbar}>
            <Box /> {/* replace with SearchBox later */}
            <UploadFile
              accept={acceptTypes}
              isUploading={isUploading}
              isDisabled={isDisabled}
              minimize
              onChange={onUploadDocument}
            />
          </Box>
          <Box className={classes.tableWrapper}>
            <LoadingOverlay visible={isLoading} />
            <DocumentsTable
              documents={files}
              onViewDocument={onViewDocument}
              onRemoveDocument={onRemoveDocument}
            />
          </Box>
          {pagination && (
            <Box className={classes.pagination}>
              <CustomPagination pagination={pagination} onChange={fetchDocumentList} />
            </Box>
          )}
        </Box>
      )}
      {files && isEmpty(files) && (
        <UploadFile
          accept={acceptTypes}
          isUploading={isUploading}
          isDisabled={isDisabled}
          onChange={onUploadDocument}
        />
      )}
      {uploaders && <UploadStatusModal opened={opened} uploaders={uploaders} onClose={close} />}
    </Box>
  );
};

export default KBDocuments;
