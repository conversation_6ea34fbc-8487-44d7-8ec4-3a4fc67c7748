import { useAppContext } from '@/contexts/AppContext';
import { useKbContext } from '@/contexts/KbContext';
import useFileUpload from '@/hooks/useFileUpload';
import { FileAPI } from '@/services/api/v1';
import {
  ACCESS_LEVEL,
  type AccessLevel,
  type DocumentFileV1,
  FILE_CONTENT_TYPE,
  FILE_UPLOAD_STATUS,
  type FileContentType,
  KB_TYPE,
  type KnowledgeBase,
  type KnowledgeBaseType,
  type PresignedURLResultV1,
} from '@/types';
import { renderWithProviders } from '@/utils/unitTest';
import { notifications } from '@mantine/notifications';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import KBDocuments from './index';

// Mock external dependencies
vi.mock('@/contexts/AppContext');
vi.mock('@/contexts/KbContext');
vi.mock('@/hooks/useFileUpload');
vi.mock('@mantine/notifications');
vi.mock('@/services/api/v1');

// Mock child components
vi.mock('./DocumentsTable', () => ({
  default: ({ documents, onViewDocument, onRemoveDocument }: any) => (
    <div data-testid='documents-table'>
      {documents.map((doc: DocumentFileV1) => (
        <div key={doc.id} data-testid={`document-${doc.id}`}>
          <span data-testid='document-name'>{doc.metadata.fileName}</span>
          <button
            type='button'
            data-testid={`view-${doc.id}`}
            onClick={() => onViewDocument(doc.id)}
          >
            View
          </button>
          <button
            type='button'
            data-testid={`remove-${doc.id}`}
            onClick={() => onRemoveDocument(doc.id)}
          >
            Remove
          </button>
        </div>
      ))}
    </div>
  ),
}));

vi.mock('../common', () => ({
  CustomPagination: ({ pagination, onChange }: any) => (
    <div data-testid='pagination'>
      <button
        type='button'
        data-testid='prev-page'
        onClick={() => onChange('backward', pagination.previousCursor)}
        disabled={!pagination.previousCursor}
      >
        Previous
      </button>
      <button
        type='button'
        data-testid='next-page'
        onClick={() => onChange('forward', pagination.nextCursor)}
        disabled={!pagination.nextCursor}
      >
        Next
      </button>
    </div>
  ),
  UploadFile: ({ onChange, isUploading, isDisabled, minimize, accept }: any) => (
    <div data-testid='upload-file'>
      <input
        data-testid='file-input'
        type='file'
        onChange={(e) => onChange(e.target.files ? Array.from(e.target.files) : null)}
        disabled={isDisabled}
        accept={accept}
        multiple
      />
      <span data-testid='upload-status'>{isUploading ? 'Uploading...' : 'Upload'}</span>
      <span data-testid='minimize-status'>{minimize ? 'minimized' : 'full'}</span>
    </div>
  ),
  UploadStatusModal: ({ opened, uploaders, onClose }: any) =>
    opened ? (
      <div data-testid='upload-status-modal'>
        <div data-testid='uploaders-count'>{Object.keys(uploaders).length}</div>
        <button type='button' data-testid='close-modal' onClick={onClose}>
          Close
        </button>
      </div>
    ) : null,
}));

// Test constants
const MOCK_KB_ID = 'kb-123';
const MOCK_KB: KnowledgeBase = {
  id: MOCK_KB_ID,
  org_id: 'org-123',
  name: 'Test Knowledge Base',
  description: 'Test Description',
  baseType: KB_TYPE.document as KnowledgeBaseType,
  type: KB_TYPE.document as KnowledgeBaseType,
  accessLevel: ACCESS_LEVEL.private as AccessLevel,
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-01'),
};

const MOCK_DOCUMENT_FILE: DocumentFileV1 = {
  id: 'doc-123',
  orgId: 'org-123',
  kbId: MOCK_KB_ID,
  downloadUrl: 'https://example.com/download',
  uploadStatus: FILE_UPLOAD_STATUS.uploaded,
  downloadUrlExpires: new Date('2024-01-01'),
  metadata: {
    fileName: 'test-document.pdf',
    contentLength: 1024,
    contentType: FILE_CONTENT_TYPE.pdf as FileContentType,
  },
  tokenUsage: 100,
  creator: 'user-123',
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-01'),
};

const MOCK_DOCUMENT_FILE_2: DocumentFileV1 = {
  id: 'doc-456',
  orgId: 'org-123',
  kbId: MOCK_KB_ID,
  downloadUrl: 'https://example.com/download2',
  uploadStatus: FILE_UPLOAD_STATUS.uploaded,
  downloadUrlExpires: new Date('2024-01-01'),
  metadata: {
    fileName: 'test-document-2.txt',
    contentLength: 512,
    contentType: FILE_CONTENT_TYPE.txt as FileContentType,
  },
  tokenUsage: 50,
  creator: 'user-123',
  createdAt: new Date('2023-01-02'),
  updatedAt: new Date('2023-01-02'),
};

const MOCK_PAGINATION = {
  previousCursor: 'prev-cursor',
  nextCursor: 'next-cursor',
  hasMore: true,
  hasPrevious: true,
};

const MOCK_PRESIGNED_URL_RESULT: PresignedURLResultV1 = {
  fileRecord: {
    id: 'new-doc-123',
    orgId: 'org-123',
    kbId: MOCK_KB_ID,
    uploadUrl: 'https://example.com/upload',
    downloadUrl: 'https://example.com/download',
    downloadUrlExpires: new Date('2024-01-01'),
    metadata: {
      fileName: 'new-document.pdf',
      contentLength: 2048,
      contentType: FILE_CONTENT_TYPE.pdf as FileContentType,
    },
    creator: 'user-123',
    createdAt: new Date(),
    updatedAt: new Date(),
  },
};

// Mock functions
const mockOpenConfirmModal = vi.fn();
const mockCloseConfirmModal = vi.fn();
const mockGetDocumentList = vi.fn();
const mockGetDocumentLink = vi.fn();
const mockRemoveDocument = vi.fn();
const mockUploadFiles = vi.fn();
const mockNotificationShow = vi.fn();
const mockFileAPIGetPresignedUrl = vi.fn();

describe('KBDocuments', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetAllMocks();

    // Mock useAppContext
    (useAppContext as any).mockReturnValue({
      openConfirmModal: mockOpenConfirmModal,
      closeConfirmModal: mockCloseConfirmModal,
    });

    // Mock useKbContext
    (useKbContext as any).mockReturnValue({
      isLoading: false,
      getDocumentList: mockGetDocumentList,
      getDocumentLink: mockGetDocumentLink,
      removeDocument: mockRemoveDocument,
    });

    // Mock useFileUpload
    (useFileUpload as any).mockReturnValue({
      isUploading: false,
      uploadFiles: mockUploadFiles,
      uploaders: {},
    });

    // Mock notifications
    (notifications.show as any).mockImplementation(mockNotificationShow);

    // Mock FileAPI
    (FileAPI.getPresignedUrl as any).mockImplementation(mockFileAPIGetPresignedUrl);
  });

  describe('Component Rendering', () => {
    it('should render empty state when no documents exist', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [],
        pagination: undefined,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId('upload-file')).toBeInTheDocument();
        expect(screen.queryByTestId('documents-table')).not.toBeInTheDocument();
      });
    });

    it('should render documents table with documents', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [MOCK_DOCUMENT_FILE, MOCK_DOCUMENT_FILE_2],
        pagination: MOCK_PAGINATION,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId('documents-table')).toBeInTheDocument();
        expect(screen.getByTestId(`document-${MOCK_DOCUMENT_FILE.id}`)).toBeInTheDocument();
        expect(screen.getByTestId(`document-${MOCK_DOCUMENT_FILE_2.id}`)).toBeInTheDocument();
      });
    });

    it('should render pagination when documents exist', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [MOCK_DOCUMENT_FILE],
        pagination: MOCK_PAGINATION,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId('pagination')).toBeInTheDocument();
      });
    });

    it('should render upload file component in toolbar when documents exist', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [MOCK_DOCUMENT_FILE],
        pagination: MOCK_PAGINATION,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId('upload-file')).toBeInTheDocument();
        expect(screen.getByTestId('minimize-status')).toHaveTextContent('minimized');
      });
    });

    it('should show loading overlay when loading', async () => {
      (useKbContext as any).mockReturnValue({
        isLoading: true,
        getDocumentList: mockGetDocumentList,
        getDocumentLink: mockGetDocumentLink,
        removeDocument: mockRemoveDocument,
      });

      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [MOCK_DOCUMENT_FILE],
        pagination: MOCK_PAGINATION,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId('documents-table')).toBeInTheDocument();
      });
    });
  });

  describe('Document List Fetching', () => {
    it('should fetch document list on component mount', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [MOCK_DOCUMENT_FILE],
        pagination: MOCK_PAGINATION,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(mockGetDocumentList).toHaveBeenCalledWith(MOCK_KB_ID, 'backward', '');
      });
    });

    it('should handle 404 error for unsupported KB version', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: '404',
        data: null,
        pagination: undefined,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId('upload-file')).toBeInTheDocument();
        const fileInput = screen.getByTestId('file-input');
        expect(fileInput).toBeDisabled();
      });
    });

    it('should handle pagination navigation', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [MOCK_DOCUMENT_FILE],
        pagination: MOCK_PAGINATION,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId('pagination')).toBeInTheDocument();
      });

      const nextButton = screen.getByTestId('next-page');
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(mockGetDocumentList).toHaveBeenCalledWith(
          MOCK_KB_ID,
          'forward',
          MOCK_PAGINATION.nextCursor
        );
      });
    });
  });

  describe('File Upload', () => {
    it('should handle file upload', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [],
        pagination: undefined,
      });

      mockFileAPIGetPresignedUrl.mockResolvedValue({
        results: [MOCK_PRESIGNED_URL_RESULT],
      });

      // Mock uploadFiles to call the getPresignedUrl function
      mockUploadFiles.mockImplementation(async ({ getPresignedUrl, files }) => {
        if (getPresignedUrl && files) {
          await getPresignedUrl(files);
        }
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId('upload-file')).toBeInTheDocument();
      });

      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      const fileInput = screen.getByTestId('file-input');

      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      });

      fireEvent.change(fileInput);

      await waitFor(() => {
        expect(mockUploadFiles).toHaveBeenCalled();
        expect(mockFileAPIGetPresignedUrl).toHaveBeenCalledWith(MOCK_KB_ID, [
          {
            fileName: 'test.pdf',
            contentLength: 12,
            contentType: 'application/pdf',
          },
        ]);
      });
    });

    it('should show upload status modal when uploading', async () => {
      (useFileUpload as any).mockReturnValue({
        isUploading: true,
        uploadFiles: mockUploadFiles,
        uploaders: { 'file-1': { id: 'file-1', status: 'uploading' } },
      });

      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [],
        pagination: undefined,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.queryByTestId('upload-status-modal')).not.toBeInTheDocument();
      });

      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      const fileInput = screen.getByTestId('file-input');

      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      });

      fireEvent.change(fileInput);

      await waitFor(() => {
        expect(screen.getByTestId('upload-status-modal')).toBeInTheDocument();
      });
    });

    it('should show success notification after upload completion', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [],
        pagination: undefined,
      });

      mockUploadFiles.mockImplementation(async ({ onCompleted }) => {
        if (onCompleted) {
          onCompleted();
        }
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId('upload-file')).toBeInTheDocument();
      });

      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      const fileInput = screen.getByTestId('file-input');

      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      });

      fireEvent.change(fileInput);

      await waitFor(() => {
        expect(mockNotificationShow).toHaveBeenCalledWith({
          title: 'upload.uploadSuccessTitle',
          message: 'upload.uploadSuccessMessage',
          color: 'teal',
        });
      });
    });
  });

  describe('Document Actions', () => {
    beforeEach(async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [MOCK_DOCUMENT_FILE, MOCK_DOCUMENT_FILE_2],
        pagination: MOCK_PAGINATION,
      });
    });

    it('should handle document view action', async () => {
      mockGetDocumentLink.mockResolvedValue({
        status: 'success',
        downloadUrl: 'https://example.com/download',
      });

      // Mock window.open
      const mockWindowOpen = vi.fn();
      Object.defineProperty(window, 'open', {
        value: mockWindowOpen,
        writable: true,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId(`view-${MOCK_DOCUMENT_FILE.id}`)).toBeInTheDocument();
      });

      const viewButton = screen.getByTestId(`view-${MOCK_DOCUMENT_FILE.id}`);
      fireEvent.click(viewButton);

      await waitFor(() => {
        expect(mockGetDocumentLink).toHaveBeenCalledWith(MOCK_KB_ID, MOCK_DOCUMENT_FILE.id);
        expect(mockWindowOpen).toHaveBeenCalledWith('https://example.com/download', '_blank');
      });
    });

    it('should handle document view error', async () => {
      mockGetDocumentLink.mockResolvedValue({
        status: 'error',
        downloadUrl: null,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId(`view-${MOCK_DOCUMENT_FILE.id}`)).toBeInTheDocument();
      });

      const viewButton = screen.getByTestId(`view-${MOCK_DOCUMENT_FILE.id}`);
      fireEvent.click(viewButton);

      await waitFor(() => {
        expect(mockNotificationShow).toHaveBeenCalledWith({
          title: 'upload.fileNotFound',
          message: 'upload.fileNotFoundMessage',
          color: 'red',
        });
      });
    });

    it('should handle document remove action', async () => {
      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId(`remove-${MOCK_DOCUMENT_FILE.id}`)).toBeInTheDocument();
      });

      const removeButton = screen.getByTestId(`remove-${MOCK_DOCUMENT_FILE.id}`);
      fireEvent.click(removeButton);

      expect(mockOpenConfirmModal).toHaveBeenCalledWith({
        onConfirm: expect.any(Function),
        title: 'deleteDocumentConfirmTitle',
      });
    });

    it('should handle document remove confirmation', async () => {
      let confirmCallback: (() => void) | undefined;
      mockOpenConfirmModal.mockImplementation(({ onConfirm }) => {
        confirmCallback = onConfirm;
      });

      mockRemoveDocument.mockResolvedValue({ status: 'success' });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId(`remove-${MOCK_DOCUMENT_FILE.id}`)).toBeInTheDocument();
      });

      const removeButton = screen.getByTestId(`remove-${MOCK_DOCUMENT_FILE.id}`);
      fireEvent.click(removeButton);

      expect(confirmCallback).toBeDefined();

      if (confirmCallback) {
        await confirmCallback();
      }

      await waitFor(() => {
        expect(mockRemoveDocument).toHaveBeenCalledWith(MOCK_KB_ID, MOCK_DOCUMENT_FILE.id);
        expect(mockCloseConfirmModal).toHaveBeenCalled();
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty KB ID', async () => {
      const kbWithoutId = { ...MOCK_KB, id: '' };
      renderWithProviders(<KBDocuments kb={kbWithoutId} />);

      expect(mockGetDocumentList).not.toHaveBeenCalled();
    });

    it('should handle upload with no files', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [],
        pagination: undefined,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId('upload-file')).toBeInTheDocument();
      });

      const fileInput = screen.getByTestId('file-input');
      fireEvent.change(fileInput);

      // Component should call uploadFiles but with no files
      expect(mockUploadFiles).toHaveBeenCalledWith({
        files: [],
        getPresignedUrl: expect.any(Function),
        onCompleted: expect.any(Function),
      });
    });

    it('should handle file upload with no presigned URL results', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [],
        pagination: undefined,
      });

      mockFileAPIGetPresignedUrl.mockResolvedValue({
        results: [],
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId('upload-file')).toBeInTheDocument();
      });

      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      const fileInput = screen.getByTestId('file-input');

      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      });

      fireEvent.change(fileInput);

      // Component should call uploadFiles even if presigned URL returns empty results
      expect(mockUploadFiles).toHaveBeenCalledWith({
        files: [file],
        getPresignedUrl: expect.any(Function),
        onCompleted: expect.any(Function),
      });
    });

    it('should handle file upload with null presigned URL response', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [],
        pagination: undefined,
      });

      mockFileAPIGetPresignedUrl.mockResolvedValue(null);

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId('upload-file')).toBeInTheDocument();
      });

      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      const fileInput = screen.getByTestId('file-input');

      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      });

      fireEvent.change(fileInput);

      // Component should call uploadFiles even if presigned URL returns null
      expect(mockUploadFiles).toHaveBeenCalledWith({
        files: [file],
        getPresignedUrl: expect.any(Function),
        onCompleted: expect.any(Function),
      });
    });

    it('should handle disabled state correctly', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: '404',
        data: null,
        pagination: undefined,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        const fileInput = screen.getByTestId('file-input');
        expect(fileInput).toBeDisabled();
      });
    });
  });

  describe('Upload Status Modal', () => {
    it('should close upload status modal', async () => {
      (useFileUpload as any).mockReturnValue({
        isUploading: false,
        uploadFiles: mockUploadFiles,
        uploaders: { 'file-1': { id: 'file-1', status: 'completed' } },
      });

      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [],
        pagination: undefined,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId('upload-file')).toBeInTheDocument();
      });

      // Trigger file upload to open modal
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      const fileInput = screen.getByTestId('file-input');

      Object.defineProperty(fileInput, 'files', {
        value: [file],
        writable: false,
      });

      fireEvent.change(fileInput);

      await waitFor(() => {
        expect(screen.getByTestId('upload-status-modal')).toBeInTheDocument();
      });

      const closeButton = screen.getByTestId('close-modal');
      fireEvent.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByTestId('upload-status-modal')).not.toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle document list fetch failure gracefully', async () => {
      // Instead of rejecting, return an error response that the component can handle
      mockGetDocumentList.mockResolvedValue({
        status: 'error',
        data: null,
        pagination: undefined,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(mockGetDocumentList).toHaveBeenCalled();
      });

      // When fetchDocumentList returns an error, the component doesn't update state
      // so files remains undefined and nothing is rendered
      expect(screen.queryByTestId('upload-file')).not.toBeInTheDocument();
      expect(screen.queryByTestId('documents-table')).not.toBeInTheDocument();
    });

    it('should handle document link fetch failure', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [MOCK_DOCUMENT_FILE],
        pagination: MOCK_PAGINATION,
      });

      // Instead of rejecting, return an error response
      mockGetDocumentLink.mockResolvedValue({
        status: 'error',
        downloadUrl: null,
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId(`view-${MOCK_DOCUMENT_FILE.id}`)).toBeInTheDocument();
      });

      const viewButton = screen.getByTestId(`view-${MOCK_DOCUMENT_FILE.id}`);

      fireEvent.click(viewButton);

      await waitFor(() => {
        expect(mockGetDocumentLink).toHaveBeenCalledWith(MOCK_KB_ID, MOCK_DOCUMENT_FILE.id);
        expect(mockNotificationShow).toHaveBeenCalledWith({
          title: 'upload.fileNotFound',
          message: 'upload.fileNotFoundMessage',
          color: 'red',
        });
      });
    });

    it('should handle document removal failure gracefully', async () => {
      mockGetDocumentList.mockResolvedValue({
        status: 'success',
        data: [MOCK_DOCUMENT_FILE],
        pagination: MOCK_PAGINATION,
      });

      let confirmCallback: (() => Promise<void>) | undefined;
      mockOpenConfirmModal.mockImplementation(({ onConfirm }) => {
        confirmCallback = onConfirm;
      });

      // Instead of rejecting, return an error response
      mockRemoveDocument.mockResolvedValue({
        status: 'error',
      });

      renderWithProviders(<KBDocuments kb={MOCK_KB} />);

      await waitFor(() => {
        expect(screen.getByTestId(`remove-${MOCK_DOCUMENT_FILE.id}`)).toBeInTheDocument();
      });

      const removeButton = screen.getByTestId(`remove-${MOCK_DOCUMENT_FILE.id}`);
      fireEvent.click(removeButton);

      expect(confirmCallback).toBeDefined();

      if (confirmCallback) {
        await confirmCallback();
      }

      await waitFor(() => {
        expect(mockRemoveDocument).toHaveBeenCalledWith(MOCK_KB_ID, MOCK_DOCUMENT_FILE.id);
        expect(mockCloseConfirmModal).toHaveBeenCalled();
      });
    });
  });
});
