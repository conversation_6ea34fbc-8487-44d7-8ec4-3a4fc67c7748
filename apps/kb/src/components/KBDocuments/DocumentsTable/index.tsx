import type { DocumentFileV1 } from '@/types/file';
import { fileSize, fileType } from '@/utils/file';
import { Table, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback } from 'react';

const useStyles = createStyles((theme) => ({
  table: {
    fontSize: rem(13),
    color: theme.colors.decaNavy[5],
    border: `1px solid ${theme.colors.gray[4]}`,
    borderRadius: rem(10),
    borderCollapse: 'unset',
    borderSpacing: 0,
    '> thead > tr > th': {
      height: rem(50),
      fontWeight: 700,
      fontSize: theme.fontSizes.sm,
      lineHeight: rem(19),
      color: theme.colors.decaNavy[5],
      '&:last-child': {
        textAlign: 'center',
      },
    },
    '&[data-hover] > tbody > tr:hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
  },
  documentName: {
    cursor: 'pointer',
    '&:hover': {
      textDecoration: 'underline',
    },
  },
  actionColumn: {
    maxWidth: rem(100),
    textAlign: 'center',
  },
  removeIcon: {
    cursor: 'pointer',
    '&:hover': {
      stroke: theme.colors.decaRed[5],
    },
  },
}));

interface DocumentsTableProps {
  documents: DocumentFileV1[];
  onViewDocument: (fileId: string) => void;
  onRemoveDocument: (fileId: string) => void;
}

const DocumentsTable: React.FC<DocumentsTableProps> = ({
  documents,
  onViewDocument,
  onRemoveDocument,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('details');

  const rows = useCallback(() => {
    return (documents || []).map((file: DocumentFileV1) => (
      <tr key={file.id}>
        <td width={300}>
          <button
            type='button'
            className={classes.documentName}
            onClick={() => onViewDocument(file.id)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                onViewDocument(file.id);
              }
            }}
            style={{
              border: 'none',
              background: 'none',
              padding: 0,
              textAlign: 'left',
              width: '100%',
            }}
          >
            {file.metadata.fileName}
          </button>
        </td>
        <td width={100}>{fileType(file.metadata.contentType)}</td>
        <td width={80}>{fileSize(file.metadata.contentLength)}</td>
        <td width={50} className={classes.actionColumn}>
          <button
            type='button'
            onClick={() => onRemoveDocument(file.id)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                onRemoveDocument(file.id);
              }
            }}
            style={{ border: 'none', background: 'none', padding: 0 }}
          >
            <IconTrash className={classes.removeIcon} />
          </button>
        </td>
      </tr>
    ));
  }, [documents, onViewDocument, onRemoveDocument]);

  return (
    <Table withTableBorder highlightOnHover verticalSpacing='sm' className={classes.table}>
      <thead>
        <tr>
          <th>{t('fileName')}</th>
          <th>{t('fileType')}</th>
          <th>{t('fileSize')}</th>
          <th className={classes.actionColumn}>{t('fileActionDelete')}</th>
        </tr>
      </thead>
      <tbody>{rows()}</tbody>
    </Table>
  );
};

export default DocumentsTable;
