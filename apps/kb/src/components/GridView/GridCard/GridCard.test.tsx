import { DocumentAPI } from '@/services/api/v2/document';
import {
  type DocumentFile,
  FILE_UPLOAD_STATUS,
  type FileLink,
  type Folder,
  type KnowledgeBase,
} from '@/types';
import { renderWithProviders } from '@/utils/unitTest';
import { Image } from '@mantine/core';
import { act, fireEvent, screen } from '@testing-library/react';
import type React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import GridCard from './index';

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
}));

// Mock useExportJob hook
vi.mock('@/components/KBExport/hooks/useExportJob', () => ({
  useExportJob: () => ({
    exportJob: vi.fn(),
    isExporting: false,
  }),
}));

// Mock DocumentAPI
vi.mock('@/services/api/v2/document', () => ({
  DocumentAPI: {
    getLink: vi.fn(),
  },
}));

// Mock react-router-dom
vi.mock('react-router-dom', () => ({
  Link: ({ children, to }: { children: React.ReactNode; to: string }) => (
    <a href={to} data-testid='mock-link'>
      {children}
    </a>
  ),
  useNavigate: vi.fn(() => vi.fn()),
}));

// Mock common components
vi.mock('@/components/common', () => ({
  KBMenu: ({ onEdit, onDelete, onDownload }: any) => (
    <div data-testid='kb-menu'>
      {onEdit && (
        <button type='button' onClick={onEdit} data-testid='edit-button'>
          Edit
        </button>
      )}
      {onDelete && (
        <button type='button' onClick={onDelete} data-testid='delete-button'>
          Delete
        </button>
      )}
      {onDownload && (
        <button type='button' onClick={onDownload} data-testid='download-button'>
          Download
        </button>
      )}
    </div>
  ),
  IllustrationIcon: ({ type, contentType, imageUrl, onError }: any) => {
    if (imageUrl) {
      return (
        <Image
          src={imageUrl}
          alt='illustration'
          onError={onError}
          data-testid='illustration-image'
        />
      );
    }

    return <div>{`${type}-${contentType || ''}`}</div>;
  },
  AccessIcon: ({ accessLevel }: any) => <div data-testid='access-icon'>{accessLevel}</div>,
  FileStatusIcon: ({ status }: any) => <div data-testid='file-status-icon'>{status}</div>,
}));

const mockFolder: Folder = {
  id: 'folder-1',
  path: '/test',
  name: 'Test Folder',
  count: 0,
  childFolderCount: 0,
  childKbCount: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockDocument: DocumentFile = {
  id: 'doc-1',
  orgId: 'org-1',
  folderId: 'folder-1',
  type: 'document',
  metadata: {
    name: 'Test Document',
    contentType: 'application/pdf',
    contentLength: 1024,
    uploadStatus: FILE_UPLOAD_STATUS.uploaded,
  },
  accessLevel: 'private',
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockKnowledgeBase: KnowledgeBase = {
  id: 'kb-1',
  name: 'Test Knowledge Base',
  description: 'Test Description',
  type: 'article',
  baseType: 'article',
  accessLevel: 'public',
  org_id: 'org-1',
  createdAt: new Date(),
  updatedAt: new Date(),
};

describe('GridCard', () => {
  const mockProps = {
    onEdit: vi.fn(),
    onDelete: vi.fn(),
    onDownload: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render folder correctly', () => {
    renderWithProviders(<GridCard item={mockFolder} {...mockProps} />);

    expect(screen.getByText('Test Folder')).toBeInTheDocument();
    expect(screen.getByTestId('illustration-icon')).toHaveTextContent('folder');
    expect(screen.getByTestId('mock-link')).toHaveAttribute('href', '/kb/folder/folder-1');
  });

  it('should render document correctly', () => {
    renderWithProviders(<GridCard item={mockDocument} {...mockProps} />);

    expect(screen.getByText('Test Document')).toBeInTheDocument();
    expect(screen.getByTestId('illustration-icon')).toHaveTextContent('document-application/pdf');
    expect(screen.getByTestId('access-icon')).toHaveTextContent('private');
    expect(screen.getByTestId('file-status-icon')).toHaveTextContent('uploaded');
    expect(screen.getByTestId('mock-link')).toHaveAttribute('href', '/kb/document/doc-1');
  });

  it('should render knowledge base correctly', () => {
    renderWithProviders(<GridCard item={mockKnowledgeBase} {...mockProps} />);

    expect(screen.getByText('Test Knowledge Base')).toBeInTheDocument();
    expect(screen.getByTestId('illustration-icon')).toHaveTextContent('article');
    expect(screen.getByTestId('access-icon')).toHaveTextContent('public');
    expect(screen.getByTestId('mock-link')).toHaveAttribute('href', '/kb/kb-1');
  });

  it('should handle edit action for folder', () => {
    renderWithProviders(<GridCard item={mockFolder} {...mockProps} />);

    const editButton = screen.getByTestId('edit-button');
    fireEvent.click(editButton);

    expect(mockProps.onEdit).toHaveBeenCalledWith(mockFolder);
  });

  it('should handle delete action', () => {
    renderWithProviders(<GridCard item={mockFolder} {...mockProps} />);

    const deleteButton = screen.getByTestId('delete-button');
    fireEvent.click(deleteButton);

    expect(mockProps.onDelete).toHaveBeenCalledWith(mockFolder);
  });

  it('should handle download action for uploaded document', () => {
    renderWithProviders(<GridCard item={mockDocument} {...mockProps} />);

    const downloadButton = screen.getByTestId('download-button');
    fireEvent.click(downloadButton);

    expect(mockProps.onDownload).toHaveBeenCalledWith(mockDocument);
  });

  it('should not show download button for non-uploaded documents', () => {
    const nonUploadedDoc: DocumentFile = {
      ...mockDocument,
      metadata: {
        ...mockDocument.metadata,
        uploadStatus: FILE_UPLOAD_STATUS.failed,
      },
    };

    renderWithProviders(<GridCard item={nonUploadedDoc} {...mockProps} />);

    expect(screen.queryByTestId('download-button')).not.toBeInTheDocument();
  });

  it('should not show edit button for documents', () => {
    renderWithProviders(<GridCard item={mockDocument} {...mockProps} />);

    expect(screen.queryByTestId('edit-button')).not.toBeInTheDocument();
  });

  it('not show icon access for folder', () => {
    renderWithProviders(<GridCard item={mockFolder} {...mockProps} />);

    expect(screen.queryByTestId('access-icon')).not.toBeInTheDocument();
  });

  describe('Image handling', () => {
    const imageDocument: DocumentFile = {
      ...mockDocument,
      metadata: {
        ...mockDocument.metadata,
        contentType: 'image/jpeg',
        downloadUrl: 'https://example.com/test.jpg',
      },
    };

    it('should render image correctly', () => {
      renderWithProviders(<GridCard item={imageDocument} {...mockProps} />);

      const image = screen.getByTestId('illustration-image');
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute('src', 'https://example.com/test.jpg');
    });

    it('should refresh image URL when image fails to load', async () => {
      const newImageUrl = 'https://example.com/new-test.jpg';
      const mockResponse: FileLink = {
        status: 'success',
        error: '',
        downloadUrl: newImageUrl,
        downloadUrlExpires: new Date().toISOString(),
      };
      vi.mocked(DocumentAPI.getLink).mockResolvedValue(mockResponse);

      renderWithProviders(<GridCard item={imageDocument} {...mockProps} />);

      const image = screen.getByTestId('illustration-image');

      await act(async () => {
        fireEvent.error(image);
      });

      // Wait for the state update to complete
      await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 0));
      });

      expect(DocumentAPI.getLink).toHaveBeenCalledWith('doc-1');
      expect(image).toHaveAttribute('src', newImageUrl);
    });

    it('should not refresh image URL if already loading', async () => {
      let resolvePromise: (value: FileLink) => void;
      const mockResponse: FileLink = {
        status: 'success',
        error: '',
        downloadUrl: 'https://example.com/new-test.jpg',
        downloadUrlExpires: new Date().toISOString(),
      };

      vi.mocked(DocumentAPI.getLink).mockImplementation(
        () =>
          new Promise((resolve) => {
            resolvePromise = resolve;
          })
      );

      renderWithProviders(<GridCard item={imageDocument} {...mockProps} />);

      const image = screen.getByTestId('illustration-image');

      // Trigger first error
      await act(async () => {
        fireEvent.error(image);
      });

      // Trigger second error immediately
      await act(async () => {
        fireEvent.error(image);
      });

      // Resolve the pending promise
      await act(async () => {
        resolvePromise!(mockResponse);
        await new Promise((resolve) => setTimeout(resolve, 0));
      });

      expect(DocumentAPI.getLink).toHaveBeenCalledTimes(1);
      expect(image).toHaveAttribute('src', mockResponse.downloadUrl);
    });

    it('should handle image refresh error gracefully', async () => {
      // Spy on console.error to prevent it from cluttering the test output
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      vi.mocked(DocumentAPI.getLink).mockRejectedValue(new Error('Failed to get link'));

      renderWithProviders(<GridCard item={imageDocument} {...mockProps} />);

      const image = screen.getByTestId('illustration-image');

      await act(async () => {
        fireEvent.error(image);
      });

      // Wait for the state update to complete
      await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 0));
      });

      expect(DocumentAPI.getLink).toHaveBeenCalledWith('doc-1');
      expect(image).toHaveAttribute('src', 'https://example.com/test.jpg');

      // Verify that the error was logged
      expect(consoleSpy).toHaveBeenCalled();

      // Clean up
      consoleSpy.mockRestore();
    });
  });
});
