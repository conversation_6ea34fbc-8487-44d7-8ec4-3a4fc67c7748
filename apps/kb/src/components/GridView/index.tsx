import { Flex, Grid, Group, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useEffect, useRef, useState } from 'react';

import type { Explorer, Folder } from '@/types';
import { getGridColWidth } from '@/utils';
import KBCreateModal from '../KBCreateModal';
import { ExportJobButton } from '../KBExport';
import GridCard from './GridCard';

interface GridViewProps {
  folders: Folder[];
  explorers: Explorer[];
  onFetch?: () => void;
  isFolderLayout?: boolean;
  isLayoutChange?: boolean;
  onEdit: (item: Folder | Explorer) => void;
  onDelete: (item: Folder | Explorer) => void;
  onDownload: (item: Folder | Explorer) => void;
}

const BASE_GRID_CARD_WIDTH = 200;
const BASE_GRID_COLUMN_PADDING = 8;

const useStyles = createStyles(() => ({
  container: {
    width: '100%',
  },
}));

const GridView: React.FC<GridViewProps> = ({
  folders,
  explorers,
  isFolderLayout = false,
  isLayoutChange,
  onFetch,
  onEdit,
  onDelete,
  onDownload,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('home');
  const [colMaxWidth, setColMaxWidth] = useState<number>();
  const gridRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (gridRef.current) {
      setColMaxWidth(
        getGridColWidth(gridRef.current.offsetWidth, BASE_GRID_CARD_WIDTH, BASE_GRID_COLUMN_PADDING)
      );
    }
  }, [gridRef.current, isLayoutChange]);

  return isFolderLayout ? (
    <Flex className={classes.container} direction='column' gap={rem(30)}>
      {folders.length ? (
        <Flex direction='column' gap={rem(20)}>
          <Title order={4} c='decaNavy.5'>
            {t('grid.folders')}
          </Title>
          <Grid justify='flex-start' ref={gridRef} w={'100%'}>
            {folders.map((item, index) => (
              <Grid.Col
                key={`${item.id}-${index}`}
                span='content'
                style={{ width: colMaxWidth ? rem(colMaxWidth) : rem(BASE_GRID_CARD_WIDTH) }}
              >
                <GridCard item={item} onEdit={onEdit} onDelete={onDelete} onDownload={onDownload} />
              </Grid.Col>
            ))}
          </Grid>
        </Flex>
      ) : null}
      {explorers.length ? (
        <Flex direction='column' gap={rem(20)}>
          <Title order={4} c='decaNavy.5'>
            {t('grid.knowledgeBases')}
          </Title>
          <Grid justify='flex-start' ref={gridRef} w={'100%'}>
            {explorers.map((item, index) => (
              <Grid.Col
                key={`${item.id}-${index}`}
                span='content'
                style={{ width: colMaxWidth ? rem(colMaxWidth) : rem(BASE_GRID_CARD_WIDTH) }}
              >
                <GridCard item={item} onEdit={onEdit} onDelete={onDelete} onDownload={onDownload} />
              </Grid.Col>
            ))}
          </Grid>
        </Flex>
      ) : null}
    </Flex>
  ) : (
    <Flex className={classes.container} direction='column' gap={rem(32)}>
      <Flex justify='space-between' align='center' w={'100%'}>
        <Title order={4} c='decaNavy.5'>
          {t('grid.title')}
        </Title>
        <Group gap={rem(12)}>
          <ExportJobButton onSuccess={onFetch} />
          <KBCreateModal isNewVersion onClose={onFetch} />
        </Group>
      </Flex>
      <Grid justify='flex-start' ref={gridRef} w={'100%'}>
        {[...folders, ...explorers].map((item, index) => (
          <Grid.Col
            key={`${item.id}-${index}`}
            span='content'
            style={{ width: colMaxWidth ? rem(colMaxWidth) : rem(BASE_GRID_CARD_WIDTH) }}
          >
            <GridCard item={item} onEdit={onEdit} onDelete={onDelete} onDownload={onDownload} />
          </Grid.Col>
        ))}
      </Grid>
    </Flex>
  );
};

export default GridView;
