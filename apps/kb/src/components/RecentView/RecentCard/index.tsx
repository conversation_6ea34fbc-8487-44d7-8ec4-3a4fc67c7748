import { Box, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';
import { Link } from 'react-router-dom';

import { useExportJob } from '@/components/KBExport';
import { KBMenu } from '@/components/common';
import { useFolderContext, useKnowledgeBaseContext } from '@/contexts';
import type { Explorer, Folder, Recent } from '@/types';

interface RecentCardProps {
  item: Recent;
  icon: React.ReactNode;
  onEdit: (item: Folder | Explorer) => void;
  onDelete: (item: Folder | Explorer) => void;
}

const useStyles = createStyles((theme) => ({
  card: {
    minWidth: rem(297),
    width: '100%',
    padding: rem(25),
    borderRadius: rem(10),
    backgroundColor: theme.white,
    color: theme.colors.decaNavy[5],
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    '&:hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
  },
  link: {
    textDecoration: 'none',
    color: theme.colors.decaNavy[5],
    '&:visited': {
      color: theme.colors.decaNavy[5],
    },
  },
}));

const RecentCard: React.FC<RecentCardProps> = ({ item, icon, onEdit, onDelete }) => {
  const { classes } = useStyles();
  const { t } = useTranslate(['home', 'export']);
  const { getFolder } = useFolderContext();
  const { getKnowledgeBase } = useKnowledgeBaseContext();
  const { exportJob } = useExportJob();

  const onAction = useCallback(async (cb: (data: Folder | Explorer) => void) => {
    if (item.itemType === 'folder') {
      const response = await getFolder(item.itemId);

      if (response) {
        cb(response.data);
      }
    }
    if (item.itemType === 'knowledgebase') {
      const response = await getKnowledgeBase(item.itemId);

      if (response) {
        cb(response.data);
      }
    }
  }, []);

  const onExport = useCallback(async () => {
    if (item.itemType === 'folder') {
      await exportJob({
        folderPaths: [item.itemId],
        kbIds: [],
      });
    } else if (item.itemType === 'knowledgebase') {
      await exportJob({
        folderPaths: [],
        kbIds: [item.itemId],
      });
    }
  }, [item, exportJob]);

  const exportTitle =
    item.itemType === 'folder'
      ? t('export:menu.exportFolder')
      : t('export:menu.exportKnowledgeBase');

  return (
    <Box className={classes.card}>
      <Box w={'85%'}>
        <Link
          className={classes.link}
          to={item.itemType === 'folder' ? `/kb/folder/${item.itemId}` : `/kb/${item.itemId}`}
        >
          <Flex align='center' gap={rem(12)}>
            <Box>{icon}</Box>
            <Text color='decaNavy.5' truncate>
              {item.itemName}
            </Text>
          </Flex>
        </Link>
      </Box>
      <KBMenu
        onEdit={() => onAction(onEdit)}
        onDelete={() => onAction(onDelete)}
        onExport={onExport}
        exportTitle={exportTitle}
      />
    </Box>
  );
};

export default RecentCard;
