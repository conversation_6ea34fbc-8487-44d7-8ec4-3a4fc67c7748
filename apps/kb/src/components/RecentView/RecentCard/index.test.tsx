import type { Explorer, Folder, Recent } from '@/types';
import { renderWithMantine } from '@/utils/unitTest';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import RecentCard from './index';

// Test constants
const MOCK_FOLDER_ITEM: Recent = {
  id: 'folder-1',
  itemType: 'folder',
  itemId: 'folder-123',
  itemName: 'Test Folder',
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-02'),
};

const MOCK_KB_ITEM: Recent = {
  id: 'kb-1',
  itemType: 'knowledgebase',
  itemId: 'kb-456',
  itemName: 'Test Knowledge Base',
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-02'),
};

const MOCK_FOLDER_DATA: Folder = {
  id: 'folder-123',
  name: 'Test Folder',
  parentDirId: 'parent-1',
  path: '/test-folder',
  count: 0,
  childFolderCount: 0,
  childKbCount: 0,
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-02'),
  createdBy: 'user-1',
};

const MOCK_KB_DATA: Explorer = {
  id: 'kb-456',
  name: 'Test Knowledge Base',
  description: 'Test KB description',
  baseType: 'article' as const,
  type: 'article' as const,
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-02'),
};

// Mock functions
const mockOnEdit = vi.fn();
const mockOnDelete = vi.fn();
const mockGetFolder = vi.fn();
const mockGetKnowledgeBase = vi.fn();
const mockExportJob = vi.fn();

vi.mock('@/contexts', () => ({
  useFolderContext: () => ({
    getFolder: mockGetFolder,
  }),
  useKnowledgeBaseContext: () => ({
    getKnowledgeBase: mockGetKnowledgeBase,
  }),
}));

vi.mock('@/components/KBExport', () => ({
  useExportJob: () => ({
    exportJob: mockExportJob,
  }),
}));

// react-router-dom is mocked globally in unitTest.tsx

// Mock KBMenu component
vi.mock('@/components/common', () => ({
  KBMenu: ({ onEdit, onDelete, onExport, exportTitle }) => (
    <div data-testid='kb-menu'>
      <button type='button' data-testid='menu-edit' onClick={onEdit}>
        Edit
      </button>
      <button type='button' data-testid='menu-delete' onClick={onDelete}>
        Delete
      </button>
      <button type='button' data-testid='menu-export' onClick={onExport}>
        {exportTitle}
      </button>
    </div>
  ),
}));

describe('RecentCard', () => {
  const mockIcon = <div data-testid='mock-icon'>Icon</div>;

  beforeEach(() => {
    vi.resetAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render card with folder item correctly', () => {
      renderWithMantine(
        <RecentCard
          item={MOCK_FOLDER_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      expect(screen.getByText('Test Folder')).toBeInTheDocument();
      expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
      expect(screen.getByTestId('kb-menu')).toBeInTheDocument();
    });

    it('should render card with knowledge base item correctly', () => {
      renderWithMantine(
        <RecentCard
          item={MOCK_KB_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      expect(screen.getByText('Test Knowledge Base')).toBeInTheDocument();
      expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
      expect(screen.getByTestId('kb-menu')).toBeInTheDocument();
    });

    it('should render correct link for folder item', () => {
      renderWithMantine(
        <RecentCard
          item={MOCK_FOLDER_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', '/kb/folder/folder-123');
    });

    it('should render correct link for knowledge base item', () => {
      renderWithMantine(
        <RecentCard
          item={MOCK_KB_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', '/kb/kb-456');
    });
  });

  describe('Export Title Logic', () => {
    it('should set correct export title for folder', () => {
      renderWithMantine(
        <RecentCard
          item={MOCK_FOLDER_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      expect(screen.getByText('menu.exportFolder')).toBeInTheDocument();
    });

    it('should set correct export title for knowledge base', () => {
      renderWithMantine(
        <RecentCard
          item={MOCK_KB_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      expect(screen.getByText('menu.exportKnowledgeBase')).toBeInTheDocument();
    });
  });

  describe('Edit Action', () => {
    it('should handle edit action for folder item', async () => {
      mockGetFolder.mockResolvedValue({ data: MOCK_FOLDER_DATA });

      renderWithMantine(
        <RecentCard
          item={MOCK_FOLDER_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.click(screen.getByTestId('menu-edit'));

      await waitFor(() => {
        expect(mockGetFolder).toHaveBeenCalledWith('folder-123');
        expect(mockOnEdit).toHaveBeenCalledWith(MOCK_FOLDER_DATA);
      });
    });

    it('should handle edit action for knowledge base item', async () => {
      mockGetKnowledgeBase.mockResolvedValue({ data: MOCK_KB_DATA });

      renderWithMantine(
        <RecentCard
          item={MOCK_KB_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.click(screen.getByTestId('menu-edit'));

      await waitFor(() => {
        expect(mockGetKnowledgeBase).toHaveBeenCalledWith('kb-456');
        expect(mockOnEdit).toHaveBeenCalledWith(MOCK_KB_DATA);
      });
    });

    it('should not call onEdit callback when folder API returns null', async () => {
      mockGetFolder.mockResolvedValue(null);

      renderWithMantine(
        <RecentCard
          item={MOCK_FOLDER_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.click(screen.getByTestId('menu-edit'));

      await waitFor(() => {
        expect(mockGetFolder).toHaveBeenCalledWith('folder-123');
        expect(mockOnEdit).not.toHaveBeenCalled();
      });
    });

    it('should not call onEdit callback when knowledge base API returns null', async () => {
      mockGetKnowledgeBase.mockResolvedValue(null);

      renderWithMantine(
        <RecentCard
          item={MOCK_KB_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.click(screen.getByTestId('menu-edit'));

      await waitFor(() => {
        expect(mockGetKnowledgeBase).toHaveBeenCalledWith('kb-456');
        expect(mockOnEdit).not.toHaveBeenCalled();
      });
    });
  });

  describe('Delete Action', () => {
    it('should handle delete action for folder item', async () => {
      mockGetFolder.mockResolvedValue({ data: MOCK_FOLDER_DATA });

      renderWithMantine(
        <RecentCard
          item={MOCK_FOLDER_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.click(screen.getByTestId('menu-delete'));

      await waitFor(() => {
        expect(mockGetFolder).toHaveBeenCalledWith('folder-123');
        expect(mockOnDelete).toHaveBeenCalledWith(MOCK_FOLDER_DATA);
      });
    });

    it('should handle delete action for knowledge base item', async () => {
      mockGetKnowledgeBase.mockResolvedValue({ data: MOCK_KB_DATA });

      renderWithMantine(
        <RecentCard
          item={MOCK_KB_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.click(screen.getByTestId('menu-delete'));

      await waitFor(() => {
        expect(mockGetKnowledgeBase).toHaveBeenCalledWith('kb-456');
        expect(mockOnDelete).toHaveBeenCalledWith(MOCK_KB_DATA);
      });
    });

    it('should not call onDelete callback when folder API returns null', async () => {
      mockGetFolder.mockResolvedValue(null);

      renderWithMantine(
        <RecentCard
          item={MOCK_FOLDER_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.click(screen.getByTestId('menu-delete'));

      await waitFor(() => {
        expect(mockGetFolder).toHaveBeenCalledWith('folder-123');
        expect(mockOnDelete).not.toHaveBeenCalled();
      });
    });

    it('should not call onDelete callback when knowledge base API returns null', async () => {
      mockGetKnowledgeBase.mockResolvedValue(null);

      renderWithMantine(
        <RecentCard
          item={MOCK_KB_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.click(screen.getByTestId('menu-delete'));

      await waitFor(() => {
        expect(mockGetKnowledgeBase).toHaveBeenCalledWith('kb-456');
        expect(mockOnDelete).not.toHaveBeenCalled();
      });
    });
  });

  describe('Export Action', () => {
    it('should handle export action for folder item', async () => {
      renderWithMantine(
        <RecentCard
          item={MOCK_FOLDER_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.click(screen.getByTestId('menu-export'));

      await waitFor(() => {
        expect(mockExportJob).toHaveBeenCalledWith({
          folderPaths: ['folder-123'],
          kbIds: [],
        });
      });
    });

    it('should handle export action for knowledge base item', async () => {
      renderWithMantine(
        <RecentCard
          item={MOCK_KB_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.click(screen.getByTestId('menu-export'));

      await waitFor(() => {
        expect(mockExportJob).toHaveBeenCalledWith({
          folderPaths: [],
          kbIds: ['kb-456'],
        });
      });
    });
  });

  describe('Translation', () => {
    it('should render export title correctly for folder', () => {
      renderWithMantine(
        <RecentCard
          item={MOCK_FOLDER_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      // Based on the mock, the translation key should be rendered as-is
      expect(screen.getByText('menu.exportFolder')).toBeInTheDocument();
    });

    it('should render export title correctly for knowledge base', () => {
      renderWithMantine(
        <RecentCard
          item={MOCK_KB_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      expect(screen.getByText('menu.exportKnowledgeBase')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle API response without data for folder operations', async () => {
      mockGetFolder.mockResolvedValue(null);

      renderWithMantine(
        <RecentCard
          item={MOCK_FOLDER_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.click(screen.getByTestId('menu-edit'));

      await waitFor(() => {
        expect(mockGetFolder).toHaveBeenCalledWith('folder-123');
        expect(mockOnEdit).not.toHaveBeenCalled();
      });
    });

    it('should handle API response without data for knowledge base operations', async () => {
      mockGetKnowledgeBase.mockResolvedValue(null);

      renderWithMantine(
        <RecentCard
          item={MOCK_KB_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      fireEvent.click(screen.getByTestId('menu-edit'));

      await waitFor(() => {
        expect(mockGetKnowledgeBase).toHaveBeenCalledWith('kb-456');
        expect(mockOnEdit).not.toHaveBeenCalled();
      });
    });
  });

  describe('Component Structure', () => {
    it('should have correct CSS classes and structure', () => {
      renderWithMantine(
        <RecentCard
          item={MOCK_FOLDER_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      // Check main container structure
      expect(screen.getByRole('link')).toBeInTheDocument();
      expect(screen.getByTestId('kb-menu')).toBeInTheDocument();
      expect(screen.getByTestId('mock-icon')).toBeInTheDocument();
    });

    it('should render item name with truncate style', () => {
      const longNameItem = {
        ...MOCK_FOLDER_ITEM,
        itemName: 'This is a very long folder name that should be truncated when displayed',
      };

      renderWithMantine(
        <RecentCard
          item={longNameItem}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      expect(screen.getByText(longNameItem.itemName)).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper link accessibility', () => {
      renderWithMantine(
        <RecentCard
          item={MOCK_FOLDER_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', '/kb/folder/folder-123');
      expect(link).toBeVisible();
    });

    it('should have accessible menu buttons', () => {
      renderWithMantine(
        <RecentCard
          item={MOCK_FOLDER_ITEM}
          icon={mockIcon}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
        />
      );

      expect(screen.getByTestId('menu-edit')).toBeInTheDocument();
      expect(screen.getByTestId('menu-delete')).toBeInTheDocument();
      expect(screen.getByTestId('menu-export')).toBeInTheDocument();
    });
  });
});
