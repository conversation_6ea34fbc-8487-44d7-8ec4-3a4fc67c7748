import { Flex, Grid, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useEffect, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';

import type { Explorer, Folder, Recent } from '@/types';
import { getGridColWidth } from '@/utils';
import { SimpleIcon } from '../common';
import RecentCard from './RecentCard';

const BASE_GRID_CARD_WIDTH = 297;
const BASE_GRID_COLUMN_PADDING = 6;
const OFFSET_WIDTH = 136;

interface RecentViewProps {
  recentList: Array<Recent>;
  isLayoutChange?: boolean;
  onEdit: (item: Folder | Explorer) => void;
  onDelete: (item: Folder | Explorer) => void;
}

const useStyles = createStyles((theme) => ({
  container: {
    width: '100%',
    backgroundColor: theme.colors.decaBlue[0],
    padding: `${rem(28)} ${rem(40)}`,
    borderRadius: rem(10),
  },
  title: {
    fontWeight: 500,
    fontSize: rem(18),
    lineHeight: rem(28),
    color: theme.colors.decaNavy[5],
  },
}));

const RecentView: React.FC<RecentViewProps> = ({
  recentList,
  isLayoutChange,
  onEdit,
  onDelete,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('home');
  const [colMaxWidth, setColMaxWidth] = useState<number>();
  const gridRef = useRef<HTMLDivElement>(null);
  const location = useLocation();

  useEffect(() => {
    setColMaxWidth(
      getGridColWidth(
        gridRef?.current?.offsetWidth || window.screen.width - OFFSET_WIDTH,
        BASE_GRID_CARD_WIDTH,
        BASE_GRID_COLUMN_PADDING
      )
    );
  }, [gridRef.current, isLayoutChange, location.pathname]);

  return recentList.length ? (
    <Flex className={classes.container} direction='column' gap={rem(20)} mb={rem(37)}>
      <Title className={classes.title}>{t('recent.title')}</Title>
      <Grid justify='flex-start' ref={gridRef} w={'100%'}>
        {recentList.map((item) => (
          <Grid.Col
            key={item.id}
            span='content'
            style={{ width: colMaxWidth ? rem(colMaxWidth) : rem(BASE_GRID_CARD_WIDTH) }}
          >
            <RecentCard
              key={item.id}
              item={item}
              icon={<SimpleIcon type={item.itemType} />}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          </Grid.Col>
        ))}
      </Grid>
    </Flex>
  ) : null;
};

export default RecentView;
