import { Box, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronDown, IconChevronRight, IconFolder } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { type CSSProperties, useCallback, useEffect } from 'react';
import type { NodeApi } from 'react-arborist';

import { ROOT_PATH } from '@/constants/folder';
import type { TreeItem } from '@/types';
import { IconFolderEmpty } from '@resola-ai/ui/components/Icons';

interface TreeLeafProps<T> {
  node: NodeApi<T>;
  style: CSSProperties;
  dragHandle?: (el: HTMLDivElement | null) => void;
  onHover?: () => void;
  onClick?: () => void;
}

const useStyles = createStyles((theme) => ({
  leaf: {
    height: rem(32),
    display: 'flex',
    alignItems: 'center',
    cursor: 'pointer',
    borderRadius: rem(4),
    color: theme.colors.decaNavy[5],
    border: `${rem(1)} solid transparent`,
    '&:hover': {
      background: theme.colors.decaViolet[0],
    },
  },
  leafPrefix: {
    paddingLeft: rem(6),
    gap: rem(4),
    alignItems: 'center',
  },
  selected: {
    background: theme.colors.decaViolet[0],
    borderColor: theme.colors.decaViolet[5],
  },
  text: {
    color: theme.colors.decaNavy[5],
    fontSize: theme.fontSizes.md,
    fontWeight: 500,
  },
}));

/**
 * ThreeArrow component to show arrow icon for tree node
 * @param {NodeApi} node
 * @returns {React.FC<{ node: NodeApi }>}
 */
const TreeArrow: React.FC<{ node: NodeApi }> = ({ node }) => {
  if (!node.data.childFolderCount) return <Box component='span' w={rem(16)} display='flex' />;

  return (
    <Box component='span' display='flex'>
      {node.isOpen ? (
        <IconChevronDown strokeWidth={2.5} size={18} />
      ) : (
        <IconChevronRight strokeWidth={2.5} size={18} />
      )}
    </Box>
  );
};

/**
 * TreeLeaf component to show tree node
 * @param {TreeLeafProps} props
 * @returns {React.FC<TreeLeafProps<TreeItem>>}
 */
const TreeLeaf: React.FC<TreeLeafProps<TreeItem>> = ({
  node,
  style,
  dragHandle,
  onHover,
  onClick,
}) => {
  const { t } = useTranslate('home');
  const { classes, cx } = useStyles();

  /**
   * Click handler for tree node
   * @returns {void}
   */
  const clickHandler = useCallback(() => {
    onClick?.();
  }, []);

  /**
   * Mouse enter handler for tree node
   * @returns {void}
   */
  const mouseEnterHandler = useCallback(() => {
    onHover?.();
  }, []);

  useEffect(() => {
    if (node.data.id === ROOT_PATH) {
      node.open();
    }
  }, []);

  return (
    <Flex
      className={cx(classes.leaf, node.isSelected && classes.selected)}
      style={style}
      ref={dragHandle}
      onClick={clickHandler}
      gap={rem(8)}
      onMouseEnter={mouseEnterHandler}
    >
      <Flex className={classes.leafPrefix}>
        <TreeArrow node={node} />
        {node.data.childKbCount || node.data.childFolderCount ? (
          <IconFolder width={24} height={24} />
        ) : (
          <IconFolderEmpty />
        )}
      </Flex>
      <Text className={classes.text} truncate>
        {t(node.data.name)}
      </Text>
    </Flex>
  );
};

export default TreeLeaf;
