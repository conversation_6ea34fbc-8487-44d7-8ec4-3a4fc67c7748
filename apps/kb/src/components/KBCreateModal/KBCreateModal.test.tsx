import { MAX_DEPTH } from '@/constants/folder';
import { useKbAccessControl } from '@/hooks';
import { DEFAULT_ACCESS_CONTROL_PERMISSIONS } from '@/mocks/accessControlMock';
import { DocumentAPI } from '@/services/api/v2';
import { renderWithProviders } from '@/utils/unitTest';
import { modals } from '@mantine/modals';
import { notifications } from '@mantine/notifications';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import KBCreateModal from './index';

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
}));

// Mock components and hooks
// Let mock real modals
vi.mock('@mantine/modals', () => ({
  modals: {
    open: vi.fn(({ children: _children }) => {
      // Clean up any existing modals first
      const existingModal = document.querySelector('[data-testid="mantine-modal"]');
      if (existingModal) {
        existingModal.remove();
      }

      // Create a simple modal container without server-side rendering
      const modalRoot = document.createElement('div');
      modalRoot.setAttribute('data-testid', 'mantine-modal');
      modalRoot.setAttribute('role', 'dialog');

      // Create modal content container
      const modalContentDiv = document.createElement('div');
      modalContentDiv.setAttribute('data-testid', 'modal-content');
      modalRoot.appendChild(modalContentDiv);

      // Add a cancel button manually
      const cancelButton = document.createElement('button');
      cancelButton.setAttribute('data-testid', 'modal-cancel');
      cancelButton.textContent = 'modal.cancel';
      cancelButton.addEventListener('click', () => {
        modals.closeAll();
      });
      modalRoot.appendChild(cancelButton);

      document.body.appendChild(modalRoot);
      return modalRoot;
    }),
    closeAll: vi.fn(() => {
      const modals = document.querySelectorAll('[data-testid="mantine-modal"]');
      modals.forEach((modal) => modal.remove());
    }),
  },
}));

vi.mock('@mantine/hooks', () => ({
  useDisclosure: () => {
    const [opened, setOpened] = React.useState(false);
    return [
      opened,
      {
        open: () => setOpened(true),
        close: () => setOpened(false),
      },
    ];
  },
}));

vi.mock('@mantine/notifications', () => ({
  notifications: {
    show: vi.fn(),
  },
}));

vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    Modal: ({
      children,
      opened,
      title,
      onClose,
    }: {
      children: React.ReactNode;
      opened: boolean;
      title?: string;
      onClose?: () => void;
    }) =>
      opened ? (
        <dialog data-testid='mantine-modal' open>
          {title && <div>{title}</div>}
          <div>{children}</div>
          <button type='button' data-testid='modal-close' onClick={onClose}>
            Close
          </button>
        </dialog>
      ) : null,
    Flex: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    Text: ({ children }: { children: React.ReactNode }) => <span>{children}</span>,
    Box: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
    Group: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  };
});

vi.mock('@mantine/dropzone', () => ({
  Dropzone: ({
    onDrop,
    children,
  }: {
    onDrop: (files: File[]) => void;
    children: React.ReactNode;
  }) => (
    <button
      type='button'
      data-testid='file-dropzone'
      onClick={() => {
        const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });
        onDrop([file]);
      }}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });
          onDrop([file]);
        }
      }}
      style={{ border: 'none', background: 'none' }}
    >
      {children}
    </button>
  ),
}));

vi.mock('@/components/common/KBFolderForm', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='folder-form'>{children}</div>
  ),
}));

vi.mock('@/hooks', () => ({
  useKbAccessControl: vi.fn(() => DEFAULT_ACCESS_CONTROL_PERMISSIONS),
}));

const mockUploader = {
  uploadFiles: vi.fn(),
  validateFiles: vi.fn(),
  openUploadStatus: vi.fn(),
};

vi.mock('@/contexts/UploaderContext', () => ({
  useUploaderContext: () => mockUploader,
  UploaderContextProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

const mockDocument = {
  updateStatusDocument: vi.fn(),
  getLink: vi.fn(),
};

vi.mock('@/contexts', () => ({
  useDocumentContext: () => mockDocument,
  DocumentContextProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  UploaderContextProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

vi.mock('@/services/api/v2', () => ({
  DocumentAPI: {
    getPresignedUrl: vi.fn().mockResolvedValue({
      uploadResults: [
        {
          fileId: 'test-file',
          presignedUrl: 'https://test-url.com',
        },
      ],
    }),
  },
}));

vi.mock('@/components', () => ({
  KBBasicForm: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  KBDetailForm: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  Modal: ({
    children,
    opened,
    title,
    onClose,
  }: {
    children: React.ReactNode;
    opened: boolean;
    title?: string;
    onClose?: () => void;
  }) =>
    opened ? (
      <dialog data-testid='mantine-modal' open>
        {title && <div>{title}</div>}
        <div>{children}</div>
        <button type='button' data-testid='modal-close' onClick={onClose}>
          Close
        </button>
      </dialog>
    ) : null,
  ModalTitleWithCloseButton: ({ title, onClose }: { title: string; onClose: () => void }) => (
    <div>
      <span>{title}</span>
      <button type='button' onClick={onClose}>
        Close
      </button>
    </div>
  ),
  KBDocumentFilesForm: ({
    onSubmitted,
    files,
  }: {
    onSubmitted: (files: File[], extraData: any[]) => void;
    files: File[];
  }) => {
    // Immediately submit when files are present
    if (files.length > 0) {
      onSubmitted(
        files,
        files.map((file) => ({ name: file.name, accessLevel: 'public' }))
      );
    }
    return <div data-testid='document-form'>Document Form</div>;
  },
}));

vi.mock('@resola-ai/ui', () => ({
  DecaButton: ({ children, onClick }: { children: React.ReactNode; onClick?: () => void }) => (
    <button type='button' onClick={onClick}>
      {children}
    </button>
  ),
}));

describe('KBCreateModal', () => {
  const mockProps = {
    onClose: vi.fn(),
    parentDirId: 'test-folder',
    folderLevel: 0,
  };

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(useKbAccessControl).mockReturnValue(DEFAULT_ACCESS_CONTROL_PERMISSIONS);
    vi.mocked(DocumentAPI.getPresignedUrl).mockClear();
    vi.mocked(notifications.show).mockClear();
    // Clean up any existing modals before each test
    const modals = document.querySelectorAll('[data-testid="mantine-modal"]');
    modals.forEach((modal) => modal.remove());
  });

  it('should render create button correctly', () => {
    renderWithProviders(<KBCreateModal {...mockProps} />);

    expect(screen.getByText('createNewKnowledgeBase')).toBeInTheDocument();
  });

  it('should not render when user has no permissions', () => {
    vi.mocked(useKbAccessControl).mockReturnValue({
      ...DEFAULT_ACCESS_CONTROL_PERMISSIONS,
      permKb: { ...DEFAULT_ACCESS_CONTROL_PERMISSIONS.permKb, canCreate: false },
      permFolder: { ...DEFAULT_ACCESS_CONTROL_PERMISSIONS.permFolder, canCreate: false },
      permDocument: { ...DEFAULT_ACCESS_CONTROL_PERMISSIONS.permDocument, canCreate: false },
    });

    renderWithProviders(<KBCreateModal {...mockProps} />);

    expect(screen.queryByText('createNewKnowledgeBase')).not.toBeInTheDocument();
  });

  it('should close modal when cancel button is clicked', async () => {
    renderWithProviders(<KBCreateModal {...mockProps} isNewVersion />);

    const createButton = screen.getByText('createNewKnowledgeBase');
    fireEvent.click(createButton);

    // Wait for modal to be rendered
    await waitFor(() => {
      const modal = screen.getByTestId('mantine-modal');
      expect(modal).toBeInTheDocument();
    });

    // Find cancel button within modal using data-testid
    const cancelButton = await screen.findByTestId('modal-cancel');

    // Ensure modals.closeAll is not called before click
    expect(vi.mocked(modals.closeAll)).not.toHaveBeenCalled();

    // Click the cancel button
    fireEvent.click(cancelButton);

    // Verify modals.closeAll was called
    expect(vi.mocked(modals.closeAll)).toHaveBeenCalled();
  });

  it('should respect MAX_DEPTH for folder creation', () => {
    renderWithProviders(<KBCreateModal {...mockProps} isNewVersion folderLevel={MAX_DEPTH + 1} />);

    const createButton = screen.getByText('createNewKnowledgeBase');
    fireEvent.click(createButton);

    expect(screen.queryByText('modal.createFolder')).not.toBeInTheDocument();
  });

  describe('Modal Behavior', () => {
    it('should open modal directly when isNewVersion is false', async () => {
      renderWithProviders(<KBCreateModal {...mockProps} isNewVersion={false} />);

      const createButton = screen.getByText('createNewKnowledgeBase');
      fireEvent.click(createButton);

      await waitFor(() => {
        expect(screen.getByRole('dialog')).toBeInTheDocument();
      });
      expect(modals.open).not.toHaveBeenCalled();
    });

    it('should open modal through modals.open when isNewVersion is true', async () => {
      renderWithProviders(<KBCreateModal {...mockProps} isNewVersion={true} />);

      const createButton = screen.getByText('createNewKnowledgeBase');
      fireEvent.click(createButton);

      await waitFor(() => {
        expect(modals.open).toHaveBeenCalled();
      });
    });

    it('should close modal and call onClose when handleClose is triggered', async () => {
      renderWithProviders(<KBCreateModal {...mockProps} isNewVersion={false} />);

      // Open the modal
      const createButton = screen.getByText('createNewKnowledgeBase');
      fireEvent.click(createButton);

      // Wait for modal to be rendered
      await waitFor(() => {
        const modal = screen.getByTestId('mantine-modal');
        expect(modal).toBeInTheDocument();
      });

      // Find and click the modal close button
      const closeButton = await screen.findByTestId('modal-close');
      fireEvent.click(closeButton);

      await waitFor(() => {
        expect(mockProps.onClose).toHaveBeenCalled();
      });
    });
  });
});
