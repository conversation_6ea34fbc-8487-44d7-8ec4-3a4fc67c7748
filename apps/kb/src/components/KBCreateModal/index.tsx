import { Flex, Text, rem } from '@mantine/core';
import { Dropzone } from '@mantine/dropzone';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { modals } from '@mantine/modals';
import { DecaButton } from '@resola-ai/ui';
import { IconBook, IconFolder, IconPlus, IconUpload } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useMemo, useState } from 'react';

import { KBBasicForm, KBDetailForm, Modal, ModalTitleWithCloseButton } from '@/components';
import { MAX_DOCUMENT_SIZE } from '@/constants/file';
import { MAX_DEPTH } from '@/constants/folder';
import { useDocumentContext } from '@/contexts';
import { useUploaderContext } from '@/contexts/UploaderContext';
import { useKbAccessControl } from '@/hooks';
import { DocumentAPI } from '@/services/api/v2';
import {
  type ExtraDataFile,
  FILE_CONTENT_TYPE,
  FILE_CONTENT_TYPE_EXTENSIONS,
  type FileChangeStatus,
  type FileContentType,
  type KnowledgeBaseType,
} from '@/types';
import { notifications } from '@mantine/notifications';
import KBDocumentFilesForm from '../common/KBDocumentFilesForm';
import KBFolderForm from '../common/KBFolderForm';

interface KBCreateModalProps {
  isNewVersion?: boolean;
  onClose?: () => void;
  parentDirId?: string;
  folderLevel?: number;
}

const useStyles = createStyles((theme) => ({
  button: {
    fontWeight: 700,
    padding: `0 ${rem(24)}`,
  },
  typeBox: {
    width: '100%',
    height: rem(74),
    color: theme.colors.decaNavy[5],
    border: `1px solid ${theme.colors.decaLight[2]}`,
    borderRadius: rem(5),
    padding: `${rem(20)} ${rem(40)}`,
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: theme.colors.decaViolet[0],
      border: `1px solid ${theme.colors.decaViolet[4]}`,
    },
  },
  modal: {
    width: rem(600),
    padding: rem(10),
    borderRadius: rem(20),
    flex: 'unset',
  },
  title: {
    color: theme.colors.decaNavy[5],
    fontSize: rem(20),
    fontWeight: 700,
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  close: {
    color: theme.colors.decaNavy[5],
    fontSize: rem(20),
  },
  icon: {
    width: rem(24),
    height: rem(24),
  },
  dropzone: {
    padding: 0,
    border: 'none',
    backgroundColor: 'transparent',
  },
}));

const KBCreateModal: React.FC<KBCreateModalProps> = ({
  isNewVersion = false,
  onClose,
  parentDirId,
  folderLevel = 0,
}) => {
  const { t } = useTranslate('kb');
  const { permKb, permFolder, permDocument } = useKbAccessControl();
  const { updateStatusDocument, getLink } = useDocumentContext();
  const { classes } = useStyles();
  const [opened, { open, close }] = useDisclosure(false);
  const [createType, setCreateType] = useState<KnowledgeBaseType>();
  const { uploadFiles, validateFiles, openUploadStatus } = useUploaderContext();
  const [files, setFiles] = useState<File[]>([]);
  const title = useMemo(() => {
    if (createType === 'document') {
      return t('modal.uploadingFiles');
    }

    return createType === 'folder' ? t('modal.createFolder') : t('modal.createKnowledgeBase');
  }, [createType]);
  const handleClose = useCallback(() => {
    close();
    onClose?.();
  }, [onClose]);

  const handleOpen = useCallback((type?: KnowledgeBaseType) => {
    modals.closeAll();
    setCreateType(type);
    open();
  }, []);

  const openCreateModal = useCallback(() => {
    if (isNewVersion) {
      modals.open({
        title: (
          <ModalTitleWithCloseButton
            title={t('modal.create')}
            onClose={() => modals.closeAll()}
            data-testid='close-button'
          />
        ),
        centered: true,
        withCloseButton: false,
        classNames: {
          content: classes.modal,
          title: classes.title,
          close: classes.close,
        },
        children: (
          <Flex w={'100%'} direction='column' gap='lg'>
            {permFolder.canCreate && folderLevel <= MAX_DEPTH ? (
              <Flex className={classes.typeBox} align='center' onClick={() => handleOpen('folder')}>
                <IconFolder className={classes.icon} />
                <Text w={'100%'} ta='center'>
                  {t('modal.createFolder')}
                </Text>
              </Flex>
            ) : null}
            {permKb.canCreate && (
              <Flex
                className={classes.typeBox}
                align='center'
                onClick={() => handleOpen('article')}
              >
                <IconBook className={classes.icon} />
                <Text w={'100%'} ta='center'>
                  {t('modal.createKnowledgeBase')}
                </Text>
              </Flex>
            )}
            {permDocument.canCreate && (
              <Dropzone
                onDrop={openModalUploadFiles}
                accept={{
                  [FILE_CONTENT_TYPE.pdf]: FILE_CONTENT_TYPE_EXTENSIONS[FILE_CONTENT_TYPE.pdf],
                  [FILE_CONTENT_TYPE.csv]: FILE_CONTENT_TYPE_EXTENSIONS[FILE_CONTENT_TYPE.csv],
                  [FILE_CONTENT_TYPE.txt]: FILE_CONTENT_TYPE_EXTENSIONS[FILE_CONTENT_TYPE.txt],
                  [FILE_CONTENT_TYPE.msword]:
                    FILE_CONTENT_TYPE_EXTENSIONS[FILE_CONTENT_TYPE.msword],
                  [FILE_CONTENT_TYPE.document]:
                    FILE_CONTENT_TYPE_EXTENSIONS[FILE_CONTENT_TYPE.document],
                  [FILE_CONTENT_TYPE.markdown]:
                    FILE_CONTENT_TYPE_EXTENSIONS[FILE_CONTENT_TYPE.markdown],
                  [FILE_CONTENT_TYPE.image]: FILE_CONTENT_TYPE_EXTENSIONS[FILE_CONTENT_TYPE.image],
                }}
                className={classes.dropzone}
              >
                <Flex className={classes.typeBox} gap='xs' align='center' sx={{ height: 'unset' }}>
                  <IconUpload className={classes.icon} />
                  <Flex direction='column' w={'100%'} justify='center' align='center'>
                    <Text w={'100%'} ta='center' sx={{ whiteSpace: 'pre-line' }}>
                      {t('modal.uploadFile')}
                    </Text>
                    <Text size='xs' color='dimmed'>
                      {t('modal.uploadFileDescription', { maxSize: MAX_DOCUMENT_SIZE })}
                    </Text>
                  </Flex>
                </Flex>
              </Dropzone>
            )}
            <Flex w={'100%'} justify='flex-end'>
              <DecaButton
                variant='neutral'
                onClick={() => modals.closeAll()}
                radius={'xl'}
                size='md'
              >
                {t('modal.cancel')}
              </DecaButton>
            </Flex>
          </Flex>
        ),
      });
    } else {
      open();
    }
  }, [isNewVersion, folderLevel, parentDirId]);

  const handleErrorUpload = useCallback(
    ({ fileId, status }: FileChangeStatus) => {
      updateStatusDocument(fileId, status);
    },
    [updateStatusDocument]
  );

  const handleCompleteUpload = useCallback(
    async ({ fileId, status }: FileChangeStatus) => {
      await Promise.all([updateStatusDocument(fileId, status), getLink(fileId)]);
      onClose?.();
    },
    [onClose, updateStatusDocument]
  );

  const getPresignedUrl = useCallback(
    async (files: File[], parentDirId: string | undefined, extraDataFiles?: ExtraDataFile[]) => {
      if (files.length === 0) {
        return [];
      }

      const data = await DocumentAPI.getPresignedUrl(
        files.map((file) => {
          const extraDataFile = extraDataFiles?.find(
            (fileAccessLevel) => fileAccessLevel.name === file.name
          );
          return {
            name: file.name,
            contentLength: file.size,
            contentType: file.type as FileContentType,
            folderId: parentDirId ?? '/root',
            accessLevel: extraDataFile?.accessLevel || 'public',
          };
        })
      );

      return data?.uploadResults ?? [];
    },
    []
  );

  const onUploadDocument = useCallback(
    async (files: File[], extraDataFiles: ExtraDataFile[]) => {
      // validate file size
      if (!files.length) {
        return;
      }

      const error = validateFiles(files, { maxSize: MAX_DOCUMENT_SIZE });
      if (error) {
        // show toast
        notifications.show({
          message: t(error.message, { size: error.expandData?.maxSize }),
          color: 'red',
        });
        return;
      }

      openUploadStatus();
      await uploadFiles({
        files,
        extraDataFiles,
        getPresignedUrl: (files: File[], extraDataFiles?: ExtraDataFile[]) =>
          getPresignedUrl(files, parentDirId, extraDataFiles),
        onCompleted: handleCompleteUpload,
        onError: handleErrorUpload,
      });
      close();
    },
    [openUploadStatus, uploadFiles, handleCompleteUpload, handleErrorUpload, parentDirId]
  );

  const openModalUploadFiles = useCallback(async (inputFiles: File[] | null) => {
    if (!inputFiles || inputFiles.length === 0) {
      return;
    }
    setFiles(inputFiles);
    handleOpen('document');
  }, []);

  const KBCreatingForm = useMemo(() => {
    if (createType === 'document') {
      return <KBDocumentFilesForm onClose={close} onSubmitted={onUploadDocument} files={files} />;
    }

    if (createType === 'folder') {
      return <KBFolderForm onClose={close} onSubmitted={handleClose} parentDirId={parentDirId} />;
    }

    return <KBDetailForm onCancel={close} onSubmitted={handleClose} parentDirId={parentDirId} />;
  }, [createType, parentDirId, files, close, handleClose, onUploadDocument]);

  if (!permKb.canCreate && !permFolder.canCreate && !permDocument.canCreate) return null;

  return (
    <>
      <Modal opened={opened} onClose={handleClose} title={title}>
        {isNewVersion ? KBCreatingForm : <KBBasicForm onCancel={close} onSubmitted={handleClose} />}
      </Modal>
      <DecaButton
        className={classes.button}
        variant='primary'
        leftSection={<IconPlus className={classes.icon} />}
        onClick={openCreateModal}
        radius='sm'
        size='md'
      >
        {t('createNewKnowledgeBase')}
      </DecaButton>
    </>
  );
};

export default KBCreateModal;
