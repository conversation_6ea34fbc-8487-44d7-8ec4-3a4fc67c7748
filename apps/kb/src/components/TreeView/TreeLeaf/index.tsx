import { Box, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { usePathName } from '@resola-ai/ui/hooks';
import {
  IconChevronDown,
  IconChevronRight,
  IconEdit,
  IconFolder,
  IconTrash,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { type ContextMenuContent, useContextMenu } from 'mantine-contextmenu';
import { type CSSProperties, useCallback, useEffect } from 'react';
import type { NodeApi } from 'react-arborist';
import { useNavigate } from 'react-router-dom';

import { IconFolderEmpty, IconFolderPlus } from '@/components/Icons';
import { HOME_PATH, MAX_DEPTH, ROOT_PATH } from '@/constants/folder';
import useKbAccessControl from '@/hooks/useKbAccessControl';
import type { Folder, TreeItem } from '@/types/tree';

interface TreeLeafProps<T> {
  node: NodeApi<T>;
  style: CSSProperties;
  dragHandle?: (el: HTMLDivElement | null) => void;
  onHandleFolder: (nodeId: string, isEdit?: boolean, folder?: Folder) => void;
  onDeleteFolder: (nodeId: string) => void;
  onPrefetchChildFolders?: (nodeId: string) => void;
}

const useStyles = createStyles((theme) => ({
  leaf: {
    height: rem(40),
    display: 'flex',
    alignItems: 'center',
    padding: rem(8),
    cursor: 'pointer',
    borderRadius: rem(10),
    color: theme.colors.decaNavy[5],
    '&:hover': {
      background: theme.colors.decaViolet[0],
    },
  },
  selected: {
    background: theme.colors.decaViolet[0],
    border: `1px solid ${theme.colors.decaViolet[5]}`,
  },
  text: {
    fontSize: theme.fontSizes.md,
    fontWeight: 400,
  },
  contextMenu: {
    padding: rem(5),
    borderRadius: rem(6),
    border: `1px solid ${theme.colors.decaLight[4]}`,
    boxShadow: theme.shadows.sm,
  },
  contextMenuItem: {
    padding: rem(10),
    cursor: 'pointer',
    color: theme.colors.decaNavy[5],
    fontWeight: 500,
    '& .mantine-Text-root': {
      fontSize: rem(14),
    },
    '&:hover': {
      backgroundColor: theme.colors.decaLight[0],
    },
  },
}));

const TreeArrow: React.FC<{ node: NodeApi }> = ({ node }) => {
  if (!node.data.childFolderCount) return <Box component='span' w={rem(18)} />;

  return (
    <Box component='span'>
      {node.isOpen ? <IconChevronDown size={18} /> : <IconChevronRight size={18} />}
    </Box>
  );
};

const TreeLeaf: React.FC<TreeLeafProps<TreeItem>> = ({
  node,
  style,
  dragHandle,
  onHandleFolder,
  onDeleteFolder,
  onPrefetchChildFolders,
}) => {
  const { t } = useTranslate('home');
  const { permFolder } = useKbAccessControl();
  const { classes, cx } = useStyles();
  const { showContextMenu } = useContextMenu();
  const navigate = useNavigate();
  const pathName = usePathName();

  const onHover = useCallback(() => {
    if (
      node.data.childFolderCount &&
      node.level < MAX_DEPTH &&
      !node.data.children?.length &&
      onPrefetchChildFolders
    ) {
      onPrefetchChildFolders(node.data.id);
    }
  }, [node]);

  useEffect(() => {
    if (node.data.id === ROOT_PATH) {
      node.open();
    }
  }, []);

  const leafContextMenu: ContextMenuContent =
    node.data.id !== ROOT_PATH
      ? [
          ...(permFolder.canUpdate
            ? [
                {
                  key: 'edit',
                  icon: <IconEdit size={24} />,
                  title: t('tree.edit'),
                  onClick: () => onHandleFolder(node.data.id, true, node.data as Folder),
                },
              ]
            : []),
          ...(permFolder.canDelete
            ? [
                {
                  key: 'delete',
                  icon: <IconTrash size={24} />,
                  title: t('tree.delete'),
                  onClick: () => onDeleteFolder(node.data.id),
                },
              ]
            : []),
        ]
      : [];

  const contextMenu: ContextMenuContent =
    node.level < MAX_DEPTH
      ? [
          ...(permFolder.canCreate
            ? [
                {
                  key: 'add',
                  icon: <IconFolderPlus width={24} height={24} />,
                  title: t('tree.add'),
                  onClick: () => onHandleFolder(node.data.id, false, node.data as Folder),
                },
              ]
            : []),
          ...leafContextMenu,
        ]
      : leafContextMenu;

  return (
    <Flex
      className={cx(classes.leaf, node.isSelected && classes.selected)}
      style={style}
      ref={dragHandle}
      onClick={() => {
        if (node.data.id === ROOT_PATH) {
          node.toggle();
          if (pathName !== HOME_PATH) {
            navigate(`${HOME_PATH}/`);
          }
        } else {
          node.toggle();
          navigate(`/kb/folder/${node.data.id}`);
        }
      }}
      gap={rem(10)}
      onContextMenu={
        permFolder.canUpdate || permFolder.canCreate || permFolder.canDelete
          ? showContextMenu(contextMenu, {
              classNames: {
                root: classes.contextMenu,
                item: classes.contextMenuItem,
              },
            })
          : undefined
      }
      onMouseEnter={onHover}
    >
      <Flex gap={rem(5)} align='center'>
        <TreeArrow node={node} />
        {node.data.childKbCount || node.data.childFolderCount || node.data.id === ROOT_PATH ? (
          <IconFolder size={24} />
        ) : (
          <IconFolderEmpty width={24} height={24} />
        )}
      </Flex>
      <Text className={classes.text} truncate>
        {t(node.data.name)}
      </Text>
    </Flex>
  );
};

export default TreeLeaf;
