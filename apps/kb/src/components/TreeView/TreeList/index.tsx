import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { ContextMenuProvider } from 'mantine-contextmenu';
import { Tree } from 'react-arborist';
import type { TreeProps } from 'react-arborist/dist/module/types/tree-props';

import { FOLDER_TREE_WIDTH } from '@/constants/ui';
import type { Folder, TreeItem, Trees } from '@/types/tree';
import TreeLeaf from '../TreeLeaf';

interface TreeListProps extends TreeProps<TreeItem> {
  tree?: Trees;
  onHandleFolder: (nodeId: string, isEdit?: boolean, folder?: Folder) => void;
  onDeleteFolder: (nodeId: string) => void;
  onPrefetchChildFolders?: (nodeId: string) => void;
  treeHeight: number;
}

const useStyles = createStyles((theme) => ({
  tree: {
    scrollbarGutter: 'stable',
    '&::-webkit-scrollbar': {
      width: rem(8),
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
    },
    '&::-webkit-scrollbar-track': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
    },
    ':hover': {
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: theme.colors.decaLight[5],
      },
    },
  },
}));

const TreeList: React.FC<TreeListProps> = (props) => {
  const { classes } = useStyles();
  const { tree, onHandleFolder, onDeleteFolder, onPrefetchChildFolders, treeHeight } = props;
  return (
    <ContextMenuProvider>
      <Tree
        className={classes.tree}
        data={tree}
        openByDefault={false}
        rowHeight={50}
        width={rem(FOLDER_TREE_WIDTH)}
        height={treeHeight}
        paddingBottom={10}
        paddingTop={10}
        disableMultiSelection
        {...props}
      >
        {({ node, style, dragHandle }) => (
          <TreeLeaf
            node={node}
            style={style}
            dragHandle={dragHandle}
            onHandleFolder={onHandleFolder}
            onDeleteFolder={onDeleteFolder}
            onPrefetchChildFolders={onPrefetchChildFolders}
          />
        )}
      </Tree>
    </ContextMenuProvider>
  );
};

export default TreeList;
