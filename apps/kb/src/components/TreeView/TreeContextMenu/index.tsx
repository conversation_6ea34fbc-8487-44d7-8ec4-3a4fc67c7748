import { Modal } from '@/components';
import { KBFolderForm } from '@/components/common';
import { ROOT_PATH } from '@/constants/folder';
import type { Folder } from '@/types';
import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';

interface TreeContextMenuProps {
  opened: boolean;
  onClose: () => void;
  parentDirId?: string;
  isEdit?: boolean;
  folder?: Folder;
}

const useStyles = createStyles((theme) => ({
  content: {
    width: rem(600),
    padding: rem(10),
    borderRadius: rem(20),
    flex: 'unset',
  },
  title: {
    color: theme.colors.decaNavy[5],
    fontSize: rem(20),
    fontWeight: 700,
  },
  close: {
    color: theme.colors.decaNavy[5],
    fontSize: rem(20),
  },
  header: {
    marginBottom: rem(20),
  },
}));

const TreeContextMenu: React.FC<TreeContextMenuProps> = ({
  opened,
  onClose,
  parentDirId = ROOT_PATH,
  isEdit = false,
  folder,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('kb');

  return (
    <Modal
      className={classes.content}
      opened={opened}
      onClose={onClose}
      title={isEdit ? t('modal.editFolder') : t('modal.createFolder')}
    >
      <KBFolderForm
        onClose={onClose}
        onSubmitted={onClose}
        parentDirId={parentDirId}
        isEdit={isEdit}
        folder={folder}
      />
    </Modal>
  );
};

export default TreeContextMenu;
