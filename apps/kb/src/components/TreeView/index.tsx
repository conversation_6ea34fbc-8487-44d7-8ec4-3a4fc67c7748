import { Box, Drawer, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { IconArrowBarLeft, IconArrowBarRight, IconSubtask } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import unionWith from 'lodash/unionWith';
import { useCallback, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { HOME_PATH, ROOT_PATH } from '@/constants/folder';
import { NAVBAR_WIDTH } from '@/constants/ui';
import { useAppContext, useFolderContext } from '@/contexts';
import type { Folder } from '@/types';
import { convertListToTree } from '@/utils/tree';
import TreeContextMenu from './TreeContextMenu';
import TreeList from './TreeList';

interface TreeViewProps {
  isExpanded?: boolean;
  onExpand?: () => void;
  onClose?: () => void;
  onRefetch?: () => void;
}

const HEIGHT_OF_HEADER = 120;
const DRAWER_HEIGHT_OFFSET = 305;

const useStyles = createStyles((theme) => ({
  container: {
    width: rem(320),
    height: rem('80vh'),
  },
  iconBox: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: rem(40),
    height: rem(40),
    borderRadius: rem(20),
    background: theme.colors.decaNavy[0],
    cursor: 'pointer',
  },
  icon: {
    color: theme.colors.decaNavy[5],
    width: rem(20),
    height: rem(20),
  },
  inner: {
    left: rem(NAVBAR_WIDTH),
    top: rem(150),
    width: rem(320),
  },
  overlay: {
    display: 'none',
  },
  header: {
    display: 'none',
  },
  content: {
    paddingTop: rem(24),
    height: '90%',
    boxShadow: theme.shadows.lg,
    borderRadius: `0 ${rem(10)} ${rem(10)} 0`,
  },
  close: {
    display: 'none',
  },
  expandedContainer: {
    height: '100%',
    width: rem(300),
    padding: `${rem(12)} ${rem(16)}`,
    borderRight: `${rem(1)} solid ${theme.colors.decaLight[2]}`,
    position: 'fixed',
    backgroundColor: theme.colors.decaMono[1],
  },
  title: {
    color: theme.colors.decaNavy[5],
    fontSize: theme.fontSizes.lg,
    lineHeight: rem(24),
    fontWeight: 700,
  },
}));

const TreeView: React.FC<TreeViewProps> = ({
  isExpanded = false,
  onExpand,
  onClose,
  onRefetch,
}) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('home');
  const navigate = useNavigate();
  const location = useLocation();
  const [opened, { open, close }] = useDisclosure(false);
  const [contextMenuOpened, setContextMenuOpened] = useState(false);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [parentDirId, setParentDirId] = useState<string>();
  const [isEdit, setIsEdit] = useState(false);
  const [selectedFolder, setSelectedFolder] = useState<Folder>();
  const { getFolders, deleteFolder, shouldRefresh, setShouldRefresh } = useFolderContext();
  const { openConfirmModal, closeConfirmModal, openNoticeModal } = useAppContext();

  const fetchFolders = useCallback(
    async (parentDirId?: string, depth?: number) => {
      const response = await getFolders(parentDirId ?? ROOT_PATH, depth);

      if (response) {
        setFolders((prevFolders) =>
          unionWith(response.data ?? [], prevFolders, (a, b) => a.id === b.id).sort(
            (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          )
        );
        setShouldRefresh(false);
      }
    },
    [getFolders]
  );

  useEffect(() => {
    fetchFolders(ROOT_PATH, 1);
  }, []);

  useEffect(() => {
    if (shouldRefresh) {
      fetchFolders(selectedFolder?.id, 1);
    }
  }, [shouldRefresh]);

  const onHandleFolder = useCallback((nodeId: string, isEdit?: boolean, folder?: Folder) => {
    close();
    setParentDirId(nodeId);
    setIsEdit(isEdit ?? false);
    setSelectedFolder(folder);
    setContextMenuOpened(true);
  }, []);

  const onPrefetchChildFolders = useCallback((nodeId: string) => {
    fetchFolders(nodeId, 1);
  }, []);

  const onDeleteFolder = useCallback(
    async (nodeId: string) => {
      const folderDetail = folders.find((folder) => folder.id === nodeId);

      await deleteFolder(nodeId);
      setFolders((prevFolders) =>
        prevFolders.filter((folder) => !(folder.id === nodeId || folder.parentDirId === nodeId))
      );
      closeConfirmModal();

      if (location.pathname?.includes(nodeId)) {
        navigate(
          folderDetail?.parentDirId === ROOT_PATH
            ? HOME_PATH
            : `/kb/folder/${folderDetail?.parentDirId}`
        );
      }

      if (folderDetail?.childFolderCount || folderDetail?.childKbCount) {
        openNoticeModal({
          title: t('modal.notice.folder.childProcess.title'),
          content: t('modal.notice.folder.childProcess.content'),
          showLoader: true,
        });
      }

      onRefetch?.();
    },
    [folders, location.pathname]
  );

  return !isExpanded ? (
    <Box className={cx(opened && classes.container)} onMouseLeave={close}>
      <Box className={classes.iconBox} onMouseEnter={open} onClick={onExpand}>
        {opened ? (
          <IconArrowBarRight className={classes.icon} />
        ) : (
          <IconSubtask className={classes.icon} />
        )}
      </Box>
      <Drawer
        classNames={classes}
        opened={opened}
        onClose={() => {}}
        onMouseLeave={close}
        position='left'
      >
        <Flex direction='column' gap={rem(10)}>
          <Text className={classes.title}>{t('tree.title')}</Text>
          <TreeList
            tree={convertListToTree(folders)}
            onHandleFolder={onHandleFolder}
            onDeleteFolder={(nodeId) =>
              openConfirmModal({
                onConfirm: () => onDeleteFolder(nodeId),
                title: t('tree.confirm'),
                confirmText: t('tree.delete'),
                options: { isRemoving: true },
              })
            }
            onPrefetchChildFolders={onPrefetchChildFolders}
            treeHeight={window.innerHeight - DRAWER_HEIGHT_OFFSET}
          />
        </Flex>
      </Drawer>
      <TreeContextMenu
        opened={contextMenuOpened}
        onClose={() => setContextMenuOpened(false)}
        parentDirId={parentDirId}
        isEdit={isEdit}
        folder={selectedFolder}
      />
    </Box>
  ) : (
    <Flex className={classes.expandedContainer} direction='column' gap={rem(10)}>
      <Flex justify='space-between' align='center'>
        <Text className={classes.title}>{t('tree.title')}</Text>
        <Box className={classes.iconBox} onClick={onClose}>
          <IconArrowBarLeft className={classes.icon} />
        </Box>
      </Flex>
      <TreeList
        tree={convertListToTree(folders)}
        onHandleFolder={onHandleFolder}
        onDeleteFolder={(nodeId) =>
          openConfirmModal({
            onConfirm: () => onDeleteFolder(nodeId),
            title: t('tree.confirm'),
            confirmText: t('tree.delete'),
          })
        }
        onPrefetchChildFolders={onPrefetchChildFolders}
        treeHeight={window.innerHeight - HEIGHT_OF_HEADER}
      />
      <TreeContextMenu
        opened={contextMenuOpened}
        onClose={() => setContextMenuOpened(false)}
        parentDirId={parentDirId}
        isEdit={isEdit}
        folder={selectedFolder}
      />
    </Flex>
  );
};

export default TreeView;
