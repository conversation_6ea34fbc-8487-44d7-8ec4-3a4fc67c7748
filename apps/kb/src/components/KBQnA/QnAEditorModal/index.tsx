import { Modal, QnAManualForm } from '@/components';
import type { QnA } from '@/types/qna';
import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import { forwardRef, useImperativeHandle, useState } from 'react';

interface QnAEditorModalProps {
  className?: string;
  kbId: string;
  onSubmitted: (qna?: QnA) => void;
}
interface QnAEditorModalData {
  qna: QnA | null;
}

export type QnAEditorModalRef = HTMLDivElement & {
  open?: () => void;
  close?: () => void;
  setData: (data: QnAEditorModalData) => void;
};

const useStyles = createStyles(() => ({
  root: {
    '.mantine-Modal-content': {
      minWidth: rem(850),
    },
  },
}));

const QnAEditorModal = forwardRef(function QnAEditorModalHandler(
  { className, kbId, onSubmitted }: QnAEditorModalProps,
  ref
) {
  const { t } = useTranslate('kb');
  const { classes, cx } = useStyles();
  const [qna, setQnA] = useState<QnA | null>(null);
  const [opened, { open, close }] = useDisclosure(false);

  useImperativeHandle(ref, () => ({
    open,
    close,
    setData: ({ qna }: QnAEditorModalData) => {
      setQnA(qna);
    },
  }));

  return (
    <Modal
      opened={opened}
      onClose={close}
      className={cx(classes.root, className)}
      title={t('QnACreateModalTitle')}
    >
      <QnAManualForm
        isEdit={qna !== null}
        kbId={kbId}
        currentQnA={qna}
        onCancel={close}
        onSubmitted={onSubmitted}
      />
    </Modal>
  );
});

export default QnAEditorModal;
