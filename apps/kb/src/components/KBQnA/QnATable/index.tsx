import { Box, Table, Text, rem } from '@mantine/core';
import { createStyles, getStylesRef } from '@mantine/emotion';
import { IconEdit, IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';

import { useAppContext } from '@/contexts/AppContext';
import type { QnA, QnAStatus } from '@/types/qna';
import { formatDateTime } from '@resola-ai/ui/utils/dateTime';

interface QnATableProps {
  qna: Array<QnA>;
  onEditQnA: (qna: QnA) => void;
  onRemoveQnA: (id: string) => void;
}

const useStyles = createStyles((theme) => ({
  table: {
    fontSize: rem(13),
    color: theme.colors.decaNavy[5],
    border: `1px solid ${theme.colors.decaGrey[4]}`,
    borderRadius: rem(10),
    borderCollapse: 'unset',
    borderSpacing: 0,
    '> thead > tr > th': {
      height: rem(50),
      fontWeight: 700,
      fontSize: theme.fontSizes.sm,
      lineHeight: rem(19),
      color: theme.colors.decaNavy[5],
      [`&.${getStylesRef('infoColumn')}`]: {
        textAlign: 'center',
      },
      [`&.${getStylesRef('questionColumn')}`]: {
        paddingRight: rem(30),
        paddingLeft: rem(30),
      },
    },
    '&[data-hover] > tbody > tr:hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
    '> tbody > tr > td': {
      paddingTop: rem(20),
      paddingBottom: rem(20),
      [`&.${getStylesRef('questionColumn')}`]: {
        paddingRight: rem(30),
        paddingLeft: rem(30),
      },
    },
  },
  icon: {
    cursor: 'pointer',
  },
  questionColumn: {
    ref: getStylesRef('questionColumn'),
    maxWidth: rem(600),
  },
  infoColumn: {
    ref: getStylesRef('infoColumn'),
    textAlign: 'center',
  },
  question: {
    fontSize: theme.fontSizes.lg,
    fontWeight: 700,
    lineHeight: rem(18),
  },
  answer: {
    fontSize: theme.fontSizes.sm,
    fontWeight: 500,
    lineHeight: rem(21),
    marginTop: rem(15),
  },
  status: {
    borderRadius: rem(4),
    padding: `${rem(2)} ${rem(9)}`,
    fontSize: theme.fontSizes.sm,
    lineHeight: rem(20),
  },
  draft: {
    color: theme.colors.decaGrey[6],
    backgroundColor: theme.colors.decaGrey[1],
  },
  published: {
    color: theme.colors.decaBlue[6],
    backgroundColor: theme.colors.decaBlue[1],
  },
}));

const QnATable: React.FC<QnATableProps> = ({ qna, onEditQnA, onRemoveQnA }) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('details');
  const { openConfirmModal } = useAppContext();

  const handleDeleteQnA = useCallback(
    (id: string) => {
      return () => onRemoveQnA(id);
    },
    [onRemoveQnA]
  );

  const handleDeleteClick = useCallback(
    (qnaId: string) => {
      return () =>
        openConfirmModal({
          onConfirm: handleDeleteQnA(qnaId),
          title: t('deleteQnAConfirmTitle'),
        });
    },
    [openConfirmModal, handleDeleteQnA, t]
  );

  const status = useCallback((status: QnAStatus) => {
    switch (status) {
      case 'published':
        return (
          <Box className={cx(classes.status, classes.published)}>
            <Text>{t('published')}</Text>
          </Box>
        );
      case 'draft':
      default:
        return (
          <Box className={cx(classes.status, classes.draft)}>
            <Text>{t('draft')}</Text>
          </Box>
        );
    }
  }, []);

  const rows = useCallback(() => {
    return (qna || []).map((q) => (
      <tr key={q.id}>
        <td className={classes.questionColumn}>
          <Text className={classes.question}>{q.question}</Text>
          {q.answer && <Text className={classes.answer}>{q.answer}</Text>}
        </td>
        <td className={classes.infoColumn}>{status(q.status)}</td>
        <td className={classes.infoColumn}>
          <IconEdit
            className={classes.icon}
            onClick={() => {
              onEditQnA(q);
            }}
          />
        </td>
        <td
          className={classes.infoColumn}
          onClick={handleDeleteClick(q.id)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleDeleteClick(q.id)();
            }
          }}
        >
          <IconTrash className={classes.icon} />
        </td>
        <td className={classes.infoColumn}>{formatDateTime(q.updatedAt ?? new Date())}</td>
      </tr>
    ));
  }, [qna, status, onEditQnA, handleDeleteClick]);

  return (
    <Table highlightOnHover className={classes.table}>
      <thead>
        <tr>
          <th className={classes.questionColumn}>{t('question')}</th>
          <th className={classes.infoColumn}>{t('status')}</th>
          <th className={classes.infoColumn}>{t('edit')}</th>
          <th className={classes.infoColumn}>{t('delete')}</th>
          <th className={classes.infoColumn}>{t('updatedAt')}</th>
        </tr>
      </thead>
      <tbody>{rows()}</tbody>
    </Table>
  );
};

export default QnATable;
