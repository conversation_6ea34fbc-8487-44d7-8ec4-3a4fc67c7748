import { Box, Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type { IPaginationNextPrevious } from '@resola-ai/models';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';

import { useAppContext } from '@/contexts/AppContext';
import { useKbContext } from '@/contexts/KbContext';
import type { KnowledgeBase, KnowledgeBaseDirectionQuery } from '@/types';
import type { QnA } from '@/types/qna';
import { sortByCreatedAt } from '@/utils';
import { CustomPagination } from '../common';
import QnAEditorModal, { type QnAEditorModalRef } from './QnAEditorModal';
import QnAErrorMessage from './QnAErrorMessage';
import QnAImport from './QnAImport';
import QnATable from './QnATable';
import QnAToolbar from './QnAToolbar';

interface KBQnAProps {
  kb: KnowledgeBase;
}

const useStyles = createStyles(() => ({
  dataContainer: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(20),
  },
  actions: {
    display: 'flex',
    justifyContent: 'space-between',
    gap: rem(20),
  },
  pagination: {
    marginTop: rem(30),
    marginBottom: rem(30),
  },
}));

const KBQnA: React.FC<KBQnAProps> = ({ kb }) => {
  const { classes } = useStyles();
  const { closeConfirmModal } = useAppContext();
  const { getQnAList, removeQnA } = useKbContext();

  const navigate = useNavigate();
  const qnaEditorModalRef = useRef<QnAEditorModalRef>(null);
  const [isEditQna, setIsEditQna] = useState(false);
  const [pagination, setPagination] = useState<IPaginationNextPrevious>();
  const [qna, setQna] = useState<QnA[]>();

  const fetchQnAList = useCallback(
    async (direction: KnowledgeBaseDirectionQuery = 'backward', cursor = '') => {
      const res = await getQnAList(kb.id, direction, cursor);

      setQna(sortByCreatedAt(res?.data || []));
      setPagination(res?.pagination);
    },
    [getQnAList, kb.id]
  );

  useEffect(() => {
    fetchQnAList();
  }, []);

  const openCreateQnAModal = useCallback(() => {
    if (!qnaEditorModalRef.current) return;

    qnaEditorModalRef.current?.setData?.({ qna: null });
    qnaEditorModalRef.current?.open?.();
    setIsEditQna(false);
  }, [qnaEditorModalRef]);

  const openEditQnAModal = useCallback(
    (qna: QnA) => {
      if (!qnaEditorModalRef.current) return;

      qnaEditorModalRef.current?.setData?.({ qna });
      qnaEditorModalRef.current?.open?.();
      setIsEditQna(true);
    },
    [qnaEditorModalRef]
  );

  const onSubmitted = useCallback((qna?: QnA) => {
    if (isEditQna) {
      setQna((prev) => prev?.map((q) => (q.id === qna?.id ? qna : q)));
    } else {
      fetchQnAList();
    }
    qnaEditorModalRef.current?.close?.();
  }, []);

  const onRemoveQnA = useCallback(
    async (id: string) => {
      await removeQnA(kb.id, id);

      setQna((prev) => prev?.filter((q) => q.id !== id));
      closeConfirmModal();
    },
    [closeConfirmModal, kb.id, removeQnA]
  );

  const goToGeneratePage = useCallback(() => {
    navigate(`/kb/qna/${kb.id}/generate`);
  }, [kb, navigate]);

  if (!qna) {
    return null;
  }

  return (
    <Box>
      {qna.length ? (
        <Box className={classes.dataContainer}>
          <QnAToolbar
            minimize
            onCreateNew={openCreateQnAModal}
            onGenerate={goToGeneratePage}
            qnaJobStatus={kb.currentQaGenStatus || 'idle'}
          />
          {kb.currentQaGenStatus === 'failed' && <QnAErrorMessage />}
          <Box className={classes.actions}>
            <Box /> {/* replace with SearchBox later */}
            <QnAImport kb={kb} onImportSuccess={fetchQnAList} />
          </Box>
          <QnATable qna={qna} onEditQnA={openEditQnAModal} onRemoveQnA={onRemoveQnA} />
          {pagination && (
            <Box className={classes.pagination}>
              <CustomPagination pagination={pagination} onChange={fetchQnAList} />
            </Box>
          )}
        </Box>
      ) : (
        <Box>
          <QnAToolbar
            onCreateNew={openCreateQnAModal}
            onGenerate={goToGeneratePage}
            qnaJobStatus={kb.currentQaGenStatus || 'idle'}
          />
          {kb.currentQaGenStatus === 'failed' && <QnAErrorMessage />}
          <Flex justify={'flex-end'} w={'100%'} mt={rem(10)}>
            <QnAImport kb={kb} onImportSuccess={fetchQnAList} />
          </Flex>
        </Box>
      )}
      <QnAEditorModal ref={qnaEditorModalRef} kbId={kb.id} onSubmitted={onSubmitted} />
    </Box>
  );
};

export default KBQnA;
