import { Box, Flex, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconAlertCircle, IconX } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { memo, useState } from 'react';

const useStyles = createStyles((theme) => ({
  container: {
    border: `1px solid ${theme.colors.decaRed[5]}`,
    borderRadius: rem(4),
    padding: rem(10),
    gap: rem(10),
    justifyContent: 'space-between',
    backgroundColor: theme.colors.decaRed[0],
    alignItems: 'center',
    '& svg': {
      color: theme.colors.decaRed[5],
    },
  },
  icon: {
    width: rem(36),
    height: rem(36),
    borderRadius: rem(16),
    backgroundColor: theme.colors.decaRed[1],
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: rem(8),
  },
  alertIcon: {
    width: rem(20),
    height: rem(20),
  },
  closeIcon: {
    width: rem(20),
    height: rem(20),
    cursor: 'pointer',
  },
}));

const QnAErrorMessage: React.FC = () => {
  const { classes } = useStyles();
  const { t } = useTranslate('kb');
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) {
    return null;
  }

  return (
    <Flex w={'100%'} className={classes.container}>
      <Flex gap={rem(10)} align={'center'}>
        <Box className={classes.icon}>
          <IconAlertCircle className={classes.alertIcon} />
        </Box>
        <Text>{t('qnaGenerateFailed')}</Text>
      </Flex>
      <IconX className={classes.closeIcon} onClick={() => setIsVisible(false)} />
    </Flex>
  );
};

export default memo(QnAErrorMessage);
