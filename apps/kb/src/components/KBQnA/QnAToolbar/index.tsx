import { Box, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconMessages, IconPencilPlus, IconSettingsAutomation } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { memo, useMemo } from 'react';

import type { QnAJobStatus } from '@/types/qna';
import { getQnAJobStatusMapping } from '@/utils/qna';
import { KBActionButton } from '../../common';

interface QnAToolbarProps {
  qnaJobStatus: QnAJobStatus;
  minimize?: boolean;
  onCreateNew: () => void;
  onGenerate: () => void;
}

const useStyles = createStyles((theme) => ({
  container: {
    width: '100%',
    height: rem(400),
    display: 'flex',
    flexDirection: 'column',
    gap: rem(20),
    border: `1px solid ${theme.colors.dark[0]}`,
    borderRadius: rem(10),
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    width: rem(61.67),
    height: rem(61.67),
    color: theme.colors.decaYellow[4],
  },
  description: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(8),
    textAlign: 'center',
    fontSize: theme.fontSizes.md,
    fontWeight: 700,
    lineHeight: rem(29),
  },
  actions: {
    display: 'flex',
    gap: rem(20),
  },
}));

const QnAToolbar: React.FC<QnAToolbarProps> = ({
  minimize = false,
  onCreateNew,
  onGenerate,
  qnaJobStatus,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('details');

  const actions = useMemo(
    () => (
      <Box className={classes.actions}>
        <KBActionButton
          title={t('createQnAManual')}
          description={t('createQnAManualDescription')}
          icon={<IconPencilPlus />}
          onClick={onCreateNew}
        />
        <KBActionButton
          title={t('createQnAAuto')}
          description={t('createQnAAutoDescription')}
          icon={<IconSettingsAutomation />}
          background={getQnAJobStatusMapping(qnaJobStatus).actionColor ?? 'blue'}
          onClick={onGenerate}
          subTitle={t(getQnAJobStatusMapping(qnaJobStatus).actionText ?? '')}
        />
      </Box>
    ),
    [qnaJobStatus]
  );

  return minimize ? (
    actions
  ) : (
    <Box className={classes.container}>
      <IconMessages className={classes.icon} />
      <Box className={classes.description}>
        <Text>{t('noQnA')}</Text>
        <Text>{t('createQnAList')}</Text>
      </Box>
      {actions}
    </Box>
  );
};

export default memo(QnAToolbar);
