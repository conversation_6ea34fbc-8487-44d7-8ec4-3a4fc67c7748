import { Button, FileButton, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';

import { useKbContext } from '@/contexts/KbContext';
import type { KnowledgeBase } from '@/types';
import { notifications } from '@mantine/notifications';
import { useCallback, useState } from 'react';

interface QnAImportProps {
  kb: KnowledgeBase;
  onImportSuccess: () => void;
}

const useStyles = createStyles((theme) => ({
  button: {
    minWidth: rem(175),
    height: rem(38),
    borderRadius: rem(32),
    padding: `${rem(7)} ${rem(16)}`,
    backgroundColor: theme.colors.decaNavy[5],
    color: theme.white,
    fontSize: theme.fontSizes.md,
    lineHeight: rem(40),
    '& span.mantine-Button-label': {
      position: 'relative',
      top: rem(-2),
    },
  },
}));

const QnAImport: React.FC<QnAImportProps> = ({ kb, onImportSuccess }) => {
  const { classes } = useStyles();
  const { t } = useTranslate('details');
  const { importQnAFromCSV } = useKbContext();
  const [isUploading, setIsUploading] = useState(false);

  const onChange = useCallback(async (file: File | null) => {
    setIsUploading(true);
    const formData = new FormData();
    if (file && kb.id) {
      formData.append('uploadFile', file);

      const res = await importQnAFromCSV(kb.id, formData);

      if (res?.status === 'success') {
        notifications.show({
          title: t('import.success'),
          message: t('import.successMessage'),
          color: 'teal',
        });
        onImportSuccess();
      } else {
        notifications.show({
          title: t('import.failed'),
          message: t('import.failedMessage'),
          color: 'red',
        });
      }
    }
    setIsUploading(false);
  }, []);

  return (
    <FileButton onChange={onChange} accept='csv' disabled={isUploading}>
      {(props) => (
        <Button {...props} className={classes.button}>
          {t('import.qnaCsv')}
        </Button>
      )}
    </FileButton>
  );
};

export default QnAImport;
