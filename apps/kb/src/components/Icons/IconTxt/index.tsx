import type { IconCustomProps } from '@/types';

const IconTxt = (props: IconCustomProps) => {
  return (
    <svg
      width='36'
      height='36'
      viewBox='0 0 36 36'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      role='img'
      aria-labelledby='txt-icon-title'
      {...props}
    >
      <title id='txt-icon-title'>Text file</title>
      <path
        d='M32.8983 12.7039C32.8983 12.3296 32.77 11.9858 32.559 11.7094C32.3981 11.3503 32.1828 11.013 31.9153 10.7149C31.8958 10.6931 31.8762 10.6736 31.8566 10.654L24.4864 3.30286C23.7666 2.52597 22.751 2.08203 21.6919 2.08203H5.47483C5.45091 2.08203 5.42916 2.08203 5.40524 2.08203C3.46102 2.16473 1.94087 3.80556 2.00177 5.74671V18.0965C2.00177 18.9974 2.73248 19.7286 3.63282 19.7286C4.53316 19.7286 5.26388 18.9974 5.26388 18.0965V5.71841C5.26388 5.69448 5.26388 5.67272 5.26388 5.64878C5.25735 5.49427 5.37261 5.36152 5.52485 5.34629H21.0307V12.7039C21.0307 13.6049 21.7615 14.3361 22.6618 14.3361H29.6362V18.2771C29.6362 19.178 30.3669 19.9092 31.2672 19.9092C32.1676 19.9092 32.8983 19.178 32.8983 18.2771V13.2719C32.8983 13.1566 32.8918 13.0412 32.8831 12.9281C32.894 12.8541 32.9005 12.7801 32.9005 12.7039H32.8983ZM24.2929 7.72049L27.6528 11.074H24.2929V7.72049Z'
        fill='#1D2088'
      />
      <path
        d='M31.4321 21.9102C31.7061 21.9102 31.9801 21.9204 32.2542 21.941C32.5351 21.9615 32.7886 22.0198 33.0146 22.1157C33.2407 22.2116 33.4223 22.3589 33.5593 22.5576C33.7032 22.7494 33.7751 23.02 33.7751 23.3694C33.7751 23.712 33.7032 23.9826 33.5593 24.1813C33.4223 24.3731 33.2407 24.517 33.0146 24.6129C32.7886 24.7088 32.5351 24.7705 32.2542 24.7979C31.9801 24.8184 31.7061 24.8287 31.4321 24.8287H30.3838V33.9542C30.3838 34.2488 30.3736 34.5366 30.353 34.8175C30.3393 35.0984 30.2845 35.3484 30.1886 35.5677C30.0927 35.7869 29.9385 35.965 29.7262 36.102C29.5138 36.2322 29.2123 36.2973 28.8218 36.2973C28.4313 36.2973 28.1299 36.2322 27.9175 36.102C27.7051 35.965 27.551 35.7869 27.455 35.5677C27.3591 35.3484 27.3009 35.0984 27.2803 34.8175C27.2666 34.5366 27.2598 34.2488 27.2598 33.9542V24.8287H26.2116C25.9375 24.8287 25.6601 24.8184 25.3792 24.7979C25.1051 24.7705 24.8551 24.7088 24.629 24.6129C24.4029 24.517 24.2179 24.3731 24.0741 24.1813C23.937 23.9826 23.8685 23.712 23.8685 23.3694C23.8685 23.02 23.937 22.7494 24.0741 22.5576C24.2179 22.3589 24.4029 22.2116 24.629 22.1157C24.8551 22.0198 25.1051 21.9615 25.3792 21.941C25.6601 21.9204 25.9375 21.9102 26.2116 21.9102H31.4321Z'
        fill='#1D2088'
      />
      <path
        d='M15.9761 28.9599L13.3865 25.0342C13.2357 24.8081 13.0987 24.5821 12.9754 24.356C12.8726 24.171 12.7767 23.9757 12.6876 23.7702C12.6054 23.5647 12.5643 23.39 12.5643 23.2461C12.5643 23.0611 12.6157 22.8727 12.7185 22.6809C12.8281 22.4891 13.0165 22.3144 13.2837 22.1568C13.592 21.9924 13.8729 21.9102 14.1264 21.9102C14.3182 21.9102 14.4929 21.9547 14.6505 22.0438C14.8149 22.126 14.9622 22.239 15.0924 22.3829C15.2294 22.5199 15.3561 22.6775 15.4726 22.8556C15.5891 23.0337 15.7021 23.2118 15.8117 23.39L17.7231 26.5346H18.0314L19.9429 23.39C20.0525 23.2118 20.1655 23.0337 20.282 22.8556C20.3985 22.6775 20.5218 22.5199 20.652 22.3829C20.789 22.239 20.9363 22.126 21.0939 22.0438C21.2514 21.9547 21.4296 21.9102 21.6282 21.9102C21.7515 21.9102 21.8817 21.9341 22.0187 21.9821C22.1626 22.03 22.3133 22.0951 22.4709 22.1773C22.7586 22.3349 22.9573 22.5062 23.0669 22.6912C23.1766 22.8693 23.2314 23.0474 23.2314 23.2256C23.2314 23.3763 23.1868 23.5544 23.0978 23.7599C23.0087 23.9655 22.9094 24.1641 22.7998 24.356C22.6764 24.5821 22.5326 24.8081 22.3681 25.0342L19.799 28.9599V29.1448L22.6559 33.646C22.7998 33.872 22.9231 34.0981 23.0258 34.3242C23.1355 34.5434 23.1903 34.7627 23.1903 34.9819C23.1903 35.1669 23.1389 35.3519 23.0361 35.5368C22.9402 35.715 22.7586 35.8862 22.4915 36.0507C22.1832 36.2151 21.9023 36.2973 21.6488 36.2973C21.4707 36.2973 21.3097 36.263 21.1658 36.1945C21.0219 36.1192 20.8883 36.0233 20.765 35.9068C20.6417 35.7903 20.5252 35.6567 20.4156 35.506C20.3128 35.3553 20.2101 35.2011 20.1073 35.0436L18.0314 31.6729H17.7231L15.6473 35.0436C15.5445 35.2011 15.4383 35.3553 15.3287 35.506C15.226 35.6567 15.1129 35.7903 14.9896 35.9068C14.8731 36.0233 14.743 36.1192 14.5991 36.1945C14.4552 36.263 14.2976 36.2973 14.1264 36.2973C13.866 36.2973 13.592 36.2151 13.3042 36.0507C13.0233 35.8862 12.8281 35.715 12.7185 35.5368C12.6157 35.3519 12.5643 35.1669 12.5643 34.9819C12.5643 34.7627 12.6157 34.5434 12.7185 34.3242C12.8281 34.0981 12.9548 33.872 13.0987 33.646L15.9761 29.1448V28.9599Z'
        fill='#1D2088'
      />
      <path
        d='M9.56352 21.9102C9.83757 21.9102 10.1116 21.9204 10.3856 21.941C10.6665 21.9615 10.92 22.0198 11.1461 22.1157C11.3722 22.2116 11.5537 22.3589 11.6908 22.5576C11.8346 22.7494 11.9066 23.02 11.9066 23.3694C11.9066 23.712 11.8346 23.9826 11.6908 24.1813C11.5537 24.3731 11.3722 24.517 11.1461 24.6129C10.92 24.7088 10.6665 24.7705 10.3856 24.7979C10.1116 24.8184 9.83757 24.8287 9.56352 24.8287H8.51532V33.9542C8.51532 34.2488 8.50504 34.5366 8.48449 34.8175C8.47079 35.0984 8.41598 35.3484 8.32006 35.5677C8.22415 35.7869 8.07 35.965 7.85762 36.102C7.64524 36.2322 7.34379 36.2973 6.95329 36.2973C6.56278 36.2973 6.26133 36.2322 6.04895 36.102C5.83657 35.965 5.68242 35.7869 5.58651 35.5677C5.49059 35.3484 5.43236 35.0984 5.41181 34.8175C5.39811 34.5366 5.39125 34.2488 5.39125 33.9542V24.8287H4.34305C4.06901 24.8287 3.79154 24.8184 3.51065 24.7979C3.23661 24.7705 2.98655 24.7088 2.76046 24.6129C2.53438 24.517 2.3494 24.3731 2.20553 24.1813C2.06851 23.9826 2 23.712 2 23.3694C2 23.02 2.06851 22.7494 2.20553 22.5576C2.3494 22.3589 2.53438 22.2116 2.76046 22.1157C2.98655 22.0198 3.23661 21.9615 3.51065 21.941C3.79154 21.9204 4.06901 21.9102 4.34305 21.9102H9.56352Z'
        fill='#1D2088'
      />
    </svg>
  );
};

export default IconTxt;
