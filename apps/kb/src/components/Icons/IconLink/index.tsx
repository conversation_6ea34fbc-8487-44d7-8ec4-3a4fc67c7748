import type { IconCustomProps } from '@/types';

const IconArticle = (props: IconCustomProps) => {
  return (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      aria-label='Link'
      {...props}
    >
      <title>Link</title>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M8.86181 2.86218C9.42886 2.29513 10.1979 1.97656 10.9999 1.97656C11.8018 1.97656 12.5709 2.29513 13.1379 2.86218C13.705 3.42923 14.0236 4.19832 14.0236 5.00025C14.0236 5.80218 13.705 6.57127 13.1379 7.13832L10.4735 9.80273C10.1947 10.0866 9.86223 10.3122 9.49539 10.4663C9.12744 10.621 8.73233 10.7006 8.33321 10.7006C7.93409 10.7006 7.53899 10.621 7.17103 10.4663C6.80308 10.3117 6.46968 10.0852 6.19035 9.80015C5.93268 9.53716 5.93698 9.11507 6.19998 8.85739C6.46297 8.59972 6.88506 8.60402 7.14274 8.86702C7.29792 9.0254 7.48314 9.15122 7.68756 9.23712C7.89198 9.32301 8.11148 9.36726 8.33321 9.36726C8.55494 9.36726 8.77445 9.32301 8.97887 9.23712C9.18328 9.15122 9.36851 9.0254 9.52369 8.86702L9.52845 8.86216L9.52847 8.86218L12.1951 6.19551C12.5121 5.87851 12.6902 5.44856 12.6902 5.00025C12.6902 4.55194 12.5121 4.12199 12.1951 3.80499C11.8781 3.48799 11.4482 3.3099 10.9999 3.3099C10.5516 3.3099 10.1216 3.48799 9.80462 3.80499L9.47128 4.13832C9.21093 4.39867 8.78882 4.39867 8.52847 4.13832C8.26812 3.87797 8.26812 3.45586 8.52847 3.19551L8.86181 2.86218ZM6.50438 5.53422C6.87233 5.37961 7.26743 5.29997 7.66655 5.29997C8.06567 5.29997 8.46078 5.37961 8.82873 5.53422C9.19668 5.68884 9.53008 5.91532 9.80941 6.20041C10.0671 6.4634 10.0628 6.88549 9.79979 7.14317C9.5368 7.40084 9.11471 7.39654 8.85703 7.13354C8.70185 6.97516 8.51662 6.84934 8.31221 6.76344C8.10779 6.67755 7.88828 6.6333 7.66655 6.6333C7.44482 6.6333 7.22532 6.67755 7.0209 6.76344C6.81648 6.84934 6.63126 6.97516 6.47608 7.13354L6.47131 7.1384L6.47129 7.13838L3.80462 9.80505C3.48762 10.122 3.30953 10.552 3.30953 11.0003C3.30953 11.4486 3.48762 11.8786 3.80462 12.1956C4.12163 12.5126 4.55158 12.6907 4.99989 12.6907C5.4482 12.6907 5.87814 12.5126 6.19515 12.1956L6.52848 11.8622C6.78883 11.6019 7.21094 11.6019 7.47129 11.8622C7.73164 12.1226 7.73164 12.5447 7.47129 12.805L7.13796 13.1384C6.57091 13.7054 5.80182 14.024 4.99989 14.024C4.19795 14.024 3.42887 13.7054 2.86181 13.1384C2.29476 12.5713 1.9762 11.8022 1.9762 11.0003C1.9762 10.1984 2.29476 9.42929 2.86181 8.86224L5.5262 6.19785C5.80501 5.91397 6.13752 5.68838 6.50438 5.53422Z'
        fill='#A3A3A3'
      />
    </svg>
  );
};

export default IconArticle;
