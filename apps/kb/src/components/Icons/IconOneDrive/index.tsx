import type { IconCustomProps } from '@/types';

const IconOneDrive = (props: IconCustomProps) => {
  return (
    <svg
      width='120'
      height='120'
      viewBox='0 0 120 120'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      aria-label='OneDrive'
      {...props}
    >
      <title>OneDrive</title>
      <path
        opacity='0.9'
        d='M83.58 119.85C99.6 119.85 112.58 106.87 112.58 90.8496C112.58 74.8296 99.6 61.8496 83.58 61.8496C67.56 61.8496 54.58 74.8296 54.58 90.8496C54.58 106.87 67.56 119.85 83.58 119.85Z'
        fill='#0078D4'
      />
      <path
        d='M3.41002 77.5609C5.84002 81.2709 10.82 82.3409 14.58 79.9609C18.34 77.5809 19.46 72.6409 17.11 68.8809L3.40002 77.5609H3.41002Z'
        fill='#0078D4'
      />
      <path
        opacity='0.7'
        d='M40.41 35.3193L40.93 5.52935C24.48 5.49935 10.98 18.5693 10.51 34.9193L40.41 35.3093V35.3193Z'
        fill='#0078D4'
      />
      <path
        opacity='0.8'
        d='M23.67 95.6902L16.66 92.2402L13.25 99.2402L20.26 102.7L23.67 95.7002V95.6902Z'
        fill='#40E0D0'
      />
      <path
        opacity='0.9'
        d='M6.07001 62.88L7.5 55.43L0 54L6.06001 62.89H6.07001V62.88Z'
        fill='#0078D4'
      />
      <path
        opacity='0.8'
        d='M108.72 48C107.11 51 108.3 54.7 111.4 56.28C114.5 57.86 118.32 56.75 120 53.79L108.72 48Z'
        fill='#0078D4'
      />
      <path
        d='M96.4 7.13078L106.01 14.2108L107.56 12.1308L97.95 5.05078L96.4 7.13078Z'
        fill='#40E0D0'
      />
      <g opacity='0.9'>
        <path d='M77.4301 20H28.5701V104.3H89.6901V33.09L77.4301 20Z' fill='#E6E6E6' />
        <rect x='34.5701' y='49' width='49' height='32' fill='#0078D4' opacity='0.1' />
        <path d='M77.86 20V32.98H90.12L77.86 20Z' fill='#F2F2F2' />
        <path d='M90.12 45.9505V32.9805H77.86L90.12 45.9505Z' fill='#B3B3B3' />
      </g>
      <circle cx='60' cy='65' r='8' fill='#0078D4' />
      <path
        d='M55 65L58 68L65 61'
        stroke='white'
        strokeWidth='2'
        strokeLinecap='round'
        strokeLinejoin='round'
        fill='none'
      />
    </svg>
  );
};

export default IconOneDrive;
