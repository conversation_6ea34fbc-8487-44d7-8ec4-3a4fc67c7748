import type { IconCustomProps } from '@/types';

const IconPdf = (props: IconCustomProps) => {
  return (
    <svg
      width='34'
      height='34'
      viewBox='0 0 34 34'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      role='img'
      aria-labelledby='pdf-icon-title'
      {...props}
    >
      <title id='pdf-icon-title'>PDF file</title>
      <path
        d='M31.3998 11.8111C31.3998 11.4654 31.2777 11.1478 31.077 10.8925C30.9239 10.5609 30.719 10.2493 30.4645 9.97393C30.4459 9.95383 30.4273 9.93574 30.4086 9.91764L23.3959 3.12765C22.7109 2.41005 21.7446 2 20.7369 2H5.30631C5.28355 2 5.26285 2 5.24009 2C3.39016 2.07638 1.94374 3.59197 2.00168 5.38495V16.7921C2.00168 17.6242 2.69696 18.2996 3.55364 18.2996C4.41031 18.2996 5.10559 17.6242 5.10559 16.7921V5.35882C5.10559 5.33671 5.10559 5.31661 5.10559 5.2945C5.09938 5.15178 5.20905 5.02917 5.3539 5.0151H20.1078V11.8111C20.1078 12.6433 20.8031 13.3187 21.6598 13.3187H28.2959V16.9589C28.2959 17.7911 28.9912 18.4664 29.8479 18.4664C30.7045 18.4664 31.3998 17.7911 31.3998 16.9589V12.3358C31.3998 12.2292 31.3936 12.1227 31.3853 12.0182C31.3957 11.9498 31.4019 11.8815 31.4019 11.8111H31.3998ZM23.2117 7.20808L26.4087 10.3056H23.2117V7.20808Z'
        fill='#1D2088'
      />
      <path
        d='M29.6598 20.3203C29.9304 20.3203 30.2052 20.3325 30.4843 20.3589C30.7633 20.3852 31.0149 20.4461 31.2369 20.5455C31.461 20.6428 31.6449 20.7889 31.7865 20.9856C31.9282 21.1743 32 21.4359 32 21.7665C32 22.0972 31.9282 22.3629 31.7865 22.5576C31.6449 22.7462 31.461 22.8902 31.2369 22.9876C31.0128 23.085 30.7612 23.1478 30.4843 23.1742C30.2052 23.2006 29.9304 23.2128 29.6598 23.2128H27.2372V25.7523H28.5204C28.7846 25.7523 29.0573 25.7624 29.3343 25.7807C29.6133 25.8009 29.8628 25.8557 30.0869 25.947C30.311 26.0383 30.4949 26.1823 30.6365 26.377C30.7866 26.5717 30.8606 26.8456 30.8606 27.1985C30.8606 27.5514 30.7887 27.8232 30.6471 28.02C30.5054 28.2147 30.3215 28.3587 30.0974 28.45C29.8733 28.5413 29.6218 28.596 29.3448 28.6163C29.0658 28.6366 28.791 28.6447 28.5204 28.6447H27.2372V31.7522C27.2372 32.0382 27.2266 32.316 27.2076 32.5818C27.1949 32.8495 27.1357 33.0909 27.0342 33.3039C26.9391 33.5128 26.7869 33.6811 26.5755 33.8109C26.3641 33.9347 26.0702 33.9976 25.6897 33.9976C25.3092 33.9976 25.0048 33.9347 24.7934 33.8109C24.582 33.6811 24.4298 33.5107 24.3346 33.3039C24.2395 33.0888 24.1824 32.8475 24.1613 32.5818C24.1486 32.314 24.1401 32.0382 24.1401 31.7522V22.5677C24.1401 22.2878 24.1465 22.014 24.1613 21.7463C24.1824 21.4785 24.2395 21.2412 24.3346 21.0323C24.4298 20.8173 24.582 20.6449 24.7934 20.515C25.0048 20.3852 25.3028 20.3203 25.6897 20.3203H29.6619H29.6598Z'
        fill='#1B2482'
      />
      <path
        d='M17.4388 20.3205C18.0561 20.3205 18.6311 20.3955 19.1596 20.5456C19.6965 20.6957 20.1616 20.9209 20.5548 21.219C20.9544 21.5131 21.2673 21.8803 21.4913 22.3225C21.7218 22.7646 21.838 23.2839 21.838 23.8762V29.9329C21.838 30.44 21.781 30.8903 21.6647 31.2817C21.5484 31.6651 21.3962 32.0018 21.2059 32.2878C21.022 32.5677 20.8127 32.8091 20.5739 33.0099C20.3371 33.2046 20.0961 33.3649 19.8509 33.4886C19.2674 33.7888 18.6057 33.9571 17.8658 33.9957H14.4855C14.0986 33.9957 13.8005 33.9328 13.5891 33.8091C13.3777 33.6793 13.2255 33.5089 13.1304 33.302C13.0353 33.087 12.9782 32.8456 12.9571 32.5799C12.9444 32.3121 12.9359 32.0363 12.9359 31.7503V22.5456C12.9359 22.2657 12.9423 21.9918 12.9571 21.7241C12.9782 21.4563 13.0353 21.219 13.1304 21.0101C13.2255 20.8012 13.3777 20.6349 13.5891 20.5111C13.8005 20.3813 14.0986 20.3164 14.4855 20.3164H17.4388V20.3205ZM16.0351 22.6064V31.6732H17.3584C17.6502 31.661 17.9123 31.5941 18.1427 31.4785C18.2379 31.4339 18.333 31.371 18.4281 31.2919C18.5233 31.2128 18.6078 31.1235 18.6818 31.018C18.7558 30.9065 18.815 30.7767 18.8552 30.6266C18.9038 30.4765 18.927 30.3041 18.927 30.1093V24.3447C18.927 24.1297 18.9038 23.9411 18.8552 23.7788C18.815 23.6105 18.7558 23.4624 18.6818 23.3387C18.6078 23.2149 18.5233 23.1115 18.4281 23.0263C18.333 22.9411 18.2379 22.8742 18.1427 22.8214C17.9123 22.6977 17.6502 22.6267 17.3584 22.6064H16.0351Z'
        fill='#1B2482'
      />
      <path
        d='M5.09703 28.6062V31.7724C5.09703 32.0524 5.08646 32.3262 5.06744 32.5939C5.05475 32.8617 4.99556 33.099 4.89409 33.3079C4.79896 33.5168 4.64675 33.6852 4.43535 33.815C4.22394 33.9387 3.92375 34.0016 3.52843 34.0016C3.13311 34.0016 2.85195 33.9387 2.64266 33.815C2.43972 33.6852 2.28962 33.5148 2.19449 33.3079C2.09936 33.099 2.04228 32.8617 2.02114 32.5939C2.00846 32.3262 2 32.0524 2 31.7724V22.5495C2 22.2696 2.00634 21.9957 2.02114 21.728C2.04228 21.4603 2.09936 21.2229 2.19449 21.014C2.28962 20.8051 2.43972 20.6388 2.64266 20.515C2.85406 20.3852 3.14791 20.3203 3.52843 20.3203H6.88972C7.7311 20.3589 8.4858 20.5292 9.1496 20.8274C9.43499 20.9572 9.70981 21.1215 9.97407 21.3162C10.2447 21.511 10.4857 21.7523 10.6971 22.0383C10.9148 22.3182 11.0882 22.6529 11.2171 23.0444C11.3461 23.4278 11.4116 23.874 11.4116 24.3831C11.4116 25.0809 11.2953 25.6914 11.0649 26.2208C10.8408 26.7421 10.511 27.1823 10.0777 27.5393C9.65062 27.8902 9.12423 28.1579 8.49848 28.3405C7.88119 28.5169 7.18145 28.6041 6.40138 28.6041H5.09703V28.6062ZM6.48171 26.2796C6.82207 26.2594 7.12649 26.1823 7.3992 26.0443C7.51547 25.9855 7.62751 25.9105 7.73532 25.8192C7.84314 25.7279 7.9425 25.6164 8.03129 25.4866C8.12008 25.3507 8.18772 25.1904 8.23423 25.0079C8.2892 24.8253 8.31668 24.6164 8.31668 24.3831C8.31668 24.162 8.2892 23.9693 8.23423 23.8071C8.18772 23.6387 8.12219 23.4947 8.03974 23.3771C7.9573 23.2533 7.86639 23.1499 7.76492 23.0647C7.66345 22.9795 7.55775 22.9085 7.44993 22.8497C7.19202 22.726 6.9024 22.6509 6.5853 22.6245H5.09915V26.2796H6.48383H6.48171Z'
        fill='#1B2482'
      />
    </svg>
  );
};

export default IconPdf;
