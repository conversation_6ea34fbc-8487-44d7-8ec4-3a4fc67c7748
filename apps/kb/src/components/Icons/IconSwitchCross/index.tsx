import type { IconCustomProps } from '@/types';

const IconSwitchCross = (props: IconCustomProps) => {
  return (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      aria-label='Switch cross'
      {...props}
    >
      <title>Switch cross</title>
      <rect width='16' height='16' fill='white' fillOpacity='0.01' />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M9.33341 2.66667C9.33341 2.29848 9.63189 2 10.0001 2H12.6667C13.0349 2 13.3334 2.29848 13.3334 2.66667V5.33333C13.3334 5.70152 13.0349 6 12.6667 6C12.2986 6 12.0001 5.70152 12.0001 5.33333V4.54975L10.3516 6.58613C10.1199 6.87231 9.70012 6.91649 9.41395 6.68483C9.12778 6.45317 9.08359 6.03337 9.31525 5.7472L11.2693 3.33333H10.0001C9.63189 3.33333 9.33341 3.03486 9.33341 2.66667ZM2.86201 2.86193C3.12236 2.60158 3.54447 2.60158 3.80482 2.86193L12.0001 11.0572V10C12.0001 9.63181 12.2986 9.33333 12.6667 9.33333C13.0349 9.33333 13.3334 9.63181 13.3334 10V12.6667C13.3334 13.0349 13.0349 13.3333 12.6667 13.3333H10.0001C9.63189 13.3333 9.33341 13.0349 9.33341 12.6667C9.33341 12.2985 9.63189 12 10.0001 12H11.0573L2.86201 3.80474C2.60166 3.54439 2.60166 3.12228 2.86201 2.86193ZM6.47149 9.52859C6.73183 9.78894 6.73183 10.2111 6.47149 10.4714L3.80482 13.1381C3.54447 13.3984 3.12236 13.3984 2.86201 13.1381C2.60166 12.8777 2.60166 12.4556 2.86201 12.1953L5.52868 9.52859C5.78903 9.26825 6.21114 9.26825 6.47149 9.52859Z'
        fill='#5C5C5C'
      />
    </svg>
  );
};

export default IconSwitchCross;
