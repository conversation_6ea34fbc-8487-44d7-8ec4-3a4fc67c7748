const IconCircleCheckFilled = () => {
  return (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      aria-label='Circle check filled icon'
    >
      <title>Circle check filled icon</title>
      <path
        d='M11.3333 2.22627C12.3388 2.80684 13.1753 3.63995 13.7599 4.64314C14.3444 5.64632 14.6569 6.78478 14.6663 7.94583C14.6756 9.10687 14.3816 10.2502 13.8133 11.2627C13.245 12.2752 12.4221 13.1217 11.4261 13.7185C10.4301 14.3152 9.29556 14.6415 8.13471 14.665C6.97387 14.6885 5.82701 14.4084 4.80768 13.8524C3.78835 13.2965 2.93189 12.484 2.32309 11.4953C1.71429 10.5066 1.37425 9.37608 1.33665 8.2156L1.33331 7.9996L1.33665 7.7836C1.37398 6.63226 1.70901 5.51024 2.30907 4.52693C2.90913 3.54362 3.75375 2.73257 4.76057 2.17286C5.7674 1.61314 6.90208 1.32387 8.05398 1.33323C9.20589 1.34259 10.3357 1.65027 11.3333 2.22627ZM10.4713 6.19494C10.3565 6.08015 10.2038 6.0112 10.0418 6.00101C9.87975 5.99082 9.71958 6.0401 9.59131 6.1396L9.52865 6.19494L7.33331 8.3896L6.47131 7.52827L6.40865 7.47294C6.28037 7.37351 6.12023 7.32429 5.95825 7.33451C5.79627 7.34472 5.64358 7.41368 5.52882 7.52844C5.41406 7.64321 5.3451 7.79589 5.33488 7.95787C5.32466 8.11985 5.37388 8.27999 5.47331 8.40827L5.52865 8.47094L6.86198 9.80427L6.92465 9.8596C7.04156 9.95031 7.18533 9.99955 7.33331 9.99955C7.48129 9.99955 7.62506 9.95031 7.74198 9.8596L7.80465 9.80427L10.4713 7.1376L10.5266 7.07494C10.6261 6.94667 10.6754 6.7865 10.6652 6.62448C10.6551 6.46246 10.5861 6.30973 10.4713 6.19494Z'
        fill='#C1C1CC'
      />
    </svg>
  );
};

export default IconCircleCheckFilled;
