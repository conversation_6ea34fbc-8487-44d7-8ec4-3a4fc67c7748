import type { IconCustomProps } from '@/types';

const IconSearch = (props: IconCustomProps) => {
  return (
    <svg
      width='268'
      height='268'
      viewBox='0 0 268 268'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      role='img'
      aria-labelledby='search-icon-title'
      {...props}
    >
      <title id='search-icon-title'>Search</title>
      <g clipPath='url(#clip0_2313_14382)'>
        <circle cx='200.5' cy='186.502' r='54.5' fill='#EDEDED' />
        <path
          d='M181.172 87.8166L173.764 127.261C195.579 131.05 216.446 116.75 220.754 95.1142L181.168 87.7994L181.172 87.8166Z'
          fill='#EDEDED'
        />
        <path
          d='M174.497 72.0393L195.97 43.9628C180.192 32.2744 158.014 35.1616 146.045 50.4557L174.482 72.0323L174.511 72.0463L174.497 72.0393Z'
          fill='#E4007F'
        />
        <path d='M262.13 101L265 86.7423L250 84L262.142 101H262.13Z' fill='#F93549' />
        <path
          opacity='0.8'
          d='M118.086 22.2885C123.179 17.2671 123.269 9.0348 118.221 3.88786C113.172 -1.25908 104.986 -1.44435 99.7658 3.49739L118.086 22.2885Z'
          fill='#FFD100'
        />
        <path
          opacity='0.8'
          d='M262.5 43.5615L243.003 59.8004L246.524 64.0629L266.021 47.8239L262.5 43.5615Z'
          fill='#1D7ECE'
        />
        <path
          opacity='0.5'
          d='M91.556 82.6441L62.9996 222.265C62.8354 223.088 63.3645 223.892 64.1856 224.057L166.532 245.082C167.353 245.246 168.156 244.716 168.321 243.893L196.877 104.272C197.041 103.449 196.512 102.645 195.691 102.48L93.3441 81.4557C92.523 81.2912 91.7202 81.8214 91.556 82.6441Z'
          fill='url(#paint0_linear_2313_14382)'
        />
        <path
          d='M87.742 78.7671L59.1856 218.388C59.0214 219.211 59.5506 220.016 60.3717 220.18L162.718 241.205C163.539 241.369 164.342 240.839 164.507 240.016L193.063 100.395C193.227 99.5723 192.698 98.7679 191.877 98.6033L89.5302 77.5788C88.7091 77.4142 87.9062 77.9444 87.742 78.7671Z'
          fill='#B0D4FE'
        />
        <path
          opacity='0.5'
          d='M150.931 206.159L47.6356 221.882C46.7963 222.01 46.0299 221.443 45.9022 220.602L24.5351 79.6826C24.4074 78.8416 24.973 78.0738 25.8124 77.9458L129.108 62.2231C129.947 62.0951 130.714 62.6619 130.841 63.5029L152.19 204.422C152.318 205.263 151.752 206.031 150.913 206.159H150.931Z'
          fill='url(#paint1_linear_2313_14382)'
        />
        <path
          d='M20.9404 75.1661L42.7637 219.084L149.07 202.904L127.247 58.9863L20.9404 75.1661Z'
          fill='white'
        />
        <path
          d='M21.1592 76.6646L42.5262 217.584C42.654 218.407 43.4203 218.992 44.2597 218.864L147.555 203.141C148.376 203.013 148.96 202.245 148.833 201.404L127.465 60.4848C127.338 59.6621 126.571 59.0771 125.732 59.2051L22.4364 74.9461C21.6153 75.0741 21.0314 75.8419 21.1592 76.6829V76.6646Z'
          fill='#C9E3FF'
        />
        <path
          opacity='0.07'
          d='M77.8708 72.7886L32.3813 79.7175L31.7244 75.4029C31.5785 74.5254 32.1989 73.7027 33.0747 73.5747L75.4622 67.1211C76.3015 66.9931 77.0862 67.5781 77.2139 68.4191L77.8708 72.7886Z'
          fill='#ECF0F5'
        />
        <path
          d='M124.911 141.968C124.911 141.968 124.82 142.005 124.765 142.005L45.7562 154.016C45.3182 154.09 44.8986 153.779 44.8256 153.322C44.7526 152.883 45.0628 152.462 45.519 152.389L124.528 140.378C124.966 140.305 125.386 140.616 125.458 141.073C125.513 141.475 125.276 141.84 124.911 141.968Z'
          fill='white'
        />
        <path
          d='M129.564 172.666C129.564 172.666 129.473 172.702 129.418 172.702L50.409 184.714C49.9711 184.787 49.5514 184.476 49.4784 184.019C49.4054 183.58 49.7156 183.141 50.1718 183.087L129.181 171.075C129.619 171.002 130.038 171.313 130.111 171.77C130.166 172.172 129.929 172.538 129.564 172.666Z'
          fill='white'
        />
        <path
          d='M85.5889 163.652C85.5889 163.652 85.4977 163.689 85.443 163.689L48.0916 169.374C47.6537 169.447 47.234 169.137 47.161 168.68C47.088 168.223 47.3982 167.802 47.8544 167.747L85.2058 162.061C85.6437 161.988 86.0634 162.299 86.1363 162.756C86.1911 163.158 85.9539 163.524 85.5889 163.652Z'
          fill='white'
        />
        <path
          d='M81.356 135.679C81.356 135.679 81.2648 135.716 81.21 135.716L43.8587 141.402C43.4208 141.475 43.0011 141.164 42.9281 140.707C42.8551 140.25 43.1653 139.848 43.6215 139.775L80.9728 134.089C81.4108 134.016 81.8304 134.326 81.9034 134.784C81.9582 135.186 81.721 135.551 81.356 135.679Z'
          fill='white'
        />
        <path
          d='M127.229 157.199C127.229 157.199 127.137 157.235 127.083 157.235L91.5013 162.647C91.0634 162.72 90.6437 162.409 90.5707 161.952C90.4977 161.513 90.8079 161.093 91.2641 161.02L126.845 155.608C127.283 155.535 127.703 155.846 127.776 156.303C127.831 156.705 127.594 157.071 127.229 157.199Z'
          fill='white'
        />
        <path
          d='M117.338 174.275C115.879 174.275 114.711 173.104 114.711 171.642C114.711 150.617 131.79 133.523 152.756 133.523C154.215 133.523 155.383 134.693 155.383 136.156C155.383 137.619 154.215 138.789 152.756 138.789C134.673 138.789 119.966 153.524 119.966 171.642C119.966 173.104 118.798 174.275 117.338 174.275Z'
          fill='white'
        />
        <path
          d='M188.155 201.13C188.155 201.13 188.283 201.02 188.356 200.966C194.687 192.757 198.464 182.446 198.464 171.275C198.464 144.419 176.732 122.645 149.928 122.645C123.123 122.645 101.391 144.419 101.391 171.275C101.391 198.132 123.123 219.906 149.928 219.906C161.04 219.906 171.277 216.14 179.451 209.851L185.071 204.22L188.155 201.13ZM149.928 212.94C126.955 212.94 108.343 194.274 108.343 171.275C108.343 148.276 126.973 129.61 149.928 129.61C172.882 129.61 191.512 148.276 191.512 171.275C191.512 194.274 172.882 212.94 149.928 212.94Z'
          fill='#3539BC'
        />
        <path
          d='M185.071 204.22C184.341 204.951 184.341 206.121 185.071 206.853L186.95 208.736C189.231 206.432 191.621 204.238 192.972 203.324L190.782 201.13C190.125 200.472 189.085 200.417 188.355 200.966C188.282 201.021 188.209 201.057 188.155 201.13L185.071 204.22Z'
          fill='#4F66B3'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M184.853 204.482C184.916 204.39 184.989 204.302 185.071 204.22L188.155 201.13C188.195 201.077 188.244 201.043 188.296 201.007C188.316 200.994 188.336 200.981 188.355 200.966C189.085 200.417 190.125 200.472 190.782 201.13L192.972 203.324C191.622 204.238 189.231 206.432 186.95 208.736L186.95 208.736C184.597 211.112 182.389 213.598 181.641 214.86L179.36 212.575C178.63 211.843 178.63 210.673 179.36 209.942L179.451 209.851L185.071 204.22C184.989 204.302 184.916 204.39 184.853 204.482Z'
          fill='#3539BC'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M193.994 202.903L228.17 237.529C231.655 241.04 231.655 246.744 228.17 250.272C228.142 250.3 228.112 250.326 228.079 250.348C224.589 253.803 219.005 253.779 215.562 250.275L181.385 215.649C181.276 215.539 181.385 215.265 181.623 214.844C182.389 213.565 184.579 211.096 186.932 208.72C189.025 206.607 191.209 204.586 192.598 203.561C193.335 203.014 193.846 202.749 193.994 202.903Z'
          fill='#3539BC'
        />
        <path
          d='M13.7946 164.002L0 170.986L7.00679 184.716L20.8014 177.732L13.7764 164.002H13.7946Z'
          fill='#58C11E'
        />
        <path
          d='M40.9268 124.508L121.927 112.508'
          stroke='white'
          strokeWidth='2'
          strokeLinecap='round'
        />
        <path d='M38 109.002L119 97.002' stroke='white' strokeWidth='2' strokeLinecap='round' />
        <path d='M36 94.002L86 87.002' stroke='white' strokeWidth='2' strokeLinecap='round' />
      </g>
      <defs>
        <linearGradient
          id='paint0_linear_2313_14382'
          x1='77.2869'
          y1='152.464'
          x2='182.624'
          y2='174.012'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#EEEFFA' />
          <stop offset='1' stopColor='#D0D3E4' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_2313_14382'
          x1='61.9229'
          y1='65.4225'
          x2='115.004'
          y2='218.637'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#EEEFFA' />
          <stop offset='1' stopColor='#D0D3E4' />
        </linearGradient>
        <clipPath id='clip0_2313_14382'>
          <rect width='267.258' height='253.001' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconSearch;
