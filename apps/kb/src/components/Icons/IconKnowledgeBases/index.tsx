import type { IconCustomProps } from '@/types';

const IconKnowledgeBases = (props: IconCustomProps) => {
  return (
    <svg
      width='200'
      height='200'
      viewBox='0 0 200 200'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      role='img'
      aria-labelledby='knowledge-bases-icon-title'
      {...props}
    >
      <title id='knowledge-bases-icon-title'>Knowledge bases</title>
      <g clipPath='url(#clip0_2189_15554)'>
        <circle cx='150.307' cy='142.06' r='40.8566' fill='#EDEDED' />
        <path
          d='M135.697 68.0796L130.149 97.6497C146.488 100.49 162.117 89.7701 165.344 73.5504L135.694 68.0667L135.697 68.0796Z'
          fill='#EDEDED'
        />
        <path
          d='M130.697 56.2519L146.78 35.204C134.963 26.4416 118.352 28.6061 109.387 40.0715L130.687 56.2466L130.708 56.2571L130.697 56.2519Z'
          fill='#E4007F'
        />
        <path
          d='M196.335 77.963L198.484 67.2745L187.249 65.2188L196.343 77.963H196.335Z'
          fill='#F93549'
        />
        <path
          opacity='0.8'
          d='M88.446 18.9563C92.2606 15.1919 92.3281 9.02047 88.5467 5.162C84.7653 1.30353 78.6343 1.16464 74.7241 4.86928L88.446 18.9563Z'
          fill='#FFD100'
        />
        <path
          opacity='0.8'
          d='M196.612 34.9044L182.008 47.0781L184.645 50.2735L199.249 38.0997L196.612 34.9044Z'
          fill='#1D7ECE'
        />
        <path
          opacity='0.5'
          d='M68.5752 64.2024L47.1866 168.871C47.0636 169.488 47.46 170.091 48.075 170.214L124.732 185.976C125.347 186.099 125.949 185.702 126.072 185.085L147.46 80.416C147.583 79.7992 147.187 79.1962 146.572 79.0728L69.9146 63.3115C69.2996 63.1882 68.6982 63.5856 68.5752 64.2024Z'
          fill='url(#paint0_linear_2189_15554)'
        />
        <path
          d='M65.7189 61.2961L44.3302 165.965C44.2072 166.582 44.6036 167.185 45.2186 167.308L121.876 183.069C122.491 183.193 123.092 182.795 123.215 182.179L144.604 77.5097C144.727 76.893 144.331 76.2899 143.716 76.1666L67.0582 60.4053C66.4432 60.2819 65.8419 60.6794 65.7189 61.2961Z'
          fill='#B0D4FE'
        />
        <path
          opacity='0.5'
          d='M113.047 156.796L35.6791 168.583C35.0504 168.678 34.4764 168.254 34.3808 167.623L18.3769 61.9812C18.2812 61.3507 18.7049 60.7751 19.3336 60.6791L96.7016 48.8924C97.3303 48.7965 97.9043 49.2214 97.9999 49.8518L113.99 155.494C114.086 156.124 113.662 156.7 113.033 156.796H113.047Z'
          fill='url(#paint1_linear_2189_15554)'
        />
        <path
          d='M15.6845 58.5962L32.0301 166.486L111.653 154.356L95.3076 46.4668L15.6845 58.5962Z'
          fill='white'
        />
        <path
          d='M15.8487 59.7191L31.8525 165.361C31.9482 165.978 32.5222 166.416 33.1509 166.32L110.519 154.534C111.134 154.438 111.571 153.862 111.476 153.232L95.4717 47.5898C95.376 46.973 94.802 46.5344 94.1734 46.6304L16.8053 58.4308C16.1903 58.5267 15.753 59.1024 15.8487 59.7328V59.7191Z'
          fill='#C9E3FF'
        />
        <path
          opacity='0.07'
          d='M58.3251 56.8146L24.2536 62.009L23.7616 58.7745C23.6523 58.1166 24.117 57.4999 24.773 57.4039L56.5211 52.5659C57.1497 52.47 57.7374 52.9085 57.8331 53.539L58.3251 56.8146Z'
          fill='#ECF0F5'
        />
        <path
          d='M93.5582 108.675C93.5582 108.675 93.4899 108.702 93.4489 108.702L34.2714 117.707C33.9434 117.762 33.6291 117.529 33.5744 117.186C33.5198 116.857 33.7521 116.542 34.0938 116.487L93.2712 107.483C93.5992 107.428 93.9136 107.661 93.9682 108.003C94.0092 108.305 93.8316 108.579 93.5582 108.675Z'
          fill='white'
        />
        <path
          d='M97.0433 131.689C97.0433 131.689 96.9749 131.716 96.9339 131.716L37.7565 140.721C37.4285 140.775 37.1142 140.542 37.0595 140.2C37.0048 139.871 37.2372 139.542 37.5788 139.501L96.7563 130.496C97.0843 130.441 97.3986 130.674 97.4533 131.017C97.4943 131.319 97.3166 131.593 97.0433 131.689Z'
          fill='white'
        />
        <path
          d='M64.1061 124.931C64.1061 124.931 64.0378 124.958 63.9968 124.958L36.0208 129.221C35.6928 129.275 35.3784 129.042 35.3238 128.7C35.2691 128.357 35.5014 128.042 35.8431 128.001L63.8191 123.738C64.1471 123.684 64.4615 123.917 64.5161 124.259C64.5571 124.561 64.3795 124.835 64.1061 124.931Z'
          fill='white'
        />
        <path
          d='M60.9355 103.96C60.9355 103.96 60.8672 103.988 60.8262 103.988L32.8501 108.25C32.5221 108.305 32.2078 108.072 32.1531 107.729C32.0984 107.387 32.3308 107.085 32.6725 107.03L60.6485 102.768C60.9765 102.713 61.2908 102.946 61.3455 103.289C61.3865 103.59 61.2088 103.864 60.9355 103.96Z'
          fill='white'
        />
        <path
          d='M95.2939 120.093C95.2939 120.093 95.2256 120.12 95.1846 120.12L68.5342 124.177C68.2062 124.232 67.8919 123.999 67.8372 123.656C67.7825 123.327 68.0149 123.012 68.3565 122.957L95.0069 118.901C95.3349 118.846 95.6492 119.079 95.7039 119.421C95.7449 119.723 95.5672 119.997 95.2939 120.093Z'
          fill='white'
        />
        <path
          d='M10.3321 125.193L0 130.429L5.24806 140.722L15.5802 135.486L10.3185 125.193H10.3321Z'
          fill='#58C11E'
        />
        <path
          d='M30.6813 95.5859L91.4039 86.59'
          stroke='white'
          strokeWidth='2'
          strokeLinecap='round'
        />
        <path
          d='M28.4872 83.9609L89.2099 74.965'
          stroke='white'
          strokeWidth='2'
          strokeLinecap='round'
        />
        <path
          d='M26.9879 72.7168L64.471 67.4692'
          stroke='white'
          strokeWidth='2'
          strokeLinecap='round'
        />
      </g>
      <defs>
        <linearGradient
          id='paint0_linear_2189_15554'
          x1='57.8878'
          y1='116.544'
          x2='136.791'
          y2='132.67'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#EEEFFA' />
          <stop offset='1' stopColor='#D0D3E4' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_2189_15554'
          x1='46.3803'
          y1='51.2909'
          x2='86.2007'
          y2='166.128'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#EEEFFA' />
          <stop offset='1' stopColor='#D0D3E4' />
        </linearGradient>
        <clipPath id='clip0_2189_15554'>
          <rect width='200' height='186' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconKnowledgeBases;
