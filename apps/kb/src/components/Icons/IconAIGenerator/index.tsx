import type { IconCustomProps } from '@/types';

const IconAIGenerator: React.FC<
  IconCustomProps & {
    color?: string;
    width?: number;
    height?: number;
  }
> = ({ color = '#1D2088', width = 24, height = 24, ...props }) => (
  <svg
    className='ai-generator-icon'
    width={width}
    height={height}
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    aria-label='AI generator icon'
    {...props}
  >
    <title>AI generator icon</title>
    <rect width={width} height={height} fill={color} fillOpacity='0.01' />
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M4.08569 17.1935C4.08569 16.6606 4.51775 16.2285 5.05073 16.2285H8.52356C9.05653 16.2285 9.48859 16.6606 9.48859 17.1935C9.48859 17.7265 9.05653 18.1586 8.52356 18.1586H6.01576V20.6664C6.01576 21.1994 5.5837 21.6314 5.05073 21.6314C4.51775 21.6314 4.08569 21.1994 4.08569 20.6664V17.1935Z'
      fill={color}
    />
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M13.7506 3.76C12.0069 3.37132 10.1835 3.57255 8.56655 4.3321C6.94957 5.09166 5.63051 6.36655 4.81635 7.95674C4.0022 9.54693 3.73902 11.3624 4.06812 13.1183C4.1663 13.6422 3.82122 14.1465 3.29737 14.2446C2.77352 14.3428 2.26926 13.9977 2.17108 13.4739C1.76323 11.2978 2.08939 9.04787 3.09836 7.07716C4.10734 5.10644 5.74204 3.52648 7.74595 2.58517C9.74987 1.64386 12.0096 1.39448 14.1705 1.87617C16.3315 2.35785 18.2714 3.54334 19.6858 5.24662C20.0263 5.65664 19.9699 6.26506 19.5599 6.60556C19.1499 6.94605 18.5415 6.88969 18.201 6.47966C17.0596 5.10527 15.4943 4.14868 13.7506 3.76ZM20.6953 9.0288C21.2191 8.93062 21.7234 9.2757 21.8216 9.79955C22.2294 11.9756 21.9033 14.2256 20.8943 16.1963C19.8853 18.167 18.2506 19.747 16.2467 20.6883C14.2428 21.6296 11.9831 21.879 9.8221 21.3973C7.66115 20.9156 5.72126 19.7301 4.30684 18.0268C3.96634 17.6168 4.02271 17.0084 4.43274 16.6679C4.84277 16.3274 5.45119 16.3838 5.79168 16.7938C6.933 18.1682 8.49832 19.1248 10.242 19.5134C11.9857 19.9021 13.8091 19.7009 15.4261 18.9413C17.0431 18.1818 18.3621 16.9069 19.1763 15.3167C19.9905 13.7265 20.2536 11.911 19.9245 10.1551C19.8264 9.63124 20.1714 9.12698 20.6953 9.0288Z'
      fill={color}
    />
    <path
      fillRule='evenodd'
      clipRule='evenodd'
      d='M18.942 1.64258C19.4749 1.64258 19.907 2.07464 19.907 2.60761V6.08044C19.907 6.61342 19.4749 7.04548 18.942 7.04548H15.4691C14.9362 7.04548 14.5041 6.61342 14.5041 6.08044C14.5041 5.54747 14.9362 5.11541 15.4691 5.11541H17.9769V2.60761C17.9769 2.07464 18.409 1.64258 18.942 1.64258ZM11.9912 9.28415C12.2705 9.83348 12.6427 10.3342 13.0943 10.763C13.4769 11.1262 13.9082 11.4298 14.3735 11.6663C13.9084 11.9027 13.4773 12.2061 13.0948 12.5691C12.6434 12.9974 12.2714 13.4976 11.9921 14.0464C11.7165 13.5005 11.349 13.0024 10.9025 12.5751C10.5203 12.2093 10.0888 11.9036 9.62282 11.6656C10.0888 11.4273 10.5204 11.1213 10.9025 10.7553C11.3487 10.328 11.7159 9.82988 11.9912 9.28415ZM7.75609 10.2773C8.43744 10.1587 9.06805 9.83983 9.56752 9.36145C10.0668 8.88324 10.4124 8.26725 10.5604 7.59194C10.5605 7.59161 10.5606 7.59128 10.5607 7.59095L10.5896 7.45722L10.5901 7.45495C10.9171 5.96215 13.0416 5.95337 13.3822 7.44157C13.3822 7.44128 13.3823 7.44185 13.3822 7.44157L13.4188 7.60068C13.5726 8.27431 13.922 8.88749 14.4231 9.36321C14.9241 9.83879 15.5544 10.1558 16.2348 10.2744M7.75609 10.2773C6.19841 10.5482 6.19825 12.7847 7.75609 13.0555C7.76983 13.0579 7.78357 13.0599 7.7973 13.0617C8.46296 13.1856 9.07843 13.5009 9.56805 13.9695C10.0669 14.4469 10.4125 15.0618 10.5611 15.736L10.588 15.8654L10.5901 15.875C10.917 17.3674 13.0405 17.3766 13.382 15.8896C13.3821 15.8889 13.3823 15.8882 13.3824 15.8876L13.4181 15.7341L13.4188 15.731C13.5727 15.0576 13.9222 14.4446 14.4233 13.9691C14.9244 13.4936 15.5548 13.1767 16.2354 13.0582L16.2368 13.058C17.7862 12.7858 17.8041 10.5468 16.2348 10.2744'
      fill={color}
    />
  </svg>
);

export default IconAIGenerator;
