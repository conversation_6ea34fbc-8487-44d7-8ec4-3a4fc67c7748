import type { IconCustomProps } from '@/types';

const IconUnlink = (props: IconCustomProps) => {
  return (
    <svg
      width='20'
      height='20'
      viewBox='0 0 20 20'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      role='img'
      aria-labelledby='unlink-icon-title'
      {...props}
    >
      <title id='unlink-icon-title'>Unlink</title>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6.66666 1.66699C7.12689 1.66699 7.49999 2.04009 7.49999 2.50033V4.16699C7.49999 4.62723 7.12689 5.00033 6.66666 5.00033C6.20642 5.00033 5.83332 4.62723 5.83332 4.16699V2.50033C5.83332 2.04009 6.20642 1.66699 6.66666 1.66699ZM11.0774 3.57764C11.7862 2.86883 12.7476 2.47062 13.75 2.47062C14.7524 2.47062 15.7138 2.86883 16.4226 3.57764C17.1314 4.28646 17.5296 5.24782 17.5296 6.25023C17.5296 7.25265 17.1314 8.21401 16.4226 8.92282L13.0921 12.2533C12.7436 12.6082 12.3279 12.8902 11.8694 13.0828C11.4094 13.2761 10.9156 13.3757 10.4167 13.3757C9.91776 13.3757 9.42388 13.2761 8.96394 13.0828C8.504 12.8896 8.08724 12.6065 7.73809 12.2501C7.41599 11.9214 7.42137 11.3938 7.75011 11.0717C8.07885 10.7496 8.60646 10.7549 8.92856 11.0837C9.12254 11.2817 9.35407 11.4389 9.60959 11.5463C9.86511 11.6537 10.1395 11.709 10.4167 11.709C10.6938 11.709 10.9682 11.6537 11.2237 11.5463C11.4792 11.4389 11.7108 11.2817 11.9048 11.0837L11.9107 11.0776L11.9107 11.0776L15.2441 7.74431C15.6403 7.34806 15.8629 6.81062 15.8629 6.25023C15.8629 5.68985 15.6403 5.15241 15.2441 4.75616C14.8478 4.3599 14.3104 4.13729 13.75 4.13729C13.1896 4.13729 12.6522 4.3599 12.2559 4.75616L11.8392 5.17282C11.5138 5.49826 10.9862 5.49826 10.6607 5.17282C10.3353 4.84739 10.3353 4.31975 10.6607 3.99431L11.0774 3.57764ZM1.66666 6.66699C1.66666 6.20676 2.03975 5.83366 2.49999 5.83366H4.16666C4.62689 5.83366 4.99999 6.20676 4.99999 6.66699C4.99999 7.12723 4.62689 7.50033 4.16666 7.50033H2.49999C2.03975 7.50033 1.66666 7.12723 1.66666 6.66699ZM8.13061 6.9177C8.59055 6.72443 9.08443 6.62488 9.58333 6.62488C10.0822 6.62488 10.5761 6.72443 11.0361 6.9177C11.496 7.11097 11.9127 7.39407 12.2619 7.75042C12.584 8.07917 12.5786 8.60678 12.2499 8.92887C11.9211 9.25097 11.3935 9.24559 11.0714 8.91685C10.8775 8.71887 10.6459 8.56159 10.3904 8.45422C10.1349 8.34685 9.8605 8.29154 9.58333 8.29154C9.30617 8.29154 9.03179 8.34685 8.77627 8.45422C8.52074 8.56159 8.28921 8.71887 8.09524 8.91685L8.08929 8.92292L8.08926 8.92289L4.75592 12.2562C4.35967 12.6525 4.13705 13.1899 4.13705 13.7503C4.13705 14.3107 4.35967 14.8481 4.75592 15.2444C5.15218 15.6406 5.68961 15.8632 6.25 15.8632C6.81039 15.8632 7.34782 15.6406 7.74408 15.2444L8.16074 14.8277C8.48618 14.5023 9.01382 14.5023 9.33926 14.8277C9.66469 15.1532 9.66469 15.6808 9.33926 16.0062L8.92259 16.4229C8.21377 17.1317 7.25242 17.5299 6.25 17.5299C5.24758 17.5299 4.28623 17.1317 3.57741 16.4229C2.8686 15.7141 2.47039 14.7527 2.47039 13.7503C2.47039 12.7479 2.8686 11.7865 3.57741 11.0777L6.90789 7.74723C7.25641 7.39238 7.67205 7.11039 8.13061 6.9177ZM17.5 14.167H15.8333C15.3731 14.167 15 13.7939 15 13.3337C15 12.8734 15.3731 12.5003 15.8333 12.5003H17.5C17.9602 12.5003 18.3333 12.8734 18.3333 13.3337C18.3333 13.7939 17.9602 14.167 17.5 14.167ZM13.3333 15.0003C13.7936 15.0003 14.1667 15.3734 14.1667 15.8337V17.5003C14.1667 17.9606 13.7936 18.3337 13.3333 18.3337C12.8731 18.3337 12.5 17.9606 12.5 17.5003V15.8337C12.5 15.3734 12.8731 15.0003 13.3333 15.0003Z'
        fill='#DF2F41'
      />
    </svg>
  );
};

export default IconUnlink;
