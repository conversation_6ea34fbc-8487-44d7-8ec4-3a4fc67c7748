import type { IconCustomProps } from '@/types';

const IconMd = (props: IconCustomProps) => {
  return (
    <svg
      width='36'
      height='36'
      viewBox='0 0 36 36'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      aria-label='Markdown file'
      {...props}
    >
      <title>Markdown file</title>
      <path
        d='M33.0836 12.7039C33.0836 12.3296 32.9553 11.9858 32.7443 11.7094C32.5834 11.3503 32.3681 11.013 32.1006 10.7149C32.0811 10.6931 32.0615 10.6736 32.0419 10.654L24.6717 3.30286C23.9519 2.52597 22.9363 2.08203 21.8772 2.08203H5.66013C5.63621 2.08203 5.61446 2.08203 5.59054 2.08203C3.64632 2.16473 2.12618 3.80556 2.18707 5.74671V18.0965C2.18707 18.9974 2.91778 19.7286 3.81813 19.7286C4.71847 19.7286 5.44918 18.9974 5.44918 18.0965V5.71841C5.44918 5.69448 5.44918 5.67272 5.44918 5.64878C5.44266 5.49427 5.55792 5.36152 5.71015 5.34629H21.216V12.7039C21.216 13.6049 21.9468 14.3361 22.8471 14.3361H29.8215V18.2771C29.8215 19.178 30.5522 19.9092 31.4526 19.9092C32.3529 19.9092 33.0836 19.178 33.0836 18.2771V13.2719C33.0836 13.1566 33.0771 13.0412 33.0684 12.9281C33.0793 12.8541 33.0858 12.7801 33.0858 12.7039H33.0836ZM24.4782 7.72049L27.8381 11.074H24.4782V7.72049Z'
        fill='#1D2088'
      />
      <path
        d='M25.7644 21.9102C26.3878 21.9102 26.9667 21.9889 27.5011 22.1465C28.0424 22.3041 28.5117 22.5404 28.909 22.8556C29.3132 23.1639 29.6284 23.551 29.8545 24.0168C30.0874 24.4827 30.2039 25.0274 30.2039 25.6508V32.0223C30.2039 32.5566 30.1456 33.0294 30.0292 33.4404C29.9127 33.8446 29.7585 34.1975 29.5667 34.4989C29.3817 34.7935 29.1693 35.047 28.9296 35.2594C28.6898 35.4649 28.4466 35.6327 28.1999 35.7629C27.6107 36.0781 26.9428 36.2562 26.196 36.2973H22.7842C22.3937 36.2973 22.0922 36.2322 21.8799 36.102C21.6675 35.965 21.5133 35.7869 21.4174 35.5677C21.3215 35.3416 21.2633 35.0881 21.2427 34.8072C21.229 34.5263 21.2222 34.2351 21.2222 33.9337V24.2532C21.2222 23.9586 21.229 23.6709 21.2427 23.39C21.2633 23.1091 21.3215 22.859 21.4174 22.6398C21.5133 22.4206 21.6675 22.2459 21.8799 22.1157C22.0922 21.9787 22.3937 21.9102 22.7842 21.9102H25.7644ZM24.3462 24.3149V33.8515H25.6822C25.9768 33.8378 26.2405 33.7693 26.4735 33.646C26.5694 33.598 26.6653 33.5329 26.7612 33.4507C26.8571 33.3685 26.9428 33.2726 27.0181 33.163C27.0935 33.0465 27.1517 32.9095 27.1928 32.7519C27.2408 32.5943 27.2648 32.4128 27.2648 32.2072V26.1441C27.2648 25.918 27.2408 25.7193 27.1928 25.548C27.1517 25.3699 27.0935 25.2158 27.0181 25.0856C26.9428 24.9554 26.8571 24.8458 26.7612 24.7568C26.6653 24.6677 26.5694 24.5958 26.4735 24.5409C26.2405 24.4108 25.9768 24.3354 25.6822 24.3149H24.3462Z'
        fill='#1D2088'
      />
      <path
        d='M11.521 32.9677C11.3224 32.9677 11.1511 32.93 11.0072 32.8547C10.8702 32.7793 10.7503 32.6834 10.6475 32.5669C10.5448 32.4504 10.4557 32.3203 10.3803 32.1764C10.3118 32.0325 10.2536 31.8921 10.2056 31.7551L8.25311 26.1235H7.98592V34.6942C7.98592 34.9134 7.97221 35.1189 7.94481 35.3107C7.91741 35.5026 7.85575 35.6739 7.75983 35.8246C7.67077 35.9684 7.53375 36.0849 7.34877 36.174C7.17065 36.2562 6.92401 36.2973 6.60886 36.2973C6.25261 36.2973 5.97172 36.2562 5.76619 36.174C5.56066 36.0849 5.40651 35.9684 5.30374 35.8246C5.20098 35.6739 5.13589 35.5026 5.10849 35.3107C5.08108 35.1189 5.06738 34.9134 5.06738 34.6942V24.2532C5.06738 23.9586 5.07423 23.6709 5.08794 23.39C5.10849 23.1091 5.16672 22.859 5.26264 22.6398C5.35855 22.4206 5.50927 22.2459 5.7148 22.1157C5.92719 21.9787 6.2252 21.9102 6.60886 21.9102H8.00647C8.2394 21.9102 8.43466 21.9204 8.59223 21.941C8.75666 21.9547 8.90738 22.0129 9.0444 22.1157C9.18142 22.2116 9.31501 22.3692 9.44518 22.5884C9.5822 22.8076 9.7295 23.1194 9.88707 23.5236C10.0515 23.9209 10.2399 24.4313 10.4523 25.0548C10.6647 25.6714 10.925 26.4318 11.2333 27.3362L11.5005 28.1583H11.7471L12.076 27.1306C12.3569 26.2674 12.5967 25.5378 12.7953 24.9417C12.994 24.3457 13.1687 23.8558 13.3194 23.4722C13.4702 23.0817 13.6038 22.7837 13.7202 22.5781C13.8435 22.3657 13.9703 22.2116 14.1005 22.1157C14.2375 22.0129 14.3848 21.9547 14.5423 21.941C14.6999 21.9204 14.8917 21.9102 15.1178 21.9102H16.6182C17.0019 21.9102 17.2965 21.9787 17.502 22.1157C17.7144 22.2459 17.8685 22.4206 17.9644 22.6398C18.0603 22.859 18.1151 23.1091 18.1288 23.39C18.1494 23.6709 18.1597 23.9586 18.1597 24.2532V34.6942C18.1597 34.9134 18.146 35.1189 18.1186 35.3107C18.0912 35.5026 18.0261 35.6739 17.9233 35.8246C17.8206 35.9684 17.6664 36.0849 17.4609 36.174C17.2553 36.2562 16.9745 36.2973 16.6182 36.2973C16.3031 36.2973 16.053 36.2562 15.868 36.174C15.6899 36.0849 15.5529 35.9684 15.457 35.8246C15.3679 35.6739 15.3097 35.5026 15.2823 35.3107C15.2548 35.1189 15.2411 34.9134 15.2411 34.6942V26.1235H14.9534L13.0214 31.7551C12.9666 31.8921 12.9049 32.0325 12.8364 32.1764C12.7679 32.3203 12.6823 32.4504 12.5795 32.5669C12.4768 32.6834 12.3569 32.7793 12.2198 32.8547C12.0828 32.93 11.9184 32.9677 11.7266 32.9677H11.521Z'
        fill='#1D2088'
      />
    </svg>
  );
};

export default IconMd;
