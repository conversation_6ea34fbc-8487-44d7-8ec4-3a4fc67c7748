import type { IconCustomProps } from '@/types';

const IconArticle = (props: IconCustomProps) => {
  return (
    <svg
      width='120'
      height='120'
      viewBox='0 0 120 120'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      aria-label='Article icon'
      {...props}
    >
      <title>Article icon</title>
      <g clipPath='url(#clip0_2271_3468)'>
        <path
          d='M90 104.659C93.4222 110.054 100.42 111.625 105.712 108.157C111.003 104.688 112.575 97.4745 109.278 92L90.0096 104.659H90Z'
          fill='#E6E6E6'
        />
        <path
          d='M67.7424 60.1694L85.9938 9.17943C57.735 -0.527156 26.8685 14.0185 16.5374 41.9322L67.7424 60.1694Z'
          fill='#E6E6E6'
        />
        <path
          opacity='0.4'
          d='M84 95C100.016 95 113 82.0163 113 66C113 49.9837 100.016 37 84 37C67.9837 37 55 49.9837 55 66C55 82.0163 67.9837 95 84 95Z'
          fill='#B3B3B3'
          fillOpacity='0.8'
        />
        <path
          d='M0.740112 62L-1 74.5661C5.94756 75.4389 12.328 70.6256 13.3463 63.7328L0.727223 62H0.740112Z'
          fill='#9B2E8D'
        />
        <path
          opacity='0.7'
          d='M14.6745 11.0007L8 14.3828L11.3964 21.0293L18.0709 17.6471L14.6745 11.0007Z'
          fill='#ECAE00'
        />
        <path d='M103.679 38.9023L115.1 41.7775L118 30.418L103.679 38.9023Z' fill='#D9D9D9' />
        <path
          d='M23.5744 79L14 87.166L15.7944 89.2522L25.3687 81.0862L23.5744 79Z'
          fill='#33A4E4'
        />
        <path
          opacity='0.7'
          fillRule='evenodd'
          clipRule='evenodd'
          d='M42.7555 23.668C37.1877 23.668 32.6622 27.5572 32.6622 32.3449V100.002H82.3561C87.9239 100.002 92.4494 96.1123 92.4494 91.3246V28.8792C95.7974 28.8792 98.5078 26.5432 98.5078 23.6808H42.7678L42.7555 23.668Z'
          fill='#E4007F'
        />
        <path
          d='M102.702 28.8781H92.7562C92.7562 26.0029 94.9883 23.6797 97.7232 23.6797C100.458 23.6797 102.69 26.0158 102.69 28.8781H102.702Z'
          fill='#E4007F'
        />
        <path
          d='M26.4443 94.8027H36.9791C36.9791 97.6779 34.6244 100.001 31.7179 100.001C28.8113 100.001 26.4566 97.6651 26.4566 94.8027H26.4443Z'
          fill='#E4007F'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M78.4438 94.8027H31.7179V100.001H83.7174C80.8108 100.001 78.4561 97.6651 78.4561 94.8027H78.4438Z'
          fill='#BF2C6F'
        />
        <path
          d='M75.1027 80.5966C74.7471 81.1742 75.3848 81.8673 75.9489 81.5079L80.6461 78.5043C80.9895 78.2861 81.0508 77.7984 80.7687 77.4903L79.0395 75.642C78.7574 75.3468 78.2914 75.3981 78.0706 75.7447L75.1027 80.5966Z'
          fill='#C6C7D7'
        />
        <path
          d='M80.6951 79.1719L77.4329 75.6806L100.949 54L104.211 57.4913L92.4532 68.3316L80.6951 79.1719Z'
          fill='#3539BC'
        />
        <path d='M44 48H80' stroke='white' strokeWidth='2' strokeLinecap='round' />
        <path d='M44 57H80' stroke='white' strokeWidth='2' strokeLinecap='round' />
        <path d='M44 65H80' stroke='white' strokeWidth='2' strokeLinecap='round' />
        <path d='M44 74H69' stroke='white' strokeWidth='2' strokeLinecap='round' />
        <path d='M44 82H65' stroke='white' strokeWidth='2' strokeLinecap='round' />
        <path d='M45 38H66' stroke='white' strokeWidth='2' strokeLinecap='round' />
      </g>
      <defs>
        <clipPath id='clip0_2271_3468'>
          <rect width='120' height='120' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconArticle;
