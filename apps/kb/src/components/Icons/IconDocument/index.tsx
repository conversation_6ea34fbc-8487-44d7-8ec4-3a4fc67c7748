import type { IconCustomProps } from '@/types';

const IconDocument = (props: IconCustomProps) => {
  return (
    <svg
      width='121'
      height='120'
      viewBox='0 0 121 120'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      aria-label='Document file icon'
      {...props}
    >
      <title>Document file icon</title>
      <g clipPath='url(#clip0_2271_5008)'>
        <path
          d='M50.6287 98.9177C60.6321 114.166 81.1392 118.599 96.6306 108.793C112.108 98.9868 116.735 78.6425 107.065 63.1875L50.6287 98.9315V98.9177Z'
          fill='#E6E6E6'
        />
        <path
          d='M99.2704 28.2583L107.343 10.5934C97.5198 6.28423 86.0437 10.5796 81.5004 20.2338L99.2704 28.2583Z'
          fill='#E6E6E6'
        />
        <path
          d='M93.6296 76.2251L97.4087 57.5106C87.0301 55.5632 76.985 62.2065 74.7898 72.4684L93.6157 76.2251H93.6296Z'
          fill='#D9453E'
        />
        <path
          d='M110.296 35.3486L103.808 38.6309L107.11 45.081L113.598 41.7987L110.296 35.3486Z'
          fill='#7FB549'
        />
        <path d='M8.4475 75.383L10.601 64.1819L-0.666748 62.0273L8.4475 75.383Z' fill='#D9D9D9' />
        <path
          opacity='0.8'
          d='M45.7659 5C44.2098 7.98327 45.363 11.6709 48.364 13.2454C51.3651 14.8199 55.0608 13.715 56.6864 10.7594L45.7659 5Z'
          fill='#D02381'
        />
        <path
          opacity='0.8'
          d='M119.316 64.141L108.437 59.2285L107.357 61.5926L118.236 66.505L119.316 64.141Z'
          fill='#1D7ECE'
        />
        <path
          opacity='0.8'
          d='M92.3333 41.9107L34.6882 34.6309L26.4077 99.4256L84.0528 106.705L92.3333 41.9107Z'
          fill='#CCCCCC'
        />
        <path
          opacity='0.9'
          d='M88.9293 35.4126L31.2842 28.1328L22.6865 95.4106L80.3316 102.69L88.9293 35.4126Z'
          fill='#FFC94F'
        />
        <path opacity='0.8' d='M76.1323 30.0254H18.0239V95.3269H76.1323V30.0254Z' fill='#CCCCCC' />
        <path opacity='0.9' d='M72.0161 23.7402H13.9077V91.5587H72.0161V23.7402Z' fill='#FFC94F' />
        <path
          d='M29.0713 35.0449H56.8524'
          stroke='white'
          strokeWidth='2'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M21.4895 43.834H64.4343'
          stroke='white'
          strokeWidth='2'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M21.4895 50.3164H64.4343'
          stroke='white'
          strokeWidth='2'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M21.4895 56.8125H64.4343'
          stroke='white'
          strokeWidth='2'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M21.4895 63.2949H64.4343'
          stroke='white'
          strokeWidth='2'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M21.4895 69.791H64.4343'
          stroke='white'
          strokeWidth='2'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M21.4895 76.2734H64.4343'
          stroke='white'
          strokeWidth='2'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M34.1212 82.7539H51.8026'
          stroke='white'
          strokeWidth='2'
          strokeLinecap='round'
          strokeLinejoin='round'
        />
        <path
          d='M22.0167 34.4637C21.8468 34.4946 21.6738 34.5081 21.494 34.5093L21.1519 34.5085C19.3284 34.5026 17.8478 33.0169 17.8537 31.1934L17.863 26.494C17.8634 26.1249 18.1655 25.8315 18.5302 25.8326C18.8993 25.833 19.1919 26.1308 19.1915 26.4999L19.1822 31.1993C19.1779 32.2891 20.0645 33.1808 21.1542 33.1851L21.4964 33.1859C22.343 33.1894 23.0367 32.5008 23.0394 31.6498L23.068 23.506C23.0711 22.6325 22.3603 21.9158 21.4824 21.9135L20.8972 21.9119C19.9654 21.9103 19.2115 22.6639 19.2048 23.5921L19.204 23.8847C19.2036 24.2538 18.9015 24.5472 18.5368 24.546C18.1721 24.5448 17.8751 24.2479 17.8755 23.8788L17.8763 23.5862C17.8802 21.9251 19.2368 20.5759 20.8979 20.5798L21.4832 20.5814C23.0905 20.5861 24.3952 21.8967 24.3905 23.504L24.362 31.6478C24.3565 33.0528 23.3444 34.2178 22.0072 34.4609L22.0167 34.4637Z'
          fill='#D9453E'
        />
      </g>
      <defs>
        <clipPath id='clip0_2271_5008'>
          <rect width='120' height='120' fill='white' transform='translate(0.333252)' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconDocument;
