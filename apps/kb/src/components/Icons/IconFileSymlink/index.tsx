import type { IconCustomProps } from '@/types';

const IconFileSymlink = (props: IconCustomProps) => {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      aria-label='File symlink icon'
      {...props}
    >
      <title>File symlink icon</title>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M7 4C6.73478 4 6.48043 4.10536 6.29289 4.29289C6.10536 4.48043 6 4.73478 6 5V11C6 11.5523 5.55228 12 5 12C4.44772 12 4 11.5523 4 11V5C4 4.20435 4.31607 3.44129 4.87868 2.87868C5.44129 2.31607 6.20435 2 7 2H14C14.2652 2 14.5196 2.10536 14.7071 2.29289L19.7071 7.29289C19.8946 7.48043 20 7.73478 20 8V19C20 19.7957 19.6839 20.5587 19.1213 21.1213C18.5587 21.6839 17.7957 22 17 22H7.5C6.94772 22 6.5 21.5523 6.5 21C6.5 20.4477 6.94772 20 7.5 20H17C17.2652 20 17.5196 19.8946 17.7071 19.7071C17.8946 19.5196 18 19.2652 18 19V9H15C14.4696 9 13.9609 8.78929 13.5858 8.41421C13.2107 8.03914 13 7.53043 13 7V4H7ZM15 5.41421L16.5858 7H15V5.41421ZM8.29289 10.2929C8.68342 9.90237 9.31658 9.90237 9.70711 10.2929L12.7071 13.2929C13.0976 13.6834 13.0976 14.3166 12.7071 14.7071L9.70711 17.7071C9.31658 18.0976 8.68342 18.0976 8.29289 17.7071C7.90237 17.3166 7.90237 16.6834 8.29289 16.2929L9.58579 15H7C6.46957 15 5.96086 15.2107 5.58579 15.5858C5.21071 15.9609 5 16.4696 5 17V21C5 21.5523 4.55228 22 4 22C3.44772 22 3 21.5523 3 21V17C3 15.9391 3.42143 14.9217 4.17157 14.1716C4.92172 13.4214 5.93913 13 7 13H9.58579L8.29289 11.7071C7.90237 11.3166 7.90237 10.6834 8.29289 10.2929Z'
        fill='#5C5C5C'
      />
    </svg>
  );
};

export default IconFileSymlink;
