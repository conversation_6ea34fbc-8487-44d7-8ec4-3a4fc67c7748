const IconCirclePlusFilled = () => {
  return (
    <svg
      width='16'
      height='16'
      viewBox='0 0 16 16'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      aria-label='Circle plus filled icon'
    >
      <title>Circle plus filled icon</title>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M1.33331 7.99967C1.33331 6.23156 2.03569 4.53587 3.28593 3.28563C4.53618 2.03539 6.23187 1.33301 7.99998 1.33301C9.76809 1.33301 11.4638 2.03539 12.714 3.28563C13.9643 4.53587 14.6666 6.23156 14.6666 7.99967C14.6666 9.76779 13.9643 11.4635 12.714 12.7137C11.4638 13.964 9.76809 14.6663 7.99998 14.6663C6.23187 14.6663 4.53618 13.964 3.28593 12.7137C2.03569 11.4635 1.33331 9.76779 1.33331 7.99967ZM8.66665 5.19967C8.66665 5.02286 8.59641 4.85329 8.47138 4.72827C8.34636 4.60325 8.17679 4.53301 7.99998 4.53301C7.82317 4.53301 7.6536 4.60325 7.52858 4.72827C7.40355 4.85329 7.33331 5.02286 7.33331 5.19967V7.33301H5.19998C5.02317 7.33301 4.8536 7.40325 4.72858 7.52827C4.60355 7.65329 4.53331 7.82286 4.53331 7.99967C4.53331 8.17649 4.60355 8.34606 4.72858 8.47108C4.8536 8.5961 5.02317 8.66634 5.19998 8.66634H7.33331V10.7997C7.33331 10.9765 7.40355 11.1461 7.52858 11.2711C7.6536 11.3961 7.82317 11.4663 7.99998 11.4663C8.17679 11.4663 8.34636 11.3961 8.47138 11.2711C8.59641 11.1461 8.66665 10.9765 8.66665 10.7997V8.66634H10.8C10.9768 8.66634 11.1464 8.5961 11.2714 8.47108C11.3964 8.34606 11.4666 8.17649 11.4666 7.99967C11.4666 7.82286 11.3964 7.65329 11.2714 7.52827C11.1464 7.40325 10.9768 7.33301 10.8 7.33301H8.66665V5.19967Z'
        fill='#62D821'
      />
    </svg>
  );
};

export default IconCirclePlusFilled;
