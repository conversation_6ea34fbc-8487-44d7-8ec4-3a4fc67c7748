import type { IconCustomProps } from '@/types';

const IconDoc = (props: IconCustomProps) => {
  return (
    <svg
      width='38'
      height='39'
      viewBox='0 0 38 39'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      aria-label='Document icon'
      {...props}
    >
      <title>Document icon</title>
      <path
        d='M33.7701 12.6219C33.7701 12.2476 33.6418 11.9038 33.4309 11.6274C33.2699 11.2683 33.0546 10.931 32.7871 10.6329C32.7676 10.6111 32.748 10.5915 32.7284 10.5719L25.3582 3.22083C24.6384 2.44394 23.6228 2 22.5637 2H6.34665C6.32273 2 6.30098 2 6.27706 2C4.33284 2.08269 2.8127 3.72353 2.87359 5.66467V18.0145C2.87359 18.9154 3.60431 19.6466 4.50465 19.6466C5.40499 19.6466 6.1357 18.9154 6.1357 18.0145V5.63638C6.1357 5.61245 6.1357 5.59068 6.1357 5.56675C6.12918 5.41224 6.24444 5.27949 6.39667 5.26426H21.9026V12.6219C21.9026 13.5228 22.6333 14.254 23.5336 14.254H30.508V18.1951C30.508 19.096 31.2387 19.8272 32.1391 19.8272C33.0394 19.8272 33.7701 19.096 33.7701 18.1951V13.1899C33.7701 13.0745 33.7636 12.9592 33.7549 12.846C33.7658 12.7721 33.7723 12.6981 33.7723 12.6219H33.7701ZM25.1647 7.63846L28.5247 10.9919H25.1647V7.63846Z'
        fill='#1D2088'
      />
      <path
        d='M30.0429 21.8281C30.7965 21.8281 31.4439 21.9138 31.9852 22.085C32.5332 22.2563 32.9923 22.479 33.3622 22.753C33.739 23.0271 34.0405 23.3388 34.2665 23.6882C34.4926 24.0307 34.6639 24.3801 34.7804 24.7364C34.9037 25.0926 34.9825 25.4352 35.0167 25.764C35.0578 26.0929 35.0784 26.3772 35.0784 26.617C35.0784 26.7198 35.0681 26.8465 35.0476 26.9972C35.0339 27.1411 34.9756 27.2815 34.8729 27.4186C34.7701 27.5556 34.6057 27.672 34.3796 27.768C34.1604 27.8639 33.8452 27.9118 33.4341 27.9118C33.2971 27.9118 33.1258 27.905 32.9203 27.8913C32.7148 27.8707 32.5161 27.8262 32.3243 27.7577C32.1325 27.6823 31.968 27.5796 31.831 27.4494C31.694 27.3124 31.6255 27.124 31.6255 26.8842C31.6255 26.5142 31.6015 26.1819 31.5535 25.8874C31.5124 25.5928 31.4336 25.3427 31.3172 25.1372C31.2007 24.9316 31.0397 24.7741 30.8342 24.6644C30.6287 24.5548 30.3649 24.5 30.0429 24.5C29.7072 24.5 29.4469 24.572 29.2619 24.7158C29.0837 24.8528 28.9536 25.0036 28.8714 25.168C28.7755 25.3667 28.7206 25.5893 28.7069 25.836V32.3513C28.7206 32.6527 28.7755 32.9165 28.8714 33.1426C28.9536 33.3413 29.0837 33.5228 29.2619 33.6872C29.4469 33.8517 29.7072 33.9339 30.0429 33.9339C30.3649 33.9339 30.6287 33.8688 30.8342 33.7386C31.0397 33.6016 31.2007 33.42 31.3172 33.194C31.4336 32.9679 31.5124 32.7075 31.5535 32.4129C31.6015 32.1184 31.6255 31.8101 31.6255 31.4881C31.6255 31.3853 31.6426 31.2791 31.6769 31.1695C31.7111 31.0599 31.7899 30.9605 31.9132 30.8715C32.0365 30.7756 32.2181 30.6968 32.4579 30.6351C32.6977 30.5735 33.0231 30.5426 33.4341 30.5426C33.8452 30.5426 34.1604 30.5906 34.3796 30.6865C34.6057 30.7756 34.7701 30.8817 34.8729 31.0051C34.9756 31.1215 35.0339 31.238 35.0476 31.3545C35.0681 31.4641 35.0784 31.536 35.0784 31.5703C35.0784 32.3239 34.9516 32.9987 34.6982 33.5947C34.4447 34.1839 34.085 34.6841 33.6191 35.0951C33.1601 35.5062 32.6086 35.8213 31.9646 36.0406C31.3275 36.2529 30.6252 36.3591 29.8579 36.3591C29.7346 36.3591 29.5325 36.3489 29.2516 36.3283C28.9776 36.3078 28.6693 36.2529 28.3267 36.1639C27.9842 36.0748 27.6313 35.9412 27.2682 35.7631C26.9051 35.585 26.5728 35.3383 26.2714 35.0232C25.9768 34.7012 25.7336 34.3038 25.5418 33.8311C25.3499 33.3515 25.254 32.7692 25.254 32.0841V26.3498C25.254 25.788 25.3225 25.2913 25.4596 24.8597C25.5966 24.4281 25.7747 24.0547 25.9939 23.7396C26.22 23.4176 26.4769 23.1504 26.7647 22.938C27.0524 22.7188 27.347 22.5372 27.6485 22.3933C28.3473 22.0645 29.1454 21.8761 30.0429 21.8281Z'
        fill='#1D2088'
      />
      <path
        d='M13.8062 25.9798C13.8062 25.3221 13.9021 24.7638 14.0939 24.3048C14.2857 23.8389 14.5289 23.4518 14.8235 23.1435C15.125 22.8352 15.4538 22.5954 15.8101 22.4242C16.1732 22.246 16.5226 22.1124 16.8583 22.0234C17.2008 21.9343 17.5091 21.8795 17.7832 21.859C18.0572 21.8384 18.2593 21.8281 18.3895 21.8281H18.7389C18.8691 21.8281 19.0712 21.8384 19.3452 21.859C19.6192 21.8795 19.9241 21.9343 20.2598 22.0234C20.5955 22.1124 20.9415 22.246 21.2977 22.4242C21.6608 22.5954 21.9863 22.8352 22.274 23.1435C22.5686 23.4518 22.8084 23.8389 22.9934 24.3048C23.1852 24.7638 23.2811 25.3221 23.2811 25.9798V32.1252C23.2811 32.8035 23.1852 33.3789 22.9934 33.8517C22.8084 34.3244 22.5686 34.7183 22.274 35.0335C21.9863 35.3486 21.6608 35.5987 21.2977 35.7837C20.9415 35.9618 20.5955 36.0954 20.2598 36.1844C19.9241 36.2735 19.6192 36.3283 19.3452 36.3489C19.0712 36.3694 18.8691 36.3797 18.7389 36.3797H18.3895C18.2593 36.3797 18.0572 36.3694 17.7832 36.3489C17.5091 36.3283 17.2008 36.2735 16.8583 36.1844C16.5226 36.0954 16.1732 35.9618 15.8101 35.7837C15.4538 35.5987 15.125 35.3486 14.8235 35.0335C14.5289 34.7183 14.2857 34.3244 14.0939 33.8517C13.9021 33.3789 13.8062 32.8035 13.8062 32.1252V25.9798ZM17.218 32.5157C17.2317 32.7692 17.2899 32.9919 17.3927 33.1837C17.4817 33.355 17.6153 33.5091 17.7934 33.6461C17.9784 33.7832 18.2388 33.8517 18.5745 33.8517C18.9033 33.8517 19.1568 33.7832 19.3349 33.6461C19.5131 33.5091 19.6466 33.355 19.7357 33.1837C19.8316 32.9919 19.8899 32.7692 19.9104 32.5157V25.8154C19.8899 25.5688 19.8316 25.3461 19.7357 25.1474C19.6466 24.983 19.5131 24.8323 19.3349 24.6953C19.1568 24.5514 18.9033 24.4795 18.5745 24.4795C18.2388 24.4795 17.9784 24.5514 17.7934 24.6953C17.6153 24.8323 17.4817 24.983 17.3927 25.1474C17.2899 25.3461 17.2317 25.5688 17.218 25.8154V32.5157Z'
        fill='#1D2088'
      />
      <path
        d='M7.41405 21.9102C8.03749 21.9102 8.61641 21.9889 9.15078 22.1465C9.69202 22.3041 10.1613 22.5404 10.5587 22.8556C10.9629 23.1639 11.278 23.551 11.5041 24.0168C11.737 24.4827 11.8535 25.0274 11.8535 25.6508V32.0223C11.8535 32.5566 11.7953 33.0294 11.6788 33.4404C11.5623 33.8446 11.4082 34.1975 11.2164 34.4989C11.0314 34.7935 10.819 35.047 10.5792 35.2594C10.3394 35.4649 10.0962 35.6327 9.84959 35.7629C9.2604 36.0781 8.59243 36.2562 7.84567 36.2973H4.43386C4.04335 36.2973 3.74191 36.2322 3.52952 36.102C3.31714 35.965 3.16299 35.7869 3.06708 35.5677C2.97117 35.3416 2.91293 35.0881 2.89238 34.8072C2.87868 34.5263 2.87183 34.2351 2.87183 33.9337V24.2532C2.87183 23.9586 2.87868 23.6709 2.89238 23.39C2.91293 23.1091 2.97117 22.859 3.06708 22.6398C3.16299 22.4206 3.31714 22.2459 3.52952 22.1157C3.74191 21.9787 4.04335 21.9102 4.43386 21.9102H7.41405ZM5.99589 24.3149V33.8515H7.33184C7.62643 33.8378 7.8902 33.7693 8.12313 33.646C8.21905 33.598 8.31496 33.5329 8.41087 33.4507C8.50679 33.3685 8.59243 33.2726 8.66779 33.163C8.74315 33.0465 8.80138 32.9095 8.84249 32.7519C8.89045 32.5943 8.91442 32.4128 8.91442 32.2072V26.1441C8.91442 25.918 8.89045 25.7193 8.84249 25.548C8.80138 25.3699 8.74315 25.2158 8.66779 25.0856C8.59243 24.9554 8.50679 24.8458 8.41087 24.7568C8.31496 24.6677 8.21905 24.5958 8.12313 24.5409C7.8902 24.4108 7.62643 24.3354 7.33184 24.3149H5.99589Z'
        fill='#1D2088'
      />
    </svg>
  );
};

export default IconDoc;
