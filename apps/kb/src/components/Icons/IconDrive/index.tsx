import type { IconCustomProps } from '@/types';

const IconDrive = (props: IconCustomProps) => {
  return (
    <svg
      width='121'
      height='121'
      viewBox='0 0 121 121'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      aria-label='Drive'
      {...props}
    >
      <title>Drive</title>
      <g clipPath='url(#clip0_2340_4678)'>
        <path
          opacity='0.8'
          d='M9.44678 84.9096C14.1568 92.0896 23.8068 94.1696 31.0968 89.5596C38.3768 84.9396 40.5568 75.3696 36.0068 68.0996L9.44678 84.9196V84.9096Z'
          fill='#2494F0'
        />
        <path
          d='M102.207 90.0007L113.167 56.6307C94.6467 50.8607 74.8867 60.8807 68.6667 79.1407L102.207 89.9907V90.0007Z'
          fill='#7FB549'
        />
        <path
          d='M34.7567 22L13.6667 33.66C20.2767 45.24 34.8767 49.47 46.5467 43.2L34.7767 22.02H34.7667L34.7567 22Z'
          fill='#D9453E'
        />
        <path
          d='M104.329 9.6915L97.3219 6.24163L93.9127 13.2427L100.919 16.7025L104.329 9.70146L104.329 9.6915Z'
          fill='#9B2E8D'
        />
        <path
          opacity='0.7'
          d='M47.7367 14.88L49.1667 7.43L41.6667 6L47.7267 14.89L47.7367 14.88Z'
          fill='#E4007F'
        />
        <path
          opacity='0.7'
          d='M103.382 99C101.775 101.995 102.964 105.701 106.067 107.279C109.17 108.863 112.984 107.751 114.667 104.786L103.382 99Z'
          fill='#FBB03B'
        />
        <path
          opacity='0.8'
          d='M101.067 34.1345L110.676 41.2169L112.226 39.1358L102.618 32.0535L101.067 34.1345Z'
          fill='#1D7ECE'
        />
        <g opacity='0.9'>
          <path d='M78.5237 20.0605H29.6667V104.358H90.7869V33.1507' fill='#E6E6E6' />
          <path d='M78.5236 20.0605V33.0419H90.7868L78.5236 20.0605Z' fill='#F2F2F2' />
          <path d='M90.7868 46.0115V33.041H78.5236L90.7868 46.0115Z' fill='#B3B3B3' />
        </g>
        <path
          d='M52.9218 45.1191H68.1517L82.4718 69.8056H67.2094L52.9218 45.1191Z'
          fill='url(#paint0_linear_2340_4678)'
        />
        <path
          d='M82.4612 69.8047L74.8353 82.9982H46.2601L53.9076 69.8047H82.4612Z'
          fill='url(#paint1_linear_2340_4678)'
        />
        <path
          d='M46.2601 82.9991L38.6667 69.8056L52.9218 45.1191L60.5693 58.3452L46.2709 82.9991H46.2601Z'
          fill='url(#paint2_linear_2340_4678)'
        />
        <path
          opacity='0.1'
          d='M46.2601 82.9982L60.4719 69.8047H53.9076L46.2601 82.9982Z'
          fill='black'
        />
        <path
          opacity='0.1'
          d='M82.4611 69.8059H67.2203L63.8623 64.0215L82.4611 69.8059Z'
          fill='black'
        />
        <path
          opacity='0.1'
          d='M52.9218 45.1191L57.3196 63.9346L60.5692 58.3452L52.9218 45.1191Z'
          fill='black'
        />
      </g>
      <defs>
        <linearGradient
          id='paint0_linear_2340_4678'
          x1='52.977'
          y1='45.6575'
          x2='77.8732'
          y2='73.5913'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#F6C338' />
          <stop offset='0.52' stopColor='#FFD351' />
          <stop offset='1' stopColor='#F6C338' />
        </linearGradient>
        <linearGradient
          id='paint1_linear_2340_4678'
          x1='83.5855'
          y1='70.0213'
          x2='74.9891'
          y2='93.6141'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#286EE6' />
          <stop offset='0.52' stopColor='#4286FB' />
          <stop offset='1' stopColor='#286EE6' />
        </linearGradient>
        <linearGradient
          id='paint2_linear_2340_4678'
          x1='52.5102'
          y1='44.5212'
          x2='37.6576'
          y2='78.8686'
          gradientUnits='userSpaceOnUse'
        >
          <stop stopColor='#069B5A' />
          <stop offset='0.53' stopColor='#11AA62' />
          <stop offset='1' stopColor='#069B5A' />
        </linearGradient>
        <clipPath id='clip0_2340_4678'>
          <rect width='120' height='120' fill='white' transform='translate(0.666748)' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconDrive;
