import type { CustomIconProps } from '@resola-ai/ui/components/Icons';

const IconImage = (props: CustomIconProps) => {
  return (
    <svg
      width='101'
      height='101'
      viewBox='0 0 101 101'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      aria-label='Image file'
      {...props}
    >
      <title>Image file</title>
      <rect
        width='87.7248'
        height='59.2309'
        rx='4'
        transform='matrix(0.965712 -0.259615 0.258025 0.966138 0.600098 22.7754)'
        fill='#E3E3E8'
      />
      <rect x='13.1001' y='11.6641' width='87.5' height='59.1667' rx='4' fill='#EEEEF1' />
      <rect x='18.1001' y='17.5' width='76.6667' height='48.3333' rx='4' fill='#E3E3E8' />
      <path
        d='M38.1004 34.1667C41.3221 34.1667 43.9338 31.555 43.9338 28.3333C43.9338 25.1117 41.3221 22.5 38.1004 22.5C34.8788 22.5 32.2671 25.1117 32.2671 28.3333C32.2671 31.555 34.8788 34.1667 38.1004 34.1667Z'
        fill='white'
      />
      <path
        d='M94.7668 47.2036V62.4809C94.7668 63.3693 94.3204 64.2214 93.526 64.8496C92.7315 65.4778 91.654 65.8307 90.5305 65.8307H22.3364C21.2128 65.8307 20.1353 65.4778 19.3409 64.8496C18.5464 64.2214 18.1001 63.3693 18.1001 62.4809V60.5197L51.1982 40.6731C51.5987 40.434 52.0853 40.3022 52.5871 40.2969C53.0889 40.2916 53.5798 40.4131 53.9882 40.6436L59.5793 43.8109C60.0066 44.0529 60.5241 44.175 61.0494 44.1577C61.5746 44.1404 62.0774 43.9846 62.4778 43.7153L76.0144 34.6083C76.4677 34.3032 77.0502 34.1452 77.6448 34.1659C78.2395 34.1866 78.8022 34.3845 79.2198 34.7198L94.7668 47.2036Z'
        fill='white'
      />
    </svg>
  );
};

export default IconImage;
