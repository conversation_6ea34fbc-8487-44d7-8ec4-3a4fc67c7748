import type { IconCustomProps } from '@/types';

const IconImportSuccess = (props: IconCustomProps) => {
  return (
    <svg
      width='102'
      height='102'
      viewBox='0 0 102 102'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      role='img'
      aria-labelledby='import-success-icon-title'
      {...props}
    >
      <title id='import-success-icon-title'>Import success</title>
      <g clipPath='url(#clip0_5368_38957)'>
        <path
          d='M84.2368 19.4163C83.6749 19.4184 83.1138 19.3774 82.5582 19.2938C82.1641 19.2329 81.8105 19.0182 81.575 18.697C81.3396 18.3757 81.2416 17.9743 81.3027 17.5809C81.3637 17.1876 81.5788 16.8346 81.9006 16.5996C82.2224 16.3645 82.6246 16.2667 83.0187 16.3277C83.3262 16.3746 90.2117 17.3122 92.2902 10.8136C94.8396 2.83521 95.3741 1.17399 99.7661 0.0471698C100.151 -0.0494303 100.559 0.0101068 100.901 0.212773C101.242 0.415439 101.489 0.744758 101.588 1.12878C101.687 1.51279 101.63 1.92029 101.429 2.26224C101.228 2.60418 100.899 2.85277 100.515 2.95369C97.8103 3.64792 97.6597 3.89364 95.1542 11.7265C93.1928 17.8561 87.8448 19.4155 84.2399 19.4155'
          fill='#F93549'
        />
        <path
          d='M34.1593 0C35.0637 0 35.9478 0.267706 36.6998 0.769266C37.4518 1.27083 38.0379 1.98371 38.384 2.81778C38.7301 3.65184 38.8207 4.56962 38.6443 5.45505C38.4678 6.34049 38.0323 7.15382 37.3928 7.79218C36.7532 8.43054 35.9384 8.86528 35.0514 9.0414C34.1643 9.21753 33.2449 9.12713 32.4093 8.78165C31.5737 8.43617 30.8596 7.85112 30.3571 7.10048C29.8546 6.34985 29.5864 5.46734 29.5864 4.56455C29.5864 3.35396 30.0682 2.19295 30.9258 1.33693C31.7834 0.480907 32.9465 0 34.1593 0Z'
          fill='#F93549'
        />
        <path
          d='M44.5327 6.9707C44.9818 6.9707 45.4208 7.10364 45.7942 7.35271C46.1676 7.60178 46.4586 7.95578 46.6304 8.36996C46.8023 8.78413 46.8472 9.23986 46.7595 9.67952C46.6718 10.1192 46.4555 10.523 46.1379 10.8399C45.8203 11.1569 45.4156 11.3726 44.9752 11.46C44.5347 11.5474 44.0781 11.5024 43.6633 11.3307C43.2484 11.159 42.8939 10.8684 42.6445 10.4956C42.3951 10.1228 42.262 9.68456 42.2622 9.23628C42.2624 8.63534 42.5017 8.05908 42.9275 7.63423C43.3533 7.20937 43.9307 6.9707 44.5327 6.9707Z'
          fill='#F1B700'
        />
        <path
          d='M52.1121 82.6387C52.5207 82.6387 52.9201 82.7596 53.2598 82.9862C53.5995 83.2128 53.8643 83.5348 54.0206 83.9116C54.177 84.2884 54.2179 84.703 54.1382 85.1029C54.0585 85.5029 53.8617 85.8704 53.5728 86.1587C53.2839 86.4471 52.9159 86.6435 52.5151 86.723C52.1144 86.8026 51.6991 86.7618 51.3216 86.6057C50.9441 86.4496 50.6215 86.1854 50.3945 85.8463C50.1675 85.5072 50.0464 85.1085 50.0464 84.7007C50.0464 84.1538 50.264 83.6293 50.6514 83.2426C51.0388 82.8559 51.5643 82.6387 52.1121 82.6387Z'
          fill='#D8D9C6'
        />
        <path
          d='M14.751 28.7617C15.619 28.7617 16.4676 29.0186 17.1893 29.5C17.911 29.9814 18.4736 30.6656 18.8057 31.4661C19.1379 32.2666 19.2248 33.1474 19.0555 33.9972C18.8861 34.847 18.4682 35.6276 17.8544 36.2403C17.2406 36.853 16.4586 37.2702 15.6072 37.4393C14.7559 37.6083 13.8734 37.5215 13.0715 37.19C12.2695 36.8584 11.5841 36.2969 11.1018 35.5764C10.6196 34.856 10.3622 34.009 10.3622 33.1426C10.3622 31.9807 10.8246 30.8664 11.6476 30.0448C12.4707 29.2233 13.587 28.7617 14.751 28.7617Z'
          fill='#F1B700'
        />
        <path
          d='M22.503 79.543C23.1165 79.543 23.7163 79.7246 24.2265 80.0648C24.7366 80.4051 25.1342 80.8887 25.369 81.4545C25.6038 82.0204 25.6653 82.643 25.5456 83.2437C25.4259 83.8443 25.1304 84.3961 24.6966 84.8292C24.2627 85.2622 23.7099 85.5571 23.1082 85.6766C22.5064 85.7961 21.8827 85.7348 21.3158 85.5004C20.7489 85.266 20.2644 84.8691 19.9236 84.3599C19.5827 83.8507 19.4008 83.252 19.4008 82.6395C19.4007 82.2329 19.4808 81.8302 19.6367 81.4544C19.7925 81.0787 20.0211 80.7373 20.3091 80.4497C20.5972 80.1621 20.9393 79.934 21.3157 79.7785C21.6921 79.6229 22.0955 79.5429 22.503 79.543Z'
          fill='#BC81D4'
        />
        <path
          d='M98.8986 37.5234C99.5122 37.5234 100.112 37.705 100.622 38.0453C101.132 38.3856 101.53 38.8692 101.765 39.435C101.999 40.0008 102.061 40.6235 101.941 41.2241C101.821 41.8248 101.526 42.3766 101.092 42.8096C100.658 43.2427 100.106 43.5376 99.5038 43.6571C98.902 43.7766 98.2783 43.7153 97.7114 43.4809C97.1446 43.2465 96.6601 42.8496 96.3192 42.3404C95.9783 41.8312 95.7964 41.2325 95.7964 40.62C95.7964 39.7988 96.1232 39.0111 96.705 38.4304C97.2868 37.8497 98.0758 37.5234 98.8986 37.5234Z'
          fill='#D8D9C6'
        />
        <path
          d='M64.8691 84.5566C65.7056 84.5566 66.5234 84.8042 67.2189 85.2681C67.9144 85.732 68.4565 86.3914 68.7767 87.1628C69.0968 87.9343 69.1805 88.7831 69.0174 89.6021C68.8542 90.421 68.4513 91.1733 67.8598 91.7637C67.2683 92.3542 66.5147 92.7563 65.6943 92.9192C64.8738 93.0821 64.0234 92.9984 63.2506 92.6789C62.4777 92.3594 61.8172 91.8182 61.3524 91.124C60.8877 90.4297 60.6396 89.6135 60.6396 88.7785C60.6396 87.6588 61.0852 86.5849 61.8784 85.7932C62.6716 85.0014 63.7474 84.5566 64.8691 84.5566Z'
          fill='#F1B700'
        />
        <path
          d='M52.3284 13.7695C58.631 13.7695 64.792 15.635 70.0324 19.1302C75.2728 22.6253 79.3572 27.593 81.7691 33.4052C84.1811 39.2174 84.8122 45.613 83.5828 51.7833C82.3533 57.9535 79.3185 63.6213 74.862 68.0699C70.4056 72.5185 64.7277 75.5481 58.5463 76.7756C52.3649 78.0031 45.9576 77.3734 40.1348 74.9662C34.3119 72.5589 29.3349 68.4821 25.8332 63.2514C22.3314 58.0207 20.4622 51.871 20.4619 45.5798C20.4619 37.1437 23.8193 29.053 29.7954 23.0877C35.7715 17.1224 43.8769 13.7711 52.3284 13.7711'
          fill='#62D821'
        />
        <path
          fillRule='evenodd'
          clipRule='evenodd'
          d='M67.6092 37.0847C68.2833 37.7576 68.2833 38.8485 67.6092 39.5214L50.3482 56.7511C49.6741 57.424 48.5812 57.424 47.9071 56.7511L39.2766 48.1362C38.6025 47.4634 38.6025 46.3725 39.2766 45.6996C39.9506 45.0267 41.0436 45.0267 41.7176 45.6996L49.1276 53.0961L65.1681 37.0847C65.8422 36.4119 66.9351 36.4119 67.6092 37.0847Z'
          fill='white'
        />
        <path
          d='M88.1229 81.1943C87.7242 81.1943 87.3418 81.0362 87.0599 80.7548C86.778 80.4733 86.6196 80.0917 86.6196 79.6937C86.6422 79.2266 86.5668 78.76 86.398 78.3238C86.2293 77.8876 85.971 77.4915 85.6398 77.1608C85.3085 76.8302 84.9116 76.5724 84.4747 76.4039C84.0377 76.2355 83.5702 76.1602 83.1023 76.1828C82.7036 76.1828 82.3212 76.0247 82.0393 75.7433C81.7574 75.4619 81.599 75.0802 81.599 74.6822C81.599 74.2842 81.7574 73.9026 82.0393 73.6211C82.3212 73.3397 82.7036 73.1816 83.1023 73.1816C85.7249 73.1816 89.627 74.916 89.627 79.6945C89.627 80.0925 89.4686 80.4741 89.1867 80.7555C88.9047 81.037 88.5224 81.1951 88.1237 81.1951'
          fill='#F93549'
        />
        <path
          d='M1.24276 70.1372C4.14262 70.0576 7.33485 70.6692 9.90728 68.9443C11.129 68.05 12.0513 66.8079 12.553 65.381C13.1841 64.0306 13.2753 62.4908 12.8079 61.0757C12.576 60.3941 12.1548 59.7925 11.5935 59.3408C11.0321 58.8891 10.3536 58.6061 9.63721 58.5246C8.95569 58.4542 8.26969 58.6067 7.68258 58.9592C7.09546 59.3118 6.63902 59.8452 6.38204 60.4792C5.31929 63.2784 8.14904 65.8072 10.6147 66.4092C13.9368 67.2187 17.035 65.6251 19.8369 64.0156C20.1191 63.8373 20.321 63.5566 20.4 63.2327C20.479 62.9088 20.429 62.5669 20.2604 62.279C20.0919 61.9912 19.818 61.7799 19.4964 61.6897C19.1749 61.5995 18.8308 61.6373 18.5367 61.7953C16.7355 62.8291 14.7582 64.0362 12.6239 64.0681C11.1652 64.0903 9.37351 63.5353 8.84453 62.0705C8.78423 61.9454 8.75292 61.8085 8.75292 61.6697C8.75292 61.5309 8.78423 61.3939 8.84453 61.2689C8.94411 61.0987 8.74733 61.354 8.89073 61.2259C9.03413 61.0979 8.67245 61.3174 8.99669 61.1591C9.02856 61.144 9.21577 61.0796 9.05166 61.1345C9.10105 61.1186 9.44999 61.0677 9.23409 61.0844C9.34685 61.0561 9.46486 61.0557 9.57777 61.0835C9.69068 61.1112 9.79507 61.1661 9.88178 61.2434C10.1182 61.4386 10.2987 61.6926 10.4052 61.9798C10.6366 62.8811 10.523 63.8361 10.0865 64.6581C9.76706 65.5463 9.18018 66.314 8.40636 66.8561C6.31751 68.1181 3.60009 67.5058 1.24754 67.5718C-0.407126 67.6171 -0.413499 70.1889 1.24754 70.1435'
          fill='#F93549'
        />
      </g>
      <defs>
        <clipPath id='clip0_5368_38957'>
          <rect width='102' height='93' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconImportSuccess;
