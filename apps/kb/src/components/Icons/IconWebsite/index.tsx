import type { IconCustomProps } from '@/types';

const IconWebsite = (props: IconCustomProps) => {
  return (
    <svg
      width='120'
      height='120'
      viewBox='0 0 120 120'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      role='img'
      aria-labelledby='website-icon-title'
      {...props}
    >
      <title id='website-icon-title'>Website</title>
      <path
        opacity='0.7'
        d='M26.73 33.6094C23.95 36.2794 24.09 40.6494 27.07 43.3994C30.05 46.1494 34.71 46.2594 37.57 43.6594L26.73 33.6194V33.6094Z'
        fill='#1F8ACC'
      />
      <path
        opacity='0.9'
        d='M102.64 11.8602L67.49 11.4102L67.17 46.5202L102.32 46.9702L102.64 11.8602Z'
        fill='#FFC94F'
      />
      <path
        d='M58.7201 84.5088C59.1601 99.5788 71.6801 111.589 86.8401 111.399C101.98 111.199 114.06 98.8788 113.95 83.7988L58.7201 84.5188V84.5088Z'
        fill='#ED4DA6'
      />
      <g opacity='0.9'>
        <path d='M80.1299 20H31.2799V104.3H92.3999V33.09' fill='#E6E6E6' />
        <path d='M80.1299 20V32.98H92.3899L80.1299 20Z' fill='#F2F2F2' />
        <path d='M92.3999 45.9602V32.9902H80.1399L92.3999 45.9602Z' fill='#B3B3B3' />
      </g>
      <g opacity='0.8'>
        <path
          d='M80.2301 57.6504C82.1701 62.7204 82.1801 67.7504 80.2301 72.8104C79.8201 72.4804 79.4601 72.1704 79.0801 71.8804C77.5901 70.7504 75.9301 69.9504 74.1701 69.3504C73.9201 69.2704 73.8601 69.1704 73.8901 68.9004C74.0801 66.8804 74.1301 64.8604 73.9801 62.8304C73.9501 62.3604 73.9201 61.8804 73.8701 61.4104C73.8501 61.2504 73.9301 61.2104 74.0601 61.1604C74.7401 60.8904 75.4301 60.6404 76.1001 60.3404C77.5701 59.6804 78.9201 58.8304 80.1101 57.7504C80.1401 57.7304 80.1701 57.7104 80.2401 57.6504H80.2301Z'
          fill='#00096F'
        />
        <path
          d='M42.5701 72.8099C40.6301 67.7399 40.6301 62.7199 42.5701 57.6699C43.1001 58.0799 43.5801 58.4899 44.0901 58.8499C45.4901 59.8399 47.0201 60.5699 48.6401 61.1199C48.8901 61.1999 48.9501 61.2999 48.9201 61.5699C48.7301 63.6099 48.6701 65.6499 48.8301 67.6999C48.8601 68.1499 48.8901 68.6099 48.9401 69.0599C48.9601 69.2199 48.8801 69.2599 48.7501 69.3099C48.1101 69.5599 47.4601 69.7899 46.8401 70.0699C45.3201 70.7399 43.9101 71.5999 42.6901 72.7399C42.6701 72.7599 42.6401 72.7699 42.5701 72.8099Z'
          fill='#00096F'
        />
        <path
          d='M53.1001 52.7109C53.4901 52.9509 53.8501 53.1709 54.2201 53.3809C55.8401 54.3409 57.5801 54.9409 59.4301 55.2409C59.6701 55.2809 59.9101 55.3009 60.1501 55.3209C60.3001 55.3309 60.3801 55.3909 60.3801 55.5609C60.3801 57.2909 60.3801 59.0209 60.3801 60.7509C60.3801 60.7809 60.3801 60.8009 60.3601 60.8609C59.8901 60.8609 59.4201 60.8909 58.9601 60.8609C57.7701 60.7709 56.5701 60.7009 55.3801 60.5309C54.0501 60.3409 52.7301 60.0609 51.4101 59.8109C51.2601 59.7809 51.1801 59.7309 51.2101 59.5509C51.5801 57.2609 52.1401 55.0209 53.0201 52.8809C53.0401 52.8309 53.0701 52.7809 53.1001 52.7109Z'
          fill='#2494F0'
        />
        <path
          d='M69.7402 52.6699C70.0502 53.5999 70.4102 54.4699 70.6402 55.3699C71.0002 56.7399 71.2702 58.1399 71.5802 59.5199C71.6202 59.6999 71.5602 59.7799 71.3902 59.8099C69.9102 60.0699 68.4402 60.3899 66.9502 60.5799C65.5602 60.7499 64.1502 60.7799 62.7602 60.8799C62.6602 60.8799 62.5602 60.8799 62.4402 60.8799C62.4402 60.7699 62.4202 60.6899 62.4202 60.6099C62.4202 58.9499 62.4202 57.2799 62.4202 55.6199C62.4202 55.4299 62.4702 55.3399 62.6702 55.3199C65.1302 55.0799 67.3702 54.2099 69.4402 52.8699C69.5202 52.8199 69.6002 52.7699 69.7402 52.6799V52.6699Z'
          fill='#2494F0'
        />
        <path
          d='M53.0901 77.7896C52.8801 77.2296 52.6701 76.6896 52.4901 76.1496C51.9001 74.4496 51.5101 72.7096 51.2201 70.9396C51.1901 70.7696 51.2501 70.6996 51.4101 70.6696C52.9201 70.4096 54.4201 70.0796 55.9301 69.8896C57.2901 69.7196 58.6701 69.6896 60.0401 69.5996C60.1401 69.5996 60.2401 69.5996 60.3701 69.5996C60.3701 69.6996 60.3901 69.7796 60.3901 69.8596C60.3901 71.5196 60.3901 73.1896 60.3901 74.8496C60.3901 75.0696 60.3201 75.1396 60.1101 75.1596C57.6601 75.3896 55.4401 76.2496 53.4001 77.5896C53.3101 77.6496 53.2201 77.7096 53.0901 77.7896Z'
          fill='#00096F'
        />
        <path
          d='M69.7101 77.7405C69.0801 77.3805 68.4901 77.0205 67.8701 76.7005C66.2501 75.8505 64.5301 75.3205 62.7001 75.1505C62.5501 75.1405 62.4101 75.1305 62.4201 74.9105C62.4201 73.1805 62.4201 71.4505 62.4201 69.7205C62.4201 69.6905 62.4201 69.6705 62.4401 69.6105C62.8101 69.6105 63.1901 69.5905 63.5701 69.6105C64.8701 69.7105 66.1701 69.7705 67.4601 69.9405C68.7801 70.1205 70.0901 70.4105 71.4001 70.6605C71.5501 70.6905 71.6201 70.7505 71.5901 70.9205C71.2301 73.1705 70.6701 75.3705 69.8201 77.4905C69.7901 77.5705 69.7501 77.6405 69.7101 77.7305V77.7405Z'
          fill='#00096F'
        />
        <path
          d='M60.3701 62.9401V67.5301C57.2001 67.6001 54.0602 67.9101 50.9802 68.6501C50.7602 67.8101 50.7401 63.3701 50.9601 61.8301C54.0401 62.5801 57.1801 62.8901 60.3701 62.9501V62.9401Z'
          fill='#00096F'
        />
        <path
          d='M71.8301 68.6501C68.7501 67.9201 65.6301 67.5901 62.4501 67.5301V62.9501C65.6001 62.8801 68.7401 62.5901 71.8201 61.8301C72.0601 62.5601 72.0701 67.6301 71.8301 68.6501Z'
          fill='#00096F'
        />
        <path
          d='M49.1901 59.1591C48.5201 58.8691 47.8601 58.6091 47.2301 58.3091C45.9201 57.6791 44.7101 56.8891 43.6801 55.8391C43.5601 55.7191 43.5201 55.6291 43.6101 55.4591C44.9801 52.9891 46.7801 50.8891 49.0201 49.1591C49.0301 49.1491 49.0501 49.1391 49.0901 49.1191C49.1401 49.1591 49.1901 49.2091 49.2401 49.2591C49.9101 49.9291 50.5801 50.5991 51.2601 51.2591C51.3601 51.3591 51.4201 51.4391 51.3501 51.5891C50.3101 53.9691 49.6501 56.4491 49.2301 59.0091C49.2301 59.0391 49.2201 59.0591 49.1901 59.1591Z'
          fill='#2494F0'
        />
        <path
          d='M73.7901 49.0998C74.5701 49.8298 75.3901 50.5498 76.1501 51.3198C77.3501 52.5498 78.3301 53.9498 79.1701 55.4398C79.2701 55.6198 79.2601 55.7198 79.1101 55.8598C77.7501 57.2298 76.1101 58.1498 74.3301 58.8598C74.0901 58.9598 73.8501 59.0398 73.6101 59.1198C73.4001 58.1198 73.2101 57.1298 72.9701 56.1598C72.6001 54.6298 72.1201 53.1198 71.4801 51.6798C71.3901 51.4698 71.4301 51.3598 71.5901 51.2098C72.3101 50.5398 73.0101 49.8398 73.7801 49.0898L73.7901 49.0998Z'
          fill='#2494F0'
        />
        <path
          d='M49.0501 81.3301C47.1801 79.8801 45.5801 78.1601 44.3001 76.1401C44.0701 75.7701 43.8601 75.3901 43.6201 75.0201C43.5101 74.8501 43.5501 74.7501 43.6801 74.6201C44.8101 73.4801 46.1401 72.6501 47.5901 71.9901C48.1001 71.7601 48.6301 71.5601 49.1901 71.3301C49.3001 71.8701 49.3901 72.3801 49.4901 72.8801C49.8901 74.9201 50.4801 76.9001 51.3101 78.8001C51.4001 79.0001 51.3601 79.1001 51.2101 79.2401C50.4901 79.9201 49.7801 80.6201 49.0401 81.3201L49.0501 81.3301Z'
          fill='#00096F'
        />
        <path
          d='M73.6201 71.3498C74.7301 71.7298 75.7901 72.2098 76.7801 72.8098C77.6301 73.3298 78.4301 73.9198 79.1301 74.6298C79.2501 74.7498 79.2701 74.8298 79.1901 74.9898C77.8301 77.4598 76.0101 79.5398 73.8101 81.2798C73.7801 81.2998 73.7501 81.3198 73.7501 81.3098C72.9901 80.5798 72.2301 79.8598 71.4901 79.1298C71.4401 79.0798 71.4401 78.9098 71.4901 78.8198C72.1201 77.3498 72.6201 75.8398 73.0001 74.2798C73.2301 73.3198 73.4201 72.3398 73.6301 71.3398L73.6201 71.3498Z'
          fill='#00096F'
        />
        <path
          d='M60.3701 53.2995C58.0001 53.0695 55.9101 52.1795 53.9801 50.8095C55.4801 48.1695 57.2101 45.8395 60.3701 45.0195V53.2995Z'
          fill='#2494F0'
        />
        <path
          d='M68.8201 50.82C66.8901 52.18 64.8101 53.06 62.4401 53.3V45C62.9201 45.17 63.3701 45.3 63.8001 45.49C65.0401 46.04 66.0301 46.92 66.9001 47.94C67.6501 48.82 68.2701 49.79 68.8201 50.82Z'
          fill='#2494F0'
        />
        <path
          d='M60.3701 85.4197C59.4901 85.2497 58.7201 84.9197 58.0201 84.4397C56.2301 83.2297 55.0201 81.5397 53.9901 79.6597C55.9201 78.2997 58.0001 77.4197 60.3701 77.1797V85.4197Z'
          fill='#00096F'
        />
        <path
          d='M62.4401 85.4497V77.1797C64.8001 77.4097 66.8801 78.2997 68.8301 79.6697C67.3301 82.2797 65.6101 84.6297 62.4401 85.4497Z'
          fill='#00096F'
        />
        <path
          d='M54.7201 46.0801C53.9401 47.1701 53.1301 48.3001 52.3101 49.4401C51.8501 48.9701 51.3501 48.4601 50.8301 47.9301C52.0701 47.1501 53.4001 46.5301 54.7201 46.0801Z'
          fill='#2494F0'
        />
        <path
          d='M68.0901 46.0801C69.4101 46.5301 70.7301 47.1601 72.0101 47.9501C71.4801 48.4801 70.9801 48.9901 70.5001 49.4701C69.6801 48.3201 68.8701 47.1901 68.0901 46.0901V46.0801Z'
          fill='#2494F0'
        />
        <path
          d='M54.8002 84.4005C53.4002 83.9405 52.0802 83.3405 50.8002 82.5305C51.3202 82.0105 51.8302 81.5105 52.3702 80.9805C53.0702 82.2105 53.8502 83.3605 54.8002 84.4005Z'
          fill='#00096F'
        />
        <path
          d='M72.0001 82.5298C70.7401 83.3298 69.4101 83.9298 68.0801 84.3998C68.8601 83.3098 69.6701 82.1698 70.5101 81.0098C70.9801 81.4898 71.4801 81.9998 72.0001 82.5398V82.5298Z'
          fill='#00096F'
        />
      </g>
      <path d='M44.4399 6.11914L43.6899 16.0891L53.6099 16.8691L44.4399 6.11914Z' fill='#9B2E8D' />
      <path
        d='M117.84 49.2285L108.27 57.3985L110.06 59.4885L119.63 51.3185L117.84 49.2285Z'
        fill='#7FB549'
      />
      <path
        d='M23.45 78.4494L0.380005 78.6495C0.660005 91.3795 11.01 101.579 23.7 101.599L23.46 78.4395L23.45 78.4494Z'
        fill='#E6E6E6'
      />
      <path
        opacity='0.7'
        d='M22.5401 83.5086C25.2201 83.5086 27.3901 81.3386 27.3901 78.6586C27.3901 75.9786 25.2201 73.8086 22.5401 73.8086C19.8601 73.8086 17.6901 75.9786 17.6901 78.6586C17.6901 81.3386 19.8601 83.5086 22.5401 83.5086Z'
        fill='#D30D0D'
      />
    </svg>
  );
};

export default IconWebsite;
