import {
  ARTICLE_GENERATOR_PROMPT_MAX_LENGTH,
  ARTICLE_GENERATOR_PROMPT_MAX_ROWS,
  ARTICLE_GENERATOR_PROMPT_MIN_ROWS,
  PROMPT_LIST_MAX_LENGTH,
} from '@/constants/job';
import { zodResolver } from '@hookform/resolvers/zod';
import { Box, Flex, Group, Input, Text, Tooltip, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useState } from 'react';
import { type SubmitHandler, useForm } from 'react-hook-form';
import { TextInput, Textarea } from 'react-hook-form-mantine';
import { z } from 'zod';

import { Modal } from '@/components';
import { useAppContext } from '@/contexts';
import { useNotifications } from '@/hooks';
import type { Prompt } from '@/types';
import { IconCircleCheck, IconInfoCircle } from '@tabler/icons-react';

interface CustomizePromptModalProps {
  className?: string;
  opened: boolean;
  defaultPrompt?: Prompt;
  prompts: Prompt[];
  onClose: () => void;
  onPromptSubmitted: (prompt: Prompt) => Promise<void>;
  onPromptSelected?: (prompt: Prompt) => void;
  onPromptDeleted?: (prompt: Prompt) => Promise<void>;
  onApplyPrompt?: (prompt: Prompt) => void;
}

const useStyles = createStyles((theme) => ({
  description: {
    marginBottom: theme.spacing.md,
  },
  actions: {
    paddingTop: theme.spacing.xl,
    gap: theme.spacing.md,
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  buttonGroup: {
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    flexDirection: 'row',
  },
  inputInner: {
    margin: `${theme.spacing.xs} 0`,
  },
  prompts: {
    marginTop: theme.spacing.xs,
    backgroundColor: theme.colors.gray[0],
    borderRadius: theme.radius.sm,
    border: `1px solid ${theme.colors.dark[0]}`,
    maxHeight: rem(200),
    overflow: 'auto',
  },
  prompt: {
    borderBottom: `1px solid ${theme.colors.dark[0]}`,
    padding: `5px ${theme.spacing.md}`,
    width: '100%',
    '&:last-child': {
      borderBottom: 'none',
    },
  },
  info: {
    marginTop: theme.spacing.xs,
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing.xs,
    backgroundColor: theme.colors.decaBlue[0],
    color: theme.colors.decaBlue[9],
    padding: `${theme.spacing.xs} ${theme.spacing.sm}`,
    borderRadius: theme.radius.xs,
  },
  message: {
    padding: `${theme.spacing.xs} ${theme.spacing.sm}`,
    borderRadius: theme.radius.xs,
    backgroundColor: theme.colors.decaGreen[0],
    color: theme.colors.decaGreen[9],
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
}));

const CustomizePromptModal: React.FC<CustomizePromptModalProps> = ({
  className,
  opened,
  defaultPrompt,
  prompts,
  onClose,
  onPromptSubmitted,
  onPromptSelected,
  onPromptDeleted,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate(['kb', 'common']);
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const [message, setMessage] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { notifyError } = useNotifications(t);
  const zodSchema = z.object({
    title: z.string().min(1, { message: t('validation.titleRequired') }),
    content: z
      .string()
      .min(1, { message: t('validation.contentRequired') })
      .max(ARTICLE_GENERATOR_PROMPT_MAX_LENGTH, {
        message: t('validation.contentMaxLength', {
          maxLength: ARTICLE_GENERATOR_PROMPT_MAX_LENGTH,
        }),
      }),
    id: z.string().optional(),
  });

  const {
    control,
    formState: { errors },
    handleSubmit,
    reset,
    watch,
  } = useForm<Prompt>({
    defaultValues: {
      id: defaultPrompt?.id || '',
      title: defaultPrompt?.title || '',
      content: defaultPrompt?.content || '',
    },
    mode: 'onChange',
    resolver: zodResolver(zodSchema),
  });

  const promptLength = watch('content').length;

  const onReset = useCallback(() => {
    reset({
      id: defaultPrompt?.id || '',
      title: defaultPrompt?.title || '',
      content: defaultPrompt?.content || '',
    });
  }, [defaultPrompt, reset]);

  useEffect(() => {
    if (!opened) return;

    if (prompts.length) {
      reset(prompts[0]);
    } else {
      onReset();
    }
  }, [opened, prompts, onReset]);

  const onSubmit: SubmitHandler<Prompt> = useCallback(
    async (data: Prompt) => {
      setIsLoading(true);
      try {
        await onPromptSubmitted(data);
        setMessage(
          data.id
            ? t('promptCustomize.updateSuccess.message')
            : t('promptCustomize.createSuccess.message')
        );
        setTimeout(() => {
          setMessage('');
          setIsLoading(false);
          onClose();
        }, 2000);
      } catch (error) {
        setIsLoading(false);
        notifyError(t('promptCustomize.error.title'), t('promptCustomize.error.message'));
      }
    },
    [onPromptSubmitted, onClose]
  );

  const handlePromptSelected = useCallback(
    (prompt: Prompt) => {
      reset(prompt);
      onPromptSelected?.(prompt);
    },
    [reset, onPromptSelected]
  );

  const handlePromptDeleted = useCallback(
    (prompt: Prompt) => {
      openConfirmModal({
        onConfirm: async () => {
          await onPromptDeleted?.(prompt);

          closeConfirmModal();
        },
        title: t('promptCustomize.deleteConfirmTitle'),
        options: {
          zIndex: 1000,
          overlayProps: {
            zIndex: 1000,
          },
        },
      });
    },
    [onPromptDeleted, openConfirmModal, closeConfirmModal, t]
  );

  return (
    <Modal
      title={t('promptCustomize.title')}
      opened={opened}
      onClose={onClose}
      className={className}
      size={rem(720)}
    >
      <Box className={classes.description}>
        <Text component='p'>{t('promptCustomize.description')}</Text>
      </Box>
      <form>
        <Input.Wrapper label={t('promptCustomize.formTitle')}>
          <Box className={classes.inputInner}>
            <TextInput
              control={control}
              name='title'
              placeholder={t('promptCustomize.formTitle')}
              error={errors.title?.message}
            />
          </Box>
        </Input.Wrapper>
        <Input.Wrapper label={t('promptCustomize.formContent')}>
          <Box className={classes.inputInner}>
            <Textarea
              control={control}
              name='content'
              placeholder={t('promptCustomize.formContent')}
              minRows={ARTICLE_GENERATOR_PROMPT_MIN_ROWS}
              maxRows={ARTICLE_GENERATOR_PROMPT_MAX_ROWS}
              autosize
              error={errors.content?.message ? (errors.content?.message as string) : null}
            />
            <Text component='p' size='sm' c='dimmed' ta='right' mt='xs'>
              {t('promptCustomize.formContentLimit', {
                currentLength: promptLength,
                maxLength: ARTICLE_GENERATOR_PROMPT_MAX_LENGTH,
              })}
            </Text>
          </Box>
        </Input.Wrapper>
      </form>

      {prompts.length > 0 && (
        <Box>
          <Text component='p'>{t('promptCustomize.pastCustomPrompts')}</Text>
          <Text component='p' className={classes.info}>
            <IconInfoCircle size={20} />
            {t('promptCustomize.pastCustomPromptsDescription', {
              limit: PROMPT_LIST_MAX_LENGTH,
            })}
          </Text>
          <Group className={classes.prompts} gap='0'>
            {prompts.map((prompt) => (
              <Group
                justify='space-between'
                align='center'
                key={prompt.id || prompt.title}
                className={classes.prompt}
              >
                <Text component='p' lineClamp={1} flex={1}>
                  {prompt.title}
                </Text>
                <Group gap='xl'>
                  <DecaButton
                    variant='neutral_text'
                    c='navy'
                    size='sm'
                    onClick={() => handlePromptSelected(prompt)}
                  >
                    {t('actions.apply', { ns: 'common' })}
                  </DecaButton>
                  <DecaButton
                    variant='neutral_text'
                    c='navy'
                    size='sm'
                    onClick={() => handlePromptDeleted(prompt)}
                  >
                    {t('actions.delete', { ns: 'common' })}
                  </DecaButton>
                </Group>
              </Group>
            ))}
          </Group>
        </Box>
      )}
      <Flex className={classes.actions}>
        <Tooltip
          label={t('promptCustomize.resetTooltip')}
          withArrow
          styles={{
            tooltip: {
              whiteSpace: 'break-spaces',
            },
          }}
        >
          <DecaButton variant='secondary' radius='sm' onClick={onReset}>
            {t('actions.reset', { ns: 'common' })}
          </DecaButton>
        </Tooltip>
        <Group className={classes.buttonGroup}>
          {message && (
            <Text className={classes.message}>
              <IconCircleCheck size={16} />
              {message}
            </Text>
          )}
          <DecaButton variant='neutral' radius='sm' onClick={onClose}>
            {t('actions.cancel', { ns: 'common' })}
          </DecaButton>
          <DecaButton
            variant='primary'
            radius='sm'
            onClick={handleSubmit(onSubmit)}
            loading={isLoading}
            disabled={isLoading}
          >
            {t('actions.doSave', { ns: 'common' })}
          </DecaButton>
        </Group>
      </Flex>
    </Modal>
  );
};

export default CustomizePromptModal;
