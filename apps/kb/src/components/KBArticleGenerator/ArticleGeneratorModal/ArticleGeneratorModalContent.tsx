import { DocumentSelector } from '@/components/TreeViewSelector';
import {
  DEFAULT_ARTICLE_GENERATOR_PROMPT_CONTENT,
  DEFAULT_ARTICLE_GENERATOR_PROMPT_TITLE,
} from '@/constants/job';
import { useAppContext, useArticleGeneratorContext, useKBSelectionContext } from '@/contexts';
import { useModalManager, useNotifications } from '@/hooks';
import type { Prompt } from '@/types';
import { type JobMetadata, JobType } from '@/types/job';
import { Box, Flex, Group, Stack, Text, rem } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import CustomizePromptModal from '../CustomizePromptModal';

interface ArticleGeneratorModalContentProps {
  baseId?: string;
  onClose: () => void;
}

const ArticleGeneratorModalContent = ({
  onClose,
  baseId = '',
}: ArticleGeneratorModalContentProps) => {
  const { t } = useTranslate(['kb', 'common']);
  const navigate = useNavigate();
  const { modalClasses } = useModalManager();
  const { notifyError } = useNotifications(t);

  const {
    isGenerating,
    createPrompt,
    customPrompt,
    generateArticle,
    prompts,
    deletePrompt,
    updatePrompt,
    setCustomPrompt,
  } = useArticleGeneratorContext();
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const [promptModalOpen, { toggle: togglePromptModal }] = useDisclosure(false);
  const {
    documentSelection: { selectedDocuments },
  } = useKBSelectionContext();

  const handleGenerate = useCallback(async () => {
    if (!selectedDocuments.length) return;
    const metadata: JobMetadata = {
      documentIds: selectedDocuments,
      customPrompt: {
        title: customPrompt.title,
        content: customPrompt.content,
      },
    };

    try {
      const response = await generateArticle({
        metadata,
        jobType: JobType.ContentGeneration,
      });

      if (response?.id) {
        navigate('/kb/jobs');
      }
    } catch (error) {
      notifyError(t('errors.somethingWentWrong.title'), t('errors.somethingWentWrong.message'));
    } finally {
      closeConfirmModal();
      onClose();
    }
  }, [customPrompt, generateArticle, notifyError, t, onClose, selectedDocuments, navigate, baseId]);

  const confirmAndGenerateArticle = useCallback(() => {
    openConfirmModal({
      title: t('documentSelector.confirmTitle'),
      content: t('documentSelector.confirmMessage'),
      confirmText: t('actions.generate', { ns: 'common' }),
      cancelText: t('actions.cancel', { ns: 'common' }),
      onConfirm: () => {
        closeConfirmModal();
        handleGenerate();
      },
      options: {
        className: modalClasses.confirmModal,
        modalSize: rem(465),
      },
    });
  }, [openConfirmModal, t, handleGenerate, modalClasses]);

  const handlePromptSubmit = useCallback(
    async (customizedPrompt: Prompt) => {
      if (customizedPrompt.id) {
        await updatePrompt(customizedPrompt);
      } else {
        await createPrompt(customizedPrompt);
      }
    },
    [createPrompt, updatePrompt, setCustomPrompt]
  );

  const handlePromptSelected = useCallback(
    async (prompt: Prompt) => {
      await updatePrompt(prompt);
      setCustomPrompt(prompt);
    },
    [setCustomPrompt, updatePrompt]
  );

  const handlePromptDeleted = useCallback(
    async (prompt: Prompt) => {
      if (prompt.id) {
        await deletePrompt(prompt.id);
      }
    },
    [deletePrompt]
  );

  return (
    <Stack gap='md'>
      <Box>
        <Text component='p'>{t('documentSelector.description')}</Text>
      </Box>
      <DocumentSelector />

      <Flex pt='xl' gap='md' justify='space-between'>
        <Group>
          <CustomizePromptModal
            className={`${modalClasses.confirmModal} ${modalClasses.mediumModal}`}
            defaultPrompt={{
              id: '',
              title: DEFAULT_ARTICLE_GENERATOR_PROMPT_TITLE,
              content: DEFAULT_ARTICLE_GENERATOR_PROMPT_CONTENT,
            }}
            opened={promptModalOpen}
            prompts={prompts}
            onClose={togglePromptModal}
            onPromptSubmitted={handlePromptSubmit}
            onPromptDeleted={handlePromptDeleted}
            onPromptSelected={handlePromptSelected}
          />
          <DecaButton variant='primary' radius='sm' onClick={togglePromptModal}>
            {t('promptCustomize.title')}
          </DecaButton>
        </Group>
        <Group gap='md'>
          <DecaButton variant='neutral' radius='sm' onClick={onClose}>
            {t('actions.cancel', { ns: 'common' })}
          </DecaButton>
          <DecaButton
            variant='primary'
            loading={isGenerating}
            disabled={isEmpty(selectedDocuments)}
            radius='sm'
            onClick={confirmAndGenerateArticle}
          >
            {t('actions.generate', { ns: 'common' })}
          </DecaButton>
        </Group>
      </Flex>
    </Stack>
  );
};

export default ArticleGeneratorModalContent;
