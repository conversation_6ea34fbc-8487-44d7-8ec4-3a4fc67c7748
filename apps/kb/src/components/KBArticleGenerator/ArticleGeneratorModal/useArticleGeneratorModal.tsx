import { ArticleGeneratorContextProvider, KBSelectionContextProvider } from '@/contexts';
import { useModalManager } from '@/hooks';
import { modals } from '@mantine/modals';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';
import ArticleGeneratorModalContent from './ArticleGeneratorModalContent';

interface UseArticleGeneratorModalProps {
  afterGeneration?: () => void;
}

export const useArticleGeneratorModal = ({ afterGeneration }: UseArticleGeneratorModalProps) => {
  const { t } = useTranslate(['kb', 'common']);
  const { modalClasses, createModal } = useModalManager();

  const handleClose = useCallback(() => {
    modals.closeAll();
    afterGeneration?.();
  }, [afterGeneration]);

  const buildArticleGeneratorModal = useCallback(
    (baseId?: string) =>
      createModal({
        title: t('documentSelector.title'),
        classNames: {
          content: modalClasses.largeModal,
        },
        onClose: handleClose,
        children: (
          <ArticleGeneratorContextProvider>
            <KBSelectionContextProvider>
              <ArticleGeneratorModalContent baseId={baseId} onClose={handleClose} />
            </KBSelectionContextProvider>
          </ArticleGeneratorContextProvider>
        ),
      }),
    [handleClose, t, modalClasses, createModal]
  );

  return {
    openArticleGeneratorModal: useCallback(
      (baseId?: string) => {
        modals.open(buildArticleGeneratorModal(baseId));
      },
      [buildArticleGeneratorModal]
    ),
  };
};
