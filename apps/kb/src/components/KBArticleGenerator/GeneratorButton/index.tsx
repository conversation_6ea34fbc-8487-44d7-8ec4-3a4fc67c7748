import { DecaButton } from '@resola-ai/ui';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback } from 'react';
import { useArticleGeneratorModal } from '../ArticleGeneratorModal/useArticleGeneratorModal';

interface GeneratorButtonProps {
  baseId?: string;
  afterGenerated?: () => void;
}

const GeneratorButton: React.FC<GeneratorButtonProps> = ({ baseId, afterGenerated }) => {
  const { t } = useTranslate('article');
  const { openArticleGeneratorModal } = useArticleGeneratorModal({
    afterGeneration: useCallback(() => {
      afterGenerated?.();
    }, [afterGenerated]),
  });

  const handleGenerate = useCallback(() => {
    openArticleGeneratorModal(baseId);
  }, [openArticleGeneratorModal, baseId]);

  return (
    <DecaButton
      leftSection={<IconPlus color='white' />}
      radius='sm'
      variant='primary'
      onClick={handleGenerate}
    >
      {t('articleCollection.aiGenerate')}
    </DecaButton>
  );
};

export default GeneratorButton;
