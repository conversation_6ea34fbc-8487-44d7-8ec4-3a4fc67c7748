import { <PERSON>, Divider, Flex, Loader } from '@mantine/core';
import { useIntersection } from '@mantine/hooks';
import { TooltipWithOverflowText } from '@resola-ai/ui/components';
import { IconCheck } from '@tabler/icons-react';
import isEmpty from 'lodash/isEmpty';
import { useCallback, useEffect, useRef, useState } from 'react';
import type { NodeApi } from 'react-arborist';

import { CompactTreeList, SearchBox } from '@/components';
import KBTypeIcon from '@/components/KBGrid/KBTypeIcon';
import { ROOT_PATH } from '@/constants/folder';
import { useKBSelectionContext } from '@/contexts/KBSelectionContext';
import { type Folder, KB_TYPE, type KnowledgeBase } from '@/types';
import { convertListToTree } from '@/utils/tree';
import { EmptyFolder, FolderSelector } from '../FolderSelector';
import useSelectorStyles from '../useSelectorStyles';

/**
 * KBSelector component to show folder and knowledge base list
 * @returns {React.FC}
 */
interface IKBSelectorProps {
  disabledBases?: string[];
  supportBaseTypes?: string[];
}

const KBSelector: React.FC<IKBSelectorProps> = ({
  disabledBases = [],
  supportBaseTypes = [KB_TYPE.article, KB_TYPE.document, KB_TYPE.dataSource],
}) => {
  // Memoize style classes
  const { classes, cx } = useSelectorStyles();

  // Group related state
  const [searchState, setSearchState] = useState({
    searching: false,
    keyword: '',
  });

  // Destructure for convenience
  const { searching, keyword } = searchState;

  const {
    baseSelection: { bases, selectedBase, basesLoading, basesLoadingMore },
    folderSelection: { folders, selectedFolder },
    setBases,
    setSelectedBase,
    setSelectedFolder,
    fetchFolders,
    fetchBases,
    fetchMoreKnowledgeBases,
    search,
  } = useKBSelectionContext();

  // Fetch more bases when the bottom of the list is reached
  const containerRef = useRef(null);
  const { ref: bottomRef, entry: bottomEntry } = useIntersection({
    root: containerRef.current,
    threshold: 1,
  });

  /**
   * Check if the base is disabled
   * @param {KnowledgeBase} base - The base to check
   * @returns {boolean}
   */
  const isDisabledBase = useCallback(
    (base: KnowledgeBase) =>
      disabledBases.includes(base.id) || !supportBaseTypes.includes(base.baseType),
    [disabledBases, supportBaseTypes]
  );

  /**
   * Handle folder click event
   * @param {Folder} folder - The clicked folder
   * @returns {void}
   * @dependencies folder: Folder
   */
  const folderClickHandler = useCallback(
    async (folder: Folder) => {
      const { id: folderId } = folder;

      setSelectedFolder(folderId);

      if (selectedFolder !== folderId) {
        setBases([]);
        setSelectedBase('');
      }

      if (folderId !== selectedFolder) {
        await fetchBases(folderId);
      }
    },
    [fetchBases, fetchFolders, selectedFolder, setBases, setSelectedBase, setSelectedFolder]
  );

  /**
   * Handle folder click event
   * @param {NodeApi} node - The clicked node
   * @returns {void}
   */
  const folderNodeClickHandler = useCallback(
    async (node: NodeApi) => {
      if (node.data?.id) {
        await fetchFolders(node.data.id, 1);
        await folderClickHandler(node.data as Folder);
      }

      node.toggle();
    },
    [fetchBases, fetchFolders, selectedFolder, setBases, setSelectedBase, setSelectedFolder]
  );

  /**
   * Reload current folder
   * @returns {void}
   */
  const reloadCurrentFolder = useCallback(async () => {
    await fetchFolders(ROOT_PATH, 1);

    if (selectedFolder) {
      await fetchFolders(selectedFolder, 1);
      await fetchBases(selectedFolder);
    }
  }, [fetchBases, fetchFolders, selectedFolder]);

  /**
   * Handle search event
   * @param {string} keyword - The search keyword
   * @returns {void}
   */
  const searchHandler = useCallback(
    async (searchKeyword: string) => {
      setSearchState((prev) => ({ ...prev, searching: true }));

      try {
        if (!searchKeyword) {
          await reloadCurrentFolder();
          setSearchState({ searching: false, keyword: '' });
          return;
        }

        await search(searchKeyword, ROOT_PATH);
        setSearchState({ searching: false, keyword: searchKeyword });
      } catch (error) {
        // Handle error appropriately
        setSearchState((prev) => ({ ...prev, searching: false }));
      }
    },
    [search, reloadCurrentFolder]
  );

  useEffect(() => {
    if (containerRef?.current && bottomEntry?.isIntersecting) {
      fetchMoreKnowledgeBases();
    }
  }, [bottomEntry, containerRef?.current, fetchMoreKnowledgeBases]);

  // Memoize base item render
  const renderBaseItem = useCallback(
    (base: KnowledgeBase) => (
      <Flex
        key={base.id}
        className={cx(
          classes.baseItem,
          selectedBase === base.id && classes.selected,
          isDisabledBase(base) && classes.disabled
        )}
        onClick={() => {
          if (!isDisabledBase(base)) {
            setSelectedBase(base.id);
          }
        }}
      >
        <Flex>
          <KBTypeIcon type={base.baseType} className={classes.fileIcon} />
          <TooltipWithOverflowText classNames={classes.documentName} text={base.name} />
        </Flex>
        {selectedBase === base.id && (
          <Box className={classes.checkIconBox}>
            <IconCheck width={24} height={24} />
          </Box>
        )}
      </Flex>
    ),
    [classes, cx, selectedBase, isDisabledBase, setSelectedBase]
  );

  return (
    <Box className={classes.wrapper}>
      <Box className={classes.searchSection}>
        <SearchBox loading={searching} className={classes.searchBox} onSearch={searchHandler} />
      </Box>
      <Box className={classes.selectorSection}>
        <Box className={classes.selectorCol}>
          {keyword ? (
            <FolderSelector folders={folders} onFolderClick={folderClickHandler} />
          ) : (
            <CompactTreeList
              tree={convertListToTree(folders)}
              onLeafClick={folderNodeClickHandler}
            />
          )}
        </Box>
        <Divider orientation='vertical' />
        <Box className={classes.selectorCol}>
          <Flex ref={containerRef} className={classes.basesList}>
            {!basesLoading && !isEmpty(bases) && bases.map(renderBaseItem)}

            <Box mih={1}>
              {!basesLoading && !basesLoadingMore && !isEmpty(bases) && <Box ref={bottomRef} />}
            </Box>
            {!basesLoading && isEmpty(bases) && (
              <Flex justify='center' align='center' h='100%'>
                <EmptyFolder />
              </Flex>
            )}
            {(basesLoading || basesLoadingMore) && (
              <Flex justify='center' align='center' h='100%'>
                <Loader size={20} />
              </Flex>
            )}
          </Flex>
        </Box>
      </Box>
    </Box>
  );
};

export default KBSelector;
