import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

const useSelectorStyles = createStyles((theme) => ({
  wrapper: {
    display: 'flex',
    flexDirection: 'column',
    gap: 0,
    borderRadius: theme.radius.md,
    border: `${rem(1)} solid ${theme.colors.decaLight[4]}`,
  },
  searchSection: {
    padding: `${rem(12)} ${rem(20)}`,
    borderBottom: `${rem(1)} solid ${theme.colors.decaLight[4]}`,
    display: 'flex',
    justifyContent: 'space-between',
  },
  searchBox: {
    width: '100%',
    maxWidth: rem(360),
  },
  selectorSection: {
    minHeight: rem(285),
    display: 'flex',
    justifyContent: 'space-between',
  },
  selectorCol: {
    width: '50%',
    minWidth: rem(150),
    padding: rem(12),
  },
  basesList: {
    display: 'flex',
    flexDirection: 'column',
    height: rem(270),
    overflowY: 'auto',
    marginRight: rem(-6),
    paddingRight: rem(6),
    '&::-webkit-scrollbar': {
      width: rem(8),
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
    },
    '&::-webkit-scrollbar-track': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
    },
    ':hover': {
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: theme.colors.decaLight[5],
      },
    },
  },
  baseItem: {
    padding: `${rem(4)} ${rem(8)}`,
    gap: rem(4),
    marginBottom: rem(4),
    cursor: 'pointer',
    borderRadius: rem(4),
    alignItems: 'center',
    justifyContent: 'space-between',
    border: `${rem(1)} solid transparent`,
    ':hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
    '& .mantine-Text-root': {
      color: theme.colors.decaNavy[5],
      fontSize: rem(14),
      fontWeight: 500,
      lineHeight: rem(24),
    },
  },
  articleList: {
    display: 'flex',
    flexDirection: 'column',
    height: rem(270),
    overflowY: 'auto',
    scrollbarGutter: 'stable',
  },
  articleItem: {
    padding: `${rem(4)} ${rem(8)}`,
    gap: rem(8),
    marginBottom: rem(4),
    cursor: 'pointer',
    borderRadius: rem(4),
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  selected: {
    backgroundColor: theme.colors.decaViolet[0],
    borderColor: theme.colors.decaViolet[5],
  },
  disabled: {
    cursor: 'not-allowed',
    opacity: 0.7,
    backgroundColor: theme.colors.decaLight[1],
    ':hover': {
      backgroundColor: theme.colors.decaLight[1],
    },
  },
  fileIcon: {
    color: theme.colors.decaNavy[5],
    marginRight: rem(8),
    width: rem(24),
    height: rem(24),
  },
  checkIconBox: {
    color: theme.colors.decaNavy[5],
    width: rem(30),
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
  articleIcon: {
    color: theme.colors.decaNavy[5],
    width: rem(24),
    height: rem(24),
  },
  documentName: {
    '&.mantine-Text-root': {
      maxWidth: rem(360),
      width: '100%',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
  },
  articleBaseName: {
    '&.mantine-Text-root': {
      maxWidth: rem(230),
      width: '100%',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis',
    },
  },
}));

export default useSelectorStyles;
