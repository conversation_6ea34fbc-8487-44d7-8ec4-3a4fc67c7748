// MemoryRouter is mocked globally in unitTest.tsx
import { KB_TYPE } from '@/types';
import { MantineWrapper } from '@/utils/unitTest';
import { act, fireEvent, render, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { MultiBasesSelector } from './index';

// Mock the useMultiBasesSelector hook
vi.mock('./useMultiBasesSelector', () => ({
  useMultiBasesSelector: vi.fn(),
}));

// Mock all component dependencies
vi.mock('@/components', () => ({
  SearchBox: ({ onSearch, loading, className }: any) => (
    <div className={className}>
      <input
        placeholder='Search...'
        onChange={(e) => onSearch?.(e.target.value)}
        onKeyDown={(e) =>
          e.key === 'Enter' && onSearch && onSearch((e.target as HTMLInputElement).value)
        }
      />
      {loading && <div>Loading...</div>}
    </div>
  ),
  CompactTreeList: ({ tree, onLeafClick }: any) => (
    <div>
      {tree?.map((item: any) => (
        <button
          type='button'
          key={item.id}
          onClick={() => onLeafClick?.({ data: item, toggle: vi.fn() })}
        >
          {item.name}
        </button>
      ))}
    </div>
  ),
}));

vi.mock('@/components/KBGrid/KBTypeIcon', () => ({
  default: ({ type, className }: any) => <div className={className}>Icon-{type}</div>,
}));

vi.mock('@/utils/tree', () => ({
  convertListToTree: (list: any[]) => list || [],
}));

vi.mock('../FolderSelector', () => ({
  EmptyFolder: () => <div>No items</div>,
  FolderSelector: ({ folders, onFolderClick }: any) => (
    <div>
      {folders?.map((folder: any) => (
        <button type='button' key={folder.id} onClick={() => onFolderClick?.(folder)}>
          {folder.name}
        </button>
      ))}
    </div>
  ),
}));

vi.mock('../useSelectorStyles', () => ({
  default: () => ({
    classes: {
      wrapper: 'wrapper',
      searchSection: 'searchSection',
      searchBox: 'searchBox',
      selectorSection: 'selectorSection',
      selectorCol: 'selectorCol',
      basesList: 'basesList',
      baseItem: 'baseItem',
      selected: 'selected',
      disabled: 'disabled',
      checkIconBox: 'checkIconBox',
      fileIcon: 'fileIcon',
      documentName: 'documentName',
    },
    cx: (...args: any[]) => args.filter(Boolean).join(' '),
  }),
}));

vi.mock('@resola-ai/ui/components', () => ({
  TooltipWithOverflowText: ({ text, classNames }: any) => (
    <span className={classNames}>{text}</span>
  ),
}));

vi.mock('@resola-ai/ui/components/DecaCheckbox', () => ({
  DecaCheckbox: ({ checked, disabled }: any) => (
    <input type='checkbox' checked={checked} disabled={disabled} readOnly />
  ),
}));

// Mock intersection observer
const mockIntersectionObserver = vi.fn();
mockIntersectionObserver.mockReturnValue({
  observe: () => null,
  unobserve: () => null,
  disconnect: () => null,
});
window.IntersectionObserver = mockIntersectionObserver;

// Constants for testing
const MOCK_FOLDERS = [
  { id: 'root', name: 'Root', parentId: null },
  { id: 'folder1', name: 'Folder 1', parentId: 'root' },
  { id: 'folder2', name: 'Folder 2', parentId: 'root' },
];

const MOCK_BASES = [
  { id: 'kb1', name: 'KB 1', baseType: KB_TYPE.article },
  { id: 'kb2', name: 'KB 2', baseType: KB_TYPE.document },
  { id: 'kb3', name: 'KB 3', baseType: KB_TYPE.dataSource },
];

const MOCK_CHILDREN_FOLDERS = [
  { id: 'child1', name: 'Child 1', parentId: 'folder1' },
  { id: 'child2', name: 'Child 2', parentId: 'folder1' },
];

// Default mock implementation for useMultiBasesSelector
const mockHookValue = {
  // Search state
  searching: false,
  keyword: '',

  // Current folder state
  currentFolder: null,

  // Parent folders state
  selectedParentFolders: [],

  // Children folders state
  childrenFolders: MOCK_CHILDREN_FOLDERS,
  selectedChildFolders: [],
  childrenFoldersLoading: false,

  // Selected KBs state
  selectedKBs: [],

  // Context state
  bases: MOCK_BASES,
  basesLoading: false,
  basesLoadingMore: false,
  folders: MOCK_FOLDERS,
  selectedFolder: '/root',

  // Refs
  containerRef: { current: null },
  bottomRef: vi.fn(),

  // Handlers
  isDisabledBase: vi.fn().mockReturnValue(false),
  folderClickHandler: vi.fn(),
  childFolderClickHandler: vi.fn(),
  folderNodeClickHandler: vi.fn(),
  searchHandler: vi.fn(),
  handleSelectAll: vi.fn(),
  handleKBSelection: vi.fn(),
};

// Import the hook mock
import { useMultiBasesSelector } from './useMultiBasesSelector';

// Test wrapper component that includes all necessary providers
// Import the mocked router from react-router-dom (globally mocked in unitTest.tsx)
import { MemoryRouter } from 'react-router-dom';

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <MemoryRouter>
    <MantineWrapper>{children}</MantineWrapper>
  </MemoryRouter>
);

describe('MultiBasesSelector', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useMultiBasesSelector as unknown as ReturnType<typeof vi.fn>).mockReturnValue(mockHookValue);
  });

  it('renders without crashing', () => {
    render(<MultiBasesSelector />, { wrapper: TestWrapper });
    expect(screen.getByText('selection.parentFolders')).toBeInTheDocument();
    expect(screen.getByText('selection.childrenFolders')).toBeInTheDocument();
    expect(screen.getByText('selection.knowledgeBases')).toBeInTheDocument();
  });

  it('displays knowledge bases correctly', () => {
    render(<MultiBasesSelector />, { wrapper: TestWrapper });

    MOCK_BASES.forEach((base) => {
      expect(screen.getByText(base.name)).toBeInTheDocument();
    });
  });

  it('handles disabled bases correctly', () => {
    const mockHookWithDisabled = {
      ...mockHookValue,
      isDisabledBase: vi.fn((base) => base.id === 'kb1'),
    };

    (useMultiBasesSelector as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      mockHookWithDisabled
    );

    const disabledBases = ['kb1'];
    render(<MultiBasesSelector disabledBases={disabledBases} />, { wrapper: TestWrapper });

    // Find the KB 1 text and check that the component renders it
    const kb1Text = screen.getByText('KB 1');
    expect(kb1Text).toBeInTheDocument();

    // Verify the hook was called with the correct props
    expect(useMultiBasesSelector).toHaveBeenCalledWith({
      disabledBases,
      supportBaseTypes: [KB_TYPE.article, KB_TYPE.document, KB_TYPE.dataSource],
      onSelectionChanged: undefined,
    });
  });

  it('supports base type filtering', () => {
    const supportBaseTypes = [KB_TYPE.article];
    render(<MultiBasesSelector supportBaseTypes={supportBaseTypes} />, { wrapper: TestWrapper });

    // Verify the hook was called with the correct props
    expect(useMultiBasesSelector).toHaveBeenCalledWith({
      disabledBases: [],
      supportBaseTypes,
      onSelectionChanged: undefined,
    });

    // Verify all KBs are rendered
    expect(screen.getByText('KB 1')).toBeInTheDocument();
    expect(screen.getByText('KB 2')).toBeInTheDocument();
  });

  it('handles knowledge base selection', async () => {
    const mockHandleKBSelection = vi.fn();
    const mockHookWithSelection = {
      ...mockHookValue,
      handleKBSelection: mockHandleKBSelection,
      selectedKBs: ['kb1'],
    };

    (useMultiBasesSelector as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      mockHookWithSelection
    );

    render(<MultiBasesSelector />, { wrapper: TestWrapper });

    // Click on KB 1
    const kb1Text = screen.getByText('KB 1');
    const kb1Container = kb1Text.closest('div');

    await act(async () => {
      fireEvent.click(kb1Container!);
    });

    await waitFor(() => {
      expect(mockHandleKBSelection).toHaveBeenCalledWith('kb1');
    });
  });

  it('handles search functionality', async () => {
    const mockSearchHandler = vi.fn();
    const mockHookWithSearch = {
      ...mockHookValue,
      searchHandler: mockSearchHandler,
      searching: true,
    };

    (useMultiBasesSelector as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      mockHookWithSearch
    );

    render(<MultiBasesSelector />, { wrapper: TestWrapper });

    // The SearchBox component uses 'searchPlaceholder' from translation
    const searchInput = screen.getByPlaceholderText('Search...');

    await act(async () => {
      fireEvent.change(searchInput, { target: { value: 'test' } });
      fireEvent.keyDown(searchInput, { key: 'Enter', code: 'Enter' });
    });

    await waitFor(() => {
      expect(mockSearchHandler).toHaveBeenCalledWith('test');
    });
  });

  it('handles select all functionality', async () => {
    const mockHandleSelectAll = vi.fn();
    const mockHookWithSelectAll = {
      ...mockHookValue,
      handleSelectAll: mockHandleSelectAll,
    };

    (useMultiBasesSelector as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      mockHookWithSelectAll
    );

    render(<MultiBasesSelector />, { wrapper: TestWrapper });

    // The select all button shows 'selectAll' from translation
    const selectAllButton = screen.getByText('selectAll');

    await act(async () => {
      fireEvent.click(selectAllButton);
    });

    await waitFor(() => {
      expect(mockHandleSelectAll).toHaveBeenCalled();
    });
  });

  it('shows deselect all when items are selected', () => {
    const mockHookWithSelections = {
      ...mockHookValue,
      selectedChildFolders: ['child1', 'child2'],
      childrenFolders: MOCK_CHILDREN_FOLDERS,
    };

    (useMultiBasesSelector as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      mockHookWithSelections
    );

    render(<MultiBasesSelector />, { wrapper: TestWrapper });

    // Should show deselect all when folders are selected
    expect(screen.getByText('deselectAll')).toBeInTheDocument();
  });

  it('calls onSelectionChanged callback when provided', () => {
    const onSelectionChanged = vi.fn();

    render(<MultiBasesSelector onSelectionChanged={onSelectionChanged} />, {
      wrapper: TestWrapper,
    });

    // Verify the hook was called with the callback
    expect(useMultiBasesSelector).toHaveBeenCalledWith({
      disabledBases: [],
      supportBaseTypes: [KB_TYPE.article, KB_TYPE.document, KB_TYPE.dataSource],
      onSelectionChanged,
    });
  });

  it('handles child folder selection', async () => {
    const mockChildFolderClickHandler = vi.fn();
    const mockHookWithChildHandler = {
      ...mockHookValue,
      childFolderClickHandler: mockChildFolderClickHandler,
      selectedChildFolders: ['child1'],
    };

    (useMultiBasesSelector as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      mockHookWithChildHandler
    );

    render(<MultiBasesSelector />, { wrapper: TestWrapper });

    // Find and click a child folder
    const child1Text = screen.getByText('Child 1');
    const child1Container = child1Text.closest('div');

    await act(async () => {
      fireEvent.click(child1Container!);
    });

    await waitFor(() => {
      expect(mockChildFolderClickHandler).toHaveBeenCalledWith(MOCK_CHILDREN_FOLDERS[0], 0);
    });
  });

  it('displays loading state for children folders', () => {
    const mockHookWithLoading = {
      ...mockHookValue,
      childrenFoldersLoading: true,
      childrenFolders: [],
    };

    (useMultiBasesSelector as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      mockHookWithLoading
    );

    render(<MultiBasesSelector />, { wrapper: TestWrapper });

    // Should show loader in children folders section
    const childrenSection = screen.getByText('selection.childrenFolders').closest('div');
    expect(childrenSection).toBeInTheDocument();

    // Check for loader in the children folders section (Mantine Loader)
    const loader = document.querySelector('.mantine-Loader-root');
    expect(loader).toBeInTheDocument();
  });

  it('displays loading state for knowledge bases', () => {
    const mockHookWithLoading = {
      ...mockHookValue,
      basesLoading: true,
      bases: [],
    };

    (useMultiBasesSelector as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      mockHookWithLoading
    );

    render(<MultiBasesSelector />, { wrapper: TestWrapper });

    // Should show loader in knowledge bases section
    const basesSection = screen.getByText('selection.knowledgeBases').closest('div');
    expect(basesSection).toBeInTheDocument();

    // Check for loader in the knowledge bases section (Mantine Loader)
    const loader = document.querySelector('.mantine-Loader-root');
    expect(loader).toBeInTheDocument();
  });

  it('handles empty states correctly', () => {
    const mockHookWithEmpty = {
      ...mockHookValue,
      childrenFolders: [],
      bases: [],
      basesLoading: false,
      childrenFoldersLoading: false,
    };

    (useMultiBasesSelector as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      mockHookWithEmpty
    );

    render(<MultiBasesSelector />, { wrapper: TestWrapper });

    // Should show empty states (EmptyFolder components are rendered but may not have specific text)
    expect(screen.getByText('selection.childrenFolders')).toBeInTheDocument();
    expect(screen.getByText('selection.knowledgeBases')).toBeInTheDocument();
  });

  it('handles folder tree navigation', async () => {
    const mockFolderNodeClickHandler = vi.fn();
    const mockHookWithTreeHandler = {
      ...mockHookValue,
      folderNodeClickHandler: mockFolderNodeClickHandler,
      folders: MOCK_FOLDERS,
    };

    (useMultiBasesSelector as unknown as ReturnType<typeof vi.fn>).mockReturnValue(
      mockHookWithTreeHandler
    );

    render(<MultiBasesSelector />, { wrapper: TestWrapper });

    // The component should render folder tree or folder selector
    expect(screen.getByText('selection.parentFolders')).toBeInTheDocument();
  });

  it('passes all props correctly to useMultiBasesSelector hook', () => {
    const props = {
      disabledBases: ['kb1', 'kb2'],
      supportBaseTypes: [KB_TYPE.article, KB_TYPE.document],
      onSelectionChanged: vi.fn(),
    };

    render(<MultiBasesSelector {...props} />, { wrapper: TestWrapper });

    expect(useMultiBasesSelector).toHaveBeenCalledWith({
      disabledBases: props.disabledBases,
      supportBaseTypes: props.supportBaseTypes,
      onSelectionChanged: props.onSelectionChanged,
    });
  });
});
