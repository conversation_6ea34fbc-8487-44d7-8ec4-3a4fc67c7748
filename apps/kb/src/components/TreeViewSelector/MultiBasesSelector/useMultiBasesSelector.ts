import { useIntersection } from '@mantine/hooks';
import { useCallback, useEffect, useRef, useState } from 'react';
import type { NodeA<PERSON> } from 'react-arborist';

import { ROOT_PATH } from '@/constants/folder';
import { useKBSelectionContext } from '@/contexts/KBSelectionContext';
import { type Folder, KB_TYPE, type KnowledgeBase } from '@/types';

interface UseMultiBasesSelectorProps {
  disabledBases?: string[];
  supportBaseTypes?: string[];
  onSelectionChanged?: (parentFolders: string[], childFolders: string[], kbs: string[]) => void;
}

interface UseMultiBasesSelectorReturn {
  // Search state
  searching: boolean;
  keyword: string;

  // Current folder state (from tree navigation)
  currentFolder: Folder | null;

  // Parent folders state (with multi-select)
  selectedParentFolders: string[];

  // Children folders state (with multi-select)
  childrenFolders: Folder[];
  selectedChildFolders: string[];
  childrenFoldersLoading: boolean;

  // Selected KBs state
  selectedKBs: string[];

  // Context state
  bases: KnowledgeBase[];
  basesLoading: boolean;
  basesLoadingMore: boolean;
  folders: Folder[];
  selectedFolder: string;

  // Refs
  containerRef: React.RefObject<HTMLDivElement>;
  bottomRef: (node?: Element | null) => void;

  // Handlers
  isDisabledBase: (base: KnowledgeBase) => boolean;
  folderClickHandler: (folder: Folder) => Promise<void>;
  childFolderClickHandler: (folder: Folder, index: number) => Promise<void>;
  folderNodeClickHandler: (node: NodeApi) => Promise<void>;
  searchHandler: (searchKeyword: string) => Promise<void>;
  handleSelectAll: () => void;
  handleKBSelection: (baseId: string) => void;
}

export const useMultiBasesSelector = ({
  disabledBases = [],
  supportBaseTypes = [KB_TYPE.article, KB_TYPE.document, KB_TYPE.dataSource],
  onSelectionChanged: onSelectionChangeCallback,
}: UseMultiBasesSelectorProps): UseMultiBasesSelectorReturn => {
  // Group related state
  const [searchState, setSearchState] = useState({
    searching: false,
    keyword: '',
  });

  // Current folder state (selected from tree)
  const [currentFolder, setCurrentFolder] = useState<Folder | null>(null);

  // State for children folders
  const [childrenFolders, setChildrenFolders] = useState<Folder[]>([]);
  const [selectedChildFolders, setSelectedChildFolders] = useState<string[]>([]);
  const [childrenFoldersLoading, setChildrenFoldersLoading] = useState(false);

  // Track the last checked children folder for KB fetching
  const [, setLastCheckedChildFolder] = useState<string>('');

  // Track selected parent folders
  const [selectedParentFolders] = useState<string[]>([]);

  // State for selected KBs (multi-selection)
  const [selectedKBs, setSelectedKBs] = useState<string[]>([]);

  // Destructure for convenience
  const { searching, keyword } = searchState;

  const {
    baseSelection: { bases, basesLoading, basesLoadingMore },
    folderSelection: { folders, selectedFolder },
    setBases,
    setSelectedBase,
    setSelectedFolder,
    fetchFolders,
    fetchBases,
    fetchMoreKnowledgeBases,
    search,
  } = useKBSelectionContext();

  // Fetch more bases when the bottom of the list is reached
  const containerRef = useRef<HTMLDivElement>(null);
  const { ref: bottomRef, entry: bottomEntry } = useIntersection({
    root: containerRef.current,
    threshold: 1,
  });

  /**
   * Check if the base is disabled
   * @param {KnowledgeBase} base - The base to check
   * @returns {boolean}
   */
  const isDisabledBase = useCallback(
    (base: KnowledgeBase) =>
      disabledBases.includes(base.id) || !supportBaseTypes.includes(base.baseType),
    [disabledBases, supportBaseTypes]
  );

  /**
   * Fetch children folders for the selected folder
   * @param {string} parentFolderId - The parent folder ID
   */
  const fetchChildrenFolders = useCallback(
    async (parentFolderId: string) => {
      if (!parentFolderId) {
        setChildrenFolders([]);
        return;
      }

      setChildrenFoldersLoading(true);
      try {
        const response = await fetchFolders(parentFolderId, 1);
        if (response?.status === 'success' && response.data) {
          setChildrenFolders(response.data);
        } else {
          setChildrenFolders([]);
        }
      } catch (error) {
        console.error('Error fetching children folders:', error);
        setChildrenFolders([]);
      } finally {
        setChildrenFoldersLoading(false);
      }
    },
    [fetchFolders]
  );

  /**
   * Handle folder selection state updates
   * @param folderId - The ID of the folder being selected/deselected
   * @param index - The index of the folder in the list
   * @param currentSelection - Current selected folders
   * @returns New selection array
   */
  const handleFolderSelectionUpdate = (
    folderId: string,
    index: number,
    currentSelection: string[]
  ): string[] => {
    const isSelected = currentSelection.includes(folderId);

    if (isSelected) {
      return handleFolderDeselection(folderId, currentSelection, index);
    }
    return handleFolderSelection(folderId, index, currentSelection);
  };

  /**
   * Handle folder deselection logic
   * @param folderId - The ID of the folder being deselected
   * @param currentSelection - Current selected folders
   * @param index - The index of the folder being deselected
   * @returns New selection array
   */
  const handleFolderDeselection = (
    folderId: string,
    currentSelection: string[],
    index: number
  ): string[] => {
    // If current folder is ROOT, just deselect individual folders without parent-child logic
    if (currentFolder?.path === ROOT_PATH) {
      return currentSelection.filter((id) => id !== folderId);
    }

    // If unchecking the first folder item (parent), uncheck all folders
    if (index === 0) {
      return [];
    }

    // If unchecking any child folder (not the first), also uncheck the first folder (parent)
    const firstFolderId = childrenFolders[0]?.id;
    if (firstFolderId && currentSelection.includes(firstFolderId)) {
      return currentSelection.filter((id) => id !== firstFolderId && id !== folderId);
    }

    // Default case: just remove this folder
    return currentSelection.filter((id) => id !== folderId);
  };

  /**
   * Handle folder selection logic
   * @param folderId - The ID of the folder being selected
   * @param index - The index of the folder in the list
   * @param currentSelection - Current selected folders
   * @returns New selection array
   */
  const handleFolderSelection = (
    folderId: string,
    index: number,
    currentSelection: string[]
  ): string[] => {
    // If current folder is ROOT, just select individual folders without parent-child logic
    if (currentFolder?.path === ROOT_PATH) {
      return [...currentSelection, folderId];
    }

    // If selecting the first folder item (parent), select all child folders
    if (index === 0) {
      const allFolderIds = childrenFolders.map((f) => f.id);
      return allFolderIds;
    }

    // If selecting any child folder, add it to current selection (support multiple child selections)
    const newSelection = [...currentSelection, folderId];

    // Check if all child folders are now selected
    const allChildFolderIds = childrenFolders.slice(1).map((f) => f.id); // Exclude first folder (parent)
    const allChildrenSelected = allChildFolderIds.every((id) => newSelection.includes(id));

    // If all children are selected, also select the parent (first folder)
    if (allChildrenSelected && childrenFolders.length > 1) {
      const parentFolderId = childrenFolders[0]?.id;
      if (parentFolderId && !newSelection.includes(parentFolderId)) {
        return [...newSelection, parentFolderId];
      }
    }

    return newSelection;
  };

  /**
   * Handle folder click event (for backward compatibility)
   * @param {Folder} folder - The clicked folder
   * @returns {void}
   * @dependencies folder: Folder
   */
  const folderClickHandler = useCallback(
    async (folder: Folder) => {
      // Set as current folder and fetch its children
      setCurrentFolder(folder);
      setSelectedFolder(folder.id);
      await fetchChildrenFolders(folder.id);

      // Clear previous selections when navigating to a new folder
      setSelectedChildFolders([]);
      setLastCheckedChildFolder('');

      // Clear KBs and fetch new ones from the current folder
      setBases([]);
      await fetchBases(folder.id);
    },
    [fetchChildrenFolders, setSelectedFolder, setBases, fetchBases]
  );

  const childFolderClickHandler = useCallback(
    async (folder: Folder, index: number) => {
      const { id: folderId } = folder;

      // Update folder selection
      setSelectedChildFolders((prev) => {
        const newSelection = handleFolderSelectionUpdate(folderId, index, prev);

        // Update the last checked folder (use the most recently checked one)
        if (newSelection.includes(folderId) && !prev.includes(folderId)) {
          // Folder was just checked
          setLastCheckedChildFolder(folderId);

          // Fetch KBs from this folder
          fetchBases(folderId).then((response) => {
            if (response?.status === 'success' && response.data) {
              setBases(response.data);
            }
          });
        } else if (newSelection.length > 0) {
          // Use the last item in the selection
          const lastSelected = newSelection[newSelection.length - 1];
          setLastCheckedChildFolder(lastSelected);

          // Fetch KBs from the last selected folder
          fetchBases(lastSelected).then((response) => {
            if (response?.status === 'success' && response.data) {
              setBases(response.data);
            }
          });
        } else {
          // No folders selected, clear KBs
          setLastCheckedChildFolder('');
          setBases([]);
        }

        return newSelection;
      });
    },
    [fetchBases, setBases, childrenFolders]
  );

  /**
   * Handle folder click event
   * @param {NodeApi} node - The clicked node
   * @returns {void}
   */
  const folderNodeClickHandler = useCallback(
    async (node: NodeApi) => {
      if (node.data?.id) {
        const folderId = node.data.id;
        const folderData = node.data as Folder;

        // Always set as current folder for tree navigation (don't toggle for navigation)
        setCurrentFolder(folderData);
        setSelectedFolder(folderId);

        // Always fetch children folders and KBs when navigating to a folder
        try {
          // Fetch children folders for this folder
          await fetchChildrenFolders(folderId);

          // Clear previous children selections when navigating to new folder
          setSelectedChildFolders([]);
          setLastCheckedChildFolder('');

          // Always fetch KBs from the current folder
          const response = await fetchBases(folderId);
          if (response?.status === 'success') {
            setBases(response.data || []);
          } else {
            setBases([]);
          }
        } catch (error) {
          console.error('Error fetching data for folder:', error);
          setBases([]);
        }
      }

      node.toggle();
    },
    [fetchBases, fetchChildrenFolders, setSelectedFolder, setBases]
  );

  /**
   * Reload current folder
   * @returns {void}
   */
  const reloadCurrentFolder = useCallback(async () => {
    await fetchFolders(ROOT_PATH, 1);

    if (selectedFolder) {
      await fetchFolders(selectedFolder, 1);
      await fetchBases(selectedFolder);
      await fetchChildrenFolders(selectedFolder);
    }
  }, [fetchBases, fetchFolders, selectedFolder, fetchChildrenFolders]);

  /**
   * Handle search event
   * @param {string} keyword - The search keyword
   * @returns {void}
   */
  const searchHandler = useCallback(
    async (searchKeyword: string) => {
      setSearchState((prev) => ({ ...prev, searching: true }));

      try {
        if (!searchKeyword) {
          await reloadCurrentFolder();
          setSearchState({ searching: false, keyword: '' });
          return;
        }

        await search(searchKeyword, ROOT_PATH);
        setSearchState({ searching: false, keyword: searchKeyword });
      } catch (error) {
        // Handle error appropriately
        console.error('Search error:', error);
        setSearchState((prev) => ({ ...prev, searching: false }));
      }
    },
    [search, reloadCurrentFolder]
  );

  /**
   * Handle knowledge base selection
   * @param {string} baseId - The base ID to toggle selection
   */
  const handleKBSelection = useCallback(
    (baseId: string) => {
      setSelectedKBs((prev) => {
        if (prev.includes(baseId)) {
          return prev.filter((id) => id !== baseId);
        }
        // Keep the single selection state in sync for backward compatibility only when checking
        setSelectedBase(baseId);
        return [...prev, baseId];
      });
    },
    [setSelectedBase]
  );

  /**
   * Handle select all/deselect all event
   * Priority: Folders first, then KBs if no folders available
   * @returns {void}
   */
  const handleSelectAll = useCallback(() => {
    // Priority 1: Handle children folders if they exist
    if (childrenFolders.length > 0) {
      const allChildFolderIds = childrenFolders.map((folder) => folder.id);
      const allChildFoldersSelected = allChildFolderIds.every((id) =>
        selectedChildFolders.includes(id)
      );

      if (allChildFoldersSelected) {
        // Deselect all child folders
        setSelectedChildFolders([]);
        setLastCheckedChildFolder('');
        // Clear KBs when deselecting folders
        setBases([]);
      } else {
        // Select all child folders
        setSelectedChildFolders(allChildFolderIds);
        // Set the last folder as the source for KBs
        const lastFolder = allChildFolderIds[allChildFolderIds.length - 1];
        setLastCheckedChildFolder(lastFolder);

        // Fetch KBs from the last folder
        fetchBases(lastFolder).then((response) => {
          if (response?.status === 'success' && response.data) {
            setBases(response.data);
          }
        });
      }
      return;
    }

    // Priority 2: Handle KBs only if no children folders available
    const availableBases = bases.filter((base) => !isDisabledBase(base));
    if (availableBases.length > 0) {
      const allAvailableBaseIds = availableBases.map((base) => base.id);
      const allBasesSelected = allAvailableBaseIds.every((id) => selectedKBs.includes(id));

      if (allBasesSelected) {
        // Deselect all KBs
        setSelectedKBs([]);
        setSelectedBase('');
      } else {
        // Select all available KBs
        setSelectedKBs(allAvailableBaseIds);
        setSelectedBase(availableBases[0].id);
      }
    }
  }, [
    childrenFolders,
    selectedChildFolders,
    bases,
    selectedKBs,
    isDisabledBase,
    setSelectedBase,
    setLastCheckedChildFolder,
    setBases,
    fetchBases,
  ]);

  // Call onSelectionChangeCallback when selections change
  useEffect(() => {
    if (onSelectionChangeCallback) {
      // Pass selected parent folders, selected children folders, and selected KBs
      onSelectionChangeCallback(selectedParentFolders, selectedChildFolders, selectedKBs);
    }
  }, [onSelectionChangeCallback, selectedParentFolders, selectedChildFolders, selectedKBs]);

  // Handle intersection for infinite scrolling
  useEffect(() => {
    if (containerRef?.current && bottomEntry?.isIntersecting) {
      fetchMoreKnowledgeBases();
    }
  }, [bottomEntry, containerRef?.current, fetchMoreKnowledgeBases]);

  // Initialize by fetching root folders on component mount
  useEffect(() => {
    fetchFolders(ROOT_PATH, 1);
    fetchBases(ROOT_PATH);
  }, [fetchFolders, fetchBases]);

  return {
    // Search state
    searching,
    keyword,

    // Current folder state (from tree navigation)
    currentFolder,

    // Parent folders state (with multi-select)
    selectedParentFolders,

    // Children folders state (with multi-select)
    childrenFolders,
    selectedChildFolders,
    childrenFoldersLoading,

    // Selected KBs state
    selectedKBs,

    // Context state
    bases,
    basesLoading,
    basesLoadingMore,
    folders,
    selectedFolder,

    // Refs
    containerRef,
    bottomRef,

    // Handlers
    isDisabledBase,
    folderClickHandler,
    childFolderClickHandler,
    folderNodeClickHandler,
    searchHandler,
    handleSelectAll,
    handleKBSelection,
  };
};
