import { An<PERSON>, <PERSON>, Divider, Flex, Loader, Text, rem } from '@mantine/core';
import { TooltipWithOverflowText } from '@resola-ai/ui/components';
import { DecaCheckbox } from '@resola-ai/ui/components/DecaCheckbox';
import { IconFolder } from '@tabler/icons-react';
import isEmpty from 'lodash/isEmpty';
import { useCallback } from 'react';

import { CompactTreeList, SearchBox } from '@/components';
import KBTypeIcon from '@/components/KBGrid/KBTypeIcon';
import { ROOT_PATH } from '@/constants/folder';
import { type Folder, KB_TYPE, type KnowledgeBase } from '@/types';
import { convertListToTree } from '@/utils/tree';
import { useTranslate } from '@tolgee/react';
import { EmptyFolder, FolderSelector } from '../FolderSelector';
import useSelectorStyles from '../useSelectorStyles';
import { useMultiBasesSelector } from './useMultiBasesSelector';

/**
 * MultiBasesSelector component to show folder and knowledge base list
 * @returns {React.FC}
 */
interface IMultiBasesSelectorProps {
  disabledBases?: string[];
  supportBaseTypes?: string[];
  onSelectionChanged?: (parentFolders: string[], childFolders: string[], kbs: string[]) => void;
}

const MultiBasesSelector: React.FC<IMultiBasesSelectorProps> = ({
  disabledBases = [],
  supportBaseTypes = [KB_TYPE.article, KB_TYPE.document, KB_TYPE.dataSource],
  onSelectionChanged: onSelectionChangeCallback,
}) => {
  // Memoize style classes
  const { classes, cx } = useSelectorStyles();
  const { t } = useTranslate(['common', 'home']);

  // Use the custom hook for all logic
  const {
    // Search state
    searching,
    keyword,
    currentFolder,
    // Children folders state
    childrenFolders,
    selectedChildFolders,
    childrenFoldersLoading,

    // Selected KBs state
    selectedKBs,

    // Context state
    bases,
    basesLoading,
    basesLoadingMore,
    folders,

    // Refs
    containerRef,
    bottomRef,

    // Handlers
    isDisabledBase,
    folderClickHandler,
    childFolderClickHandler,
    folderNodeClickHandler,
    searchHandler,
    handleSelectAll,
    handleKBSelection,
  } = useMultiBasesSelector({
    disabledBases,
    supportBaseTypes,
    onSelectionChanged: onSelectionChangeCallback,
  });
  // Memoize base item render with multi-selection support
  const renderBaseItem = useCallback(
    (base: KnowledgeBase) => {
      // Check if any child folder is checked (not just selected for navigation)
      const isAnyChildFolderChecked = selectedChildFolders.length > 0;

      // Only disable checkbox if any child folder is actually checked or if the base itself is disabled
      const isDisabled = isDisabledBase(base) || isAnyChildFolderChecked;

      // Determine if this KB should be checked based on its presence in selectedKBs
      const isChecked = selectedKBs.includes(base.id);

      return (
        <Flex
          key={base.id}
          className={cx(
            classes.baseItem,
            isChecked && classes.selected,
            isDisabled && classes.disabled
          )}
          onClick={() => {
            if (!isDisabled) {
              handleKBSelection(base.id);
            }
          }}
        >
          <Box className={classes.checkIconBox}>
            <DecaCheckbox
              checked={isChecked}
              readOnly
              size='xs'
              disabled={isDisabled ? true : undefined}
            />
          </Box>
          <Flex style={{ width: 'calc(100% - 30px)', minWidth: 0 }}>
            <KBTypeIcon type={base.baseType} className={classes.fileIcon} />
            <TooltipWithOverflowText classNames={classes.documentName} text={base.name} />
          </Flex>
        </Flex>
      );
    },
    [classes, cx, selectedKBs, isDisabledBase, handleKBSelection, selectedChildFolders]
  );

  // Render child folder item
  const renderChildFolderItem = useCallback(
    (folder: Folder, index: number) => (
      <Flex
        key={folder.id}
        className={cx(
          classes.baseItem,
          selectedChildFolders.includes(folder.id) && classes.selected
        )}
        style={{
          paddingLeft:
            index !== 0 && folder.id !== ROOT_PATH && currentFolder?.path !== ROOT_PATH
              ? rem(24)
              : '0',
        }}
        onClick={() => childFolderClickHandler(folder, index)}
      >
        <Box className={classes.checkIconBox}>
          <DecaCheckbox checked={selectedChildFolders.includes(folder.id)} readOnly size='xs' />
        </Box>
        <Flex
          style={{
            width: 'calc(100% - 30px)',
            minWidth: 0,
          }}
        >
          <IconFolder className={classes.fileIcon} />
          <TooltipWithOverflowText
            classNames={classes.documentName}
            text={folder.id === ROOT_PATH ? t('tree.root', { ns: 'home' }) : folder.name}
          />
        </Flex>
      </Flex>
    ),
    [classes, cx, selectedChildFolders, childFolderClickHandler, t]
  );

  // Determine if all items are selected for Select All / Deselect All button
  // Priority: Folders first, then KBs if no folders available
  const getSelectAllButtonText = useCallback(() => {
    // Priority 1: Check children folders if they exist
    if (childrenFolders.length > 0) {
      const allChildFolderIds = childrenFolders.map((folder) => folder.id);
      const allChildFoldersSelected = allChildFolderIds.every((id) =>
        selectedChildFolders.includes(id)
      );
      return allChildFoldersSelected ? t('deselectAll') : t('selectAll');
    }

    // Priority 2: Check KBs only if no children folders available
    const availableBases = bases.filter((base) => !isDisabledBase(base));
    if (availableBases.length > 0) {
      const allAvailableBaseIds = availableBases.map((base) => base.id);
      const allBasesSelected = allAvailableBaseIds.every((id) => selectedKBs.includes(id));
      return allBasesSelected ? t('deselectAll') : t('selectAll');
    }

    // Default case: no items to select
    return t('selectAll');
  }, [childrenFolders, selectedChildFolders, bases, isDisabledBase, selectedKBs, t]);

  return (
    <Box className={classes.wrapper}>
      <Box className={classes.searchSection}>
        <Flex justify='space-between' align='center' gap='md' w='100%'>
          <SearchBox loading={searching} className={classes.searchBox} onSearch={searchHandler} />
          <Anchor
            component='button'
            onClick={handleSelectAll}
            size='sm'
            style={{ whiteSpace: 'nowrap' }}
          >
            {getSelectAllButtonText()}
          </Anchor>
        </Flex>
      </Box>
      <Box className={classes.selectorSection}>
        {/* Parent Folders Section */}
        <Box className={classes.selectorCol}>
          <Text size='sm' fw={500} mb='xs'>
            {t('selection.parentFolders')}
          </Text>
          {keyword ? (
            <FolderSelector folders={folders} onFolderClick={folderClickHandler} />
          ) : (
            <CompactTreeList
              tree={convertListToTree(folders)}
              onLeafClick={folderNodeClickHandler}
            />
          )}
        </Box>

        <Divider orientation='vertical' />

        {/* Children Folders Section */}
        <Box className={classes.selectorCol}>
          <Text size='sm' fw={500} mb='xs'>
            {t('selection.childrenFolders')}
          </Text>
          <Flex className={classes.basesList}>
            {!childrenFoldersLoading &&
              !isEmpty(childrenFolders) &&
              childrenFolders.map((folder, index) => renderChildFolderItem(folder, index))}

            {!childrenFoldersLoading && isEmpty(childrenFolders) && (
              <Flex justify='center' align='center' h='100%'>
                <EmptyFolder />
              </Flex>
            )}

            {childrenFoldersLoading && (
              <Flex justify='center' align='center' h='100%'>
                <Loader size={20} />
              </Flex>
            )}
          </Flex>
        </Box>

        <Divider orientation='vertical' />

        {/* Knowledge Bases Section */}
        <Box className={classes.selectorCol}>
          <Text size='sm' fw={500} mb='xs'>
            {t('selection.knowledgeBases')}
          </Text>
          <Flex ref={containerRef} className={classes.basesList}>
            {!basesLoading && !isEmpty(bases) && bases.map(renderBaseItem)}

            <Box mih={1}>
              {!basesLoading && !basesLoadingMore && !isEmpty(bases) && <Box ref={bottomRef} />}
            </Box>
            {!basesLoading && isEmpty(bases) && (
              <Flex justify='center' align='center' h='100%'>
                <EmptyFolder />
              </Flex>
            )}
            {(basesLoading || basesLoadingMore) && (
              <Flex justify='center' align='center' h='100%'>
                <Loader size={20} />
              </Flex>
            )}
          </Flex>
        </Box>
      </Box>
    </Box>
  );
};

export default MultiBasesSelector;
