import { <PERSON>, Divider, Flex, Loader } from '@mantine/core';
import { useIntersection } from '@mantine/hooks';
import { TooltipWithOverflowText } from '@resola-ai/ui/components';
import { IconCheck, IconFileText } from '@tabler/icons-react';
import isEmpty from 'lodash/isEmpty';
import { useCallback, useEffect, useRef, useState } from 'react';
import type { NodeApi } from 'react-arborist';

import { SearchBox } from '@/components';
import { ROOT_PATH } from '@/constants/folder';
import { useKBSelectionContext } from '@/contexts/KBSelectionContext';
import { type DocumentFile, type Folder, SearchTypeEnums } from '@/types';
import { convertListToTree } from '@/utils/tree';
import { CombineFolderSelector, EmptyFolder } from '../FolderSelector';
import useSelectorStyles from '../useSelectorStyles';

interface IDocumentSelectorProps {
  disabledDocuments?: string[];
  documentIds?: string[];
}

const DocumentSelector: React.FC<IDocumentSelectorProps> = ({ disabledDocuments = [] }) => {
  const { classes, cx } = useSelectorStyles();
  const [searching, setSearching] = useState<boolean>(false);
  const [keyword, setKeyword] = useState<string>('');
  const {
    folderSelection: { folders, selectedFolder },
    documentSelection: { documents, selectedDocuments, documentsLoading, documentsLoadingMore },
    setSelectedDocument,
    setSelectedFolder,
    fetchFolders,
    fetchDocuments,
    fetchMoreDocuments,
    search,
  } = useKBSelectionContext();

  const containerRef = useRef(null);
  const { ref: bottomRef, entry: bottomEntry } = useIntersection({
    root: containerRef.current,
    threshold: 1,
  });

  const isDisabledDocument = useCallback(
    (document: DocumentFile): boolean => {
      return disabledDocuments.includes(document.id);
    },
    [disabledDocuments]
  );

  const folderClickHandler = useCallback(
    async (folder: Folder): Promise<void> => {
      const { id: folderId } = folder;

      // Only fetch new data if folder has changed
      if (folderId !== selectedFolder) {
        setSelectedFolder(folderId);
        await fetchDocuments(folderId);
      }
    },
    [fetchDocuments, selectedFolder, setSelectedFolder]
  );

  const folderNodeClickHandler = useCallback(
    async (node: NodeApi) => {
      if (node.data?.id) {
        await fetchFolders(node.data.id, 1);
        await folderClickHandler(node.data as Folder);
      }

      node.toggle();
    },
    [fetchFolders, folderClickHandler]
  );

  const reloadCurrentFolder = useCallback(async () => {
    await fetchFolders(ROOT_PATH, 1);

    if (selectedFolder) {
      await fetchFolders(selectedFolder, 1);
      await fetchDocuments(selectedFolder);
    }
  }, [fetchDocuments, fetchFolders, selectedFolder]);

  const searchHandler = useCallback(
    async (keyword: string): Promise<void> => {
      try {
        setSearching(true);

        if (!keyword.trim()) {
          await reloadCurrentFolder();
          setKeyword('');
          return;
        }

        await search(keyword, ROOT_PATH, [SearchTypeEnums.document]);
        setKeyword(keyword);
      } finally {
        setSearching(false);
      }
    },
    [reloadCurrentFolder, search]
  );

  useEffect(() => {
    const shouldLoadMore =
      containerRef?.current &&
      bottomEntry?.isIntersecting &&
      !documentsLoading &&
      !documentsLoadingMore;

    if (shouldLoadMore) {
      fetchMoreDocuments();
    }
  }, [bottomEntry, fetchMoreDocuments, documentsLoading, documentsLoadingMore]);

  return (
    <Box className={classes.wrapper}>
      <Box className={classes.searchSection}>
        <SearchBox loading={searching} className={classes.searchBox} onSearch={searchHandler} />
      </Box>
      <Box className={classes.selectorSection}>
        <Box className={classes.selectorCol}>
          <CombineFolderSelector
            folders={folders}
            onFolderClick={folderClickHandler}
            keyword={keyword}
            onLeafClick={folderNodeClickHandler}
            tree={convertListToTree(folders)}
          />
        </Box>
        <Divider orientation='vertical' />
        <Box className={classes.selectorCol}>
          <Flex ref={containerRef} className={classes.basesList}>
            {!documentsLoading &&
              !isEmpty(documents) &&
              documents.map((document: DocumentFile) => (
                <Flex
                  key={document.id}
                  className={cx(
                    classes.baseItem,
                    selectedDocuments.includes(document.id) && classes.selected,
                    isDisabledDocument(document) && classes.disabled
                  )}
                  onClick={() => {
                    if (!isDisabledDocument(document)) {
                      setSelectedDocument(document.id);
                    }
                  }}
                >
                  <Flex>
                    <IconFileText className={classes.fileIcon} />
                    <TooltipWithOverflowText
                      classNames={classes.documentName}
                      text={document.metadata?.name}
                    />
                  </Flex>
                  {selectedDocuments.includes(document.id) && (
                    <Box className={classes.checkIconBox}>
                      <IconCheck width={24} height={24} />
                    </Box>
                  )}
                </Flex>
              ))}

            <Box mih={1}>
              {!documentsLoading && !documentsLoadingMore && !isEmpty(documents) && (
                <Box ref={bottomRef} />
              )}
            </Box>
            {!documentsLoading && isEmpty(documents) && (
              <Flex justify='center' align='center' h='100%'>
                <EmptyFolder />
              </Flex>
            )}
            {(documentsLoading || documentsLoadingMore) && (
              <Flex justify='center' align='center' h='100%'>
                <Loader size={20} />
              </Flex>
            )}
          </Flex>
        </Box>
      </Box>
    </Box>
  );
};

export default DocumentSelector;
