import { Box, Checkbox, Divider, Flex, Loader } from '@mantine/core';
import { useIntersection } from '@mantine/hooks';
import { TooltipWithOverflowText } from '@resola-ai/ui/components';
import { IconLicense } from '@tabler/icons-react';
import isEmpty from 'lodash/isEmpty';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import type { NodeApi } from 'react-arborist';

import { SearchBox } from '@/components';
import KBTypeIcon from '@/components/KBGrid/KBTypeIcon';
import { ROOT_PATH } from '@/constants/folder';
import { useKBSelectionContext } from '@/contexts/KBSelectionContext';
import { type Article, type Folder, KB_TYPE, type KnowledgeBase, SearchTypeEnums } from '@/types';
import { convertListToTree } from '@/utils/tree';
import { DecaButton } from '@resola-ai/ui/components/DecaButton';
import { useTranslate } from '@tolgee/react';
import { CombineFolderSelector, EmptyFolder } from '../FolderSelector';
import useSelectorStyles from '../useSelectorStyles';

interface IArticleSelectorProps {
  /** Callback function when an article is clicked */
  onArticleClick?: (articleId: string) => void;
  /** Whether to hide articles that are not supported */
  disabledArticles?: string[];
  /** Options for the article selector */
  options?: {
    limit?: number;
  };
}

const LoaderOrEmpty = ({
  loading,
  loadingMore,
  isEmpty,
}: {
  loading: boolean;
  loadingMore: boolean;
  isEmpty: boolean;
}) => {
  if (loading || loadingMore) {
    return (
      <Flex justify='center' align='center' h='100%'>
        <Loader />
      </Flex>
    );
  }

  if (isEmpty) {
    return (
      <Flex justify='center' align='center' h='100%'>
        <EmptyFolder />
      </Flex>
    );
  }

  return null;
};

// Create a BottomRef component for infinite scroll
const BottomRef = ({ show, reference }: { show: boolean; reference: any }) => (
  <Box mih={1}>{show && <Box ref={reference} />}</Box>
);

const ArticleSelector: React.FC<IArticleSelectorProps> = ({ disabledArticles = [], options }) => {
  const { t } = useTranslate(['article', 'common']);
  const { classes, cx } = useSelectorStyles();
  const [searching, setSearching] = useState<boolean>(false);
  const [keyword, setKeyword] = useState<string>('');
  const {
    baseSelection: { bases, selectedBase, basesLoading, basesLoadingMore },
    folderSelection: { folders, selectedFolder },
    setBases,
    setSelectedBase,
    setSelectedFolder,
    fetchFolders,
    fetchBases,
    fetchMoreKnowledgeBases,
    search,
    setArticles,
    setSelectedArticle,
    selectAllArticles,
    fetchArticles,
    fetchMoreArticles,
    articleSelection: { articles, selectedArticles, articlesLoading, articlesLoadingMore },
  } = useKBSelectionContext();
  const baseArticles = useMemo(() => {
    return bases.filter((base: KnowledgeBase) => base.baseType === KB_TYPE.article);
  }, [bases]);
  const containerRef = useRef(null);
  const containerArticleRef = useRef(null);
  const { ref: bottomRef, entry: bottomEntry } = useIntersection({
    root: containerRef.current,
    threshold: 1,
  });
  const { ref: bottomArticleRef, entry: bottomArticleEntry } = useIntersection({
    root: containerArticleRef.current,
    threshold: 1,
  });

  const folderClickHandler = useCallback(
    async (folder: Folder): Promise<void> => {
      const { id: folderId } = folder;

      // Only fetch new data if folder has changed
      if (folderId !== selectedFolder) {
        setSelectedFolder(folderId);
        setBases([]);
        setSelectedBase('');
        setArticles([]);
        await fetchBases(folderId);
      }
    },
    [
      fetchBases,
      selectedFolder,
      setBases,
      setSelectedBase,
      setSelectedFolder,
      setArticles,
      setSelectedArticle,
    ]
  );

  const baseClickHandler = useCallback(
    async (base: KnowledgeBase) => {
      const { id: baseId } = base;
      if (baseId !== selectedBase) {
        setSelectedBase(baseId);
        setArticles([]);
        await fetchArticles(baseId);
      }
    },
    [setSelectedBase, selectedBase, setArticles, setSelectedArticle]
  );

  const folderNodeClickHandler = useCallback(
    async (node: NodeApi) => {
      if (node.data?.id) {
        await fetchFolders(node.data.id, 1);
        await folderClickHandler(node.data as Folder);
      }

      node.toggle();
    },
    [fetchFolders, folderClickHandler]
  );

  const reloadCurrentFolder = useCallback(async () => {
    await fetchFolders(ROOT_PATH, 1);

    if (selectedFolder) {
      await fetchFolders(selectedFolder, 1);
      await fetchBases(selectedFolder);
    }
    if (selectedBase) {
      await fetchArticles(selectedBase);
    }
  }, [fetchBases, fetchFolders, selectedFolder]);

  const searchHandler = useCallback(
    async (keyword: string): Promise<void> => {
      try {
        setSearching(true);

        if (!keyword.trim()) {
          await reloadCurrentFolder();
          setKeyword('');
          return;
        }

        await search(keyword, ROOT_PATH, [
          SearchTypeEnums.folder,
          SearchTypeEnums.base,
          SearchTypeEnums.article,
        ]);
        setKeyword(keyword);
      } finally {
        setSearching(false);
      }
    },
    [reloadCurrentFolder, search]
  );

  const isDisabledSelectAll = useMemo(() => {
    if (!options?.limit || !articles.length) return false;

    const remaining = options?.limit - selectedArticles.length;
    return remaining <= articles.length;
  }, [options?.limit, selectedArticles, articles]);

  const isDisabledArticle = useCallback(
    (article: Article) =>
      disabledArticles.includes(article.id) ||
      (options?.limit &&
        selectedArticles.length >= options?.limit &&
        !selectedArticles.includes(article.id)),
    [disabledArticles, options?.limit, selectedArticles]
  );

  // Implement infinite scroll for knowledge bases
  useEffect(() => {
    const shouldLoadMore =
      containerRef?.current && bottomEntry?.isIntersecting && !basesLoading && !basesLoadingMore;
    if (shouldLoadMore) {
      fetchMoreKnowledgeBases();
    }
  }, [bottomEntry, fetchMoreKnowledgeBases, basesLoading, basesLoadingMore]);

  useEffect(() => {
    const shouldLoadMore =
      containerArticleRef?.current &&
      bottomArticleEntry?.isIntersecting &&
      !articlesLoading &&
      !articlesLoadingMore;

    if (shouldLoadMore && selectedBase) {
      fetchMoreArticles();
    }
  }, [bottomArticleEntry, fetchMoreArticles, articlesLoading, articlesLoadingMore]);

  return (
    <Box className={classes.wrapper}>
      <Box className={classes.searchSection}>
        <SearchBox
          loading={searching}
          className={classes.searchBox}
          onSearch={searchHandler}
          placeholder={t('articleSelector.placeholderSearch')}
        />
        <Flex>
          <DecaButton
            variant='primary_text'
            radius='xl'
            onClick={selectAllArticles}
            disabled={isDisabledSelectAll}
          >
            {t('articleSelector.selectAll')}
          </DecaButton>
        </Flex>
      </Box>
      <Box className={classes.selectorSection}>
        <Box className={classes.selectorCol}>
          <CombineFolderSelector
            folders={folders}
            onFolderClick={folderClickHandler}
            keyword={keyword}
            onLeafClick={folderNodeClickHandler}
            tree={convertListToTree(folders)}
          />
        </Box>
        <Divider orientation='vertical' />
        <Box className={classes.selectorCol}>
          <Flex ref={containerRef} className={classes.basesList}>
            {!basesLoading &&
              !isEmpty(baseArticles) &&
              baseArticles.map((base) => (
                <Flex
                  key={base.id}
                  className={cx(classes.baseItem, selectedBase === base.id && classes.selected)}
                  onClick={() => {
                    baseClickHandler(base);
                  }}
                >
                  <Flex>
                    <KBTypeIcon type={base.baseType} className={classes.fileIcon} />
                    <TooltipWithOverflowText
                      classNames={classes.articleBaseName}
                      text={base.name}
                    />
                  </Flex>
                </Flex>
              ))}
            <BottomRef
              show={!basesLoading && !basesLoadingMore && !isEmpty(bases)}
              reference={bottomRef}
            />
            <LoaderOrEmpty
              loading={basesLoading}
              loadingMore={basesLoadingMore}
              isEmpty={isEmpty(bases)}
            />
          </Flex>
        </Box>
        <Divider orientation='vertical' />
        <Box className={classes.selectorCol}>
          <Flex ref={containerArticleRef} className={classes.articleList}>
            {!articlesLoading &&
              !isEmpty(articles) &&
              articles.map((article) => (
                <Flex
                  key={article.id}
                  className={cx(
                    classes.articleItem,
                    selectedArticles.includes(article.id) && classes.selected,
                    isDisabledArticle(article) && classes.disabled
                  )}
                  onClick={(event) => {
                    if (isDisabledArticle(article)) {
                      return;
                    }
                    event.stopPropagation();
                    setSelectedArticle(article.id);
                  }}
                >
                  <Checkbox checked={selectedArticles.includes(article.id)} onChange={() => {}} />
                  <Box className={classes.articleIcon}>
                    <IconLicense />
                  </Box>
                  <TooltipWithOverflowText text={article.title} />
                </Flex>
              ))}
            <BottomRef
              show={!articlesLoading && !articlesLoadingMore && !isEmpty(articles)}
              reference={bottomArticleRef}
            />
            <LoaderOrEmpty
              loading={articlesLoading}
              loadingMore={articlesLoadingMore}
              isEmpty={isEmpty(articles)}
            />
          </Flex>
        </Box>
      </Box>
    </Box>
  );
};

export default ArticleSelector;
