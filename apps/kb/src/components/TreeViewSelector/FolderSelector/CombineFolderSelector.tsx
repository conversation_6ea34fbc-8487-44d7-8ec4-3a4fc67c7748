import { CompactTreeList } from '@/components';
import type { Folder, TreeItem } from '@/types';
import type { Node<PERSON><PERSON> } from 'react-arborist';
import { FolderSelector } from '.';

interface CombineFolderSelectorProps {
  folders: Folder[];
  onFolderClick: (folder: Folder) => void;
  keyword: string;
  onLeafClick: (node: <PERSON>de<PERSON><PERSON>) => void;
  tree: TreeItem[];
  onLeafHover?: (node: <PERSON>de<PERSON><PERSON>) => void;
}

export const CombineFolderSelector = ({
  folders,
  onFolderClick,
  keyword,
  onLeafClick,
  tree,
  onLeafHover,
}: CombineFolderSelectorProps) => {
  return (
    <>
      {keyword ? (
        <FolderSelector folders={folders} onFolderClick={onFolderClick} />
      ) : (
        <CompactTreeList tree={tree} onLeafClick={onLeafClick} onLeafHover={onLeafHover} />
      )}
    </>
  );
};

export default CombineFolderSelector;
