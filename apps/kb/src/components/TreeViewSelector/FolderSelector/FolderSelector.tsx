import type { Folder } from '@/types';
import { Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { TooltipWithOverflowText } from '@resola-ai/ui/components';
import { IconFolder } from '@tabler/icons-react';
import type React from 'react';
import { memo } from 'react';
import EmptyFolder from './EmptyFolder';

const useStyles = createStyles((theme) => ({
  list: {
    display: 'flex',
    flexDirection: 'column',
    height: rem(274),
    overflowY: 'auto',
    scrollbarGutter: 'stable',
    '&::-webkit-scrollbar': {
      width: rem(8),
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
      transition: 'background-color 200ms ease',
    },
    '&::-webkit-scrollbar-track': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
    },
    '&:hover': {
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: theme.colors.decaLight[5],
      },
    },
  },
  item: {
    padding: rem(4),
    gap: rem(4),
    cursor: 'pointer',
    borderRadius: rem(4),
    alignItems: 'center',
    border: `${rem(1)} solid transparent`,
    transition: 'background-color 150ms ease',
    '&:hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
  },
  folderIcon: {
    width: rem(24),
    height: rem(24),
    color: theme.colors.decaNavy[5],
  },
}));

interface FolderSelectorProps {
  folders: Folder[];
  onFolderClick: (folder: Folder) => void;
}

const FolderSelector: React.FC<FolderSelectorProps> = ({ folders, onFolderClick }) => {
  const { classes } = useStyles();

  if (folders.length === 0) {
    return (
      <Flex justify='center' align='center' h='100%'>
        <EmptyFolder />
      </Flex>
    );
  }

  return (
    <Flex className={classes.list}>
      {folders?.map((folder) => (
        <Flex key={folder.id} className={classes.item} onClick={() => onFolderClick(folder)}>
          <Flex w={rem(24)}>
            <IconFolder className={classes.folderIcon} />
          </Flex>
          <TooltipWithOverflowText text={folder.name} />
        </Flex>
      ))}
    </Flex>
  );
};

export default memo(FolderSelector);
