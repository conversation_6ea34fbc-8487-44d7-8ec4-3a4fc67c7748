import { Flex, Image, Text, rem } from '@mantine/core';
import { getPublicUrl } from '@resola-ai/utils';
import { useTranslate } from '@tolgee/react';

const EMPTY_IMAGE = 'images/img_edgecase.png';

interface EmptyFolderProps {
  message?: string;
  imageWidth?: number;
  imageHeight?: number;
}

const EmptyFolder = ({ message, imageWidth, imageHeight }: EmptyFolderProps) => {
  const { t } = useTranslate('common');

  return (
    <Flex direction='column' align='center'>
      <Image
        src={getPublicUrl(EMPTY_IMAGE)}
        alt='empty workspace'
        w={rem(imageWidth ?? 120)}
        h={rem(imageHeight ?? 120)}
      />
      <Text>{message ?? t('emptyMessage')}</Text>
    </Flex>
  );
};

export default EmptyFolder;
