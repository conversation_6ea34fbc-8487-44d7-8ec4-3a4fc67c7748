import { EntityType, type ExportableEntityType } from '@/types/entities';
import { type ButtonProps, Tooltip, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { IconUpload } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import React, { useCallback, useMemo } from 'react';
import { useExportJob } from '../hooks/useExportJob';

const useStyles = createStyles(() => ({
  button: {
    fontWeight: 700,
    padding: `0 ${rem(24)}`,
  },
}));

/**
 * Props interface for export job button component
 */
interface ExportJobByEntityButtonProps extends Omit<ButtonProps, 'onClick' | 'variant'> {
  /** ID of the entity to export */
  entityId: string;
  /** Type of entity to export (folder or knowledgebase) */
  entityType: ExportableEntityType;
  /** Optional callback when export is successful */
  onSuccess?: () => void;
  /** Optional callback when export fails */
  onError?: (error: Error) => void;
  /** Optional tooltip text to show on hover */
  tooltip?: string;
  /** Optional aria-label for accessibility. If not provided, will use export title */
  ariaLabel?: string;
}

/**
 * Button component that triggers an export job for a specific entity (folder or knowledgebase)
 * Uses the same styling as ExportJobButton but with entity-specific export logic
 *
 * @example
 * ```tsx
 * <ExportJobByEntityButton
 *   entityId="123"
 *   entityType={EntityType.FOLDER}
 *   onSuccess={() => console.log('Export successful')}
 * />
 * ```
 */
export const ExportJobByEntityButton: React.FC<ExportJobByEntityButtonProps> = React.memo(
  ({
    entityId,
    entityType,
    onSuccess,
    onError,
    tooltip,
    ariaLabel,
    children,
    disabled,
    ...buttonProps
  }) => {
    const { t } = useTranslate('export');
    const { exportJob, isExporting } = useExportJob();
    const { classes } = useStyles();

    // Memoize the export title to prevent unnecessary recalculations
    const exportTitle = useMemo(
      () =>
        entityType === EntityType.FOLDER ? t('menu.exportFolder') : t('menu.exportKnowledgeBase'),
      [entityType, t]
    );

    // Memoize the export configuration to prevent unnecessary object creation
    const exportConfig = useMemo(() => {
      if (entityType === EntityType.FOLDER) {
        return { folderPaths: [entityId], kbIds: [] };
      }
      return { folderPaths: [], kbIds: [entityId] };
    }, [entityId, entityType]);

    // Memoize the click handler to prevent unnecessary function creation
    const handleExport = useCallback(async () => {
      try {
        await exportJob(exportConfig);
        onSuccess?.();
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : t('errors.unknown');
        onError?.(error instanceof Error ? error : new Error(errorMessage));
      }
    }, [exportJob, exportConfig, onSuccess, onError, t]);

    // Memoize the button content to prevent unnecessary re-renders
    const buttonContent = useMemo(
      () => (
        <DecaButton
          className={classes.button}
          variant='primary'
          leftSection={<IconUpload size={24} aria-hidden='true' />}
          onClick={handleExport}
          radius='sm'
          size='md'
          loading={isExporting}
          disabled={disabled || isExporting}
          aria-label={ariaLabel || exportTitle}
          {...buttonProps}
        >
          {children || exportTitle}
        </DecaButton>
      ),
      [
        classes.button,
        handleExport,
        isExporting,
        disabled,
        ariaLabel,
        exportTitle,
        children,
        buttonProps,
      ]
    );

    // Wrap button in tooltip if tooltip text is provided
    if (tooltip) {
      return (
        <Tooltip label={tooltip} position='top' data-testid='mantine-tooltip-portal'>
          {buttonContent}
        </Tooltip>
      );
    }

    return buttonContent;
  }
);

ExportJobByEntityButton.displayName = 'ExportJobByEntityButton';

export default ExportJobByEntityButton;
