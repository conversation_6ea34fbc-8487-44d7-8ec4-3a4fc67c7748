import { EntityType } from '@/types/entities';
import { AllTheProviders } from '@/utils/unitTest';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { type Mock, vi } from 'vitest';
import { useExportJob } from '../hooks/useExportJob';
import { ExportJobByEntityButton } from './ExportJobByEntityButton';

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
}));

// Mock the useExportJob hook
vi.mock('../hooks/useExportJob', () => ({
  useExportJob: vi.fn(),
}));

// Mock @tabler/icons-react IconUpload
vi.mock('@tabler/icons-react', () => ({
  IconUpload: () => <div data-testid='IconUpload' />,
}));

// Mock @mantine/core Tooltip
vi.mock('@mantine/core', async () => {
  const actual = await vi.importActual('@mantine/core');
  return {
    ...actual,
    Tooltip: ({ children, label, ...props }: any) => (
      <div data-testid='mantine-tooltip-portal' data-tooltip-label={label} {...props}>
        {children}
      </div>
    ),
  };
});

// Mock @resola-ai/ui DecaButton
vi.mock('@resola-ai/ui', () => ({
  DecaButton: ({ children, onClick, leftSection, loading, ...props }: any) => (
    <button
      onClick={onClick}
      className='mantine-Button-root'
      data-variant={props.variant}
      data-size={props.size}
      data-radius={props.radius}
      data-loading={loading ? 'true' : undefined}
      {...props}
    >
      {leftSection}
      {children}
    </button>
  ),
}));

describe('ExportJobByEntityButton', () => {
  const mockExportJob = vi.fn();
  const mockOnSuccess = vi.fn();
  const mockOnError = vi.fn();

  beforeEach(() => {
    vi.resetAllMocks();
    (useExportJob as Mock).mockReturnValue({
      exportJob: mockExportJob,
      isExporting: false,
    });
  });

  const renderComponent = (props = {}) => {
    return render(
      <AllTheProviders>
        <ExportJobByEntityButton
          entityId='test-id'
          entityType={EntityType.FOLDER}
          onSuccess={mockOnSuccess}
          {...props}
        />
      </AllTheProviders>
    );
  };

  describe('Rendering', () => {
    it('renders with folder export title', () => {
      renderComponent({ entityType: EntityType.FOLDER });
      expect(screen.getByText('menu.exportFolder')).toBeInTheDocument();
    });

    it('renders with knowledgebase export title', () => {
      renderComponent({ entityType: EntityType.BASE });
      expect(screen.getByText('menu.exportKnowledgeBase')).toBeInTheDocument();
    });

    it('renders with custom children', () => {
      renderComponent({ children: 'Custom Export' });
      expect(screen.getByText('Custom Export')).toBeInTheDocument();
    });

    it('renders with upload icon', () => {
      renderComponent();
      expect(screen.getByTestId('IconUpload')).toBeInTheDocument();
    });
  });

  describe('Export Actions', () => {
    it('calls exportJob with folder parameters and triggers onSuccess when successful', async () => {
      mockExportJob.mockResolvedValueOnce(undefined);
      renderComponent({ entityType: EntityType.FOLDER, entityId: 'folder-123' });

      fireEvent.click(screen.getByRole('button'));

      await waitFor(() => {
        expect(mockExportJob).toHaveBeenCalledWith({
          folderPaths: ['folder-123'],
          kbIds: [],
        });
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });

    it('calls exportJob with knowledgebase parameters and triggers onSuccess when successful', async () => {
      mockExportJob.mockResolvedValueOnce(undefined);
      renderComponent({ entityType: EntityType.BASE, entityId: 'kb-123' });

      fireEvent.click(screen.getByRole('button'));

      await waitFor(() => {
        expect(mockExportJob).toHaveBeenCalledWith({
          folderPaths: [],
          kbIds: ['kb-123'],
        });
        expect(mockOnSuccess).toHaveBeenCalled();
      });
    });

    it('triggers onError with Error instance when export fails', async () => {
      const error = new Error('Export failed');
      mockExportJob.mockRejectedValueOnce(error);
      renderComponent({ onError: mockOnError });

      fireEvent.click(screen.getByRole('button'));

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith(error);
      });
    });

    it('triggers onError with new Error when non-Error is thrown', async () => {
      mockExportJob.mockRejectedValueOnce('Unknown error');
      renderComponent({ onError: mockOnError });

      fireEvent.click(screen.getByRole('button'));

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith(expect.any(Error));
      });
    });
  });

  describe('Button Props', () => {
    it('applies custom button props', () => {
      renderComponent({ disabled: true, 'data-testid': 'export-button' });
      const button = screen.getByTestId('export-button');
      expect(button).toBeDisabled();
    });

    it('maintains consistent styling', () => {
      renderComponent();
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('data-variant', 'primary');
      expect(button).toHaveAttribute('data-size', 'md');
      expect(button).toHaveAttribute('data-radius', 'sm');
      expect(button).toHaveStyle({ fontWeight: 700 });
    });
  });

  describe('Loading State', () => {
    it('disables button and shows loading state when export is in progress', () => {
      (useExportJob as Mock).mockReturnValue({
        exportJob: mockExportJob,
        isExporting: true,
      });

      renderComponent();
      const button = screen.getByRole('button');
      expect(button).toBeDisabled();
      expect(button).toHaveAttribute('data-loading', 'true');
    });
  });

  describe('Accessibility', () => {
    it('uses default aria-label based on export title', () => {
      renderComponent({ entityType: EntityType.FOLDER });
      expect(screen.getByRole('button')).toHaveAttribute('aria-label', 'menu.exportFolder');
    });

    it('uses custom aria-label when provided', () => {
      renderComponent({ ariaLabel: 'Custom Export Label' });
      expect(screen.getByRole('button')).toHaveAttribute('aria-label', 'Custom Export Label');
    });
  });

  describe('Tooltip', () => {
    it('renders tooltip wrapper when tooltip prop is provided', () => {
      renderComponent({ tooltip: 'Export this folder' });

      const tooltipWrapper = screen.getByTestId('mantine-tooltip-portal');
      expect(tooltipWrapper).toBeInTheDocument();
      expect(tooltipWrapper).toHaveAttribute('data-tooltip-label', 'Export this folder');
    });

    it('does not render tooltip when tooltip prop is not provided', () => {
      renderComponent();
      expect(screen.queryByTestId('mantine-tooltip-portal')).not.toBeInTheDocument();
    });
  });
});
