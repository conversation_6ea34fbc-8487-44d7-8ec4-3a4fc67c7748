import { MantineWrapper } from '@/utils/unitTest';
import { fireEvent, render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useExportJobModal } from '../hooks/useExportJobModal';
import { ExportJobButton } from './ExportJobButton';

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key,
  }),
}));

// Mock the useExportJobModal hook
vi.mock('../hooks/useExportJobModal', () => ({
  useExportJobModal: vi.fn(),
}));

// Mock react-i18next

// Mock @tabler/icons-react IconUpload
vi.mock('@tabler/icons-react', () => ({
  IconUpload: ({ size }: { size: number }) => (
    <svg className='tabler-icon-upload' width={size} height={size} data-testid='IconUpload' />
  ),
}));

// Mock @resola-ai/ui DecaButton
vi.mock('@resola-ai/ui', () => ({
  DecaButton: ({ children, onClick, style, className, leftSection, ...props }: any) => (
    <button
      onClick={onClick}
      className={`mantine-Button-root ${className || ''}`}
      style={style}
      data-variant={props.variant}
      data-size={props.size}
      data-radius={props.radius}
      {...props}
    >
      {leftSection}
      {children}
    </button>
  ),
}));

describe('ExportJobButton', () => {
  const mockOpenExportJobModal = vi.fn();
  const mockOnSuccess = vi.fn();

  beforeEach(() => {
    vi.resetAllMocks();
    (useExportJobModal as any).mockReturnValue({
      openExportJobModal: mockOpenExportJobModal,
    });
  });

  it('renders with default props', () => {
    render(
      <MantineWrapper>
        <ExportJobButton />
      </MantineWrapper>
    );

    const button = screen.getByRole('button', { name: 'export' });
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('mantine-Button-root');

    // Find the upload icon by test id instead of class
    const icon = screen.getByTestId('IconUpload');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('width', '24');
    expect(icon).toHaveAttribute('height', '24');
  });

  it('renders with custom children', () => {
    render(
      <MantineWrapper>
        <ExportJobButton>Custom Export</ExportJobButton>
      </MantineWrapper>
    );

    expect(screen.getByRole('button', { name: 'Custom Export' })).toBeInTheDocument();
  });

  it('calls openExportJobModal when clicked', () => {
    render(
      <MantineWrapper>
        <ExportJobButton />
      </MantineWrapper>
    );

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(mockOpenExportJobModal).toHaveBeenCalledTimes(1);
  });

  it('passes onSuccess callback to useExportJobModal', () => {
    render(
      <MantineWrapper>
        <ExportJobButton onSuccess={mockOnSuccess} />
      </MantineWrapper>
    );

    expect(useExportJobModal).toHaveBeenCalledWith({
      onSuccess: mockOnSuccess,
    });
  });

  it('applies custom button props', () => {
    render(
      <MantineWrapper>
        <ExportJobButton data-testid='custom-button' className='custom-class' />
      </MantineWrapper>
    );

    const button = screen.getByTestId('custom-button');
    expect(button).toHaveClass('custom-class');
  });

  it('applies custom styling', () => {
    const customStyle = { backgroundColor: 'red' };
    render(
      <MantineWrapper>
        <ExportJobButton style={customStyle} />
      </MantineWrapper>
    );

    const button = screen.getByRole('button');
    // Accept the computed value for red
    expect(button).toHaveStyle('background-color: rgb(255, 0, 0);');
  });
});
