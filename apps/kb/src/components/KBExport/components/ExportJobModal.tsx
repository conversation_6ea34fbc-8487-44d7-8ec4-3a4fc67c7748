import { MultiBasesSelector } from '@/components/TreeViewSelector';
import { useKBSelectionContext } from '@/contexts';
import { Alert, Box, Group, Radio, Stack, Text } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { IconAlertTriangle } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback, useState } from 'react';
import { useExportJob } from '../hooks/useExportJob';

// File type constants
const FILE_TYPES = {
  CSV: 'csv' as const,
  EXCEL: 'excel' as const,
} as const;

type FileType = (typeof FILE_TYPES)[keyof typeof FILE_TYPES];

interface ExportJobModalProps {
  onClose: () => void;
  onSuccess?: () => void;
}

const ExportJobModal = ({ onClose, onSuccess }: ExportJobModalProps) => {
  const { t } = useTranslate(['export', 'common']);

  // Local state to track selections from MultiBasesSelector
  const [, setSelectedFolders] = useState<string[]>([]);
  const [selectedChildFolders, setSelectedChildFolders] = useState<string[]>([]);
  const [selectedKBs, setSelectedKBs] = useState<string[]>([]);
  const [fileType, setFileType] = useState<FileType>(FILE_TYPES.CSV);

  const {
    baseSelection: { bases },
    folderSelection: { folders },
  } = useKBSelectionContext();

  const { isExporting, exportJob } = useExportJob({
    onSuccess: () => {
      onClose();
      onSuccess?.();
    },
  });

  const handleSelectionChange = useCallback(
    (parentFolders: string[], childFolders: string[], kbs: string[]) => {
      setSelectedFolders(parentFolders);
      setSelectedChildFolders(childFolders);
      setSelectedKBs(kbs);
    },
    []
  );

  const handleExport = useCallback(async () => {
    if (selectedChildFolders.length === 0 && selectedKBs.length === 0) return;

    const exportData = {
      folderPaths: selectedChildFolders,
      kbIds: selectedKBs,
      fileType,
    };

    try {
      await exportJob(exportData);
    } catch (error) {
      console.error('Export failed:', error);
    }
  }, [selectedChildFolders, selectedKBs, fileType, exportJob]);

  const isExportDisabled = selectedChildFolders.length === 0 && selectedKBs.length === 0;

  // Get names for display
  const selectedFolderNames = selectedChildFolders
    .map((folderId) => folders.find((folder) => folder.id === folderId)?.name)
    .filter(Boolean);

  const selectedKBNames = selectedKBs
    .map((kbId) => bases.find((base) => base.id === kbId)?.name)
    .filter(Boolean);

  return (
    <Stack gap='md'>
      <Text size='sm' c='dimmed'>
        {t('exportCreating.selectKnowledgeBasesAndFolders')}
      </Text>

      <Alert icon={<IconAlertTriangle size={16} />} color='yellow' variant='light'>
        <Text size='sm'>{t('exportCreating.warningKnowledgeBasesOnly')}</Text>
      </Alert>

      <Group justify='space-between' align='flex-end'>
        <Stack gap='xs'>
          <Text size='sm' fw={500}>
            {t('exportCreating.fileType')}:
          </Text>
          <Radio.Group
            value={fileType}
            onChange={(value) => setFileType(value as FileType)}
            name='fileType'
          >
            <Group mt='xs'>
              <Radio value={FILE_TYPES.CSV} label='CSV' />
              <Radio value={FILE_TYPES.EXCEL} label='Excel' />
            </Group>
          </Radio.Group>
        </Stack>

        {(selectedFolderNames.length > 0 || selectedKBNames.length > 0) && (
          <Stack gap='xs' align='flex-end'>
            {selectedFolderNames.length > 0 && (
              <Text size='sm'>
                <Text component='span' fw={500}>
                  {t('exportCreating.selectedFolder')}:
                </Text>
                {` ${selectedFolderNames.length} ${selectedFolderNames.length === 1 ? t('exportCreating.item') : t('exportCreating.items')}`}
              </Text>
            )}
            {selectedKBNames.length > 0 && (
              <Text size='sm'>
                <Text component='span' fw={500}>
                  {t('exportCreating.selectedKB')}:
                </Text>
                {` ${selectedKBNames.length} ${selectedKBNames.length === 1 ? t('exportCreating.item') : t('exportCreating.items')}`}
              </Text>
            )}
          </Stack>
        )}
      </Group>

      <Box style={{ height: '400px' }}>
        <MultiBasesSelector onSelectionChanged={handleSelectionChange} />
      </Box>

      <Group justify='right'>
        <DecaButton variant='neutral' onClick={onClose} size='md'>
          {t('actions.cancel', { ns: 'common' })}
        </DecaButton>

        <DecaButton
          variant='primary'
          onClick={handleExport}
          loading={isExporting}
          disabled={isExportDisabled}
          size='md'
        >
          {t('exportCreating.createExportJob')}
        </DecaButton>
      </Group>
    </Stack>
  );
};

export default ExportJobModal;
