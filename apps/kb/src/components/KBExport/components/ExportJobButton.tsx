import { type ButtonProps, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { IconUpload } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useExportJobModal } from '../hooks/useExportJobModal';

const useStyles = createStyles(() => ({
  button: {
    fontWeight: 700,
    padding: `0 ${rem(24)}`,
  },
}));

interface ExportJobButtonProps extends Omit<ButtonProps, 'onClick' | 'variant'> {
  /**
   * Callback function called when export job is successfully created
   */
  onSuccess?: () => void;
}

/**
 * Button component that triggers the export job modal
 * Can be placed anywhere in the application with customizable styling
 */
export const ExportJobButton: React.FC<ExportJobButtonProps> = ({
  onSuccess,
  children,
  ...buttonProps
}) => {
  const { t } = useTranslate('export');
  const { openExportJobModal } = useExportJobModal({ onSuccess });
  const { classes } = useStyles();

  return (
    <DecaButton
      className={classes.button}
      variant='primary'
      leftSection={<IconUpload size={24} />}
      onClick={openExportJobModal}
      radius='sm'
      size='md'
      {...buttonProps}
    >
      {children || t('export')}
    </DecaButton>
  );
};

export default ExportJobButton;
