import { renderWithMantine } from '@/utils/unitTest';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the browser language detector to prevent i18n issues
vi.mock('i18next-browser-languagedetector', () => ({
  default: {
    type: 'languageDetector',
    init: vi.fn(),
    detect: vi.fn(() => 'en'),
    cacheUserLanguage: vi.fn(),
  },
}));

// Mock i18next completely
vi.mock('i18next', () => ({
  default: {
    init: vi.fn().mockResolvedValue({}),
    use: vi.fn().mockReturnThis(),
    t: vi.fn((key: string) => key),
    changeLanguage: vi.fn().mockResolvedValue({}),
  },
}));

// Mock the UI package's i18n module directly
vi.mock('@resola-ai/ui/i18n', () => ({
  default: {
    init: vi.fn().mockResolvedValue({}),
    use: vi.fn().mockReturnThis(),
    t: vi.fn((key: string) => key),
    changeLanguage: vi.fn().mockResolvedValue({}),
  },
}));

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: (key: string) => key }),
}));

// Use vi.hoisted to create mock functions that can be used in vi.mock
const { mockOnClose, mockOnSuccess, mockExportJob } = vi.hoisted(() => ({
  mockOnClose: vi.fn(),
  mockOnSuccess: vi.fn(),
  mockExportJob: vi.fn(),
}));

// Mock contexts
vi.mock('@/contexts', () => ({
  useKBSelectionContext: () => ({
    baseSelection: {
      bases: [
        { id: 'kb1', name: 'Knowledge Base 1', baseType: 'article' },
        { id: 'kb2', name: 'Knowledge Base 2', baseType: 'document' },
        { id: 'kb3', name: 'Knowledge Base 3', baseType: 'dataSource' },
      ],
    },
    folderSelection: {
      folders: [
        { id: 'folder1', name: 'Folder 1', parentDirId: 'root' },
        { id: 'folder2', name: 'Folder 2', parentDirId: 'root' },
        { id: 'folder3', name: 'Child Folder', parentDirId: 'folder1' },
      ],
    },
  }),
}));

// Mock useExportJob hook - use a mock that can be modified in individual tests
vi.mock('../hooks/useExportJob', () => ({
  useExportJob: () => ({
    isExporting: false,
    exportJob: mockExportJob,
  }),
}));

// Mock MultiBasesSelector component
vi.mock('@/components/TreeViewSelector', () => ({
  MultiBasesSelector: ({
    onSelectionChanged,
  }: {
    onSelectionChanged?: (parentFolders: string[], childFolders: string[], kbs: string[]) => void;
  }) => (
    <div data-testid='multi-bases-selector'>
      <button
        type='button'
        data-testid='select-folders-button'
        onClick={() => onSelectionChanged?.(['folder1'], ['folder3'], [])}
      >
        Select Folders
      </button>
      <button
        type='button'
        data-testid='select-kbs-button'
        onClick={() => onSelectionChanged?.([], [], ['kb1', 'kb2'])}
      >
        Select KBs
      </button>
      <button
        type='button'
        data-testid='select-mixed-button'
        onClick={() => onSelectionChanged?.(['folder1'], ['folder3'], ['kb1'])}
      >
        Select Mixed
      </button>
      <button
        type='button'
        data-testid='clear-selection-button'
        onClick={() => onSelectionChanged?.([], [], [])}
      >
        Clear Selection
      </button>
    </div>
  ),
}));

// Mock Tabler icons
vi.mock('@tabler/icons-react', () => ({
  IconAlertTriangle: () => <span data-testid='alert-icon'>Alert</span>,
}));

import ExportJobModal from './ExportJobModal';

describe('ExportJobModal', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset the mock implementation to default resolved state
    mockExportJob.mockResolvedValue(undefined);
  });

  describe('Component Rendering', () => {
    it('renders all required elements correctly', () => {
      renderWithMantine(<ExportJobModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);

      // Check for translation keys as they appear in the actual render
      expect(screen.getByText('exportCreating.selectKnowledgeBasesAndFolders')).toBeInTheDocument();
      expect(screen.getByText('exportCreating.warningKnowledgeBasesOnly')).toBeInTheDocument();

      // Look for the actual SVG icon instead of the test ID since the mock isn't working
      expect(screen.getByRole('alert')).toBeInTheDocument(); // The Alert component has role="alert"

      expect(screen.getByText(/exportCreating\.fileType/)).toBeInTheDocument();
      expect(screen.getByTestId('multi-bases-selector')).toBeInTheDocument();

      // Find buttons by role or text content that's actually rendered
      expect(screen.getByRole('button', { name: /actions\.cancel/i })).toBeInTheDocument();
      expect(
        screen.getByRole('button', { name: /exportCreating\.createExportJob/i })
      ).toBeInTheDocument();
    });

    it('renders file type radio buttons with correct default selection', () => {
      renderWithMantine(<ExportJobModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);

      const csvRadio = screen.getByRole('radio', { name: 'CSV' });
      const excelRadio = screen.getByRole('radio', { name: 'Excel' });

      expect(csvRadio).toBeInTheDocument();
      expect(excelRadio).toBeInTheDocument();
      expect(csvRadio).toBeChecked();
      expect(excelRadio).not.toBeChecked();
    });

    it('renders export button as disabled initially', () => {
      renderWithMantine(<ExportJobModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);

      const exportButton = screen.getByRole('button', { name: /exportCreating\.createExportJob/i });
      expect(exportButton).toBeDisabled();
    });
  });

  describe('File Type Selection', () => {
    it('allows switching between CSV and Excel file types', () => {
      renderWithMantine(<ExportJobModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);

      const csvRadio = screen.getByRole('radio', { name: 'CSV' });
      const excelRadio = screen.getByRole('radio', { name: 'Excel' });

      // Switch to Excel
      fireEvent.click(excelRadio);
      expect(excelRadio).toBeChecked();
      expect(csvRadio).not.toBeChecked();

      // Switch back to CSV
      fireEvent.click(csvRadio);
      expect(csvRadio).toBeChecked();
      expect(excelRadio).not.toBeChecked();
    });
  });

  describe('Selection Handling', () => {
    it('updates selection state when folders are selected', () => {
      renderWithMantine(<ExportJobModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);

      const selectFoldersButton = screen.getByTestId('select-folders-button');
      fireEvent.click(selectFoldersButton);

      // Check if selection text appears - look for the actual translated key
      expect(screen.getByText(/exportCreating\.selectedFolder/)).toBeInTheDocument();
      expect(screen.getByText(/1 exportCreating\.item/)).toBeInTheDocument();
    });

    it('updates selection state when knowledge bases are selected', () => {
      renderWithMantine(<ExportJobModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);

      const selectKBsButton = screen.getByTestId('select-kbs-button');
      fireEvent.click(selectKBsButton);

      expect(screen.getByText(/exportCreating\.selectedKB/)).toBeInTheDocument();
      expect(screen.getByText(/2 exportCreating\.items/)).toBeInTheDocument();
    });

    it('handles mixed selections correctly', () => {
      renderWithMantine(<ExportJobModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);

      const selectMixedButton = screen.getByTestId('select-mixed-button');
      fireEvent.click(selectMixedButton);

      expect(screen.getByText(/exportCreating\.selectedFolder/)).toBeInTheDocument();
      expect(screen.getByText(/exportCreating\.selectedKB/)).toBeInTheDocument();

      // Check that both selections show 1 item each by finding all matching elements
      const itemTexts = screen.getAllByText(/1 exportCreating\.item/);
      expect(itemTexts).toHaveLength(2); // One for folders, one for KBs
    });

    it('enables export button when items are selected', () => {
      renderWithMantine(<ExportJobModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);

      const exportButton = screen.getByRole('button', { name: /exportCreating\.createExportJob/i });
      expect(exportButton).toBeDisabled();

      const selectKBsButton = screen.getByTestId('select-kbs-button');
      fireEvent.click(selectKBsButton);

      expect(exportButton).not.toBeDisabled();
    });

    it('disables export button when no items are selected', () => {
      renderWithMantine(<ExportJobModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);

      // First select some items
      const selectKBsButton = screen.getByTestId('select-kbs-button');
      fireEvent.click(selectKBsButton);

      const exportButton = screen.getByRole('button', { name: /exportCreating\.createExportJob/i });
      expect(exportButton).not.toBeDisabled();

      // Then clear selection
      const clearSelectionButton = screen.getByTestId('clear-selection-button');
      fireEvent.click(clearSelectionButton);

      expect(exportButton).toBeDisabled();
    });
  });

  describe('Export Functionality', () => {
    it('calls exportJob with correct data when export button is clicked', async () => {
      renderWithMantine(<ExportJobModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);

      // Select some items first
      const selectMixedButton = screen.getByTestId('select-mixed-button');
      fireEvent.click(selectMixedButton);

      // Change file type to Excel
      const excelRadio = screen.getByRole('radio', { name: 'Excel' });
      fireEvent.click(excelRadio);

      // Click export button
      const exportButton = screen.getByRole('button', { name: /exportCreating\.createExportJob/i });
      fireEvent.click(exportButton);

      await waitFor(() => {
        expect(mockExportJob).toHaveBeenCalledWith({
          folderPaths: ['folder3'], // Only child folders, not parent folders
          kbIds: ['kb1'],
          fileType: 'excel',
        });
      });
    });

    it('handles export job errors gracefully', async () => {
      // Spy on console.error and mock it to prevent the error from appearing in test output
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      mockExportJob.mockRejectedValue(new Error('Export failed'));

      renderWithMantine(<ExportJobModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);

      // Select items and export
      const selectKBsButton = screen.getByTestId('select-kbs-button');
      fireEvent.click(selectKBsButton);

      const exportButton = screen.getByRole('button', { name: /exportCreating\.createExportJob/i });
      fireEvent.click(exportButton);

      await waitFor(() => {
        expect(consoleErrorSpy).toHaveBeenCalledWith('Export failed:', expect.any(Error));
      });

      // Restore console.error and reset mock for subsequent tests
      consoleErrorSpy.mockRestore();
      mockExportJob.mockResolvedValue(undefined);
    });
  });

  describe('Callback Handling', () => {
    it('calls onClose when cancel button is clicked', () => {
      renderWithMantine(<ExportJobModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);

      const cancelButton = screen.getByRole('button', { name: /actions\.cancel/i });
      fireEvent.click(cancelButton);

      expect(mockOnClose).toHaveBeenCalledTimes(1);
    });
  });

  describe('Data Mapping', () => {
    it('uses only child folders in export data', async () => {
      renderWithMantine(<ExportJobModal onClose={mockOnClose} onSuccess={mockOnSuccess} />);

      // Select mixed items
      const selectMixedButton = screen.getByTestId('select-mixed-button');
      fireEvent.click(selectMixedButton);

      const exportButton = screen.getByRole('button', { name: /exportCreating\.createExportJob/i });
      fireEvent.click(exportButton);

      await waitFor(() => {
        expect(mockExportJob).toHaveBeenCalledWith({
          folderPaths: ['folder3'], // Only child folders, not parent folders
          kbIds: ['kb1'],
          fileType: 'csv', // Default file type
        });
      });
    });
  });
});
