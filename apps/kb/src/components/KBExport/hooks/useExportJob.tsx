import { useNotifications } from '@/hooks';
import { JobAPI } from '@/services/api/v2/job';
import { Anchor } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { T, useTranslate } from '@tolgee/react';
import { useCallback, useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface ExportJobData {
  folderPaths: string[];
  kbIds: string[];
  fileType?: 'csv' | 'excel';
}

interface UseExportJobProps {
  onSuccess?: () => void;
}

export const useExportJob = ({ onSuccess }: UseExportJobProps = {}) => {
  const { t } = useTranslate(['export', 'common']);
  const { notifyError } = useNotifications(t);
  const navigate = useNavigate();
  const [isExporting, setIsExporting] = useState(false);

  const exportJob = useCallback(
    async (data: ExportJobData) => {
      setIsExporting(true);

      try {
        const payload = {
          metadata: {
            locationFilter: {
              baseIds: data.kbIds,
              folderIds: data.folderPaths,
            },
            fileType: data.fileType || 'csv',
          },
        };

        const response = await JobAPI.createExportJob(payload);

        // Show notification with link to Job detail page
        notifications.show({
          title: t('exportCreating.exportCompleted'),
          message: (
            <T
              keyName='exportCreating.clickToViewJobDetail'
              params={{
                CustomLink: (
                  <Anchor
                    c='decaBlue.6'
                    size='sm'
                    onClick={() => navigate(`/kb/jobs/${(response.data as any).id}`)}
                  />
                ),
              }}
              ns='export'
            />
          ),
          color: 'green',
        });

        onSuccess?.();
        return response;
      } catch (error) {
        notifyError(t('exportCreating.exportFailed'), t('exportCreating.exportJobFailed'));
        throw error;
      } finally {
        setIsExporting(false);
      }
    },
    [notifyError, t, onSuccess, navigate]
  );

  return {
    isExporting,
    exportJob,
  };
};
