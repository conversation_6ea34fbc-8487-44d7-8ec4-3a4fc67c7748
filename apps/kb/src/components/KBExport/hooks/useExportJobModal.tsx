import { KBSelectionContextProvider } from '@/contexts';
import { useModalManager } from '@/hooks';
import { modals } from '@mantine/modals';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';
import ExportJobModal from '../components/ExportJobModal';

interface UseExportJobModalProps {
  onSuccess?: () => void;
}

export const useExportJobModal = ({ onSuccess }: UseExportJobModalProps = {}) => {
  const { t } = useTranslate(['export', 'common']);
  const { modalClasses, createModal } = useModalManager();

  const handleClose = useCallback(() => {
    modals.closeAll();
  }, []);

  const openExportJobModal = useCallback(() => {
    modals.open(
      createModal({
        title: t('exportCreating.selectKnowledgeBasesAndFoldersToExport'),
        classNames: {
          content: modalClasses.extraLargeModal,
        },
        onClose: handleClose,
        children: (
          <KBSelectionContextProvider>
            <ExportJobModal onClose={handleClose} onSuccess={onSuccess} />
          </KBSelectionContextProvider>
        ),
      })
    );
  }, [handleClose, t, modalClasses, createModal, onSuccess]);

  return {
    openExportJobModal,
  };
};
