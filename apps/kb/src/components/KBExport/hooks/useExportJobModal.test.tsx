import { act, renderHook } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Test constants
const MOCK_MODAL_CLASSES = {
  extraLargeModal: 'extra-large-modal-class',
  modal: 'modal-class',
};

// Use vi.hoisted to create mock functions that can be used in vi.mock
const { mockOnSuccess, mockModalsOpen, mockModalsCloseAll, mockCreateModal, mockT } = vi.hoisted(
  () => ({
    mockOnSuccess: vi.fn(),
    mockModalsOpen: vi.fn(),
    mockModalsCloseAll: vi.fn(),
    mockCreateModal: vi.fn(),
    mockT: vi.fn(),
  })
);

// Mock react-i18next
vi.mock('@tolgee/react', async () => {
  const actual = await vi.importActual('@tolgee/react');
  return {
    ...actual,
    useTranslate: () => ({
      t: mockT,
      i18n: {
        language: 'en',
      },
    }),
    initReactI18next: {
      type: '3rdParty',
      init: () => {},
    },
  };
});

// Mock @mantine/modals
vi.mock('@mantine/modals', () => ({
  modals: {
    open: mockModalsOpen,
    closeAll: mockModalsCloseAll,
  },
}));

// Mock useModalManager hook
vi.mock('@/hooks', () => ({
  useModalManager: () => ({
    modalClasses: MOCK_MODAL_CLASSES,
    createModal: mockCreateModal,
  }),
}));

// Mock KBSelectionContextProvider
vi.mock('@/contexts', () => ({
  KBSelectionContextProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='kb-selection-context-provider'>{children}</div>
  ),
}));

// Mock ExportJobModal component
vi.mock('./ExportJobModal', () => ({
  default: ({ onClose, onSuccess }: { onClose: () => void; onSuccess?: () => void }) => (
    <div data-testid='export-job-modal'>
      <button type='button' data-testid='close-button' onClick={onClose}>
        Close
      </button>
      <button type='button' data-testid='success-button' onClick={onSuccess}>
        Success
      </button>
    </div>
  ),
}));

import { useExportJobModal } from './useExportJobModal';

describe('useExportJobModal', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockT.mockImplementation((key: string) => `translated_${key}`);
    mockCreateModal.mockImplementation((config: any) => config);
  });

  describe('Initial State', () => {
    it('returns openExportJobModal function', () => {
      const { result } = renderHook(() => useExportJobModal());

      expect(typeof result.current.openExportJobModal).toBe('function');
    });

    it('returns openExportJobModal function with onSuccess callback', () => {
      const { result } = renderHook(() => useExportJobModal({ onSuccess: mockOnSuccess }));

      expect(typeof result.current.openExportJobModal).toBe('function');
    });

    it('works without any props', () => {
      const { result } = renderHook(() => useExportJobModal());

      expect(result.current).toEqual({
        openExportJobModal: expect.any(Function),
      });
    });
  });

  describe('Translation Handling', () => {
    it('calls translation function with correct parameters', () => {
      const { result } = renderHook(() => useExportJobModal());

      act(() => {
        result.current.openExportJobModal();
      });

      expect(mockT).toHaveBeenCalledWith('exportCreating.selectKnowledgeBasesAndFoldersToExport');
    });

    it('uses translated title in modal configuration', () => {
      const { result } = renderHook(() => useExportJobModal());

      act(() => {
        result.current.openExportJobModal();
      });

      expect(mockCreateModal).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'translated_exportCreating.selectKnowledgeBasesAndFoldersToExport',
        })
      );
    });
  });

  describe('Modal Configuration', () => {
    it('creates modal with correct configuration', () => {
      const { result } = renderHook(() => useExportJobModal({ onSuccess: mockOnSuccess }));

      act(() => {
        result.current.openExportJobModal();
      });

      expect(mockCreateModal).toHaveBeenCalledWith({
        title: 'translated_exportCreating.selectKnowledgeBasesAndFoldersToExport',
        classNames: {
          content: 'extra-large-modal-class',
        },
        onClose: expect.any(Function),
        children: expect.any(Object),
      });
    });

    it('uses correct modal classes from useModalManager', () => {
      const { result } = renderHook(() => useExportJobModal());

      act(() => {
        result.current.openExportJobModal();
      });

      const createModalCall = mockCreateModal.mock.calls[0][0];
      expect(createModalCall.classNames.content).toBe('extra-large-modal-class');
    });

    it('opens modal with modals.open', () => {
      const { result } = renderHook(() => useExportJobModal());

      act(() => {
        result.current.openExportJobModal();
      });

      expect(mockModalsOpen).toHaveBeenCalledTimes(1);
      expect(mockModalsOpen).toHaveBeenCalledWith(
        expect.objectContaining({
          title: expect.any(String),
          classNames: expect.any(Object),
          onClose: expect.any(Function),
          children: expect.any(Object),
        })
      );
    });
  });

  describe('Modal Children Structure', () => {
    it('wraps ExportJobModal with KBSelectionContextProvider', () => {
      const { result } = renderHook(() => useExportJobModal({ onSuccess: mockOnSuccess }));

      act(() => {
        result.current.openExportJobModal();
      });

      const createModalCall = mockCreateModal.mock.calls[0][0];
      const children = createModalCall.children;

      // Verify the structure includes the context provider
      expect(children).toBeDefined();
      expect(React.isValidElement(children)).toBe(true);
    });
  });

  describe('Modal Close Handling', () => {
    it('provides handleClose function to modal configuration', () => {
      const { result } = renderHook(() => useExportJobModal());

      act(() => {
        result.current.openExportJobModal();
      });

      const createModalCall = mockCreateModal.mock.calls[0][0];
      expect(typeof createModalCall.onClose).toBe('function');
    });

    it('handleClose calls modals.closeAll', () => {
      const { result } = renderHook(() => useExportJobModal());

      act(() => {
        result.current.openExportJobModal();
      });

      const createModalCall = mockCreateModal.mock.calls[0][0];
      const handleClose = createModalCall.onClose;

      act(() => {
        handleClose();
      });

      expect(mockModalsCloseAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('Success Callback Handling', () => {
    it('passes onSuccess callback to ExportJobModal when provided', () => {
      const { result } = renderHook(() => useExportJobModal({ onSuccess: mockOnSuccess }));

      act(() => {
        result.current.openExportJobModal();
      });

      // The children should contain ExportJobModal with onSuccess prop
      const createModalCall = mockCreateModal.mock.calls[0][0];
      expect(createModalCall.children).toBeDefined();
    });

    it('works correctly when onSuccess is not provided', () => {
      const { result } = renderHook(() => useExportJobModal());

      act(() => {
        result.current.openExportJobModal();
      });

      expect(mockCreateModal).toHaveBeenCalledTimes(1);
      expect(mockModalsOpen).toHaveBeenCalledTimes(1);
    });
  });

  describe('Hook Dependencies and Stability', () => {
    it('maintains stable reference for openExportJobModal when dependencies do not change', () => {
      const { result, rerender } = renderHook(() =>
        useExportJobModal({ onSuccess: mockOnSuccess })
      );

      const firstOpenFunction = result.current.openExportJobModal;

      rerender();

      const secondOpenFunction = result.current.openExportJobModal;

      expect(firstOpenFunction).toBe(secondOpenFunction);
    });

    it('updates openExportJobModal function when onSuccess changes', () => {
      const mockOnSuccess2 = vi.fn();
      const { result, rerender } = renderHook(({ onSuccess }) => useExportJobModal({ onSuccess }), {
        initialProps: { onSuccess: mockOnSuccess },
      });

      const firstOpenFunction = result.current.openExportJobModal;

      rerender({ onSuccess: mockOnSuccess2 });

      const secondOpenFunction = result.current.openExportJobModal;

      expect(firstOpenFunction).not.toBe(secondOpenFunction);
    });

    it('calls useTranslate with correct namespaces', () => {
      renderHook(() => useExportJobModal());

      // The mock should be called during hook initialization
      expect(mockT).toBeDefined();
    });
  });

  describe('Multiple Modal Operations', () => {
    it('can open modal multiple times', () => {
      const { result } = renderHook(() => useExportJobModal());

      act(() => {
        result.current.openExportJobModal();
      });

      act(() => {
        result.current.openExportJobModal();
      });

      expect(mockModalsOpen).toHaveBeenCalledTimes(2);
    });

    it('each modal open creates a new modal configuration', () => {
      const { result } = renderHook(() => useExportJobModal());

      act(() => {
        result.current.openExportJobModal();
      });

      act(() => {
        result.current.openExportJobModal();
      });

      expect(mockCreateModal).toHaveBeenCalledTimes(2);
    });
  });

  describe('Error Scenarios', () => {
    it('handles translation function errors gracefully', () => {
      mockT.mockImplementation(() => {
        throw new Error('Translation error');
      });

      const { result } = renderHook(() => useExportJobModal());

      expect(() => {
        act(() => {
          result.current.openExportJobModal();
        });
      }).toThrow('Translation error');
    });

    it('handles modal creation errors gracefully', () => {
      mockCreateModal.mockImplementation(() => {
        throw new Error('Modal creation error');
      });

      const { result } = renderHook(() => useExportJobModal());

      expect(() => {
        act(() => {
          result.current.openExportJobModal();
        });
      }).toThrow('Modal creation error');
    });
  });

  describe('Hook Return Value Consistency', () => {
    it('always returns the same shape of object', () => {
      const { result } = renderHook(() => useExportJobModal());

      expect(Object.keys(result.current)).toEqual(['openExportJobModal']);
    });

    it('openExportJobModal function is always present', () => {
      const { result } = renderHook(() => useExportJobModal());

      expect(result.current.openExportJobModal).toBeDefined();
      expect(typeof result.current.openExportJobModal).toBe('function');
    });
  });
});
