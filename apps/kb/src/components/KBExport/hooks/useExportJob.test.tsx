import { AllTheProviders } from '@/utils/unitTest';
import { act, renderHook, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Test constants
const MOCK_EXPORT_DATA = {
  folderPaths: ['folder1', 'folder2'],
  kbIds: ['kb1', 'kb2'],
  fileType: 'csv' as const,
};

const MOCK_EXPORT_DATA_EXCEL = {
  folderPaths: ['folder3'],
  kbIds: ['kb3'],
  fileType: 'excel' as const,
};

const MOCK_API_RESPONSE = {
  id: 'job-123',
  status: 'created',
  metadata: {
    locationFilter: {
      baseIds: ['kb1', 'kb2'],
      folderIds: ['folder1', 'folder2'],
    },
    fileType: 'csv',
  },
};

// Use vi.hoisted to create mock functions that can be used in vi.mock
const { mockOnSuccess, mockNotifyMessage, mockNotifyError, mockCreateExportJob, mockNavigate } =
  vi.hoisted(() => ({
    mockOnSuccess: vi.fn(),
    mockNotifyMessage: vi.fn(),
    mockNotifyError: vi.fn(),
    mockCreateExportJob: vi.fn(),
    mockNavigate: vi.fn(),
  }));

// Mock react-router-dom
vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}));

// Mock Mantine notifications
vi.mock('@mantine/notifications', () => ({
  notifications: {
    show: vi.fn(),
  },
}));

// Mock useNotifications hook
vi.mock('@/hooks', () => ({
  useNotifications: () => ({
    notifyMessage: mockNotifyMessage,
    notifyError: mockNotifyError,
  }),
  useFileUpload: () => ({
    uploadFiles: vi.fn(),
    uploaders: {},
    validateFiles: vi.fn(),
    isUploading: false,
    error: null,
  }),
}));

// Mock JobAPI
vi.mock('@/services/api/v2/job', () => ({
  JobAPI: {
    createExportJob: mockCreateExportJob,
  },
}));

import { useExportJob } from './useExportJob';

describe('useExportJob', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockCreateExportJob.mockResolvedValue({ data: MOCK_API_RESPONSE });
  });

  describe('Initial State', () => {
    it('returns initial state correctly', () => {
      const { result } = renderHook(() => useExportJob(), { wrapper: AllTheProviders });

      expect(result.current.isExporting).toBe(false);
      expect(typeof result.current.exportJob).toBe('function');
    });

    it('returns initial state with onSuccess callback', () => {
      const { result } = renderHook(() => useExportJob({ onSuccess: mockOnSuccess }), {
        wrapper: AllTheProviders,
      });

      expect(result.current.isExporting).toBe(false);
      expect(typeof result.current.exportJob).toBe('function');
    });
  });

  describe('Export Job Execution', () => {
    it('calls API with correct payload for CSV export', async () => {
      const { result } = renderHook(() => useExportJob({ onSuccess: mockOnSuccess }), {
        wrapper: AllTheProviders,
      });

      await act(async () => {
        await result.current.exportJob(MOCK_EXPORT_DATA);
      });

      expect(mockCreateExportJob).toHaveBeenCalledWith({
        metadata: {
          locationFilter: {
            baseIds: ['kb1', 'kb2'],
            folderIds: ['folder1', 'folder2'],
          },
          fileType: 'csv',
        },
      });
    });

    it('calls API with correct payload for Excel export', async () => {
      const { result } = renderHook(() => useExportJob({ onSuccess: mockOnSuccess }), {
        wrapper: AllTheProviders,
      });

      await act(async () => {
        await result.current.exportJob(MOCK_EXPORT_DATA_EXCEL);
      });

      expect(mockCreateExportJob).toHaveBeenCalledWith({
        metadata: {
          locationFilter: {
            baseIds: ['kb3'],
            folderIds: ['folder3'],
          },
          fileType: 'excel',
        },
      });
    });

    it('defaults to CSV when fileType is not provided', async () => {
      const { result } = renderHook(() => useExportJob(), { wrapper: AllTheProviders });

      const dataWithoutFileType = {
        folderPaths: ['folder1'],
        kbIds: ['kb1'],
      };

      await act(async () => {
        await result.current.exportJob(dataWithoutFileType);
      });

      expect(mockCreateExportJob).toHaveBeenCalledWith({
        metadata: {
          locationFilter: {
            baseIds: ['kb1'],
            folderIds: ['folder1'],
          },
          fileType: 'csv',
        },
      });
    });

    it('returns API response on successful export', async () => {
      const { result } = renderHook(() => useExportJob(), { wrapper: AllTheProviders });

      let response: any;
      await act(async () => {
        response = await result.current.exportJob(MOCK_EXPORT_DATA);
      });

      expect(response).toEqual({ data: MOCK_API_RESPONSE });
    });
  });

  describe('Loading State Management', () => {
    it('sets isExporting to true during API call', async () => {
      // Create a promise that we can control
      let resolvePromise: (value: any) => void;
      const controlledPromise = new Promise((resolve) => {
        resolvePromise = resolve;
      });

      mockCreateExportJob.mockReturnValue(controlledPromise);

      const { result } = renderHook(() => useExportJob(), { wrapper: AllTheProviders });

      // Start the export job but don't wait for it to complete
      act(() => {
        result.current.exportJob(MOCK_EXPORT_DATA);
      });

      // Check that isExporting is true while the promise is pending
      expect(result.current.isExporting).toBe(true);

      // Resolve the promise to complete the test
      await act(async () => {
        resolvePromise!({ data: MOCK_API_RESPONSE });
        await controlledPromise;
      });
    });

    it('resets isExporting to false after successful export', async () => {
      const { result } = renderHook(() => useExportJob(), { wrapper: AllTheProviders });

      await act(async () => {
        await result.current.exportJob(MOCK_EXPORT_DATA);
      });

      await waitFor(() => {
        expect(result.current.isExporting).toBe(false);
      });
    });

    it('resets isExporting to false after failed export', async () => {
      mockCreateExportJob.mockRejectedValue(new Error('API Error'));
      const { result } = renderHook(() => useExportJob(), { wrapper: AllTheProviders });

      await act(async () => {
        try {
          await result.current.exportJob(MOCK_EXPORT_DATA);
        } catch (error) {
          // Expected to throw
        }
      });

      await waitFor(() => {
        expect(result.current.isExporting).toBe(false);
      });
    });
  });

  describe('Success Handling', () => {
    it('calls onSuccess callback after successful export', async () => {
      const { result } = renderHook(() => useExportJob({ onSuccess: mockOnSuccess }), {
        wrapper: AllTheProviders,
      });

      await act(async () => {
        await result.current.exportJob(MOCK_EXPORT_DATA);
      });

      expect(mockOnSuccess).toHaveBeenCalledTimes(1);
    });

    it('shows success notification after successful export', async () => {
      const { notifications } = await import('@mantine/notifications');
      const { result } = renderHook(() => useExportJob(), { wrapper: AllTheProviders });

      await act(async () => {
        await result.current.exportJob(MOCK_EXPORT_DATA);
      });

      expect(notifications.show).toHaveBeenCalledWith({
        title: 'exportCreating.exportCompleted',
        message: expect.any(Object),
        color: 'green',
      });
    });

    it('does not call onSuccess when not provided', async () => {
      const { result } = renderHook(() => useExportJob(), { wrapper: AllTheProviders });

      await act(async () => {
        await result.current.exportJob(MOCK_EXPORT_DATA);
      });

      // Should not throw error when onSuccess is undefined
      const { notifications } = await import('@mantine/notifications');
      expect(notifications.show).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('shows error notification when API call fails', async () => {
      const mockError = new Error('API Error');
      mockCreateExportJob.mockRejectedValue(mockError);

      const { result } = renderHook(() => useExportJob(), { wrapper: AllTheProviders });

      await act(async () => {
        try {
          await result.current.exportJob(MOCK_EXPORT_DATA);
        } catch (error) {
          expect(error).toBe(mockError);
        }
      });

      expect(mockNotifyError).toHaveBeenCalledWith(
        'exportCreating.exportFailed',
        'exportCreating.exportJobFailed'
      );
    });

    it('rethrows the error after handling', async () => {
      const mockError = new Error('API Error');
      mockCreateExportJob.mockRejectedValue(mockError);

      const { result } = renderHook(() => useExportJob(), { wrapper: AllTheProviders });

      await expect(async () => {
        await act(async () => {
          await result.current.exportJob(MOCK_EXPORT_DATA);
        });
      }).rejects.toThrow('API Error');
    });

    it('does not call onSuccess when export fails', async () => {
      mockCreateExportJob.mockRejectedValue(new Error('API Error'));

      const { result } = renderHook(() => useExportJob({ onSuccess: mockOnSuccess }), {
        wrapper: AllTheProviders,
      });

      await act(async () => {
        try {
          await result.current.exportJob(MOCK_EXPORT_DATA);
        } catch (error) {
          // Expected to throw
        }
      });

      expect(mockOnSuccess).not.toHaveBeenCalled();
    });
  });

  describe('Hook Dependencies', () => {
    it('exportJob function reference changes when dependencies change', () => {
      const { result, rerender } = renderHook(() => useExportJob({ onSuccess: mockOnSuccess }), {
        wrapper: AllTheProviders,
      });

      const firstExportJob = result.current.exportJob;

      rerender();

      const secondExportJob = result.current.exportJob;

      // The function may change due to internal dependencies like notifyMessage, notifyError, t, onSuccess
      // This is expected behavior for useCallback with dependencies
      expect(typeof firstExportJob).toBe('function');
      expect(typeof secondExportJob).toBe('function');
    });

    it('updates exportJob function when onSuccess changes', () => {
      const mockOnSuccess2 = vi.fn();
      const { result, rerender } = renderHook(({ onSuccess }) => useExportJob({ onSuccess }), {
        initialProps: { onSuccess: mockOnSuccess },
        wrapper: AllTheProviders,
      });

      const firstExportJob = result.current.exportJob;

      rerender({ onSuccess: mockOnSuccess2 });

      const secondExportJob = result.current.exportJob;

      expect(firstExportJob).not.toBe(secondExportJob);
    });
  });

  describe('Edge Cases', () => {
    it('handles empty folderPaths and kbIds', async () => {
      const { result } = renderHook(() => useExportJob(), { wrapper: AllTheProviders });

      const emptyData = {
        folderPaths: [],
        kbIds: [],
        fileType: 'csv' as const,
      };

      await act(async () => {
        await result.current.exportJob(emptyData);
      });

      expect(mockCreateExportJob).toHaveBeenCalledWith({
        metadata: {
          locationFilter: {
            baseIds: [],
            folderIds: [],
          },
          fileType: 'csv',
        },
      });
    });

    it('handles network timeout errors', async () => {
      const timeoutError = new Error('Network timeout');
      mockCreateExportJob.mockRejectedValue(timeoutError);

      const { result } = renderHook(() => useExportJob(), { wrapper: AllTheProviders });

      await act(async () => {
        try {
          await result.current.exportJob(MOCK_EXPORT_DATA);
        } catch (error) {
          expect(error).toBe(timeoutError);
        }
      });

      expect(mockNotifyError).toHaveBeenCalled();
      expect(result.current.isExporting).toBe(false);
    });
  });
});
