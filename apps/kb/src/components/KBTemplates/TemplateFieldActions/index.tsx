import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconArrowsMove, IconEdit, IconTrash } from '@tabler/icons-react';
import type React from 'react';

const useStyles = createStyles((theme) => ({
  actions: {
    display: 'flex',
    gap: rem(10),
    flexDirection: 'column',
  },
  actionIcon: {
    width: rem(18),
    height: rem(18),
  },
  moveIcon: {
    color: theme.colors.decaNavy[5],
    cursor: 'move',
  },
  editIcon: {
    color: theme.colors.decaNavy[5],
    cursor: 'pointer',
  },
  deleteIcon: {
    color: theme.colors.decaRed[5],
    cursor: 'pointer',
  },
}));

interface TemplateFieldActionsProps {
  className?: string;
  onEdit: () => void;
  onDelete: () => void;
}

const TemplateFieldActions: React.FC<TemplateFieldActionsProps> = ({
  className,
  onEdit,
  onDelete,
}) => {
  const { classes, cx } = useStyles();

  return (
    <Box className={cx(classes.actions, className)}>
      <IconArrowsMove className={cx(classes.actionIcon, classes.moveIcon)} />
      <IconEdit className={cx(classes.actionIcon, classes.editIcon)} onClick={onEdit} />
      <IconTrash className={cx(classes.actionIcon, classes.deleteIcon)} onClick={onDelete} />
    </Box>
  );
};

export default TemplateFieldActions;
