import { IllustrationIcon } from '@/components/common';
import useKbAccessControl from '@/hooks/useKbAccessControl';
import { KB_TYPE } from '@/types/kb';
import { Box, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton, TextEllipsis } from '@resola-ai/ui';
import { IconPencil } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';

interface TemplateCardProps {
  title: string;
  description?: string;
  onView: () => void;
  onEdit: () => void;
}

const useStyles = createStyles((theme) => ({
  cardWrapper: {
    border: `${rem(1)} solid transparent`,
  },
  cardContent: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    gap: rem(8),
  },
  cardThumbnail: {
    width: rem(150),
    minWidth: rem(150),
    height: rem(150),
    borderRadius: theme.radius.md,
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F0E8FF78', // 47% opacity of decaViolet[0]
    cursor: 'pointer',
  },
  cardInformation: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(8),
    paddingLeft: theme.spacing.sm,
  },
  title: {
    fontWeight: 700,
    color: theme.colors.decaNavy[5],
    cursor: 'pointer',
  },
  description: {
    fontWeight: 500,
    color: theme.colors.decaGrey[3],
    lineHeight: rem(22),
  },
  bottomActions: {
    marginTop: theme.spacing.xs,
    '& button': {
      minWidth: rem(100),
    },
    '& svg': {
      marginRight: theme.spacing.xs,
      width: rem(16),
      height: rem(16),
    },
  },
}));

const TemplateCard: React.FC<TemplateCardProps> = ({ title, description = '', onView, onEdit }) => {
  const { t } = useTranslate('common');
  const { permTemplate } = useKbAccessControl();
  const { classes } = useStyles();

  return (
    <Box className={classes.cardWrapper}>
      <Box className={classes.cardContent}>
        <Box className={classes.cardThumbnail} onClick={onView}>
          <IllustrationIcon type={KB_TYPE.article} width={90} />
        </Box>
        <Box className={classes.cardInformation}>
          <TextEllipsis lines={2}>
            <Title className={classes.title} order={5} onClick={onView}>
              {title}
            </Title>
          </TextEllipsis>
          <TextEllipsis className={classes.description} lines={4}>
            {description}
          </TextEllipsis>
          <Box className={classes.bottomActions}>
            {permTemplate.canUpdate ? (
              <DecaButton radius={'xl'} variant='neutral' onClick={onEdit}>
                <IconPencil size={16} />
                {t('editBtn')}
              </DecaButton>
            ) : null}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default TemplateCard;
