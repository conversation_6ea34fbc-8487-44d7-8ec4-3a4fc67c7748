import { useTemplateInfoForm } from '@/hooks/useTemplateInfoForm';
import type { KBTemplate } from '@/types/template';
import { Box, Group, Input } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { RoundedButton } from '@resola-ai/ui';
import { sanitizeHtml } from '@resola-ai/ui/utils';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { TextInput, Textarea } from 'react-hook-form-mantine';

interface TemplateInfoFormProps {
  template?: KBTemplate;
  onSubmitted: (formValues: Partial<KBTemplate>) => void;
  onCancel: () => void;
}

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    flexDirection: 'column',
    gap: theme.spacing.md,
    '.mantine-InputWrapper-root': {
      width: '100%',
      display: 'flex',
      justifyContent: 'flex-start',
      alignItems: 'flex-start',

      '&.mantine-TextInput-root, &.mantine-Textarea-root': {
        flexDirection: 'column',
      },
    },
    '.mantine-InputWrapper-label': {
      width: '30%',
      marginRight: theme.spacing.xs,
      color: theme.colors.decaNavy[5],
      fontSize: theme.fontSizes.md,
    },
    '.mantine-InputWrapper-error': {
      width: '70%',
      display: 'flex',
    },
  },
  inputInner: {
    width: '70%',
    '.mantine-Input-wrapper': {
      width: '100%',
    },
  },
  buttonGroup: {
    width: '100%',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingTop: theme.spacing.md,
  },
}));

/**
 * TemplateInfoForm component displays the form to edit template information
 * @param {TemplateInfoFormProps} props
 * @returns {React.FC<TemplateInfoFormProps>}
 */
const TemplateInfoForm: React.FC<TemplateInfoFormProps> = ({ template, onCancel, onSubmitted }) => {
  const { classes } = useStyles();
  const { t } = useTranslate('common');

  const { control, handleSubmit, errors, getValues } = useTemplateInfoForm({
    currentTemplate: template,
    onSubmitted: () => {
      const sanitizedValues = {
        ...getValues(),
        description: sanitizeHtml(getValues().description),
      };
      onSubmitted?.(sanitizedValues);
    },
  });

  return (
    <form className={classes.root}>
      <Input.Wrapper label={t('form.name')} withAsterisk>
        <Box className={classes.inputInner}>
          <TextInput
            control={control}
            name='templateTitle'
            placeholder={t('form.namePlaceholder')}
            error={errors.templateTitle?.message}
          />
        </Box>
      </Input.Wrapper>

      <Input.Wrapper label={t('form.description')}>
        <Box className={classes.inputInner}>
          <Textarea
            control={control}
            name='description'
            placeholder={t('form.descriptionPlaceholder')}
            minRows={4}
            error={errors.description?.message}
          />
        </Box>
      </Input.Wrapper>

      <Group className={classes.buttonGroup}>
        <RoundedButton variant='light' onClick={() => onCancel()}>
          {t('actions.cancel')}
        </RoundedButton>

        <RoundedButton onClick={handleSubmit} variant='filled'>
          {t('actions.save')}
        </RoundedButton>
      </Group>
    </form>
  );
};

export default TemplateInfoForm;
