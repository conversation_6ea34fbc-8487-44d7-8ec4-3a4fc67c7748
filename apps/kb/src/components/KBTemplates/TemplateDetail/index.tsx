import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import type React from 'react';
import { useCallback, useEffect, useMemo } from 'react';

import { BackToLink, KBBasicInfo, Modal } from '@/components';
import { useTemplateDetailContext } from '@/contexts/TemplateDetailContext';
import { useKbAccessControl, useNotifications } from '@/hooks';
import type { KBTemplate, KBTemplateField, KBTemplateStaticFields } from '@/types/template';
import TemplateCustomFields from '../TemplateCustomFields';
import TemplateInfoForm from '../TemplateInfoForm';
import TemplateStaticFields from '../TemplateStaticFields';

const useStyles = createStyles((theme) => ({
  infoSection: {
    margin: `${rem(8)} 0`,
  },
  fieldsSection: {
    backgroundColor: theme.colors.decaLight[0],
    padding: rem(24),
    display: 'flex',
    justifyContent: 'flex-start',
    gap: rem(16),
    flexDirection: 'column',
  },
}));

interface TemplateDetailProps {
  templateId: string | undefined;
}

/**
 * TemplateDetail component displays the details of a template
 * @param {TemplateDetailProps} props
 * @returns {React.FC<TemplateDetailProps>}
 */
const TemplateDetail: React.FC<TemplateDetailProps> = ({ templateId }) => {
  const { t } = useTranslate(['settings', 'common']);
  const { notifyResponse } = useNotifications(t);
  const { classes } = useStyles();
  const { permTemplate } = useKbAccessControl();
  const [opened, { open, close }] = useDisclosure(false);
  const {
    templateDetailState: { currentTemplate },
    fetchTemplateDetail,
    updateTemplateDetail,
  } = useTemplateDetailContext();
  const templateOrders = useMemo(() => currentTemplate?.customDataOrder ?? [], [currentTemplate]);

  /**
   * Fetch the template details by templateId
   * @returns {void}
   * @dependencies [templateId]
   */
  const getTemplateDetail = useCallback(
    async (id: string) => {
      return await fetchTemplateDetail(id);
    },
    [fetchTemplateDetail]
  );

  /**
   * Handle form submission event
   * @param {Partial<KBTemplate>} formValues Form data
   * @returns {void}
   */
  const onSubmitted = useCallback(
    async (formValues: Partial<KBTemplate>) => {
      if (!templateId) return;

      const updatedResponse = await updateTemplateDetail(templateId, {
        ...(currentTemplate as KBTemplate),
        ...formValues,
      });

      updatedResponse && notifyResponse(updatedResponse);
      close();
    },
    [updateTemplateDetail, close, templateId, currentTemplate]
  );

  /**
   * Update the field orders of the template
   * @param {string[]} newOrders newOrders
   * @returns {void}
   */
  const updateFieldOrders = useCallback(
    async (newOrderedFields: KBTemplateField[]) => {
      if (!templateId || !currentTemplate || !permTemplate.canUpdate) return;

      await updateTemplateDetail(templateId, {
        ...currentTemplate,
        customDataOrder: newOrderedFields.map((field) => field.id),
      });
    },
    [updateTemplateDetail, templateId, currentTemplate]
  );

  /**
   * Save the static fields of the template
   * @param {KBTemplateStaticFields} staticFieldValues staticFieldValues
   * @returns {void}
   */
  const saveStaticFields = useCallback(
    async (staticFieldValues: KBTemplateStaticFields) => {
      if (!templateId || isEmpty(currentTemplate)) return;
      const updatedResponse = await updateTemplateDetail(templateId, {
        ...currentTemplate,
        article: {
          ...currentTemplate.article,
          ...staticFieldValues,
        },
      });

      updatedResponse && notifyResponse(updatedResponse);
    },
    [updateTemplateDetail, templateId, currentTemplate, notifyResponse]
  );

  // Fetch the template details when the component is mounted
  useEffect(() => {
    if (templateId) getTemplateDetail(templateId);
  }, [templateId]);

  return (
    <>
      <BackToLink to='/kb/settings/templates' text={t('templates.goBack')} />
      {currentTemplate && (
        <>
          <KBBasicInfo
            className={classes.infoSection}
            name={currentTemplate.templateTitle}
            description={currentTemplate.description ?? ''}
            onEdit={permTemplate.canUpdate ? open : undefined}
          />
          <Modal opened={opened} onClose={close} title={t('templates.templateModalTitle')}>
            <TemplateInfoForm
              template={currentTemplate}
              onSubmitted={onSubmitted}
              onCancel={close}
            />
          </Modal>
          <TemplateStaticFields
            disabled={!permTemplate.canUpdate}
            template={currentTemplate}
            onSave={saveStaticFields}
          />
        </>
      )}

      {templateId && (
        <TemplateCustomFields
          disabled={!permTemplate.canUpdate}
          templateId={templateId}
          templateOrders={templateOrders}
          onSorted={updateFieldOrders}
        />
      )}
    </>
  );
};

export default TemplateDetail;
