import { ArticleTextInput, KeyPhrasesInput } from '@/components';
import { LIST_DISALLOWED_CHARACTERS } from '@/constants/template';
import { KBTemplateDataTypeEnum, type KBTemplateField } from '@/types/template';
import { checkListValue } from '@/utils/template';
import { Box, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useMemo, useState } from 'react';
import TemplateFieldControl from '../TemplateFieldControl';

export const useTemplateFieldStyles = createStyles((theme) => ({
  default: {
    borderLeft: `${rem(3)} solid ${theme.colors.decaNavy[4]}`,
    backgroundColor: theme.colors.decaMono[1],
    padding: rem(16),
  },
  gray: {
    borderLeft: `${rem(3)} solid ${theme.colors.decaNavy[4]}`,
    backgroundColor: theme.colors.decaLight[0],
    padding: rem(16),
  },
  transparent: {
    backgroundColor: 'transparent',
    borderTop: `${rem(1)} solid ${theme.colors.decaLight[2]}`,
    marginTop: rem(16),
    padding: `${rem(24)} ${rem(16)} 0`,
  },
  title: {
    fontWeight: 700,
    fontSize: rem(20),
    lineHeight: rem(21),
    color: theme.colors.decaNavy[5],
    marginBottom: rem(12),
  },
  description: {
    color: theme.colors.decaGrey[3],
    lineHeight: rem(20),
  },
  inputField: {
    '& input': {
      border: 'none',
      backgroundColor: 'transparent',
      fontWeight: 500,
      fontSize: rem(16),
    },
    '& [data-disabled]': {
      backgroundColor: 'transparent !important',
    },
  },
}));

interface TemplateFieldProps {
  data: KBTemplateField;
  className?: string;
  variant?: 'default' | 'transparent' | 'gray';
  disabled?: boolean;
  onClick?: () => void;
  onChange?: (value: any) => void;
}

const TemplateField: React.FC<TemplateFieldProps> = ({
  data,
  variant = 'default',
  className,
  disabled = false,
  onClick,
  onChange,
}) => {
  const { classes, cx } = useTemplateFieldStyles();
  const { t } = useTranslate('settings');
  const { id, title, description, dataType, value } = data;
  const [currentValue, setCurrentValue] = useState<any>(value);

  /**
   * handleClickOnField is a function that handles the click event on the field
   * @type {() => void}
   * @returns {void}
   */
  const handleClickOnField = useCallback(() => {
    onClick?.();
  }, [onClick]);

  /**
   * handleSaveField is a function that handles the save event on the field
   * @type {(fieldValue?: any) => void}
   * @returns {void}
   */
  const handleSaveField = useCallback(
    (fieldValue?: any) => {
      onChange?.(fieldValue || currentValue);
    },
    [currentValue, onChange]
  );

  /**
   * handleCancelField is a function that handles the cancel event on the field
   * @type {() => void}
   * @returns {void}
   */
  const handleCancelField = useCallback(() => {
    setCurrentValue(value);
  }, [value, setCurrentValue]);

  /**
   * FieldInput is a component that renders the input field based on the dataType of the field
   * @type {React.ReactNode}
   */
  const FieldInput = useMemo(() => {
    switch (dataType) {
      case KBTemplateDataTypeEnum.text:
        return (
          <TemplateFieldControl onSave={handleSaveField} onCancel={handleCancelField}>
            <ArticleTextInput
              id={id}
              value={currentValue}
              className={classes.inputField}
              placeholder={description || t('templateCustomData.textPlaceholder')}
              disabled={disabled}
              onChange={(newValue: string) => {
                setCurrentValue(newValue);
              }}
            />
          </TemplateFieldControl>
        );

      case KBTemplateDataTypeEnum.list:
        return (
          <KeyPhrasesInput
            id={id}
            keywords={checkListValue(currentValue)}
            disallowedCharacters={LIST_DISALLOWED_CHARACTERS}
            placeholder={description || t('templateCustomData.listPlaceholder')}
            disabled={disabled}
            onChange={(newList: string[]) => {
              setCurrentValue(newList);
              handleSaveField(newList);
            }}
          />
        );
    }
  }, [id, description, dataType, currentValue, setCurrentValue, handleSaveField]);

  return (
    <Box className={cx(classes[variant], className)} onClick={handleClickOnField}>
      <Title className={classes.title} order={3}>
        {title}
      </Title>
      {FieldInput}
    </Box>
  );
};

export default TemplateField;
