import { Box, Transition, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useFocusWithin } from '@mantine/hooks';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useState } from 'react';

const useStyles = createStyles((theme) => ({
  wrapper: {
    paddingBottom: rem(36),
    position: 'relative',
  },
  formInput: {
    borderRadius: rem(8),
    paddingLeft: rem(8),
  },
  focusing: {
    outline: `${rem(1)} solid ${theme.colors.decaBlue[5]}`,
  },
  actions: {
    position: 'absolute',
    bottom: rem(-4),
  },
  actionButton: {
    marginTop: rem(12),
    minWidth: rem(90),
    '& .mantine-Button-label': {
      fontSize: theme.fontSizes.sm,
    },
  },
  cancelButton: {
    marginRight: rem(12),
  },
}));

interface TemplateFieldControlProps {
  children: React.ReactNode;
  onSave: () => void;
  onCancel?: () => void;
}

const TemplateFieldControl: React.FC<TemplateFieldControlProps> = ({
  children,
  onSave,
  onCancel,
}) => {
  const { t } = useTranslate('kb');
  const { cx, classes } = useStyles();
  const { ref: fieldControlRef, focused } = useFocusWithin();
  const [version, setVersion] = useState(0);

  return (
    <Box key={version} ref={fieldControlRef} className={classes.wrapper}>
      <Box className={cx(classes.formInput, focused && classes.focusing)}>{children}</Box>
      <Transition mounted={focused} transition='fade' duration={200} timingFunction='linear'>
        {(styles) => (
          <Box className={classes.actions} style={styles}>
            <DecaButton
              className={cx(classes.actionButton, classes.cancelButton)}
              size='sm'
              radius={'xl'}
              variant='neutral'
              onClick={() => {
                onCancel?.();

                // Handle reset to initial state of children input
                setVersion((prev) => prev + 1);
              }}
            >
              {t('cancel')}
            </DecaButton>
            <DecaButton
              className={classes.actionButton}
              size='sm'
              radius={'xl'}
              variant='primary'
              onClick={() => {
                onSave();
              }}
            >
              {t('save')}
            </DecaButton>
          </Box>
        )}
      </Transition>
    </Box>
  );
};

export default TemplateFieldControl;
