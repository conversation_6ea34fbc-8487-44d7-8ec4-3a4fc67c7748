import { ArticleTextInput, KeyPhrasesInput } from '@/components';
import { DEFAULT_STATIC_FIELDS } from '@/constants/template';
import type { KBTemplate, KBTemplateStaticFields } from '@/types/template';
import { Box, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { BlockNoteEditor } from '@resola-ai/blocknote-editor';
import { useTolgee, useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useState } from 'react';
import { useTemplateFieldStyles } from '../TemplateField';
import TemplateFieldControl from '../TemplateFieldControl';

const useStyles = createStyles((theme) => ({
  section: {
    backgroundColor: theme.colors.decaLight[0],
    padding: rem(24),
    display: 'flex',
    justifyContent: 'flex-start',
    gap: rem(16),
    flexDirection: 'column',
  },
  editorField: {
    marginLeft: rem(-16),
    zIndex: 100,
    position: 'relative',
    '&.bn-container': {
      minHeight: rem(120),
    },
  },
}));

interface TemplateStaticFieldsProps {
  template: KBTemplate;
  disabled?: boolean;
  onSave?: (data: KBTemplateStaticFields) => void;
}

const TemplateStaticFields: React.FC<TemplateStaticFieldsProps> = ({
  template,
  disabled = false,
  onSave,
}) => {
  const { t } = useTranslate(['settings', 'article']);
  const tolgee = useTolgee();
  const { classes } = useStyles();
  const { classes: fieldClasses } = useTemplateFieldStyles();
  const [fieldValues, setFieldValues] = useState<KBTemplateStaticFields>({
    title: template.article?.title || '',
    content: template.article?.content,
    contentRaw: template.article?.contentRaw,
    keywords: template.article?.keywords || [],
  });

  /**
   * Saves the changes of the field values
   * @param fieldData - the new field values
   * @returns void
   */
  const saveFieldChanges = useCallback(
    (staticFieldValues?: KBTemplateStaticFields) => {
      onSave?.(staticFieldValues || fieldValues);
    },
    [fieldValues, onSave]
  );

  /**
   * Handles the change of the title field
   * @param value - the new value of the title field
   * @returns void
   */
  const onTitleChanged = useCallback(
    (value: string) => {
      setFieldValues((prev) => ({ ...prev, title: value }));
    },
    [setFieldValues]
  );

  /**
   * Cancels the changes of the title field
   * @returns void
   */
  const cancelTitleChange = useCallback(() => {
    setFieldValues((prev) => ({ ...prev, title: template.article.title || '' }));
  }, [setFieldValues, template.article.title]);

  /**
   * Handles the change of the content field
   * @param value - the new value of the content field
   * @returns void
   */
  const onContentChanged = useCallback(
    (contentRaw: string, contentText: string) => {
      setFieldValues((prev) => ({ ...prev, content: contentText, contentRaw }));
    },
    [setFieldValues]
  );

  /**
   * Cancels the changes of the content field
   * @returns void
   */
  const cancelContentChange = useCallback(() => {
    setFieldValues((prev) => ({
      ...prev,
      content: template.article.content,
      contentRaw: template.article.contentRaw,
    }));
  }, [setFieldValues, template.article.content, template.article.contentRaw]);

  /**
   * Handles the change of the keywords field
   * @param value - the new value of the keywords field
   * @returns void
   */
  const onKeywordsChanged = useCallback(
    (value: string[]) => {
      const newValues = { ...fieldValues, keywords: value };
      saveFieldChanges(newValues);
    },
    [setFieldValues]
  );

  return (
    <Box className={classes.section}>
      <Box className={fieldClasses.default}>
        <Title className={fieldClasses.title} order={3}>
          {t(DEFAULT_STATIC_FIELDS.articleTitle.labelKey, {
            ns: DEFAULT_STATIC_FIELDS.articleTitle.namespace,
          })}
        </Title>
        {template && (
          <TemplateFieldControl onSave={saveFieldChanges} onCancel={cancelTitleChange}>
            <ArticleTextInput
              id={DEFAULT_STATIC_FIELDS.articleTitle.id}
              disabled={disabled}
              value={fieldValues.title}
              className={fieldClasses.inputField}
              placeholder={t('templateCustomData.textPlaceholder')}
              onChange={onTitleChanged}
            />
          </TemplateFieldControl>
        )}
      </Box>

      <Box className={fieldClasses.default}>
        <Title className={fieldClasses.title} order={3}>
          {t(DEFAULT_STATIC_FIELDS.content.labelKey, {
            ns: DEFAULT_STATIC_FIELDS.content.namespace,
          })}
        </Title>
        <TemplateFieldControl onSave={saveFieldChanges} onCancel={cancelContentChange}>
          <BlockNoteEditor
            isEditable={!disabled}
            className={classes.editorField}
            initialHTML={template.article?.contentRaw || ''}
            language={tolgee.getLanguage()}
            isBordered={false}
            autoFocus={false}
            usingCustomFilePanel={false}
            onChange={onContentChanged}
            onBlur={() => {}}
          />
        </TemplateFieldControl>
      </Box>

      <Box className={fieldClasses.transparent}>
        <Title className={fieldClasses.title} order={3}>
          {t(DEFAULT_STATIC_FIELDS.keyphrases.labelKey, {
            ns: DEFAULT_STATIC_FIELDS.keyphrases.namespace,
          })}
        </Title>
        <KeyPhrasesInput
          id={DEFAULT_STATIC_FIELDS.keyphrases.id}
          keywords={fieldValues.keywords || []}
          placeholder={t('templateCustomData.listPlaceholder')}
          disabled={disabled}
          onChange={onKeywordsChanged}
        />
      </Box>
    </Box>
  );
};

export default TemplateStaticFields;
