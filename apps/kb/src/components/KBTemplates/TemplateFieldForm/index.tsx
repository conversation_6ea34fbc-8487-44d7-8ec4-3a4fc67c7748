import { Box, Flex, Group, Input, LoadingOverlay, Radio, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useMemo } from 'react';
import { Controller } from 'react-hook-form';
import { TextInput } from 'react-hook-form-mantine';

import { useTemplateFieldForm } from '@/hooks/useTemplateFieldForm';
import {
  KBTemplateDataTypeEnum,
  type KBTemplateField,
  type KBTemplateFieldCreate,
} from '@/types/template';
import { DecaButton } from '@resola-ai/ui';

interface TemplateFieldFormProps {
  templateField?: KBTemplateField;
  submitting?: boolean;
  onSubmitted: (formValues: KBTemplateFieldCreate) => void;
  onCancel: () => void;
}

const useStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    flexDirection: 'column',
    gap: theme.spacing.md,
    '.mantine-InputWrapper-root': {
      width: '100%',
      display: 'flex',
      justifyContent: 'flex-start',
      alignItems: 'flex-start',

      '&.mantine-TextInput-root, &.mantine-Textarea-root': {
        flexDirection: 'column',
      },
    },
    '.mantine-InputWrapper-label': {
      width: '30%',
      marginRight: theme.spacing.xs,
      color: theme.colors.decaNavy[5],
      fontSize: theme.fontSizes.md,
    },
    '.mantine-InputWrapper-error': {
      width: '70%',
      display: 'flex',
    },
  },
  inputInner: {
    width: '70%',
    '.mantine-Input-wrapper': {
      width: '100%',
    },
  },
  buttonGroup: {
    width: '100%',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingTop: theme.spacing.md,
  },
  typeGroup: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  typeLabel: {
    fontWeight: 500,
    fontSize: theme.fontSizes.sm,
    color: theme.colors.decaNavy[5],
  },
  dataTypeRadio: {
    marginTop: rem(2),
  },
}));

/**
 * TemplateFieldForm component displays the form to edit template information
 * @param {TemplateFieldFormProps} props
 * @returns {React.FC<TemplateFieldFormProps>}
 */
const TemplateFieldForm: React.FC<TemplateFieldFormProps> = ({
  templateField,
  submitting = false,
  onCancel,
  onSubmitted,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate(['settings', 'common']);

  const { control, handleSubmit, getValues, errors, watch } = useTemplateFieldForm({
    currentTemplateField: templateField,
    onSubmitted: () => {
      onSubmitted?.(getValues());
    },
  });

  /**
   * Placeholder label based on data type
   * @returns {string}
   */
  const placeholderLabel = useMemo(() => {
    const dataType = watch('dataType');
    return dataType === KBTemplateDataTypeEnum.list
      ? t('templateCustomData.dataType.addListLabel')
      : t('templateCustomData.dataType.placeholderLabel');
  }, [watch('dataType')]);

  /**
   * Handle key press event.
   * @param event Keyboard event
   * @returns void
   */
  const handleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      if (event.key === 'Enter') {
        event.preventDefault();
        handleSubmit();
      }
    },
    [handleSubmit]
  );

  return (
    <form className={classes.root}>
      <LoadingOverlay visible={submitting} />
      <Input.Wrapper label={t('form.name', { ns: 'common' })} withAsterisk>
        <Box className={classes.inputInner}>
          <TextInput
            control={control}
            name='title'
            placeholder={t('form.namePlaceholder', { ns: 'common' })}
            error={errors.title?.message}
          />
        </Box>
      </Input.Wrapper>

      <Input.Wrapper label={placeholderLabel}>
        <Box className={classes.inputInner}>
          <TextInput
            control={control}
            name='description'
            placeholder={t('templateCustomData.dataType.enterFieldText', {
              label: placeholderLabel,
            })}
            error={errors.description?.message}
            onKeyDown={handleKeyDown}
          />
        </Box>
      </Input.Wrapper>

      <Controller
        name='dataType'
        key='dataType'
        control={control}
        render={({ field }) => (
          <Radio.Group
            label={
              <Flex direction={'column'} gap={rem(13)}>
                <Text fz='md'>{t('templateCustomData.dataType.label')}</Text>
              </Flex>
            }
            key={field.name}
            error={errors.dataType?.message}
            {...field}
          >
            <Group className={classes.typeGroup}>
              <Text color='decaGrey.5'>{t('templateCustomData.dataType.description')}</Text>
              {[KBTemplateDataTypeEnum.text, KBTemplateDataTypeEnum.list].map(
                (dataType: KBTemplateDataTypeEnum) => (
                  <Radio
                    classNames={{ inner: classes.dataTypeRadio }}
                    key={dataType}
                    value={dataType}
                    label={
                      <Box>
                        <Flex gap={rem(9)} align={'center'}>
                          <Text fz='md' className={classes.typeLabel}>
                            {t(`templateCustomData.dataType.options.${dataType}`)}
                          </Text>
                        </Flex>
                      </Box>
                    }
                  />
                )
              )}
            </Group>
          </Radio.Group>
        )}
      />

      <Group className={classes.buttonGroup}>
        <DecaButton radius={'xl'} variant='neutral' onClick={() => onCancel()}>
          {t('actions.cancel', { ns: 'common' })}
        </DecaButton>

        <DecaButton radius={'xl'} variant='primary' disabled={submitting} onClick={handleSubmit}>
          {t('actions.save', { ns: 'common' })}
        </DecaButton>
      </Group>
    </form>
  );
};

export default TemplateFieldForm;
