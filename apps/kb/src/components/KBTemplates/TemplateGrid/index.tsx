import { useTemplatesContext } from '@/contexts/TemplatesContext';
import type { KBTemplate } from '@/types/template';
import { Grid, LoadingOverlay } from '@mantine/core';
import type React from 'react';
import { useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import TemplateCard from '../TemplateCard';

/**
 * TemplateGrid component displays the list of templates
 * @returns {React.FC}
 * @param {void}
 */
const TemplateGrid: React.FC = () => {
  const navigate = useNavigate();
  const {
    templatesState: { loading, templates },
    fetchTemplates,
  } = useTemplatesContext();

  /**
   * <PERSON>le opening the template detail for editing
   * @param {string} id
   * @returns {void}
   */
  const goToTemplateDetail = useCallback(
    (id: string) => {
      navigate(`/kb/settings/templates/${id}`);
    },
    [navigate]
  );

  /**
   * Fetch the template data
   * @returns {void}
   */
  const fetchTemplateData = useCallback(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  /**
   * Fetch the template data on component mount
   * @returns {void}
   */
  useEffect(() => {
    fetchTemplateData();
  }, []);

  return (
    <Grid gutter='lg'>
      <LoadingOverlay visible={loading} />
      {templates.map((template: KBTemplate) => (
        <Grid.Col span={6} key={template.id}>
          <TemplateCard
            title={template.templateTitle}
            description={template.description}
            onView={() => goToTemplateDetail(template.id)}
            onEdit={() => goToTemplateDetail(template.id)}
          />
        </Grid.Col>
      ))}
    </Grid>
  );
};

export default TemplateGrid;
