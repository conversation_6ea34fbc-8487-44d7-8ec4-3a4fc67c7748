import { useSortable } from '@dnd-kit/sortable';
import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type React from 'react';
import { type CSSProperties, useMemo } from 'react';

import type { KBTemplateField } from '@/types/template';
import Template<PERSON>ield from '../TemplateField';
import TemplateFieldActions from '../TemplateFieldActions';

const useStyles = createStyles(() => ({
  fieldContent: {
    width: '100%',
    display: 'flex',
    gap: rem(16),
    flexDirection: 'row',
    alignItems: 'center',
  },
  fieldItem: {
    width: '100%',
  },
}));

/**
 * SortableField component is a draggable field
 * @param {KBTemplateField} field
 * @returns {React.FC<{ field: KBTemplateField }>}
 */
interface SortableFieldProps {
  field: KBTemplateField;
  disabled?: boolean;
  onEdit: () => void;
  onDelete: () => void;
  onFieldValueChange: (value: string) => void;
}
const SortableField: React.FC<SortableFieldProps> = ({
  field,
  disabled = false,
  onEdit,
  onDelete,
  onFieldValueChange,
}) => {
  const { classes } = useStyles();
  const { setNodeRef, isDragging, transition, attributes, listeners } = useSortable({
    id: field.id,
  });

  const sortStyles: CSSProperties = useMemo(
    () => ({
      opacity: isDragging ? 0.4 : undefined,
      transition,
    }),
    [isDragging, transition]
  );

  return (
    <Box key={field.id} ref={setNodeRef} style={sortStyles} {...attributes} {...listeners}>
      <Box className={classes.fieldContent}>
        <TemplateField
          disabled={disabled}
          className={classes.fieldItem}
          data={field}
          variant='gray'
          onChange={onFieldValueChange}
        />
        {!disabled && <TemplateFieldActions onEdit={onEdit} onDelete={onDelete} />}
      </Box>
    </Box>
  );
};

export default SortableField;
