import {
  type Active,
  DndContext,
  type Over,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { SortableContext, arrayMove } from '@dnd-kit/sortable';
import { Box, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import delay from 'lodash/delay';
import isEmpty from 'lodash/isEmpty';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';

import { Modal } from '@/components';
import { DEFAULT_FIELD_DATA_VALUES } from '@/constants/template';
import { useAppContext, useTemplateDetailContext } from '@/contexts';
import { useNotifications } from '@/hooks';
import {
  KBTemplateDataTypeEnum,
  type KBTemplateField,
  type KBTemplateFieldCreate,
  type KBTemplateFieldsSet,
} from '@/types/template';
import { orderCustomFieldsByTemplateOrders } from '@/utils/template';
import { DecaButton } from '@resola-ai/ui';
import TemplateFieldForm from '../TemplateFieldForm';
import SortableField from './SortableField';

const useStyles = createStyles((theme) => ({
  topSection: {
    padding: `${theme.spacing.md} 0`,
  },
  customFieldList: {
    padding: 0,
    display: 'flex',
    justifyContent: 'flex-start',
    gap: rem(24),
    flexDirection: 'column',
  },
  createButton: {
    '&.mantine-Button-root': {
      backgroundColor: theme.colors.decaMono[1],
      borderColor: theme.colors.decaGrey[6],
    },
    '& .mantine-Button-label': {
      color: theme.colors.decaGrey[6],
    },
    '& svg': {
      width: rem(16),
      height: rem(16),
    },
  },
}));

interface TemplateCustomFieldsProps {
  templateId: string;
  templateOrders?: string[];
  disabled?: boolean;
  onSorted?: (sortedFields: KBTemplateField[]) => void;
}

/**
 * TemplateCustomFields component displays the custom fields of a template
 * @param {TemplateCustomFieldsProps} props
 * @returns {React.FC<TemplateCustomFieldsProps>}
 */
const TemplateCustomFields: React.FC<TemplateCustomFieldsProps> = ({
  templateId,
  templateOrders,
  disabled = false,
  onSorted,
}) => {
  const { t } = useTranslate(['settings', 'common']);
  const { notifyResponse } = useNotifications(t);
  const { classes } = useStyles();
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const {
    templateDetailState: { customFields, fieldsLoading, fieldsCreating, fieldsUpdating },
    fetchTemplateCustomFields,
    createCustomField,
    updateCustomField,
    deleteCustomField,
    setCustomFieldValue,
  } = useTemplateDetailContext();

  const [opened, { open, close }] = useDisclosure(false);
  const [currentOrders, setCurrentOrders] = useState<string[] | undefined>(undefined);
  const [sortableFields, setSortableFields] = useState<KBTemplateField[]>([]);
  const [selectedField, setSelectedField] = useState<KBTemplateField | undefined>(undefined);
  const [submitting, setSubmitting] = useState(false);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  /**
   * closeModal function is called when the modal is closed
   * It resets the selected field and closes the modal
   * @return void
   */
  const closeModal = useCallback(() => {
    setSelectedField(undefined);
    close();
  }, [setSelectedField, close]);

  /**
   * onSubmitted function is called when a new field is created
   * It closes the modal and fetches the custom fields again
   * @param formValues
   * @return void
   */
  const onSubmitted = useCallback(
    async (formValues: KBTemplateFieldCreate) => {
      // Prevent multiple submissions
      if (fieldsCreating || fieldsUpdating) return;
      setSubmitting(true);

      /**
       * If a field is selected, it means the form is for editing
       * So, we update the field instead of creating a new one
       */
      if (selectedField) {
        const updatedResponse = await updateCustomField(templateId, selectedField.id, {
          ...selectedField,
          ...formValues,
          value:
            selectedField.dataType === formValues.dataType
              ? selectedField.value
              : DEFAULT_FIELD_DATA_VALUES[formValues.dataType as KBTemplateDataTypeEnum],
        });
        if (updatedResponse?.status === 'success') {
          closeModal();
          await fetchTemplateCustomFields(templateId);
        }

        updatedResponse && notifyResponse(updatedResponse);
      } else {
        /**
         * If no field is selected, it means the form is for creating a new field
         * So, we create a new field
         */
        const createdResponse = await createCustomField(templateId, {
          ...formValues,
          value: DEFAULT_FIELD_DATA_VALUES[formValues.dataType || KBTemplateDataTypeEnum.text],
        });

        if (createdResponse?.status === 'success') {
          closeModal();
          await fetchTemplateCustomFields(templateId);
        }
      }

      // To prevent multiple submissions, we delay the submission
      delay(() => {
        setSubmitting(false);
      }, 100);
    },
    [
      templateId,
      selectedField,
      createCustomField,
      updateCustomField,
      fetchTemplateCustomFields,
      closeModal,
      notifyResponse,
      fieldsCreating,
      fieldsUpdating,
    ]
  );

  /**
   * handleSaveValue function is called when a field value is saved
   * It saves the value of the field
   * @param fieldId
   * @return void
   */
  const handleSaveValue = useCallback(
    async (updatedCustomField: KBTemplateField) => {
      const updatedResponse = await updateCustomField(
        templateId,
        updatedCustomField.id,
        updatedCustomField
      );

      updatedResponse && notifyResponse(updatedResponse);
    },
    [templateId, updateCustomField, notifyResponse]
  );

  /**
   * handleFieldValueChange function is called when a field value is changed
   * It updates the value of the field in the state
   * @param fieldId
   * @param value
   * @return void
   */
  const handleFieldValueChange = useCallback(
    (fieldId: string, value: string) => {
      const updatedCustomField = setCustomFieldValue(fieldId, value);
      handleSaveValue(updatedCustomField as KBTemplateField);
    },
    [setCustomFieldValue, handleSaveValue]
  );

  /**
   * openEditModal function is called when a field is edited
   * It opens the modal with the field data
   * @param field
   * @return void
   */
  const openEditModal = useCallback(
    (field: KBTemplateField) => {
      setSelectedField(field);
      open();
    },
    [setSelectedField, open]
  );

  /**
   * handleDragEnd function is called when a field is dragged and dropped
   * It updates the order of the fields in the state `sortableFields`
   * @param active
   * @param over
   * @return void
   */
  const handleDragEnd = useCallback(
    (active: Active, over: Over) => {
      setSortableFields((prev) => {
        const activeIndex = prev.findIndex(({ id }) => id === active.id);
        const overIndex = prev.findIndex(({ id }) => id === over.id);
        const newSortedFields = arrayMove(prev, activeIndex, overIndex);

        onSorted?.(newSortedFields);

        return newSortedFields;
      });
    },
    [setSortableFields, onSorted]
  );

  /**
   * updateSortableFields function is called when the fields are sorted
   * It updates the order of the fields in the state and calls the `onSorted` function
   * @param newSortedFields
   * @return void
   */
  const updateSortableFields = useCallback(
    (newSortedFields: KBTemplateField[]) => {
      setSortableFields(newSortedFields);

      onSorted?.(newSortedFields);
    },
    [setSortableFields, onSorted]
  );

  /**
   * handleDeleteCustomField function is called when a field is deleted
   * It opens a confirm modal and deletes the field if confirmed
   * @param fieldId
   * @return void
   */
  const handleDeleteCustomField = useCallback(
    (fieldId: string) => {
      openConfirmModal({
        onConfirm: async () => {
          await deleteCustomField(templateId, fieldId);
          await fetchTemplateCustomFields(templateId);

          closeConfirmModal();
        },
        title: t('templateCustomData.deleteConfirmTitle'),
      });
    },
    [templateId]
  );

  /**
   * initSortableFields function is called when the fields are initialized
   * It initializes the order of the fields in the state
   * @param templateOrders
   * @param templateCustomFields
   * @return void
   */
  const initSortableFields = useCallback(
    (templateOrders: string[] | undefined, templateCustomFields: KBTemplateFieldsSet) => {
      // If the template orders and custom fields are available, sort the fields based on the orders
      const customFieldsArr = Object.values(templateCustomFields);
      const sortedFields = orderCustomFieldsByTemplateOrders(customFieldsArr, templateOrders || []);

      updateSortableFields(sortedFields);
    },
    [updateSortableFields]
  );

  // Fetch custom fields when the templateId is available
  useEffect(() => {
    fetchTemplateCustomFields(templateId);
  }, [templateId]);

  // Update the sortable fields when the custom fields are changed
  useEffect(() => {
    if (currentOrders && !fieldsLoading && !isEmpty(customFields)) {
      initSortableFields(currentOrders, customFields);
    }
  }, [customFields, currentOrders]);

  // Update the sortable fields when the template orders are changed
  useEffect(() => {
    if (JSON.stringify(templateOrders) !== JSON.stringify(currentOrders)) {
      setCurrentOrders(templateOrders);
    }
  }, [templateOrders]);

  return (
    <>
      <Box className={classes.topSection}>
        {!disabled && (
          <>
            <DecaButton
              className={classes.createButton}
              leftSection={<IconPlus size={16} />}
              size='sm'
              radius={'xl'}
              variant='neutral'
              onClick={open}
            >
              {t('templateCustomData.addTitle')}
            </DecaButton>
            <Modal
              opened={opened}
              onClose={closeModal}
              title={
                selectedField ? t('templateCustomData.editTitle') : t('templateCustomData.addTitle')
              }
            >
              <TemplateFieldForm
                submitting={submitting}
                templateField={selectedField}
                onSubmitted={onSubmitted}
                onCancel={closeModal}
              />
            </Modal>
          </>
        )}
      </Box>
      <Box>
        <DndContext
          sensors={sensors}
          onDragEnd={({ active, over }) => {
            if (disabled) return;

            if (over && active.id !== over?.id) {
              handleDragEnd(active, over);
            }
          }}
        >
          <Box className={classes.customFieldList}>
            {!isEmpty(sortableFields) ? (
              <SortableContext items={sortableFields} disabled={disabled}>
                {sortableFields.map((field: KBTemplateField) => (
                  <SortableField
                    key={field.id}
                    disabled={disabled}
                    field={field}
                    onEdit={() => {
                      openEditModal(field);
                    }}
                    onFieldValueChange={(value) => handleFieldValueChange(field.id, value)}
                    onDelete={() => handleDeleteCustomField(field.id)}
                  />
                ))}
              </SortableContext>
            ) : (
              <Text c='dimmed' ta='center'>
                {t('templateCustomData.emptyMessage')}
              </Text>
            )}
          </Box>
        </DndContext>
      </Box>
    </>
  );
};

export default TemplateCustomFields;
