import App from '@/App';
import { EXPLORER_ZINDEX, NAVBAR_WIDTH } from '@/constants/ui';
import { useUploaderContext } from '@/contexts';
import { useExplorer } from '@/hooks';
import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { usePathName } from '@resola-ai/ui/hooks';
import every from 'lodash/every';
import { useMemo } from 'react';
import BaseLayout from '../BaseLayout';
import NavbarContainer from '../Navbar';
import Navigations from '../Navbar/Navigations';
import TreeView from '../TreeView';
import { UploadStatusModal } from '../common';

const HIDE_NAVBAR_PATHS = ['/kb/unauthorized', '/kb/qna'];
const HIDE_TREEVIEW_PATHS = ['/kb/unauthorized', '/kb/qna', '/kb/settings/templates', '/kb/jobs'];

const useStyles = createStyles(() => ({
  treeBox: {
    position: 'fixed',
    top: rem(83),
    left: rem(96),
    zIndex: EXPLORER_ZINDEX,
  },
  treeBoxExpanded: {
    position: 'inherit',
    marginLeft: rem(NAVBAR_WIDTH),
  },
}));

const KBAppWrapper = () => {
  const pathName = usePathName();
  const showNavbar = useMemo(() => !HIDE_NAVBAR_PATHS.includes(pathName), [pathName]);
  const showTreeView = useMemo(
    () => every(HIDE_TREEVIEW_PATHS, (path) => !pathName.includes(path)),
    [pathName]
  );

  const { classes, cx } = useStyles();
  const { expanded, setExpanded, isSearching, setTriggerRefetch } = useExplorer();
  const { uploaders, openedUploadStatus, closeUploadStatus } = useUploaderContext();
  return (
    <BaseLayout navigationMobile={<Navigations />}>
      {showNavbar && <NavbarContainer />}
      {showTreeView && !isSearching ? (
        <Box className={cx(classes.treeBox, expanded && classes.treeBoxExpanded)}>
          <TreeView
            isExpanded={expanded}
            onExpand={() => setExpanded(true)}
            onClose={() => setExpanded(false)}
            onRefetch={() => setTriggerRefetch(true)}
          />
        </Box>
      ) : null}
      {uploaders && (
        <UploadStatusModal
          opened={openedUploadStatus}
          uploaders={uploaders}
          onClose={closeUploadStatus}
        />
      )}
      <App />
    </BaseLayout>
  );
};

export default KBAppWrapper;
