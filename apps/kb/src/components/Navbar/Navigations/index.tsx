import { ActionIcon, Group, Stack, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { usePathParams } from '@resola-ai/ui/hooks';
import { IconBook, IconSettings } from '@tabler/icons-react';
import cloneDeep from 'lodash/cloneDeep';
import { useEffect, useMemo, useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';

import { IconAIGenerator } from '@/components/Icons';
import { AppConfig } from '@/configs';

const BASE_PATH = AppConfig.BASE_PATH;

const useStyles = createStyles((theme) => ({
  activeIcon: {
    borderRadius: rem(33),
    backgroundColor: theme.colors.decaNavy[5],
    color: theme.white,
    '& .ai-generator-icon *': {
      fill: theme.white,
    },
    '&.mantine-ActionIcon-root:hover': {
      backgroundColor: theme.colors.decaNavy[5],
      color: theme.white,
      '& .ai-generator-icon *': {
        fill: theme.white,
      },
      '& svg': {
        color: theme.white,
      },
    },
  },
  icon: {
    borderRadius: rem(33),
    color: theme.colors.decaNavy[5],
    backgroundColor: 'transparent',
    '&.mantine-ActionIcon-root:hover': {
      backgroundColor: theme.colors.decaNavy[0],
      color: theme.colors.decaNavy[5],
      '& .ai-generator-icon *': {
        fill: theme.colors.decaNavy[5],
      },
    },
  },
}));

const defaultNavigationItems = [
  {
    id: 1,
    path: `${BASE_PATH}`,
    icon: <IconBook stroke={2} />,
  },
  // {
  //   id: 2,
  //   path: `${BASE_PATH}datasources`,
  //   icon: <IconFileDatabase stroke={2} />,
  // },
  {
    id: 3,
    path: `${BASE_PATH}jobs`,
    icon: <IconAIGenerator />,
  },
  {
    id: 4,
    path: `${BASE_PATH}settings/templates`,
    icon: <IconSettings stroke={2} />,
  },
];

const Navigations = () => {
  const { classes, cx } = useStyles();
  const pathParams = usePathParams();
  const location = useLocation();
  const pathName = location.pathname;
  const [activePath, setActivePath] = useState<string>(`${BASE_PATH}`);

  const navigationItems = useMemo(() => {
    const activeNav = cloneDeep(defaultNavigationItems)
      .reverse()
      .find((item) => activePath.includes(item.path));

    return defaultNavigationItems.map((item) => {
      return {
        ...item,
        active: item.id === activeNav?.id,
      };
    });
  }, [activePath]);

  const createPathWithLngParam = (path: string) => {
    return pathParams.createPathWithLngParam(path);
  };

  useEffect(() => {
    setActivePath(pathName);
  }, [pathName]);

  return (
    <Stack align='center' gap='xl'>
      {navigationItems.map((item) => (
        <NavLink to={createPathWithLngParam(item.path)} key={item.id}>
          <Group gap={'xs'}>
            <ActionIcon size={40} className={cx(classes.icon, item.active && classes.activeIcon)}>
              {item.icon}
            </ActionIcon>
          </Group>
        </NavLink>
      ))}
    </Stack>
  );
};

export default Navigations;
