import { NAVBAR_WIDTH } from '@/constants/ui';
import { Stack, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import Navigations from './Navigations';

const useStyles = createStyles((theme, _, u) => ({
  navbarContainer: {
    width: rem(NAVBAR_WIDTH),
    borderRight: `${rem(1)} solid ${theme.colors.decaLight[2]}`,
    backgroundColor: theme.colors.decaLight[0],
    height: '100%',
    padding: `${rem(20)} ${rem(8)}`,
    display: 'flex',
    position: 'fixed',
    [u.smallerThan('md')]: {
      display: 'none',
    },
  },
}));

const NavbarContainer: React.FC<any> = () => {
  const { classes } = useStyles();

  return (
    <Stack className={classes.navbarContainer}>
      <Navigations />
    </Stack>
  );
};

export default NavbarContainer;
