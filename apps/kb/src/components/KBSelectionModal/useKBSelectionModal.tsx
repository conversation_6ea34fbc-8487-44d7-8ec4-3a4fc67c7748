import { modals } from '@mantine/modals';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';

import { KBDetailForm } from '@/components/common';
import { ROOT_PATH } from '@/constants/folder';
import { KBSelectionContextProvider } from '@/contexts/KBSelectionContext';
import { useModalManager, useNotifications } from '@/hooks';
import KBSelectionModalContent, { type OnKBSelectHandler } from './KBSelectionModalContent';

interface UseKBSelectionModalProps {
  title?: string;
  description?: string;
}

export const useKBSelectionModal = ({ title = '', description = '' }: UseKBSelectionModalProps) => {
  const { t } = useTranslate('kb');
  const { notifyMessage } = useNotifications(t);
  const { modalClasses, createModal } = useModalManager();

  const buildKBSelectionModal = useCallback(
    (onSelect: OnKBSelectHandler, parentFolderId: string = ROOT_PATH, currentBaseId = '') =>
      createModal({
        title: title,
        classNames: {
          content: modalClasses.largeModal,
        },
        onClose: () => modals.closeAll(),
        children: (
          <KBSelectionContextProvider>
            <KBSelectionModalContent
              description={description}
              parentFolderId={parentFolderId}
              currentBaseId={currentBaseId}
              onSelect={onSelect}
              onClose={() => modals.closeAll()}
              onCreateNewKB={(parentFolderId: string) =>
                modals.open(buildCreateKBModal(onSelect, parentFolderId, currentBaseId))
              }
            />
          </KBSelectionContextProvider>
        ),
      }),
    [modalClasses, t]
  );

  const openSelectionModal = useCallback(
    (onSelect: OnKBSelectHandler, parentFolderId: string, currentBaseId: string) => {
      modals.open(buildKBSelectionModal(onSelect, parentFolderId, currentBaseId));
    },
    [buildKBSelectionModal]
  );

  const buildCreateKBModal = useCallback(
    (onSelect: OnKBSelectHandler, parentFolderId: string, currentBaseId: string) => {
      return createModal({
        title: t('modal.createKnowledgeBase', { ns: 'kb' }),
        classNames: {
          content: modalClasses.largeModal,
        },
        onClose: () => openSelectionModal(onSelect, parentFolderId, currentBaseId),
        children: (
          <KBDetailForm
            onCancel={() => openSelectionModal(onSelect, parentFolderId, currentBaseId)}
            onSubmitted={(response: any) => {
              if (response?.status === 'success') {
                notifyMessage(
                  t('createKB.successTitle', { ns: 'kb' }),
                  t('createKB.successMessage', { ns: 'kb' })
                );
                openSelectionModal(onSelect, parentFolderId, response.data.id);
              }
            }}
            parentDirId={parentFolderId}
          />
        ),
      });
    },
    [modalClasses, t, openSelectionModal]
  );

  return {
    openKBSelectionModal: ({
      parentFolderId = ROOT_PATH,
      currentBaseId = '',
      onSelect,
    }: {
      parentFolderId?: string;
      currentBaseId?: string;
      onSelect: OnKBSelectHandler;
    }) => {
      openSelectionModal(onSelect, parentFolderId, currentBaseId);
    },
    closeKBSelectionModal: () => modals.closeAll(),
  };
};
