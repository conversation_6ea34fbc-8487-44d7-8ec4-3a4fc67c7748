import { Box, LoadingOverlay, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';

import { KBSelector } from '@/components/TreeViewSelector';
import { ROOT_PATH } from '@/constants/folder';
import { useKBSelectionContext } from '@/contexts/KBSelectionContext';
import { KB_TYPE } from '@/types';
import { DecaButton } from '@resola-ai/ui';

export const useKBSelectionModalStyles = createStyles((theme) => ({
  wrapper: {
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    flexDirection: 'column',
    gap: theme.spacing.md,
  },
  content: {
    width: '100%',
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing.lg,
  },
  creatingSection: {
    display: 'flex',
    justifyContent: 'flex-end',
  },
  actions: {
    width: '100%',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingTop: theme.spacing.lg,
    gap: theme.spacing.md,
  },
  actionIcon: {
    width: rem(16),
    height: rem(16),
  },
}));

export type OnKBSelectHandler = (baseId: string) => Promise<void>;

interface KBSelectionModalContentProps {
  parentFolderId: string;
  currentBaseId?: string;
  description?: string;
  onCreateNewKB?: (selectedFolderId: string) => void;
  onSelect: OnKBSelectHandler;
  onClose: () => void;
}

const KBSelectionModalContent: React.FC<KBSelectionModalContentProps> = ({
  parentFolderId,
  currentBaseId,
  description,
  onCreateNewKB,
  onSelect,
  onClose,
}) => {
  const { t } = useTranslate(['kb', 'common']);
  const { classes } = useKBSelectionModalStyles();
  const [loading, setLoading] = useState<boolean>(false);

  const {
    baseSelection: { selectedBase },
    folderSelection: { selectedFolder },
    setSelectedFolder,
    setSelectedBase,
    fetchFolders,
    fetchBases,
    getKnowledgeBase,
  } = useKBSelectionContext();

  const selectHandler = useCallback(async () => {
    setLoading(true);
    if (!selectedBase) return;
    await onSelect(selectedBase);

    setLoading(false);
  }, [selectedBase, setLoading, onSelect]);

  const initBasesWhenLoading = useCallback(async () => {
    await fetchFolders(ROOT_PATH, 1);

    // If the parent folder is the root folder and the current base is provided, fetch the base and set the selected folder and base
    if ((!parentFolderId || parentFolderId === ROOT_PATH) && currentBaseId) {
      const base = await getKnowledgeBase(currentBaseId);

      if (base?.data?.id && base?.data?.parentDirId) {
        await fetchFolders(base.data.parentDirId, 1);
        await fetchBases(base.data.parentDirId);

        setSelectedFolder(base.data.parentDirId);
        setSelectedBase(base.data.id);

        return;
      }
    }

    // If the parent folder is not the root folder and the current base is provided, fetch the folder and base
    if (parentFolderId !== ROOT_PATH && currentBaseId) {
      await fetchFolders(parentFolderId, 1);
      await fetchBases(parentFolderId);

      setSelectedFolder(parentFolderId);
      setSelectedBase(currentBaseId);
    } else {
      fetchBases(ROOT_PATH);
    }
  }, [parentFolderId, currentBaseId, fetchBases, fetchFolders, getKnowledgeBase]);

  // Fetch folders and bases
  useEffect(() => {
    initBasesWhenLoading();
  }, []);

  return (
    <Box className={classes.wrapper}>
      <LoadingOverlay visible={loading} />
      <Box className={classes.content}>
        {description && <Text>{description}</Text>}
        {onCreateNewKB && (
          <Box className={classes.creatingSection}>
            <DecaButton
              radius={'xl'}
              variant='neutral'
              leftSection={<IconPlus className={classes.actionIcon} />}
              onClick={() => onCreateNewKB(selectedFolder)}
            >
              {t('modal.createKnowledgeBase')}
            </DecaButton>
          </Box>
        )}
        <Box>
          <KBSelector supportBaseTypes={[KB_TYPE.article]} />
        </Box>
      </Box>

      <Box className={classes.actions}>
        <DecaButton radius={'xl'} variant='neutral' onClick={onClose}>
          {t('actions.cancel', { ns: 'common' })}
        </DecaButton>

        <DecaButton
          radius={'xl'}
          variant='primary'
          disabled={!selectedBase}
          onClick={selectHandler}
        >
          {t('actions.save', { ns: 'common' })}
        </DecaButton>
      </Box>
    </Box>
  );
};

export default KBSelectionModalContent;
