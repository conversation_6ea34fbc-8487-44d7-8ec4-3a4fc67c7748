{"accessLevel": {"changed": {"content": "公開範囲の変更には時間がかかります。タスクページで進行状況を確認できます。", "title": "お待ちください..."}, "description": "ナレッジベースの公開範囲を設定します", "label": "公開範囲", "private": {"description": "DECA Cloudにログインしているユーザーのみが記事を参照できます", "label": "非公開"}, "processing": "変更中", "public": {"description": "チャットボットやFAQなどDECA Cloud製品を通じ、DECA Cloudにログインしていないユーザーでも記事を参照できます", "label": "公開"}}, "actions": {"apply": "適用", "cancel": "キャンセル", "close": "閉じる", "copied": "コピーしました", "copy": "コピー", "create": "作成", "createShortcut": "ショートカットを作成", "delete": "削除", "doSave": "保存する", "edit": "編集", "generate": "生成", "link": "関連つける", "move": "移動", "reset": "リセット", "retry": "再試行", "save": "保存", "unlink": "リンク解除", "view": "閲覧"}, "activity": {"title": "アクティビティ", "types": {"edit": "編集済み", "saveToKb": "<KBName />ナレッジベースに保存済み"}}, "answerRequired": "Answer is required", "answerTooLong": "Answer is too long", "answerTooShort": "Answer is too short", "apiErrors": {"400": {"message": "リクエストが無効であるか、提供できません。リクエストの本文が不正であるか、クエリ文字列が長すぎる可能性があります。", "title": "不正なリクエスト"}, "401": {"message": "リクエストはユーザー認証が必要です。", "title": "認証が必要"}, "403": {"message": "リクエストは理解されていますが、拒否されたか、アクセスが許可されていません。", "title": "禁止されたリクエスト"}, "404": {"message": "要求されたリソースは見つかりませんでしたが、将来再利用可能になる可能性があります。", "title": "見つかりません"}, "409": {"message": "リクエストは、リソースの現在の状態との競合により完了できませんでした。", "title": "競合"}, "500": {"message": "サーバーは内部エラーまたは設定ミスにより、リクエストを完了できませんでした。", "title": "サーバーエラー"}}, "apply": "適用", "articleAnalytics": {"aiwidget": "AIウィジェット", "badRating": "悪い評価の数", "chatbot": "チャットボット", "chatwindow": "チャットウィンドウ", "count": "回", "countSuffix": "件", "customerSide": "お客様側", "faq": "FAQ", "goodRating": "良い評価の数", "management": "管理画面", "noData": "データがありません", "operatorSide": "オペレーター側", "placeholder": {"dateRange": "年/月/日ー年/月/日", "selectPeriod": "期間を選択"}, "quickFilter": {"allTime": "すべての期間", "customRange": "カスタム期間", "last28Days": "過去28日間", "last7Days": "過去7日間", "last90Days": "過去90日間"}, "rating": "お客様からの評価", "viewCount": "表示回数"}, "articleCollection": {"addRelatedArticle": "関連する記事を追加する", "aiGenerate": "AIで記事を生成", "changedWarningDescription": "ページから移動すると記事を保存できなくなります", "changedWarningTitle": "変更は保存されません", "content": "本文", "createArticleManually": "記事を手動で作成", "createdAt": "作成日時", "createNew": "記事を作成", "delete": {"confirmContent": "この操作は元に戻せません。", "confirmContentWithShortcut": "元の記事を削除すると、他のナレッジベースにあるショートカットも削除されます。この操作は元に戻せません。", "confirmTitle": "この記事を削除してもよろしいですか？", "failed": {"description": "もう一度お試しください", "title": "記事の削除に失敗しました"}, "success": {"description": "記事が正常に削除されました", "title": "記事が削除されました"}}, "discardChanges": "保存せずに続行", "emptyMessage": " [記事を新規作成] ボタンをクリックして作成します", "emptyTitle": "記事が作成されていません", "goBack": "戻る", "importArticle": "CSVから記事をインポート", "info": "基本情報", "keyPhrases": "関連フレーズ", "keyPhrasesPlaceholder": "記事に関連する文を追加してEnterキーを押します", "lastUpdated": "最終更新日時", "name": "名前", "openArticle": "記事を開く", "related": "関連記事", "removeRelatedArticle": "本当にこの記事の関連付けを解除しますか?", "save": {"failed": {"description": "もう一度お試しください", "title": "記事の保存に失敗しました"}, "notReady": {"description": "数分後に再度お試しください", "title": "記事は更新の準備ができていません"}, "success": {"description": "記事が正常に保存されました", "title": "記事が保存されました"}}, "title": "タイトル", "vote": {"failed": {"description": "もう一度お試しください", "title": "記事の評価に失敗しました"}, "success": {"description": "記事が正常に評価されました", "title": "記事が評価されました"}}}, "articleDescription": "記事を保存するナレッジベース", "articleLabel": "記事", "articleMoving": {"confirmMovingMessage": "この記事を『{{toKBName}}』に\n移動してもよろしいですか?\nこの操作は元に戻せません", "description": "記事を別のナレッジベースへ移動することができます。", "failedMessage": "もう一度お試しください", "failedTitle": "記事の移動に失敗しました", "subDescription": "移動後も、カスタム項目への入力、関連するメタデータが保持されます。", "successMessage": "記事が正常に移動されました", "successTitle": "成功", "title": "記事を移動"}, "articlePublishActions": {"draft": "下書き", "published": "今すぐ公開", "schedulePublish": "公開予約", "scheduleUnpublish": "公開予約解除"}, "articleSavingModal": {"confirmSavingMessage": "これらの記事を '{{toKBName}}' に保存しますか？ \nこの操作は取り消せません。", "confirmSavingTitle": "記事を保存", "description": "以下のリストからナレッジ ベースを選択するか、ナレッジベースを新規作成して記事を保存できます。", "successMessage": "記事がナレッジベースに保存されました。", "successTitle": "記事を保存しました", "title": "記事を保存"}, "articleSelector": {"confirmMessage": "選択したファイルから記事を生成しますか？", "confirmTitle": "記事を生成", "description": "関連する記事を選択してください。最大{{limit}}つの記事を選択できます。", "placeholderSearch": "記事のタイトル、関連フレーズ、コンテンツで検索", "selectAll": "すべてを選択する", "title": "関連記事の選択"}, "articleShortcut": {"badge": "ショートカット", "creating": {"description": "記事のショートカットを作成し、別のナレッジベースに保存することができます。\nショートカットを作成しても、カスタム項目への入力や関連するメタデータは保持されます。", "failedDescription": "後でもう一度試してください。", "failedTitle": "記事ショートカットの作成に失敗しました", "successDescription": "記事ショートカットが正常に作成されました", "successTitle": "記事ショートカットが正常に作成されました", "title": "ショートカットを作成"}, "viewOriginal": "元の記事を見る"}, "at": "作成日", "backBtn": "前へ", "backToJobs": "記事生成ジョブ一覧に戻る", "backToKBDetail": "{{kbName}} へ戻る", "backToKBList": "ナレッジベース一覧へ戻る", "button": {"downloadGeneratedArticles": "ダウンロード", "generatedArticlesCSV": "カンマ区切りの値 (.csv)", "generatedArticlesExcel": "Microsoft Excel (.xlsx)", "saveToKnowledgeBase": "ナレッジベースに保存", "viewJobDetails": "ジョブ詳細を表示"}, "cancel": "キャンセル", "checkTheQnAList": "Q&A一覧を確認", "closeBtn": "閉じる", "comments": {"deleteConfirmTitle": "本当にこのコメントを削除しますか？", "empty": "まだコメントがありません。", "placeholder": "コメントを追加", "seeMore": "もっと見る", "title": "コメント"}, "confirmModalTitleDefault": "キャンセルしますか?", "continue": "続行", "create": "作成する", "created": "作成", "createdBy": "作成者", "createKB": {"successMessage": "ナレッジベースが正常に作成されました", "successTitle": "成功"}, "createNewKnowledgeBase": "新規作成", "createQnAAuto": "Q&Aを自動生成", "createQnAAutoDescription": "AIを使用し、ドキュメントナレッジベースからQ&Aを自動生成します", "createQnAList": "以下のボタンを押してQ&Aを登録します", "createQnAManual": "Q&Aを作成", "createQnAManualDescription": "Q&Aを新規作成します", "customizePrompt": "プロンプト編集", "customizePromptModalTitle": "生成プロンプト編集", "defaultPrompt": "以下の文章から20個の質問(Q)と回答(A)のリストを日本語で作成してください。回答(A)は、可能な限り詳細に回答を書いてください。", "delete": "削除", "deleteDocumentConfirmTitle": "File を削除しますか?", "deleteJob": {"confirmModal": {"message": "この記事生成ジョブを削除しますか？", "title": "記事生成ジョブを削除"}, "notifications": {"error": {"message": "記事生成ジョブの削除に失敗しました。", "title": "エラー"}, "success": {"message": "記事生成ジョブが削除されました。", "title": "成功"}}}, "deleteQnAConfirmTitle": "Q&A を削除しますか?", "description": "ここにはすべての記事生成ジョブが表示されます。記事を複数回生成し、他のページに移動できます。進捗は保存され、いつでも確認できます。", "deselectAll": "すべて選択解除", "detailPageTitle": "ジョブ詳細", "documentDescription": "ドキュメントファイルを保存するナレッジベース", "document": {"detail": {"contentType": "ファイル形式", "createdAt": "作成時間", "createdBy": "作成者", "deleteConfirmTitle": "本当にこのファイルを削除しますか？", "notFoundDesc": "このファイルは存在しないか、削除されています。", "notFoundTitle": "ファイルが見つかりません", "size": "サイズ", "status": "ステータス", "updateAccessLevelTitle": "このナレッジベースを{accessLevel}にしますか？"}}, "documentLabel": "ドキュメントファイル", "documentSelector": {"confirmMessage": "選択したファイルから記事を生成しますか？", "confirmTitle": "記事生成", "description": "以下のリストからファイルを選択して記事を生成できます。複数のファイルを選択できます。", "title": "ファイルを選択"}, "downloadGenArticles": {"fallbackMessage": "記事のダウンロードに失敗しました", "fallbackTitle": "記事のダウンロードに失敗しました", "successMessage": "記事のダウンロードに成功しました", "successTitle": "記事のダウンロードに成功しました"}, "draft": "下書き", "edit": "編集", "editBtn": "編集", "emptyJobArticles": {"message": "記事生成ジョブが空です。記事生成ジョブを確認してください。", "title": "記事が見つかりません"}, "emptyJobs": {"message": "AI Generatorボタンをクリックして新しい記事生成ジョブを作成してください。", "title": "記事生成ジョブが見つかりません"}, "emptyMessage": "データがありません", "error": {"fileSize": "ファイルサイズが大きすぎます。{size}MB以下のファイルを選択してください。"}, "errors": {"aborted": {"message": "記事生成ジョブが中止されました。もう一度お試しください。", "title": "記事生成中止"}, "contentRequired": "記事の本文は必須項目です", "defaultMessage": "記事生成中にエラーが発生しました", "defaultTitle": "記事生成エラー", "disallowedSpaceAndComma": "スペースおよびカンマは使用できません", "failed": {"message": "記事生成ジョブが失敗しました。もう一度お試しください。", "title": "記事生成失敗"}, "longContent": "記事内容が{{maxLength}}文字を超えています", "longTitle": "記事のタイトルが{{maxLength}}文字を超えています", "requiredField": "{{fieldName}}は必須項目です", "saveArticleError": "記事を保存できませんでした", "somethingWentWrong": {"message": "記事生成中にエラーが発生しました", "title": "エラー"}, "titleRequired": "記事のタイトルは必須項目です"}, "export": "エクスポート", "exportCreating": {"clickToViewJobDetail": "エクスポートジョブが正常に作成されました。<CustomLink>クリックしてジョブ詳細を表示！</CustomLink>", "createExportJob": "エクスポートジョブを作成", "downloadExport": "エクスポートをダウンロード", "exportCompleted": "エクスポート完了", "exportFailed": "エクスポート失敗", "exportJob": "エクスポートジョブ", "exportJobCreated": "エクスポートジョブが正常に作成されました", "exportJobFailed": "エクスポートジョブの作成に失敗しました", "fileType": "ファイル形式", "item": "項目", "items": "項目", "selectedFolder": "選択されたフォルダ", "selectedKB": "選択されたナレッジベース", "selectKnowledgeBasesAndFolders": "以下のリストからエクスポートするナレッジベースとフォルダを選択できます。複数のナレッジベースとフォルダを選択することができます。", "selectKnowledgeBasesAndFoldersToExport": "エクスポートするナレッジベースとフォルダを選択", "warningKnowledgeBasesOnly": "ナレッジベースとドキュメントのうち、エクスポートできるのはナレッジベースのみです。ナレッジベースとドキュメントの両方を含むフォルダを選択した場合、すべてのドキュメントは無視され、ナレッジベースのみがエクスポートされます。"}, "fileActionDelete": "削除", "fileName": "名前", "fileSize": "サイズ", "fileType": "タイプ", "folder": {"pathPlaceholder": "フォルダー名を入力してください（必須）", "pathRequired": "この項目は必須です", "pathTooLong": "フォルダ名は最大で{maxLength}文字にする必要があります。"}, "form": {"cancel": "キャンセル", "description": "説明", "descriptionPlaceholder": "説明を入力", "descriptionTooLong": "説明が{{maxLength}}文字を超えています", "name": "名前", "namePlaceholder": "名前を入力", "nameRequired": "名前は必須項目です", "nameTooLong": "名前が{{maxLength}}文字を超えています", "pressEnterToSave": "変更を保存するにはEnterキーを押してください", "save": "保存"}, "fromDatasourceDescription": "データソースからデータを取り込んで作成するナレッジベース", "fromDatasourceLabel": "データソースからデータを取り込む", "generatePageTitle": "Q&A自動生成", "generateQnA": "Q&A生成開始", "generateQnASuccess": "{kbName} に登録されました", "generating": "Q&A生成中", "generatingQnA": "Q&A生成中です。しばらくお待ちください。", "generatingQnADescription": "このページから移動してもこの画面に戻ることができます", "goToKnowledgeBase": "ナレッジベース一覧へ", "grid": {"documents": "ドキュメント", "empty": "ナレッジベースが作成されていません", "emptyMessage": " [新規作成] ボタンをクリックして作成します", "emptyMessageWithoutPermission": "新規作成するには、マネージャーに問い合わせてください", "folders": "フォルダ", "knowledgeBases": "ナレッジベース", "title": "すべてのナレッジベース"}, "import": {"confirmImport": "インポートを確認", "csvInstructions": "CSVファイルには、タイトルと本文が含まれている必要があります。", "descriptionConfirm": "記事は「{{KBName}}」にインポートする準備ができています", "downloadTemplate": "CSVテンプレートをダウンロード", "dropzone": "CSVファイルをドラッグアンドドロップするか、クリックしてアップロード", "file-invalid-type": "ファイルの形式が無効です。次の形式のファイルをアップロードしてください: {{type}}.", "file-too-large": "ファイルが大きすぎます。{{size}}より小さいファイルをアップロードしてください。", "file-too-small": "ファイルが小さすぎます。{{size}}より大きいファイルをアップロードしてください。", "import_article_content_too_long": "記事の本文が{{maxLengthContent}}文字を超えています", "import_article_missing_data": "CSVファイルの{{rows}}行目にデータがありません。", "importArticleSuccess": "成功です！", "importArticleSuccessDescription": "{{count}}件の記事が正常にインポートされました。", "import_article_title_too_long": "記事のタイトルが{{maxLengthTitle}}文字を超えています", "import_article_too_many_rows": "行数が多すぎます。{{maxRows}}行以内にしてください。", "import_csv_min_number_column": "少なくとも2つの列が必要です。", "importFailed": "失敗", "importFailedMessage": "Q&Aの一括登録に失敗しました", "importSuccess": "成功", "importSuccessMessage": "Q&Aが一括登録されました", "maxFileSize": "最大ファイルサイズ: {{size}}", "maxRows": "最大行数: {{rows}}", "qnaCsv": "CSV一括アップロード", "too-many-files": "ファイルが多すぎます。1つのファイルのみをアップロードしてください。", "uploading": "すべてのデータを解析中...", "uploadingDesc": "少し休んで、数秒待ってください。", "uploadingError": "何かがおかしいです。後でもう一度試してください。"}, "jobStatus": {"generating": "Q&A生成中", "readyToReview": "Q&A生成完了"}, "jobSummary": {"exportResult": "エクスポート結果", "label": {"contentSources": "コンテンツソース", "creator": "作成者", "downloadExport": "ダウンロードエクスポート", "promptUsed": "使用されたプロンプト", "status": "ステータス"}, "noContentSources": "コンテンツソースがありません", "noExportResultFound": "エクスポート結果が見つかりません", "viewGeneratedArticles": "生成された記事を表示"}, "jobType": {"articleDownload": "記事のエクスポート", "articleGeneration": "記事生成", "contentGeneration": "コンテンツ生成"}, "kb": "ナレッジベース", "kbCreateModalTitle": "ナレッジベース詳細", "kbDescription": "説明", "kbDescriptionPlaceholder": "ナレッジベースの説明を入力してください", "kbDescriptionTooLong": "説明が{maxLength}文字を超えています", "kbMoving": {"description": "フォルダ間でナレッジベースを移動することができます。", "subDescription": "移動後も、すべての記事、カスタム項目への入力、関連するメタデータが保持されます。", "title": "ナレッジベースを移動"}, "kbName": "名前", "kbNamePlaceholder": "ナレッジベースの名前を入力してください", "kbNameRequired": "名前は必須項目です", "kbNameTooLong": "名前が{maxLength}文字を超えています", "kbType": "タイプ", "logout": "ログアウト", "menu": {"delete": "削除する", "download": "ダウンロード", "edit": "基本情報編集", "exportFolder": "フォルダをエクスポート", "exportKnowledgeBase": "ナレッジベースをエクスポート", "move": "移動"}, "modal": {"cancel": "キャンセル", "confirmFolderDelete": "本当にこのフォルダを削除しますか?", "confirmKnowledgeBaseDelete": "本当にこのナレッジベースを\n削除しますか?", "create": " 作成する", "createFolder": "フォルダを新規作成", "createKnowledgeBase": "ナレッジベースを新規作成", "delete": "削除する", "editFolder": "フォルダを編集", "editKnowledgeBase": "ナレッジベースを編集する", "notice": {"folder": {"childProcess": {"content": "フォルダーの内容を削除するには時間がかかります。タスク ページで進行状況を確認できます。", "title": "お待ちください..."}}}, "switchAll": "すべてを切り替える", "uploadFile": "ファイルをドラッグするか\nクリックしてアップロードしてください", "uploadFileDescription": "PDF, TXT, CSV, Doc, Docx, MD, JPG, JPEG, PNG, GIF, SVG, WEBPのみ対応。容量{maxSize}MBまで", "uploadingFiles": "ファイルのアップロード", "uploadingFilesDescription": "こちらでファイルを公開・非公開に設定できます。"}, "move": "移動する", "nextBtn": "次へ", "noBtn": "いいえ", "noDataAddedToQnAKB": "生成されたQ&Aは {kbName} に追加されません!", "noDocument": "ファイルがアップロードされていません", "noDocumentFound": "一致する検索結果がありません", "noDocumentList": "ドキュメントナレッジベースが作成されていません", "noDocumentListDescription": "Q&A生成を行うドキュメントナレッジベースを作成してください", "noQnA": "Q&Aが登録されていません", "notFound": {"articleDescription": "あなたが探している記事は存在しないか、削除されています。", "articleTitle": "記事が見つかりません", "documentDescription": "あなたが探しているドキュメントは存在しないか、削除されています。", "documentTitle": "ドキュメントが見つかりません", "folderDescription": "あなたが探しているフォルダーは存在しないか、削除されています。", "folderTitle": "フォルダーが見つかりません", "fromUrl": "あなたの見つからないコンテンツは<FromUrl />からアクセスされました", "goToHome": "ホームに戻る", "jobDescription": "あなたが探しているジョブは存在しないか、削除されています。", "jobTitle": "ジョブが見つかりません", "kbDescription": "あなたが探しているナレッジベースは存在しないか、削除されています。", "kbTitle": "ナレッジベースが見つかりません", "pageDescription": "あなたが探しているページは存在しないか、削除されています。", "pageTitle": "ページが見つかりません", "templateDescription": "あなたが探しているテンプレートは存在しないか、削除されています。", "templateTitle": "テンプレートが見つかりません"}, "notifications": {"error": {"description": "エラーが発生しました。", "title": "エラー"}, "success": {"description": "リクエストが正常に処理されました", "title": "成功"}}, "okBtn": "OK", "openAction": {"article": "記事を開く", "base": "ナレッジベースを開く", "document": "ファイルを開く", "folder": "フォルダを開く"}, "pageTitle": "ナレッジベースへようこそ", "promptCustomize": {"createSuccess": {"message": "新しいプロンプトが追加されました！", "title": "成功"}, "deleteConfirmTitle": "このプロンプトを削除しますか？", "description": "ここでプロンプトをカスタマイズできます。変更は自動的に保存され、将来使用するために保存されます。", "error": {"message": "プロンプトの更新中にエラーが発生しました", "title": "エラー"}, "formContent": "プロンプト内容", "formContentLimit": "{currentLength}/{maxLength} 文字", "formTitle": "プロンプトタイトル", "pastCustomPrompts": "カスタムプロンプトを渡す", "pastCustomPromptsDescription": "最新の{limit}件のみが保存されます。古いものは自動的に削除されます。", "placeholder": "以下の文章から20個の質問(Q)と回答(A)のリストを日本語で作成してください。回答(A)は、可能な限り詳細に回答を書いてください。", "resetTooltip": "リセットすると、デフォルトのプロンプトに戻り、\n新しいプロンプトとして保存されます。", "title": "プロンプト編集", "updateSuccess": {"message": "プロンプトが保存されました！", "title": "成功"}}, "promptViewer": {"description": "プロンプト内容:", "title": "プロンプト"}, "publish": "公開", "published": "公開中", "publishStatus": {"draft": "下書き", "published": "公開中", "schedulePublish": "公開予約", "scheduleUnpublish": "公開予約解除"}, "qnaAnswer": "回答", "qnaAnswerPlaceholder": "Enter the answer", "QnACreateModalTitle": "Q&Aエディター", "qnaDescription": "チャットボットの回答に使用される一問一答を登録します", "qnaGenerateFailed": "Q&A生成中に問題が発生しました。もう一度お試しください。", "qnaGenerateStep1": "ドキュメント\nナレッジベースを選択", "qnaGenerateStep2": "Q&A自動生成", "qnaGenerateStep3": "登録するQ&Aを選択", "qnaGenerateStep4": "完了", "qnaLabel": "Q&A", "qnaQuestion": "質問", "qnaQuestionPlaceholder": "Enter the question", "question": "Q&A", "questionRequired": "Question is required", "questionTooLong": "Question is too long", "questionTooShort": "Question is too short", "readyToReview": "Q&A生成完了", "recent": {"title": "最近閲覧したナレッジベース"}, "retryJob": {"fallbackMessage": "記事生成ジョブの再試行に失敗しました。", "fallbackTitle": "記事生成ジョブの再試行に失敗しました。", "successMessage": "記事生成ジョブが再試行されました。", "successTitle": "記事生成ジョブの再試行に成功しました。"}, "runJobButton": {"exportJob": "新しいエクスポートジョブを実行", "generationJob": "新しい生成ジョブを実行", "title": "新しいジョブを実行"}, "save": "保存する", "saveAsDraft": "下書きとして保存", "scheduleModal": {"noDateTimeSelected": "日時が選択されていません", "selectDate": "日付を選択", "selected": "選択済み", "selectedDateTime": "選択された日時", "selectTime": "時間を選択", "timePlaceholder": "時:分"}, "search": "検索", "searchByDocumentName": "ドキュメントナレッジベースを検索", "search.emptyMessage": "一致する検索結果がありません", "searchPlaceholder": "検索フレーズを入力してEnterキーを押してください...", "search.searchResuls": "検索結果", "selectAll": "すべて選択", "selectDocument": "ドキュメントナレッジベースを選択", "selected": "選択済み", "selection": {"childrenFolders": "フォルダ", "knowledgeBases": "ナレッジベース", "parentFolders": "フォルダツリー"}, "settings": "設定", "sourceDocument": {"label": "ソースドキュメント", "untitled": "タイトルなしドキュメント"}, "status": "公開ステータス", "statuses": {"aborted": "中止", "failed": "失敗", "queued": "キューに入っています", "retry": "再試行", "running": "実行中", "succeeded": "成功"}, "table": {"selectionInfo": "{{selected}}件選択中 / {{total}}件"}, "templateCustomData": {"addTitle": "カスタムデータを追加", "dataType": {"addListLabel": "ボタンラベルを追加", "description": "以下のオプションからデータタイプを定義できます。", "enterFieldText": "{{label}}を入力してください", "label": "データタイプ", "options": {"list": "リスト", "text": "テキスト"}, "placeholderLabel": "プレースホルダ"}, "deleteConfirmTitle": "このカスタムデータを削除してもよろしいですか？", "editTitle": "カスタムデータを編集", "emptyMessage": "カスタムデータが見つかりません", "listPlaceholder": "値を追加", "notification": {"deleteError": "カスタムデータの削除に失敗しました", "deleteSuccess": "カスタムデータが正常に削除されました", "saveError": "カスタムデータの保存に失敗しました", "saveSuccess": "カスタムデータが正常に保存されました"}, "textPlaceholder": "ここにテキストフィールド", "titleField": {"label": "タイトル", "placeholder": "タイトルを入力してください"}}, "templates": {"goBack": "テンプレートに戻る", "templateModalTitle": "テンプレートの詳細", "title": "テンプレート"}, "timeAgo": {"day_one": "{{count}}日前", "day_other": "{{count}}日前", "hour_one": "{{count}}時間前", "hour_other": "{{count}}時間前", "justNow": "数秒前", "minute_one": "{{count}}分前", "minute_other": "{{count}}分前", "month_one": "{{count}}ヶ月前", "month_other": "{{count}}ヶ月前", "since": "以来 {{date}}"}, "title": "ジョブ", "totalSelected": "選択したQ&A: {total}", "tree": {"add": "フォルダを新規作成", "confirm": "本当にこのフォルダを削除しますか?", "delete": "削除する", "edit": "基本情報編集", "root": "すべてのナレッジベース", "title": "フォルダ"}, "undoBtn": "元に戻す", "unknown": "不明", "unknownKBName": "不明", "updatedAt": "最終更新日時", "upload": "アップロード", "uploadDialog": {"cancelUploadBtn": "アップロードをキャンセル", "cancelUploadMessage": "すべてのアップロードをキャンセルしますか？", "cancelUploadTitle": "アップロードが完了していません", "continueUploadBtn": "アップロードを続ける", "title": "アップロード進行状況"}, "uploadDoc": "アップロード", "uploadDocuments": "[アップロード] ボタンを押してファイルをアップロードします", "upload.failed": "失敗", "upload.fileNotFound": "ファイルが見つかりません", "upload.fileNotFoundMessage": "Fなんらかの理由で、ファイルが見つかりません。もう一度お試しください！", "uploadNote": "テキストを含むすべてのファイルタイプをアップロードできます (最大ファイルサイズ：100MB)", "upload.unknown": "処理中", "upload.uploaded": "成功", "upload.uploadSuccessMessage": "ドキュメントは正常にアップロードされました。", "upload.uploadSuccessTitle": "成功", "validation": {"contentMaxLength": "内容は{maxLength}文字以内にしてください", "contentRequired": "内容は必須項目です", "titleRequired": "タイトルは必須項目です"}, "warnings": {"maxSelectionReached": "一度にナレッジベースに選択して保存できる記事は最大{{max}}件までです"}, "yesBtn": "はい"}