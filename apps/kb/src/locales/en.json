{"accessLevel": {"changed": {"content": "Changing the public scope takes time. You can check the progress in Task page.", "title": "Please wait..."}, "description": "You can set the public scope of your knowledge base.", "label": "Public Scope", "private": {"description": "Only users logged in to DECA Cloud products can view this Knowledge Base", "label": "Private"}, "processing": "Changing", "public": {"description": "Through DECA Cloud products such as chatbots and FAQs, even users who are not logged in to DECA Cloud can view this Knowledge Base. (The knowledge base is not directly viewable.)", "label": "Public"}}, "actions": {"apply": "Apply", "cancel": "Cancel", "close": "Close", "copied": "<PERSON>pied", "copy": "Copy", "create": "Create", "createShortcut": "Create Shortcut", "delete": "Delete", "doSave": "Save", "edit": "Edit", "generate": "Generate", "link": "Link", "move": "Move", "reset": "Reset", "retry": "Retry", "save": "Save", "unlink": "Unlink", "view": "View"}, "activity": {"title": "Activity", "types": {"edit": "Modified", "saveToKb": "Saved in <KBName />"}}, "answerRequired": "Answer is required", "answerTooLong": "Answer is too long", "answerTooShort": "Answer is too short", "apiErrors": {"400": {"message": "The request was invalid or cannot be served. The request body may be malformed or the query string may be too long.", "title": "Bad Request"}, "401": {"message": "The request requires user authentication.", "title": "Unauthorized"}, "403": {"message": "The request is understood, but it has been refused or access is not allowed.", "title": "Forbidden"}, "404": {"message": "The requested resource could not be found but may be available again in the future.", "title": "Not Found"}, "409": {"message": "The request could not be completed due to a conflict with the current state of the resource.", "title": "Conflict"}, "500": {"message": "The server encountered an internal error or misconfiguration and was unable to complete the request.", "title": "Internal Server Error"}}, "apply": "Apply", "articleAnalytics": {"aiwidget": "AI Widget", "badRating": "Negative Reviews", "chatbot": "<PERSON><PERSON><PERSON>", "chatwindow": "Chatwindow", "customerSide": "Customer side", "faq": "FAQ", "goodRating": "Positive Reviews", "management": "Management screen", "noData": "No data available", "operatorSide": "Operator side", "placeholder": {"dateRange": "YYYY/MM/DD-YYYY/MM/DD", "selectPeriod": "Select period"}, "quickFilter": {"allTime": "All time", "customRange": "Custom Range", "last28Days": "Last 28 days", "last7Days": "Last 7 days", "last90Days": "Last 90 days"}, "rating": "Reviews from Customer", "viewCount": "Number of Views"}, "articleCollection": {"addRelatedArticle": "Add Related Article", "aiGenerate": "Generate Article with AI", "changedWarningDescription": "We wont be able to save the article if you move away from this page.", "changedWarningTitle": "Your changes wont be saved", "content": "Content", "createArticleManually": "Create a new article manually", "createdAt": "Created At", "createNew": "Create Article", "delete": {"confirmContent": "This action cannot be undone.", "confirmContentWithShortcut": "Deleting the original article will also delete shortcuts in other knowledge bases. This action cannot be undone.", "confirmTitle": "Are you sure you want to delete this article?", "failed": {"description": "Please try again later", "title": "Failed to delete the article"}, "success": {"description": "The article has been deleted successfully", "title": "Article deleted successfully"}}, "discardChanges": "Discard the Article", "emptyMessage": "Please create articles to get started", "emptyTitle": "This Article Knowledge Base is empty", "goBack": "Go Back", "importArticle": "Import articles from CSV", "info": "Info", "keyPhrases": "Key phrases", "keyPhrasesPlaceholder": "Add a Key Phrase and press Enter", "lastUpdated": "Last Updated", "name": "Name", "openArticle": "Open Article", "related": "Related Articles", "removeRelatedArticle": "Are you sure you want to remove this article from the related articles?", "save": {"failed": {"description": "Please try again later", "title": "Failed to save the article"}, "notReady": {"description": "Please try again later in a few minutes", "title": "Article is not ready to be updated"}, "success": {"description": "The article has been saved successfully", "title": "Article saved successfully"}}, "title": "Title", "vote": {"failed": {"description": "Please try again later", "title": "Failed to vote the article"}, "success": {"description": "The article has been voted successfully", "title": "Article voted successfully"}}}, "articleDescription": "Knowledge Base to save articles type", "articleLabel": "Article Collection", "articleMoving": {"confirmMovingMessage": "Are you sure you want to move this article to '{{toKBName}}'? \nThis action cannot be undone.", "description": "You can move the article between different article knowledge bases.", "failedMessage": "Please try again later", "failedTitle": "Failed to move the article", "subDescription": "All associated metadata, custom data fields, and formatting are preserved during the move.", "successMessage": "The article has been moved successfully.", "successTitle": "Success", "title": "Move Article"}, "articlePublishActions": {"draft": "Draft", "published": "Publish Now", "schedulePublish": "Schedule Publish", "scheduleUnpublish": "Schedule Un-publish"}, "articleSavingModal": {"confirmSavingMessage": "Are you sure you want to save these articles to '{{toKBName}}'? \nThis action cannot be undone.", "confirmSavingTitle": "Save Article", "description": "You can select a Knowledge Base from the list below or create a new one to save the article.", "successMessage": "The article has been saved to the Knowledge Base.", "successTitle": "Article Saved", "title": "Save Article"}, "articleSelector": {"confirmMessage": "Generate article from selected files?", "confirmTitle": "Generate article", "description": "Please select relevant articles. You can select up to {{limit}} articles.", "placeholderSearch": "Search by article title, related phrases, and content", "selectAll": "Select all", "title": "Select related articles"}, "articleShortcut": {"badge": "shortcut", "creating": {"description": "You can create a shortcut of this article and save it to another knowledge base.\nThe input of custom fields and related metadata will be preserved.", "failedDescription": "Please try again later", "failedTitle": "Failed to create article shortcut", "successDescription": "The article shortcut has been created successfully", "successTitle": "Article shortcut created successfully", "title": "Create shortcut"}, "viewOriginal": "View original article"}, "at": "at", "backBtn": "Back", "backToJobs": "Go back to Generation Job list", "backToKBDetail": "Go back to the {{kbName}}", "backToKBList": "Go back to the Knowledge Base List", "button": {"downloadGeneratedArticles": "Download", "generatedArticlesCSV": "Comma Separated Values (.csv)", "generatedArticlesExcel": "Microsoft Excel (.xlsx)", "saveToKnowledgeBase": "Save to Knowledge Base", "viewJobDetails": "View Job Details"}, "cancel": "Cancel", "checkTheQnAList": "Check the QnA list", "closeBtn": "Close", "comments": {"deleteConfirmTitle": "Are you sure you want to delete this comment?", "empty": "There are no comments yet", "placeholder": "Please add a comment", "seeMore": "See more", "title": "Comments"}, "confirmModalTitleDefault": "Are you sure you want to cancel?", "continue": "Continue", "create": "Create", "created": "Created", "createdBy": "Created by", "createKB": {"successMessage": "The knowledge base has been created successfully.", "successTitle": "Success"}, "createNewKnowledgeBase": "Create Knowledge Base", "createQnAAuto": "Auto-generate new QnA", "createQnAAutoDescription": "Generate a QnA list from a document knowledge base using AI", "createQnAList": "Please add QnA to get started!", "createQnAManual": "Add new QnA manually", "createQnAManualDescription": "Add a new QnA manually", "customizePrompt": "Customize Prompt", "customizePromptModalTitle": "Customize Prompt", "defaultPrompt": "Please create a list of 20 questions (Q) and answers (A) in Japanese from the sentences below. For answer (A), please write your answer in as much detail as possible.", "delete": "Delete", "deleteDocumentConfirmTitle": "Are you sure you want to delete this document?", "deleteJob": {"confirmModal": {"message": "Are you sure you want to delete this job?", "title": "Delete Job"}, "notifications": {"error": {"message": "The generation job has failed to delete.", "title": "Error"}, "success": {"message": "The generation job has been deleted.", "title": "Success"}}}, "deleteQnAConfirmTitle": "Are you sure you want to delete this QnA?", "description": "Here are all your article generation jobs. You can generate articles multiple times and navigate to other pages. Your progress will be saved, and you can return to check anytime.", "deselectAll": "Deselect All", "detailPageTitle": "Job Details", "documentDescription": "Knowledge Base to save documents type", "document": {"detail": {"contentType": "File Type", "createdAt": "Created At", "createdBy": "Created By", "deleteConfirmTitle": "Are you sure you want to delete this file?", "notFoundDesc": "The file you are looking for does not exist or has been deleted.", "notFoundTitle": "File Not Found", "size": "Size", "status": "Status", "updateAccessLevelTitle": "This knowledge base will be {accessLevel}."}}, "documentLabel": "Document Collection", "documentSelector": {"confirmMessage": "Proceed with generating articles from the selected files?", "confirmTitle": "Generate Articles", "description": "You can select files from the below list to generate articles. Multiple files can be selected.", "title": "Select the Files"}, "downloadGenArticles": {"fallbackMessage": "Failed to download generated articles", "fallbackTitle": "Download Generated Articles Failed", "successMessage": "Generated articles downloaded successfully", "successTitle": "Download Generated Articles Success"}, "draft": "Draft", "edit": "Edit", "editBtn": "Edit", "emptyJobArticles": {"message": "Seems your generation articles are empty. Please check your generation job.", "title": "No articles found"}, "emptyJobs": {"message": "You can create a new generation job by clicking the AI Generator button.", "title": "No generation jobs found"}, "emptyMessage": "No data available", "error": {"fileSize": "The file size is too large. Please select a file smaller than {size}."}, "errors": {"aborted": {"message": "The generation job has been aborted. Please try again.", "title": "Generation Aborted"}, "contentRequired": "Article content is required", "defaultMessage": "An error occurred while generating the article", "defaultTitle": "Generation Error", "disallowedSpaceAndComma": "Spaces and commas cannot be used.", "failed": {"message": "The generation job has failed. Please try again.", "title": "Generation Failed"}, "longContent": "The content should have at most {{maxLength}} characters", "longTitle": "The title should have at most {{maxLength}} characters", "requiredField": "{{fieldName}} is required", "saveArticleError": "Couldn't save the article", "somethingWentWrong": {"message": "Something went wrong while generating the article", "title": "Error"}, "titleRequired": "Article title is required"}, "export": "Export", "exportCreating": {"clickToViewJobDetail": "Export job created successfully. <CustomLink>Click to view the job detail!</CustomLink>", "createExportJob": "Create Export Job", "downloadExport": "Download Export", "exportCompleted": "Export completed", "exportFailed": "Export failed", "exportJob": "Export Job", "exportJobCreated": "Export job created successfully", "exportJobFailed": "Failed to create export job", "fileType": "File Type", "item": "item", "items": "items", "selectedFolder": "Selected Folder", "selectedKB": "Selected Knowledge Base", "selectKnowledgeBasesAndFolders": "You can select knowledge bases and folders from the below list to export them. Multiple knowledge bases and folders can be selected.", "selectKnowledgeBasesAndFoldersToExport": "Select Knowledge Bases and Folders to Export", "warningKnowledgeBasesOnly": "Of knowledge bases and documents, only knowledge bases can be exported. If you select folders that contain both knowledge bases and documents, all documents will be ignored and only knowledge bases will be exported."}, "fileActionDelete": "Delete", "fileName": "Name", "fileSize": "Size", "fileType": "Type", "folder": {"pathPlaceholder": "Please fill in the folder name here (required)", "pathRequired": "Folder name is required", "pathTooLong": "The folder name should have at most {maxLength} characters"}, "form": {"cancel": "Cancel", "description": "Description", "descriptionPlaceholder": "Enter the description", "descriptionTooLong": "Description should have at most {{maxLength}} characters", "name": "Name", "namePlaceholder": "Enter the name", "nameRequired": "Name is required", "nameTooLong": "Name should have at most {{maxLength}} characters", "pressEnterToSave": "Press Enter to save your changes", "save": "Save"}, "fromDatasourceDescription": "Knowledge Base to save datasource type", "fromDatasourceLabel": "From a Datasource", "generatePageTitle": "Auto-generate new QnA", "generateQnA": "Generate QnA", "generateQnASuccess": "The QnA has been added successfully into the {kbName}!", "generating": "Generating", "generatingQnA": "The QnA is generating. Please wait a moment.", "generatingQnADescription": "You can move to other pages and come back to check later.", "goToKnowledgeBase": "Go to Knowledge Base", "grid": {"documents": "Documents", "empty": "This Knowledge Base is empty.", "emptyMessage": "Please create new to get started!", "emptyMessageWithoutPermission": "Please contact your manager to create new.", "folders": "Folders", "knowledgeBases": "Knowledge Bases", "title": "All Knowledge Bases"}, "import": {"confirmImport": "Confirm Import", "csvInstructions": "Ensure your CSV file includes columns for title and content like this.", "descriptionConfirm": "The articles are ready to import to '{{KBName}}'", "downloadTemplate": "Download CSV template", "dropzone": "Drop your formatted CSV file here or click to upload", "failed": "Failed", "failedMessage": "Failed to add QnA", "file-invalid-type": "File type is invalid. Please upload a file with the following type: {{type}}.", "file-too-large": "File is too large. Please upload a file smaller than {{size}}.", "file-too-small": "File is too small. Please upload a file larger than {{size}}.", "import_article_content_too_long": "Article content is longer than {{maxLengthContent}} characters", "import_article_missing_data": "Missing data in the CSV file at rows: {{rows}}", "importArticleSuccess": "You are good to go!", "importArticleSuccessDescription": "{{count}} articles has been imported successfully.", "import_article_title_too_long": "Article title is longer than {{maxLengthTitle}} characters", "import_article_too_many_rows": "Too many rows. Please upload a file with less than {{maxRows}} rows.", "import_csv_min_number_column": "Require at least 2 columns.", "maxFileSize": "max file size: {{size}}", "maxRows": ", max rows allowed: {{rows}}", "qnaCsv": "Add QnA from CSV", "success": "Success", "successMessage": "QnA added successfully", "too-many-files": "Too many files. Please upload only one file.", "uploading": "Crunching all the data...", "uploadingDesc": "Please have a rest and give us a couple of seconds.", "uploadingError": "Something went wrong. Please try again later."}, "jobStatus": {"generating": "Generating", "readyToReview": "Ready to Review"}, "jobSummary": {"exportResult": "Export result", "label": {"contentSources": "Content Sources", "creator": "Person Who Created", "downloadExport": "Download Export", "promptUsed": "Prompt Used", "status": "Job Status"}, "noContentSources": "No content sources", "noExportResultFound": "No export result found", "viewGeneratedArticles": "View the Generated Articles"}, "jobType": {"articleDownload": "Export Articles", "articleGeneration": "Generate Article", "contentGeneration": "Generate Content"}, "kb": "Knowledge Base", "kbCreateModalTitle": "Knowledge Base Details", "kbDescription": "Description", "kbDescriptionPlaceholder": "Short description for this knowledge base", "kbDescriptionTooLong": "Description should have at most {maxLength} characters", "kbMoving": {"description": "You can move knowledge bases between folders.", "subDescription": "All articles, custom data fields, and associated metadata are retained during the move.", "title": "Move Knowledge Base"}, "kbName": "Name", "kbNamePlaceholder": "Unique name for this knowledge base", "kbNameRequired": "Name is required", "kbNameTooLong": "Name should have at most {maxLength} characters", "kbType": "Type", "logout": "Logout", "menu": {"delete": "Delete", "download": "Download", "edit": "Edit info", "exportFolder": "Export Folder", "exportKnowledgeBase": "Export Knowledge Base", "move": "Move Knowledge Base"}, "modal": {"cancel": "Cancel", "confirmFolderDelete": "Are you sure you want to delete this folder?", "confirmKnowledgeBaseDelete": "Are you sure you want to delete this knowledge base?", "create": "Create New", "createFolder": "Create a New Folder", "createKnowledgeBase": "Create a New Knowledge Base", "delete": "Delete", "editFolder": "Edit <PERSON>", "editKnowledgeBase": "Edit Knowledge Base", "notice": {"folder": {"childProcess": {"content": "Deleting folder contents takes time. You can check the progress in Task page.", "title": "Please wait..."}}}, "switchAll": "Switch All", "uploadFile": "Upload File", "uploadFileDescription": "Only PDF, TXT, CSV, Doc, Docx, MD, JPG, JPEG, PNG, GIF, SVG, WEBP are supported. Capacity up to {maxSize}MB", "uploadingFiles": "Files Upload", "uploadingFilesDescription": "You can choose to set all files as either Public or Private here."}, "move": "Move", "nextBtn": "Next", "noBtn": "No", "noDataAddedToQnAKB": "There will be no QnA added into the {kbName}!", "noDocument": "This Document Knowledge Base is empty.", "noDocumentFound": "No matching results found", "noDocumentList": "There is no Document Knowledge Base yet.", "noDocumentListDescription": "Please create a Knowledge Base to get started!.", "noQnA": "This QnA Knowledge Base is empty.", "notFound": {"articleDescription": "The article you are looking for does not exist or has been deleted.", "articleTitle": "Article Not Found", "documentDescription": "The document you are looking for does not exist or has been deleted.", "documentTitle": "Document Not Found", "folderDescription": "The folder you are looking for does not exist or has been deleted.", "folderTitle": "Folder Not Found", "fromUrl": "Your not found content was accessed from <FromUrl />", "goToHome": "Go to Home", "jobDescription": "The job you are looking for does not exist or has been completed.", "jobTitle": "Job Not Found", "kbDescription": "The knowledge base you are looking for does not exist or has been deleted.", "kbTitle": "Knowledge Base Not Found", "pageDescription": "The page you are looking for does not exist or has been deleted.", "pageTitle": "Page Not Found", "templateDescription": "The template you are looking for does not exist or has been deleted.", "templateTitle": "Template Not Found"}, "notifications": {"error": {"description": "An error has occurred.", "title": "Error"}, "success": {"description": "Your request has been processed successfully.", "title": "Success"}}, "okBtn": "OK", "openAction": {"article": "Open Article", "base": "Open Knowledge Base", "document": "Open File", "folder": "Open Folder"}, "pageTitle": "Welcome to our Knowledge Bases", "promptCustomize": {"createSuccess": {"message": "A new prompt has been saved!", "title": "Success"}, "deleteConfirmTitle": "Are you sure you want to delete this prompt?", "description": "You can customize the prompt here. The changes will be automatically saved for future use.", "error": {"message": "Something went wrong while updating the prompt", "title": "Error"}, "formContent": "Prompt Content", "formContentLimit": "{currentLength}/{maxLength} Characters", "formTitle": "Prompt Title", "pastCustomPrompts": "Past Customized Prompts", "pastCustomPromptsDescription": "Only the latest {limit} prompts are saved, older ones are automatically removed.", "placeholder": "Please create a list of 20 questions (Q) and answers (A) in Japanese from the sentences below. For answer (A), please write your answer in as much detail as possible.", "resetTooltip": "Resetting will return to the default prompt.\nClicking 'Save' will create a new prompt.", "title": "Customize the Prompt", "updateSuccess": {"message": "Your prompt has been saved!", "title": "Success"}}, "promptViewer": {"description": "Prompt content:", "title": "Prompt"}, "publish": "Publish", "published": "Published", "publishStatus": {"draft": "Draft", "published": "Published", "schedulePublish": "Publish Scheduled", "scheduleUnpublish": "Un-publish Scheduled"}, "qnaAnswer": "Answer", "qnaAnswerPlaceholder": "Enter the answer", "QnACreateModalTitle": "QnA Editor", "qnaDescription": "Knowledge Base to register QnA list used for Chatbot responses", "qnaGenerateFailed": "There is something wrong with the generation. Please check the document and generate again.", "qnaGenerateStep1": "Select a  Document KB & input prompt", "qnaGenerateStep2": "Generate QnA", "qnaGenerateStep3": "Select QnA from generated list", "qnaGenerateStep4": "Complete", "qnaLabel": "QnA", "qnaQuestion": "Question", "qnaQuestionPlaceholder": "Enter the question", "question": "QnA", "questionRequired": "Question is required", "questionTooLong": "Question is too long", "questionTooShort": "Question is too short", "readyToReview": "Ready to Review", "recent": {"title": "Recently Viewed Knowledge Bases"}, "retryJob": {"fallbackMessage": "Failed to retry job", "fallbackTitle": "Retry Job Failed", "successMessage": "The generation job has been retried.", "successTitle": "Retry Job Success"}, "runJobButton": {"exportJob": "Run a new export job", "generationJob": "Run a new generation job", "title": "Run New Job"}, "save": "Save", "saveAsDraft": "Save as Draft", "scheduleModal": {"noDateTimeSelected": "No date and time selected", "selectDate": "Select Date", "selected": "Selected", "selectedDateTime": "Selected Date & Time", "selectTime": "Select Time", "timePlaceholder": "HH:MM"}, "search": "Search", "searchByDocumentName": "Search by document <PERSON><PERSON>'s name", "search.emptyMessage": "Sorry, there are no results that match your search", "searchPlaceholder": "Type your search phrase and hit Enter...", "search.searchResuls": "Search results", "selectAll": "Select All", "selectDocument": "Please select a document knowledge base", "selected": "Selected", "selection": {"childrenFolders": "Folders", "knowledgeBases": "Knowledge Bases", "parentFolders": "Folders Tree"}, "settings": "Settings", "sourceDocument": {"label": "Source Document", "untitled": "Untitled Document"}, "status": "Status", "statuses": {"aborted": "Aborted", "failed": "Failed", "queued": "Queued", "retry": "Retry", "running": "Running", "succeeded": "Succeeded"}, "table": {"selectionInfo": "{{selected}} items selected / {{total}} items"}, "templateCustomData": {"addTitle": "Add Custom Data", "dataType": {"addListLabel": "Add button label", "description": "You can define the data type from the below options.", "enterFieldText": "Enter {{label}}", "label": "Data Type", "options": {"list": "List", "text": "Text"}, "placeholderLabel": "Placeholder"}, "deleteConfirmTitle": "Are you sure you want to delete this Custom Data?", "editTitle": "Edit Custom Data", "emptyMessage": "No custom data found", "listPlaceholder": "Add value", "notification": {"deleteError": "Failed to delete Custom Data", "deleteSuccess": "Custom Data deleted successfully", "saveError": "Failed to save Custom Data", "saveSuccess": "Custom Data saved successfully"}, "textPlaceholder": "Here text field", "titleField": {"label": "Title", "placeholder": "Enter a title"}}, "templates": {"goBack": "Go back to Templates", "templateModalTitle": "Template Details", "title": "Templates"}, "timeAgo": {"day_one": "{{count}} day ago", "day_other": "{{count}} days ago", "hour_one": "{{count}} hour ago", "hour_other": "{{count}} hours ago", "justNow": "just now", "minute_one": "{{count}} minute ago", "minute_other": "{{count}} minutes ago", "month_one": "{{count}} month ago", "month_other": "{{count}} months ago", "since": "since {{date}}"}, "title": "Generation Jobs", "totalSelected": "Total Selected: {total} QnA", "tree": {"add": "Create a New Folder", "confirm": "Are you sure you want to delete this folder?", "delete": "Delete", "edit": "Edit Info", "root": "All Knowledge Bases", "title": "Folders"}, "undoBtn": "Undo", "unknown": "Unknown", "unknownKBName": "Unknown", "updatedAt": "Last Updated", "upload": "Upload", "uploadDialog": {"cancelUploadBtn": "Cancel Uploads", "cancelUploadMessage": "Are you sure you want to cancel all ongoing upload?", "cancelUploadTitle": "Your uploads are not complete yet", "continueUploadBtn": "Continue Uploads", "title": "Upload Status"}, "uploadDoc": "Upload Documents", "uploadDocuments": "Please upload documents to get started!", "upload.failed": "Failed", "upload.fileNotFound": "File not found", "upload.fileNotFoundMessage": "For some reason, the file is not fiound. Please try again!", "uploadNote": "All file types including text are accepted. Maximum file size: 100MB", "upload.unknown": "Processing", "upload.uploaded": "Ready", "upload.uploadSuccessMessage": "Document was uploaded successfully", "upload.uploadSuccessTitle": "Success", "validation": {"contentMaxLength": "Content must not exceed {maxLength} characters", "contentRequired": "Content is required", "titleRequired": "Title is required"}, "warnings": {"maxSelectionReached": "You can only select and save up to {{max}} articles to the knowledge base at a time"}, "yesBtn": "Yes"}