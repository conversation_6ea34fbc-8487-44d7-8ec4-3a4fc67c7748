import { MAX_FOLDER_NAME_LENGTH } from '@/constants/folder';
import { z } from 'zod';

type TranslateFunction = (key: string, options?: any) => string;

export const folderSchema = (t: TranslateFunction) =>
  z.object({
    id: z.string().optional(),
    name: z
      .string({ required_error: t('folder.pathRequired') })
      .transform((value: string) => value.trim())
      .pipe(
        z
          .string()
          .min(1, { message: t('folder.pathRequired') })
          .max(MAX_FOLDER_NAME_LENGTH, {
            message: t('folder.pathTooLong', { maxLength: MAX_FOLDER_NAME_LENGTH }),
          })
      ),
    parentDirId: z.string(),
  });
