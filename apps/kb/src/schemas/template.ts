import { MAX_TEMPLATE_DESCRIPTION_LENGTH, MAX_TEMPLATE_NAME_LENGTH } from '@/constants/kb';
import { KBTemplateDataTypeEnum } from '@/types/template';
import { z } from 'zod';

type TranslateFunction = (key: string, options?: any) => string;

export const templateSchema = (t: TranslateFunction) =>
  z.object({
    templateTitle: z
      .string()
      .transform((value: string) => value.trim())
      .pipe(
        z
          .string()
          .min(1, { message: t('form.nameRequired') })
          .max(MAX_TEMPLATE_NAME_LENGTH, {
            message: t('form.nameTooLong', { maxLength: MAX_TEMPLATE_NAME_LENGTH }),
          })
      ),
    description: z
      .string()
      .transform((value: string) => value.trim())
      .pipe(
        z.string().max(MAX_TEMPLATE_DESCRIPTION_LENGTH, {
          message: t('form.descriptionTooLong', { maxLength: MAX_TEMPLATE_DESCRIPTION_LENGTH }),
        })
      ),
  });

export const templateFieldSchema = (t: TranslateFunction) =>
  z.object({
    title: z
      .string()
      .transform((value: string) => value.trim())
      .pipe(
        z
          .string()
          .min(1, { message: t('form.nameRequired') })
          .max(MAX_TEMPLATE_NAME_LENGTH, {
            message: t('form.nameTooLong', { maxLength: MAX_TEMPLATE_NAME_LENGTH }),
          })
      ),
    description: z
      .string()
      .transform((value: string) => value.trim())
      .pipe(
        z.string().max(MAX_TEMPLATE_DESCRIPTION_LENGTH, {
          message: t('form.descriptionTooLong', { maxLength: MAX_TEMPLATE_DESCRIPTION_LENGTH }),
        })
      ),
    dataType: z.nativeEnum(KBTemplateDataTypeEnum).default(KBTemplateDataTypeEnum.text),
  });
