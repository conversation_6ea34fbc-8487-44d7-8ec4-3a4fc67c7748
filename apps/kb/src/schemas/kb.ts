import { ROOT_PATH } from '@/constants/folder';
import { MAX_KB_DESCRIPTION_LENGTH, MAX_KB_NAME_LENGTH } from '@/constants/kb';
import { MAX_QNA_ANSWER_LENGTH, MAX_QNA_QUESTION_LENGTH } from '@/constants/qna';
import { ACCESS_LEVEL, PROCESS_TYPE } from '@/types';
import { z } from 'zod';
import { kbType, qnaStatus } from './constants';

type TranslateFunction = (key: string, options?: any) => string;

export const kbSchemaV1 = z.object({
  name: z.string().min(1).max(MAX_KB_NAME_LENGTH),
  description: z.string().min(1).max(MAX_KB_DESCRIPTION_LENGTH),
  type: kbType,
});

export const kbSchema = (t: TranslateFunction) =>
  z.object({
    name: z
      .string()
      .transform((value: string) => value.trim())
      .pipe(
        z
          .string()
          .min(1, { message: t('kbNameRequired') })
          .max(MAX_KB_NAME_LENGTH, {
            message: t('kbNameTooLong', { maxLength: MAX_KB_NAME_LENGTH }),
          })
      ),
    description: z
      .string()
      .transform((value: string) => value.trim())
      .pipe(
        z.string().max(MAX_KB_DESCRIPTION_LENGTH, {
          message: t('kbDescriptionTooLong', { maxLength: MAX_KB_DESCRIPTION_LENGTH }),
        })
      ),
    baseType: kbType,
    parentDirId: z.string().default(ROOT_PATH),
    accessLevel: z.nativeEnum(ACCESS_LEVEL).default(ACCESS_LEVEL.private),
    processType: z.nativeEnum(PROCESS_TYPE).default(PROCESS_TYPE.none),
  });

export const qnaSchema = z.object({
  question: z.string().min(1).max(MAX_QNA_QUESTION_LENGTH),
  answer: z.string().min(1).max(MAX_QNA_ANSWER_LENGTH),
  answerRaw: z.string(),
  status: qnaStatus,
});
