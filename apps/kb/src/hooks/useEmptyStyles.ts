import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

export const useEmptyStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: rem(20),
    minHeight: rem(374),
    height: '70%',
    borderRadius: rem(10),
    border: `${rem(1)} solid ${theme.colors.decaLight[2]}`,
    backgroundColor: theme.colors.decaLight[0],
  },
  iconBox: {
    marginBottom: rem(20),
    fontSize: rem(40),
  },
  icon: {
    width: rem(200),
  },
  contentBox: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyMessage: {
    marginBottom: rem(24),
    textAlign: 'center',
    color: theme.colors.decaGrey[4],
    fontWeight: 700,
    lineHeight: rem(29),
    fontSize: theme.fontSizes.lg,
  },
}));
