import { kbSchemaV1 } from '@/schemas';
import { KB_TYPE, type KnowledgeBase } from '@/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { type SubmitHandler, useForm } from 'react-hook-form';

type KbFormInputs = {
  name: string;
  description: string;
  type: string;
};

interface UseKbBasicFormProps {
  currentKnowledgeBase?: KnowledgeBase;
  onSubmitted?: (data: KbFormInputs) => void;
}

export const useKbBasicForm = ({ currentKnowledgeBase, onSubmitted }: UseKbBasicFormProps) => {
  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
    trigger,
    getValues,
  } = useForm({
    defaultValues: {
      name: currentKnowledgeBase?.name || '',
      description: currentKnowledgeBase?.description || '',
      type: currentKnowledgeBase?.type || KB_TYPE.qna,
    },
    mode: 'onBlur',
    resolver: zodResolver(kbSchemaV1),
  });

  const onSubmit: SubmitHandler<KbFormInputs> = (data) => {
    onSubmitted?.(data);
  };

  return {
    handleSubmit: handleSubmit(onSubmit),
    onSubmit,
    control,
    reset,
    trigger,
    getValues,
    errors,
  };
};
