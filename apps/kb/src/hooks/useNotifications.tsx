import type { MantineColor } from '@mantine/core';
import { notifications } from '@mantine/notifications';

type TranslateFunction = (key: string, options?: any) => string;

const NOTIFICATION_COLORS = {
  success: 'green',
  error: 'red',
};

export const useNotifications = (t: TranslateFunction) => {
  /**
   * Show notification for template response
   * @param {any} response - The response from the template API
   * @param {string} title - The title of the notification
   * @param {string} message - The message of the notification
   */
  const notifyResponse = (response: any, title?: string, message?: string) => {
    if (response?.status === 'success') {
      notifications.show({
        title: title || t('notifications.success.title', { ns: 'common' }),
        message: message || t('notifications.success.description', { ns: 'common' }),
        color: NOTIFICATION_COLORS.success,
      });
    } else {
      notifications.show({
        title: title || t('notifications.error.title', { ns: 'common' }),
        message: message || t('notifications.error.description', { ns: 'common' }),
        color: NOTIFICATION_COLORS.error,
      });
    }
  };

  const notifyMessage = (title: string, message: string, color?: MantineColor) => {
    notifications.show({
      title,
      message,
      color: color || NOTIFICATION_COLORS.success,
    });
  };

  const notifyError = (title: string, message: string) => {
    notifications.show({
      title,
      message,
      color: NOTIFICATION_COLORS.error,
    });
  };

  return {
    notifyResponse,
    notifyMessage,
    notifyError,
  };
};
