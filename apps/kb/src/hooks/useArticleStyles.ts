import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

export const useArticleDetailStyles = createStyles((theme) => ({
  articleFormSection: {
    padding: rem(8),
  },
  articleFormActions: {
    display: 'flex',
    justifyContent: 'flex-end',
    gap: rem(12),
  },
  articleFormField: {
    marginBottom: rem(24),
    '&:last-child': {
      marginBottom: 0,
    },
  },
  articleLabel: {
    fontSize: rem(18),
    lineHeight: rem(28),
    fontWeight: 700,
    color: theme.colors.decaNavy[5],
  },
  articleInputField: {
    '& input': {
      border: 'none',
      fontWeight: 500,
      fontSize: rem(16),
    },
  },
  articleEditorField: {
    marginLeft: rem(-16),
    '&.bn-container': {
      minHeight: rem(120),
    },
  },
  articleWrapper: {
    display: 'flex',
    flexDirection: 'column',
    padding: rem(12),
  },
  articleSection: {
    marginBottom: rem(16),
  },
  articleTitle: {
    fontSize: rem(24),
    lineHeight: rem(36),
    fontWeight: 700,
    color: theme.colors.decaNavy[5],
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'pre-line',
  },
  articleHeader: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: rem(24),
  },
  articleContent: {
    minHeight: rem(200),
  },
  articleEditButton: {
    borderColor: theme.colors.decaLight[5],
    color: theme.colors.decaGrey[6],
    svg: {
      color: theme.colors.decaGrey[6],
      width: rem(20),
      height: rem(20),
    },
  },
  articleFormFieldLabel: {
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    flexDirection: 'column',
    gap: rem(4),
    marginBottom: rem(16),
  },
  articleErrorMessage: {
    fontSize: rem(12),
    marginTop: `${rem(4)} !important`,
  },
  articleNotFound: {
    marginTop: rem(40),
  },
  articleBadge: {
    minWidth: rem(120),
  },
}));

export const useArticleViewerStyles = createStyles(
  (_, { drawerWidth, withSidebar = true }: { drawerWidth: number; withSidebar?: boolean }) => ({
    articleDrawer: {
      '& .mantine-Drawer-content': {
        minWidth: rem(drawerWidth),
        width: rem(drawerWidth),
      },
    },
    drawerFullView: {
      '& .mantine-Drawer-content': { paddingTop: 0, paddingBottom: 0 },
      '& .mantine-Drawer-body': { padding: 0, height: '100%', overflowY: 'visible' },
      '& .drawer-header-actions-bar': {
        width: withSidebar ? `calc(100% - ${rem(400)})` : '100%',
      },
    },
    bodyFullView: {
      paddingRight: withSidebar ? rem(400) : 0,
      paddingLeft: rem(48),
      minHeight: '100%',
    },
  })
);
