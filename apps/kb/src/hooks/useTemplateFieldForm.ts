import { templateFieldSchema } from '@/schemas/template';
import { KBTemplateDataTypeEnum, type KBTemplateField } from '@/types/template';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslate } from '@tolgee/react';
import { type SubmitHandler, useForm } from 'react-hook-form';

type TemplateFieldFormInputs = {
  title: string;
  description?: string;
  dataType: KBTemplateDataTypeEnum;
};

interface UseTemplateFieldFormProps {
  currentTemplateField?: KBTemplateField;
  onSubmitted?: (data: TemplateFieldFormInputs) => void;
}

export const useTemplateFieldForm = ({
  currentTemplateField,
  onSubmitted,
}: UseTemplateFieldFormProps) => {
  const { t } = useTranslate('common');
  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
    trigger,
    getValues,
    watch,
  } = useForm({
    defaultValues: {
      title: currentTemplateField?.title || '',
      description: currentTemplateField?.description || '',
      dataType: currentTemplateField?.dataType || KBTemplateDataTypeEnum.text,
    },
    mode: 'onBlur',
    resolver: zodResolver(templateFieldSchema(t)),
  });

  const onSubmit: SubmitHandler<TemplateFieldFormInputs> = (data) => {
    onSubmitted?.(data);
  };

  return {
    handleSubmit: handleSubmit(onSubmit),
    onSubmit,
    control,
    reset,
    trigger,
    getValues,
    errors,
    watch,
  };
};
