import { SearchEngine, type SearchEngineLanguage } from '@/services/SearchEngine';
import { SearchAPI } from '@/services/api/v2';
import type { SearchHistory, User } from '@/types';
import { Badge, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useSetDefaultLang } from '@resola-ai/ui/hooks';
import { IconSearch } from '@tabler/icons-react';
import { useCallback, useEffect, useState } from 'react';

const NUMBER_OF_SEARCH_HISTORY_OPTIONS = 2;

const useSearchStyles = createStyles((theme) => ({
  searchOption: {
    borderRadius: rem(6),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: rem(8),
    '&:hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
  },
  searchIcon: {
    color: theme.colors.decaGrey[6],
  },
  searchBadge: {
    backgroundColor: theme.colors.decaBlue[1],
    color: theme.colors.decaGrey[9],
    fontSize: rem(12),
    textTransform: 'none',
    fontWeight: 400,
    padding: rem(2),
    cursor: 'pointer',
  },
  searchUserOption: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'flex-start',
    gap: rem(8),
    '&:hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
  },
}));

interface SearchSuggestion {
  key: string;
  value: string;
  label: string;
}
const SUPPORTED_SEARCH_HISTORY = false;
const SUPPORTED_SEARCH_USERS = false;
export const useKbSearch = () => {
  const { classes } = useSearchStyles();
  const lang = useSetDefaultLang();

  const searchEngine = new SearchEngine(lang as SearchEngineLanguage);
  const [searchUsers, setSearchUsers] = useState<User[]>([]);
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([]);

  const adjustSearchQuery = useCallback(
    (keyword: string) => {
      return searchEngine.adjustQuery(keyword);
    },
    [searchEngine]
  );

  const getSuggestions = useCallback((keyword: string) => {
    const suggestions = searchEngine.getSuggestions(keyword);

    const searchSuggestions: SearchSuggestion[] = suggestions.map((suggestion) => ({
      key: suggestion.value,
      value: suggestion.value,
      label: suggestion.text,
    }));

    return searchSuggestions;
  }, []);

  /**
   * Render search options
   * @param keyword
   * @param OptionComponent: by default is Combobox.Option
   * @returns void
   */
  const renderSearchOptions = useCallback(
    (suggestions: SearchSuggestion[], OptionComponent: React.ComponentType<any>) => {
      return suggestions.map((suggestion) => (
        <OptionComponent
          key={suggestion.key}
          label={suggestion.label}
          className={classes.searchOption}
          value={suggestion.value}
        >
          <IconSearch size={16} className={classes.searchIcon} />
          <Badge classNames={{ root: classes.searchBadge }} color='decaBlue.1' radius='sm'>
            {suggestion.label}
          </Badge>
        </OptionComponent>
      ));
    },
    [getSuggestions]
  );

  /**
   * Render search history options
   * @param keyword
   * @param OptionComponent: by default is Combobox.Option
   * @returns void
   */
  const renderSearchHistoryOptions = useCallback(
    (_keyword: string, OptionComponent: React.ComponentType<any>) => {
      if (!searchHistory.length) return [];

      const searchHistorySuggestions = searchHistory
        .slice(-NUMBER_OF_SEARCH_HISTORY_OPTIONS)
        .map((history) => ({
          key: history.id,
          value: history.id,
          label: history.metadata?.query,
        }));

      return searchHistorySuggestions.map((suggestion) => (
        <OptionComponent
          className={classes.searchUserOption}
          key={suggestion.key}
          value={suggestion.value}
        >
          <Text>{suggestion.label}</Text>
        </OptionComponent>
      ));
    },
    [searchHistory]
  );

  /**
   * Fetch search history
   * @returns void
   */
  const fetchSearchHistory = useCallback(async () => {
    const searchHistory = await SearchAPI.searchHistory();
    if (!searchHistory?.data) return [];

    setSearchHistory(searchHistory.data);
  }, [setSearchHistory]);

  /**
   * Fetch search users
   * @returns void
   */
  const fetchSearchUsers = useCallback(async () => {
    const users = await SearchAPI.getSearchUsers();
    if (!users?.data) return [];

    setSearchUsers(users.data);
  }, [setSearchUsers]);

  useEffect(() => {
    if (SUPPORTED_SEARCH_HISTORY) fetchSearchHistory();
  }, [fetchSearchHistory]);

  useEffect(() => {
    if (SUPPORTED_SEARCH_USERS) fetchSearchUsers();
  }, [fetchSearchUsers]);

  return {
    searchHistory,
    searchUsers,
    renderSearchOptions,
    renderSearchHistoryOptions,
    adjustSearchQuery,
    getSuggestions,
  };
};
