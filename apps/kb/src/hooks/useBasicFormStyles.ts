import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

export const useBasicFormStyles = () => {
  const { classes } = createStyles((theme) => ({
    root: {
      display: 'flex',
      justifyContent: 'flex-start',
      alignItems: 'flex-start',
      flexDirection: 'column',
      gap: theme.spacing.md,
      '.mantine-InputWrapper-root': {
        width: '100%',
        display: 'flex',
        justifyContent: 'flex-start',
        alignItems: 'flex-start',

        '&.mantine-TextInput-root, &.mantine-Textarea-root': {
          flexDirection: 'column',
        },
      },
      '.mantine-InputWrapper-label': {
        width: rem('30%'),
        minWidth: rem('30%'),
        marginRight: theme.spacing.xs,
        color: theme.colors.decaNavy[5],
        fontSize: theme.fontSizes.md,
      },
      '.mantine-Input-wrapper': {
        width: '70%',
      },
      '.mantine-InputWrapper-error': {
        width: '70%',
        display: 'flex',
      },
    },
    inputInner: {
      width: '70%',
      '.mantine-Input-wrapper': {
        width: '100%',
      },
    },
    buttonGroup: {
      width: '100%',
      display: 'flex',
      justifyContent: 'flex-end',
      alignItems: 'center',
      paddingTop: theme.spacing.md,
    },
    typeGroup: {
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
    },
    typeLabel: {
      fontWeight: 500,
      fontSize: theme.fontSizes.sm,
      color: theme.colors.decaNavy[5],
    },
    typeDescription: {
      fontSize: theme.fontSizes.sm,
      color: theme.colors.decaGrey[6],
    },
    iconBox: {
      width: rem(32),
      height: rem(32),
      borderRadius: rem('50%'),
      backgroundColor: theme.colors.decaLight[1],
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      '& svg': {
        color: theme.colors.decaGrey[3],
        width: rem(18),
        height: rem(18),
      },
    },
    accessLevelRadio: {
      marginTop: rem(6),
    },
  }))();

  return { classes };
};
