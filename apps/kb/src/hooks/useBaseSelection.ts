import { ROOT_PATH } from '@/constants/folder';
import { DEFAULT_KB_RETRIEVE_LIMIT } from '@/constants/kb';
import { KbAPI } from '@/services/api/v2';
import { KBDirectionQueryEnum, type KnowledgeBase } from '@/types';
import type { IBaseSelectionState } from '@/types/selection';
import { sortByCreatedAt } from '@/utils/dateTime';
import { useCallback, useState } from 'react';

const initialState: IBaseSelectionState = {
  bases: [],
  selectedBase: '',
  basesLoading: false,
  basesLoadingMore: false,
  payloadCapture: {
    parentDirId: ROOT_PATH,
    direction: KBDirectionQueryEnum.Backward,
    pagination: { hasNextPage: false, hasPreviousPage: false, first: '', last: '' },
  },
};

export const useBaseSelection = () => {
  const [state, setState] = useState<IBaseSelectionState>(initialState);

  const getKnowledgeBase = useCallback(async (baseId: string) => {
    const response = await KbAPI.get(baseId);
    return response;
  }, []);

  const fetchBases = useCallback(async (parentDirId: string) => {
    setState((prev) => ({ ...prev, basesLoading: true }));
    const response = await KbAPI.getList(parentDirId);

    if (response?.status === 'success') {
      setState((prev) => ({
        ...prev,
        bases: sortByCreatedAt(response.data || []),
        basesLoading: false,
        payloadCapture: { ...prev.payloadCapture, parentDirId, pagination: response.pagination },
      }));
    } else {
      setState((prev) => ({ ...prev, basesLoading: false }));
    }

    return response;
  }, []);

  const fetchMoreKnowledgeBases = useCallback(async () => {
    const { parentDirId, direction, pagination } = state.payloadCapture;
    if (!pagination?.hasPreviousPage || !parentDirId) return;

    const cursor =
      direction === KBDirectionQueryEnum.Backward ? pagination?.first : pagination?.last;

    setState((prev) => ({ ...prev, basesLoadingMore: true }));

    const response = await KbAPI.getList(
      parentDirId,
      cursor,
      KBDirectionQueryEnum.Backward,
      DEFAULT_KB_RETRIEVE_LIMIT
    );

    if (response) {
      setState((prev) => ({
        ...prev,
        basesLoadingMore: false,
        payloadCapture: {
          ...prev.payloadCapture,
          pagination: response.pagination,
        },
        bases: [...prev.bases, ...(sortByCreatedAt(response.data) || [])],
      }));
    } else {
      setState((prev) => ({ ...prev, basesLoadingMore: false }));
    }
  }, [state.payloadCapture]);

  const setSelectedBase = useCallback((kbId: string) => {
    setState((prev) => ({ ...prev, selectedBase: kbId }));
  }, []);

  const setBases = useCallback((bases: KnowledgeBase[]) => {
    setState((prev) => ({ ...prev, bases }));
  }, []);

  return {
    baseSelection: state,
    getKnowledgeBase,
    fetchBases,
    fetchMoreKnowledgeBases,
    setBases,
    setSelectedBase,
  };
};
