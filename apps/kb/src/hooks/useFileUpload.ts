import { FileUploader } from '@/services/FileUploader';
import type {
  ExtraDataFile,
  FileChangeStatus,
  FileUploaderInstance,
  PresignedURLResult,
} from '@/types';
import { useState } from 'react';

/**
 * Create uploaders for each file and return a record of uploaders
 * @param files - Array of files to upload
 * @param presignedRecords - Array of presigned URL records
 * @return Record of uploaders
 */
const createUploaders = (
  files: File[],
  presignedRecords: PresignedURLResult[],
  extraDataFiles?: ExtraDataFile[]
) => {
  const uploaders = {};
  for (let fileIndex = 0; fileIndex < files.length; fileIndex++) {
    const file = files[fileIndex];
    const fileRecord = presignedRecords[fileIndex].fileRecord;

    // Use the presigned URL to upload the file
    if (fileRecord && file) {
      const fileId = presignedRecords[fileIndex].fileRecord.id;
      const extraDataFile = extraDataFiles?.find(
        (extraDataFile) => extraDataFile.name === file.name
      );
      const uploader = new FileUploader(fileId, fileRecord.metadata.uploadUrl, file, extraDataFile);
      uploaders[fileId] = uploader;
    }
  }

  return uploaders;
};

/**
 * Hook to handle file upload
 * @return FileUploadResult
 * @property isUploading - Boolean to indicate if the files are being uploaded
 * @property error - Error message if any
 * @property uploaders - Record of uploaders
 * @property uploadFiles - Function to upload files
 * @example
 * const { isUploading, error, uploadFiles, uploaders } = useFileUpload();
 * uploadFiles('kbId', [file1, file2]);
 */
interface FileUploadResult {
  isUploading: boolean;
  error: string | null;
  uploaders: Record<string, FileUploaderInstance>;
  uploadFiles: ({
    files,
    extraDataFiles,
    getPresignedUrl,
    onCompleted,
    onError,
  }: {
    files: File[];
    extraDataFiles?: ExtraDataFile[];
    getPresignedUrl: (
      files: File[],
      extraDataFiles?: ExtraDataFile[]
    ) => Promise<PresignedURLResult[]>;
    onCompleted?: (fileChangeStatus: FileChangeStatus) => void;
    onError?: (fileChangeStatus: FileChangeStatus) => void;
  }) => Promise<void>;
  validateFiles: (
    files: File[],
    validate?: { maxSize?: number }
  ) => {
    message: string;
    expandData?: {
      maxSize?: number;
    };
  } | null;
}

type UploadEventHandler = (fileChangeStatus: FileChangeStatus) => void;

const createEventHandler = (
  baseHandler: UploadEventHandler,
  additionalHandler?: UploadEventHandler
) => {
  return (fileChangeStatus: FileChangeStatus) => {
    baseHandler(fileChangeStatus);
    additionalHandler?.(fileChangeStatus);
  };
};

export const useFileUpload = (): FileUploadResult => {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploaders, setUploaders] = useState({} as Record<string, FileUploaderInstance>);

  /**
   * Sync the uploader status with the state
   * @param fileId - File ID
   * @param status - File upload status
   * @param percentage - File upload percentage
   */
  const syncUploaderStatus = ({ fileId, percentage, status }: FileChangeStatus) => {
    setUploaders((prev) => {
      if (!prev[fileId]) return prev;
      const currentUploader = { ...prev[fileId] };

      return {
        ...prev,
        [fileId]: { ...currentUploader, status, percentage },
      };
    });
  };

  const validateFiles = (
    files: File[],
    validate?: { maxSize?: number }
  ): {
    message: string;
    expandData?: {
      maxSize?: number;
    };
  } | null => {
    let message = '';
    let expandData:
      | {
          maxSize?: number;
        }
      | undefined = undefined;

    if (validate?.maxSize && files.length && error === null) {
      const maxSize = validate.maxSize;
      const maxSizeInBytes = maxSize * 1024 * 1024;
      files.forEach((file) => {
        if (file.size > maxSizeInBytes) {
          message = 'error.fileSize';
          expandData = {
            maxSize: maxSize,
          };
        }
      });
    }

    return message ? { message, expandData } : null;
  };

  /**
   * Upload files to the server
   * @param files - Array of files to upload
   * @param onCompleted - Callback function to be called when the files are uploaded
   * @param parentDirId - Parent directory ID
   */
  const uploadFiles = async ({
    files,
    extraDataFiles,
    getPresignedUrl,
    onStart,
    onCompleted,
    onError,
  }: {
    files: File[];
    extraDataFiles?: ExtraDataFile[];
    getPresignedUrl: (
      files: File[],
      extraDataFiles?: ExtraDataFile[]
    ) => Promise<PresignedURLResult[]>;
    onStart?: (fileChangeStatus: FileChangeStatus) => void;
    onCompleted?: (fileChangeStatus: FileChangeStatus) => void;
    onError?: (fileChangeStatus: FileChangeStatus) => void;
  }) => {
    setIsUploading(true);
    setError(null);

    try {
      // Call the API function to get the presigned URL
      const uploadResults = await getPresignedUrl(files, extraDataFiles);
      if (!uploadResults || !uploadResults.length) {
        return setError('An error occurred while getting the presigned URL.');
      }

      // Upload each file using the presigned URL
      const uploaders = createUploaders(files, uploadResults, extraDataFiles);
      setUploaders((prev) => ({ ...prev, ...uploaders }));

      const eventHandlers = {
        onStart: createEventHandler(syncUploaderStatus, onStart),
        onCompleted: createEventHandler(syncUploaderStatus, onCompleted),
        onError: createEventHandler(syncUploaderStatus, onError),
        onUploadProgress: createEventHandler(syncUploaderStatus),
        onTimeout: createEventHandler(syncUploaderStatus, onError),
      };
      // Start uploading the files
      await Promise.all(
        Object.values(uploaders).map((uploader: any) => {
          uploader?.upload(eventHandlers);
        })
      );
    } catch (err) {
      setError('An error occurred while uploading the file.');
    } finally {
      setIsUploading(false);
    }
  };

  return { isUploading, error, uploadFiles, uploaders, validateFiles };
};

export default useFileUpload;
