import { FolderAPI } from '@/services/api/v2';
import type { Folder } from '@/types';
import type { IFolderSelectionState } from '@/types/selection';
import unionWith from 'lodash/unionWith';
import { useCallback, useState } from 'react';

const initialState: IFolderSelectionState = {
  folders: [],
  selectedFolder: '',
  foldersLoading: false,
};

export const useFolderSelection = () => {
  const [state, setState] = useState<IFolderSelectionState>(initialState);

  const sortFolders = (folders: Folder[]) => {
    return folders.sort(
      (folderA: Folder, folderB: Folder) =>
        new Date(folderA.createdAt).getTime() - new Date(folderB.createdAt).getTime()
    );
  };

  const unionFolders = (newFolders: Folder[], prevFolders: Folder[]) => {
    return unionWith(newFolders, prevFolders, (folderA, folderB) => folderA.id === folderB.id);
  };

  const fetchFolders = useCallback(async (parentDirId: string, depth = 2) => {
    setState((prev) => ({ ...prev, foldersLoading: true }));
    const response = await FolderAPI.getAll(parentDirId, depth);

    if (response?.status === 'success') {
      setState((prev) => ({
        ...prev,
        folders: sortFolders(unionFolders(response.data ?? [], prev.folders)),
        foldersLoading: false,
      }));
    } else {
      setState((prev) => ({ ...prev, foldersLoading: false }));
    }

    return response;
  }, []);

  const setSelectedFolder = useCallback((folderId: string) => {
    setState((prev) => ({ ...prev, selectedFolder: folderId }));
  }, []);

  const setFolders = useCallback((folders: Folder[]) => {
    setState((prev) => ({ ...prev, folders }));
  }, []);

  return {
    folderSelection: state,
    fetchFolders,
    setSelectedFolder,
    setFolders,
  };
};
