import { MAX_ARTICLE_CONTENT_LENGTH, MAX_ARTICLE_TITLE_LENGTH } from '@/constants/kb';
import { isIncludedBlockNoteMediaHTML } from '@resola-ai/blocknote-editor';
import { useTranslate } from '@tolgee/react';
import { useState } from 'react';

interface ArticleValidationState {
  hasEmptyTitle: boolean;
  hasEmptyContent: boolean;
  hasTitleLengthError: boolean;
  hasContentLengthError: boolean;
}

export interface ArticleValidationErrors {
  title?: string;
  content?: string;
}

interface ValidateArticleParams {
  title?: string;
  content?: string;
  contentRaw?: string;
}

export const useArticleValidation = () => {
  const { t } = useTranslate('article');
  const [errors, setErrors] = useState<ArticleValidationErrors>({});

  const validateArticleContent = ({
    title,
    content,
    contentRaw,
  }: ValidateArticleParams): ArticleValidationState => {
    const validationState: ArticleValidationState = {
      hasEmptyTitle: false,
      hasEmptyContent: false,
      hasTitleLengthError: false,
      hasContentLengthError: false,
    };

    const updateErrors = (field: keyof ArticleValidationErrors, message?: string) => {
      setErrors((prev) => ({ ...prev, [field]: message }));
    };

    // Validate title
    if (!title?.trim()) {
      validationState.hasEmptyTitle = true;
      updateErrors('title', t('errors.titleRequired'));
    } else if (title.length > MAX_ARTICLE_TITLE_LENGTH) {
      validationState.hasTitleLengthError = true;
      updateErrors('title', t('errors.longTitle', { maxLength: MAX_ARTICLE_TITLE_LENGTH }));
    } else {
      updateErrors('title');
    }

    // Validate content
    const hasMediaContent = isIncludedBlockNoteMediaHTML(contentRaw ?? '');
    const isContentEmpty = !content?.trim() && !hasMediaContent;

    if (isContentEmpty) {
      validationState.hasEmptyContent = true;
      updateErrors('content', t('errors.contentRequired'));
    } else if (content && content.length > MAX_ARTICLE_CONTENT_LENGTH) {
      validationState.hasContentLengthError = true;
      updateErrors('content', t('errors.longContent', { maxLength: MAX_ARTICLE_CONTENT_LENGTH }));
    } else {
      updateErrors('content');
    }

    return validationState;
  };

  return {
    validateArticleContent,
    articleErrors: errors,
  };
};
