import { qnaSchema } from '@/schemas';
import type { QnAStatus } from '@/types/qna';
import { zodResolver } from '@hookform/resolvers/zod';
import { type SubmitHandler, useForm } from 'react-hook-form';

type QnAFormInputs = {
  question: string;
  answer: string;
  answerRaw: string;
  status: QnAStatus;
};

export const useQnAManualForm = ({ currentQnA, onSubmitted }) => {
  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
    trigger,
    getValues,
    setValue,
  } = useForm({
    defaultValues: currentQnA,
    mode: 'onBlur',
    resolver: zodResolver(qnaSchema),
  });

  const onSubmit: SubmitHandler<QnAFormInputs> = (data) => {
    onSubmitted?.(data);
  };

  return {
    handleSubmit: handleSubmit(onSubmit),
    onSubmit,
    control,
    reset,
    trigger,
    getValues,
    errors,
    setValue,
  };
};
