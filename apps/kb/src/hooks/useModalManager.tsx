import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useCallback } from 'react';

import { ModalTitleWithCloseButton } from '@/components';

// Type the style object for better type safety
type ModalStylesProps = {
  root: string;
  content: string;
  title: string;
  close: string;
  largeModal: string;
  extraLargeModal: string;
  mediumModal: string;
  confirmModal: string;
};

const useModalStyles = createStyles((theme) => ({
  root: {
    [`@media (max-height: ${rem(800)})`]: {
      '& .mantine-Modal-inner': {
        paddingTop: rem(100),
      },
    },
    '& .mantine-Modal-body': {
      paddingTop: `${theme.spacing.md} !important`,
    },
  },
  content: {
    width: rem(600),
    padding: rem(10),
    borderRadius: rem(20),
    flex: 'unset',
    '& .mantine-Modal-title': {
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      width: '100%',
    },
  },
  title: {
    color: theme.colors.decaNavy[5],
    fontSize: rem(20),
    fontWeight: 700,
  },
  close: {
    color: theme.colors.decaNavy[5],
    fontSize: rem(20),
  },
  largeModal: {
    width: rem(980),
    minWidth: rem(980),
  },
  extraLargeModal: {
    width: rem(1200),
    minWidth: rem(1200),
  },
  mediumModal: {
    width: rem(595),
    minWidth: rem(595),
  },
  confirmModal: {
    '& .mantine-Modal-inner': {
      zIndex: 201,
    },
    '& .mantine-Overlay-root': {
      zIndex: 201,
    },
    '& .mantine-Modal-body .mantine-Group-root': {
      display: 'flex',
    },
  },
}));

// Move interface outside of the hook scope
interface CreateModalProps {
  title: string;
  children: React.ReactNode;
  classNames?: Partial<Record<keyof ModalStylesProps, string>>;
  onClose: () => void;
}

// Add return type for better type safety
export const useModalManager = () => {
  const { cx, classes } = useModalStyles();

  const createModal = useCallback(
    ({ title, children, classNames, onClose }: CreateModalProps) => ({
      title: <ModalTitleWithCloseButton title={title} onClose={onClose} />,
      centered: true,
      withCloseButton: false,
      classNames: {
        root: cx(classes.root, classNames?.root),
        content: cx(classes.content, classNames?.content),
        title: cx(classes.title, classNames?.title),
        close: cx(classes.close, classNames?.close),
      },
      children,
    }),
    [classes, cx]
  );

  return {
    cx,
    modalClasses: classes,
    createModal,
  } as const;
};
