import { DEFAULT_KB_RETRIEVE_LIMIT } from '@/constants/kb';
import { DocumentAPI } from '@/services/api/v2';
import { type DocumentFile, KBDirectionQueryEnum } from '@/types';
import type { IDocumentSelectionState } from '@/types/selection';
import { useCallback, useEffect, useState } from 'react';

const initialState: IDocumentSelectionState = {
  documents: [],
  selectedDocuments: [],
  documentsLoading: false,
  documentsLoadingMore: false,
  payloadCapture: {
    parentDirId: '',
    direction: KBDirectionQueryEnum.Backward,
    pagination: { hasNextPage: false, hasPreviousPage: false, first: '', last: '' },
  },
};

export const useDocumentSelection = (documentIds?: string[]) => {
  const [state, setState] = useState<IDocumentSelectionState>(initialState);

  useEffect(() => {
    if (documentIds?.length) {
      setState((prev) => ({ ...prev, selectedDocuments: documentIds }));
    }
  }, [documentIds]);

  const fetchDocuments = useCallback(async (parentDirId: string) => {
    setState((prev) => ({ ...prev, documentsLoading: true }));

    const response = await DocumentAPI.getDocuments(parentDirId);

    if (response?.status === 'success') {
      setState((prev) => ({
        ...prev,
        documents: response.data,
        documentsLoading: false,
        payloadCapture: {
          ...prev.payloadCapture,
          parentDirId,
          pagination: response.pagination,
        },
      }));
    } else {
      setState((prev) => ({ ...prev, documentsLoading: false }));
    }

    return response;
  }, []);

  const fetchMoreDocuments = useCallback(async () => {
    const { parentDirId, direction, pagination } = state.payloadCapture;
    if (!pagination?.hasPreviousPage || !parentDirId) return;

    const cursor =
      direction === KBDirectionQueryEnum.Backward ? pagination?.first : pagination?.last;

    setState((prev) => ({ ...prev, documentsLoadingMore: true }));

    const response = await DocumentAPI.getDocuments(
      parentDirId,
      cursor,
      direction,
      DEFAULT_KB_RETRIEVE_LIMIT
    );

    if (response) {
      setState((prev) => ({
        ...prev,
        documentsLoadingMore: false,
        payloadCapture: {
          ...prev.payloadCapture,
          pagination: response.pagination,
        },
        documents: [...prev.documents, ...response.data],
      }));
    }

    return response;
  }, [state.payloadCapture]);

  const setSelectedDocument = useCallback((documentId: string) => {
    setState((prev) => {
      if (prev.selectedDocuments.includes(documentId)) {
        return {
          ...prev,
          selectedDocuments: prev.selectedDocuments.filter((id) => id !== documentId),
        };
      }
      return {
        ...prev,
        selectedDocuments: [...prev.selectedDocuments, documentId],
      };
    });
  }, []);

  const selectAllDocuments = useCallback(() => {
    setState((prev) => ({
      ...prev,
      selectedDocuments: prev.documents.map((document) => document.id),
    }));
  }, []);

  const clearDocumentSelection = useCallback(() => {
    setState((prev) => ({
      ...prev,
      selectedDocuments: [],
    }));
  }, []);

  const setDocuments = useCallback((documents: DocumentFile[]) => {
    setState((prev) => ({ ...prev, documents }));
  }, []);

  const isDocumentSelected = useCallback(
    (documentId: string) => {
      return state.selectedDocuments.includes(documentId);
    },
    [state.selectedDocuments]
  );

  return {
    documentSelection: state,
    fetchDocuments,
    fetchMoreDocuments,
    setDocuments,
    setSelectedDocument,
    selectAllDocuments,
    clearDocumentSelection,
    isDocumentSelected,
  };
};
