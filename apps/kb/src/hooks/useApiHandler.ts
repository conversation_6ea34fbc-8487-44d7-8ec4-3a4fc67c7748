import { AppConfig } from '@/configs';
import { API_RESPONSE_STATUS } from '@/constants/api';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { useNotifications } from '@/hooks/useNotifications';
import { EntityType, EntityTypePathMap } from '@/types/entities';
import { HTTP_ERROR_STATUS } from '@resola-ai/shared-constants';
import { useTranslate } from '@tolgee/react';
import type { AxiosError, AxiosResponse } from 'axios';
import { useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

interface ApiErrorResponse {
  status: string;
  statusCode: number;
  message: string;
  entityType?: EntityType;
  error?: AxiosError;
}

interface ServerErrorResponse {
  message?: string;
  [key: string]: any;
}

interface ApiRequestOptions {
  fallbackMessage?: string;
  fallbackTitle?: string;
  successMessage?: string;
  successTitle?: string;
  successCallback?: () => void;
  errorCallback?: () => void;
  options?: {
    responseType?: 'arraybuffer' | 'blob' | 'document' | 'json' | 'text';
    [key: string]: any;
  };
}

const REDIRECT_ENTITY_TYPES_WHEN_NOT_FOUND = [
  EntityType.DOCUMENT,
  EntityType.FOLDER,
  EntityType.BASE,
  EntityType.JOB,
  EntityType.TEMPLATE,
];

/**
 * Custom hook for handling API requests and errors consistently across the application
 * Provides error handling, notifications, and navigation for API requests
 */
export const useApiHandler = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslate('common');
  const { notifyMessage, notifyError } = useNotifications(t);
  const { handleError } = useErrorHandler();

  /**
   * Checks if the error is a 404 Not Found error
   */
  const isResourceNotFoundError = useCallback((error: AxiosError): boolean => {
    return error.response?.status === HTTP_ERROR_STATUS.NOT_FOUND;
  }, []);

  /**
   * Determines the entity type (Document, Folder, etc.) from the API URL
   * Used for proper error handling and navigation
   */
  const parseEntityTypeFromUrl = useCallback((url: string): EntityType | undefined => {
    if (url.includes(EntityTypePathMap.document)) return EntityType.DOCUMENT;
    if (url.includes(EntityTypePathMap.folder)) return EntityType.FOLDER;
    if (url.includes(EntityTypePathMap.base)) return EntityType.BASE;
    if (url.includes(EntityTypePathMap.job)) return EntityType.JOB;
    if (url.includes(EntityTypePathMap.template)) return EntityType.TEMPLATE;
    if (url.includes(EntityTypePathMap.article)) return EntityType.ARTICLE;

    return undefined;
  }, []);

  /**
   * Processes API errors and handles:
   * 1. Error message extraction and translation
   * 2. User notification
   * 3. Navigation to 404 page for not found resources
   * 4. Returns standardized error response
   */
  const processApiError = useCallback(
    (
      error: AxiosError,
      options?: {
        fallbackMessage?: string;
        fallbackTitle?: string;
      }
    ): ApiErrorResponse => {
      // Extract entity type from URL if available
      const entityType = error.config?.url ? parseEntityTypeFromUrl(error.config.url) : undefined;

      // Parse error response data
      const errorResponse = error.response?.data as ServerErrorResponse;

      // Determine error message with fallback chain
      const errorMessage =
        options?.fallbackMessage ??
        errorResponse?.message ??
        t(`apiErrors.${error.response?.status}.message`);

      // Determine error title with fallback
      const errorTitle = options?.fallbackTitle ?? t(`apiErrors.${error.response?.status}.title`);

      // Construct standardized API error response
      const apiError: ApiErrorResponse = {
        status: API_RESPONSE_STATUS.ERROR,
        statusCode: error.response?.status ?? HTTP_ERROR_STATUS.INTERNAL_SERVER_ERROR,
        message: errorMessage,
        entityType,
        error,
      };

      // Display error notification to user
      notifyError(errorTitle, errorMessage);

      // Redirect to appropriate 404 page if resource is not found
      if (
        isResourceNotFoundError(error) &&
        entityType &&
        REDIRECT_ENTITY_TYPES_WHEN_NOT_FOUND.includes(entityType)
      ) {
        const fromUrl = `${location.pathname}${location.search}`;
        navigate(`${AppConfig.BASE_PATH}404/${entityType}?fromUrl=${fromUrl}`);
      }

      // Handle conflict errors in article collection
      if (error.response?.status === HTTP_ERROR_STATUS.CONFLICT) {
        const errorCode = errorResponse.error?.code ?? '';
        handleError({ code: errorCode });
      }

      return apiError;
    },
    [navigate, parseEntityTypeFromUrl, notifyError, isResourceNotFoundError]
  );

  /**
   * Wrapper for API requests that provides consistent error handling
   * @param promise - The axios request promise to be handled
   * @returns The response data of type T
   * @throws ApiErrorResponse if the request fails
   */
  const handleApiRequest = useCallback(
    async <T>(promise: Promise<AxiosResponse<T>>, options?: ApiRequestOptions): Promise<T> => {
      try {
        const response = await promise;
        if (options?.successMessage && options?.successTitle) {
          notifyMessage(options.successTitle, options.successMessage);
        }

        options?.successCallback?.();
        return response.data;
      } catch (error) {
        const apiError = processApiError(error as AxiosError, options);

        options?.errorCallback?.();
        throw apiError;
      }
    },
    [processApiError, notifyMessage]
  );

  return {
    API_RESPONSE_STATUS,
    handleApiRequest,
  };
};
