import { DEFAULT_KB_RETRIEVE_LIMIT } from '@/constants/kb';
import { ArticleAPI } from '@/services/api/v2';
import { type Article, KBDirectionQueryEnum } from '@/types';
import type { IArticleSelectionState } from '@/types/selection';
import { useCallback, useEffect, useState } from 'react';

const initialState: IArticleSelectionState = {
  articles: [],
  selectedArticles: [],
  articlesLoading: false,
  articlesLoadingMore: false,
  payloadCapture: {
    kbId: '',
    direction: KBDirectionQueryEnum.Backward,
    pagination: { hasNextPage: false, hasPreviousPage: false, first: '', last: '' },
  },
};

export const useArticleSelection = (articleIds?: string[]) => {
  const [state, setState] = useState<IArticleSelectionState>(initialState);

  useEffect(() => {
    if (articleIds?.length) {
      setState((prev) => ({ ...prev, selectedArticles: articleIds }));
    }
  }, [articleIds]);

  const fetchArticles = useCallback(async (kbId: string) => {
    setState((prev) => ({ ...prev, articlesLoading: true }));

    const response = await ArticleAPI.getCollection(
      kbId,
      DEFAULT_KB_RETRIEVE_LIMIT,
      KBDirectionQueryEnum.Backward
    );

    if (response?.status === 'success') {
      setState((prev) => ({
        ...prev,
        articles: response.data,
        articlesLoading: false,
        payloadCapture: {
          ...prev.payloadCapture,
          kbId,
          pagination: response.pagination,
        },
      }));
    } else {
      setState((prev) => ({ ...prev, articlesLoading: false }));
    }

    return response;
  }, []);

  const fetchMoreArticles = useCallback(async () => {
    const { kbId, direction, pagination } = state.payloadCapture;
    if (!pagination?.hasPreviousPage || !kbId) return;

    const cursor =
      direction === KBDirectionQueryEnum.Backward ? pagination?.first : pagination?.last;

    setState((prev) => ({ ...prev, articlesLoadingMore: true }));

    const response = await ArticleAPI.getCollection(
      kbId,
      DEFAULT_KB_RETRIEVE_LIMIT,
      direction,
      cursor
    );

    if (response) {
      setState((prev) => ({
        ...prev,
        articlesLoadingMore: false,
        payloadCapture: {
          ...prev.payloadCapture,
          pagination: response.pagination,
        },
        articles: [...prev.articles, ...response.data],
      }));
    }
  }, [state.payloadCapture]);

  const setSelectedArticle = useCallback((articleId: string) => {
    setState((prev) => {
      if (prev.selectedArticles.includes(articleId)) {
        return {
          ...prev,
          selectedArticles: prev.selectedArticles.filter((id) => id !== articleId),
        };
      }
      return {
        ...prev,
        selectedArticles: [...prev.selectedArticles, articleId],
      };
    });
  }, []);

  const selectAllArticles = useCallback(() => {
    setState((prev) => ({
      ...prev,
      selectedArticles: prev.articles.map((article) => article.id),
    }));
  }, []);

  const clearArticleSelection = useCallback(() => {
    setState((prev) => ({
      ...prev,
      selectedArticles: [],
    }));
  }, []);

  const setArticles = useCallback((articles: Article[]) => {
    setState((prev) => ({ ...prev, articles }));
  }, []);

  const isArticleSelected = useCallback(
    (articleId: string) => {
      return state.selectedArticles.includes(articleId);
    },
    [state.selectedArticles]
  );

  return {
    articleSelection: state,
    fetchArticles,
    fetchMoreArticles,
    setArticles,
    setSelectedArticle,
    selectAllArticles,
    clearArticleSelection,
    isArticleSelected,
  };
};
