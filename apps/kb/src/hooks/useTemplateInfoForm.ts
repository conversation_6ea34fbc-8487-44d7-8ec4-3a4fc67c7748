import { templateSchema } from '@/schemas/template';
import type { KBTemplate } from '@/types/template';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslate } from '@tolgee/react';
import { type SubmitHandler, useForm } from 'react-hook-form';

type TemplateFormInputs = {
  templateTitle: string;
  description: string;
};

interface UseTemplateInfoFormProps {
  currentTemplate?: KBTemplate;
  onSubmitted?: (data: TemplateFormInputs) => void;
}

export const useTemplateInfoForm = ({ currentTemplate, onSubmitted }: UseTemplateInfoFormProps) => {
  const { t } = useTranslate('common');
  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
    trigger,
    getValues,
  } = useForm({
    defaultValues: {
      templateTitle: currentTemplate?.templateTitle || '',
      description: currentTemplate?.description || '',
    },
    mode: 'onBlur',
    resolver: zodResolver(templateSchema(t)),
  });

  const onSubmit: SubmitHandler<TemplateFormInputs> = (data) => {
    onSubmitted?.(data);
  };

  return {
    handleSubmit: handleSubmit(onSubmit),
    onSubmit,
    control,
    reset,
    trigger,
    getValues,
    errors,
  };
};
