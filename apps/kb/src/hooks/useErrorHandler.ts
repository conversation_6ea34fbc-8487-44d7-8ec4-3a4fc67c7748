import { API_ERROR_CODES } from '@/constants/api';
import { useAppContext } from '@/contexts';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';

const useErrorStyles = createStyles(() => ({
  informModal: {
    '& .mantine-Modal-inner': {
      zIndex: 201,
    },
    '& .mantine-Overlay-root': {
      zIndex: 201,
    },
  },
}));

type Error = {
  code: string;
};
type ErrorHandler = (error: Error) => void;

interface ErrorHandlerReturn {
  ERROR_CODES: Record<string, string>;
  handleError: ErrorHandler;
}

export const useErrorHandler = (): ErrorHandlerReturn => {
  const { t } = useTranslate(['article', 'common']);
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const { classes } = useErrorStyles();

  const handleError: ErrorHandler = useCallback((error: Error) => {
    switch (error.code) {
      case API_ERROR_CODES.ARTICLE_NOT_READY_TO_WRITE:
        openConfirmModal({
          onConfirm: () => {
            closeConfirmModal();
          },
          title: t('articleCollection.save.notReady.title'),
          content: t('articleCollection.save.notReady.description'),
          confirmText: t('okBtn', { ns: 'common' }),
          options: {
            className: classes.informModal,
            isShowCancel: false,
          },
        });
        break;

      default:
        break;
    }
  }, []);

  return { ERROR_CODES: API_ERROR_CODES, handleError };
};

export default useErrorHandler;
