import { MantineWrapper } from '@/utils/unitTest';
import { renderHook } from '@testing-library/react';
import { useBasicFormStyles } from '../useBasicFormStyles';

describe('useBasicFormStyles', () => {
  it('should return classes object', () => {
    const { result } = renderHook(() => useBasicFormStyles(), {
      wrapper: MantineWrapper,
    });

    expect(result.current).toBeDefined();
    expect(result.current.classes).toBeDefined();
    expect(result.current.classes.root).toBeDefined();
    expect(result.current.classes.inputInner).toBeDefined();
    expect(result.current.classes.buttonGroup).toBeDefined();
    expect(result.current.classes.typeGroup).toBeDefined();
    expect(result.current.classes.typeLabel).toBeDefined();
    expect(result.current.classes.typeDescription).toBeDefined();
    expect(result.current.classes.iconBox).toBeDefined();
    expect(result.current.classes.accessLevelRadio).toBeDefined();
  });

  it('should have valid class names', () => {
    const { result } = renderHook(() => useBasicFormStyles(), {
      wrapper: MantineWrapper,
    });

    // Check that classes have valid string values
    expect(typeof result.current.classes.root).toBe('string');
    expect(typeof result.current.classes.buttonGroup).toBe('string');
    expect(typeof result.current.classes.typeGroup).toBe('string');
  });
});
