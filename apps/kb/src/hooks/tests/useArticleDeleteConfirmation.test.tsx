import { useArticleDeleteConfirmation } from '@/hooks/useArticleDeleteConfirmation';
import { COMPLETE_MOCK_ARTICLE } from '@/mocks/articleDetailContextMock';
import { renderHook } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Custom test wrapper that doesn't rely on AllTheProviders
const customWrapper = ({ children }) => children;

// Mock for react-i18next
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key, // Just return the key as the translation
  }),
}));

// Setup mocks
vi.mock('@/services/api/v2', () => ({
  ArticleAPI: {
    delete: vi.fn(),
  },
}));

// Mock for AppContext
const mockOpenConfirmModal = vi.fn();
const mockCloseConfirmModal = vi.fn();
vi.mock('@/contexts', () => ({
  useAppContext: () => ({
    openConfirmModal: mockOpenConfirmModal,
    closeConfirmModal: mockCloseConfirmModal,
  }),
}));

// Mock for useApiHandler
const mockHandleApiRequest = vi.fn();
vi.mock('@/hooks', () => ({
  useApiHandler: () => ({
    handleApiRequest: mockHandleApiRequest,
    API_RESPONSE_STATUS: {
      SUCCESS: '200',
    },
  }),
}));

// Mock UploaderContextProvider and dependencies
vi.mock('@/contexts/UploaderContext', () => ({
  UploaderContextProvider: ({ children }) => children,
  useUploader: vi.fn(),
}));

vi.mock('react-router-dom', () => ({
  useNavigate: () => vi.fn(),
  useLocation: () => ({ pathname: '/', search: '', hash: '', state: null }),
  useParams: () => ({ articleId: 'test-article-id' }),
  BrowserRouter: ({ children }) => children,
}));

describe('useArticleDeleteConfirmation', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should expose the confirmThenDeleteArticle function', () => {
    const { result } = renderHook(() => useArticleDeleteConfirmation(), {
      wrapper: customWrapper,
    });

    expect(result.current.confirmThenDeleteArticle).toBeDefined();
    expect(typeof result.current.confirmThenDeleteArticle).toBe('function');
  });

  it('should call openConfirmModal with correct parameters', () => {
    const { result } = renderHook(() => useArticleDeleteConfirmation(), {
      wrapper: customWrapper,
    });

    const onDeletedMock = vi.fn();
    const onLoadingMock = vi.fn();

    result.current.confirmThenDeleteArticle(COMPLETE_MOCK_ARTICLE, onDeletedMock, onLoadingMock);

    expect(mockOpenConfirmModal).toHaveBeenCalledTimes(1);
    expect(mockOpenConfirmModal).toHaveBeenCalledWith(
      expect.objectContaining({
        onConfirm: expect.any(Function),
        onCancel: expect.any(Function),
        title: expect.any(String),
        content: expect.any(String),
        options: expect.objectContaining({
          isRemoving: true,
        }),
      })
    );
  });

  it('should show different confirmation message when article has shortcuts', () => {
    const { result } = renderHook(() => useArticleDeleteConfirmation(), {
      wrapper: customWrapper,
    });

    // Article with shortcuts
    const articleWithShortcuts = {
      ...COMPLETE_MOCK_ARTICLE,
      shortcutArticleIds: ['shortcut-1', 'shortcut-2'],
    };

    result.current.confirmThenDeleteArticle(articleWithShortcuts);

    const confirmModalCall = mockOpenConfirmModal.mock.calls[0][0];
    expect(confirmModalCall.content).toBeDefined();

    // Now test with an article without shortcuts
    mockOpenConfirmModal.mockClear();
    result.current.confirmThenDeleteArticle(COMPLETE_MOCK_ARTICLE);

    const confirmModalCallNoShortcuts = mockOpenConfirmModal.mock.calls[0][0];
    expect(confirmModalCallNoShortcuts.content).toBeDefined();

    // The messages should be different
    expect(confirmModalCall.content).not.toEqual(confirmModalCallNoShortcuts.content);
  });

  it('should handle the cancel action correctly', () => {
    const { result } = renderHook(() => useArticleDeleteConfirmation(), {
      wrapper: customWrapper,
    });

    const onDeletedMock = vi.fn();
    const onLoadingMock = vi.fn();

    result.current.confirmThenDeleteArticle(COMPLETE_MOCK_ARTICLE, onDeletedMock, onLoadingMock);

    // Extract the onCancel function from the mock call
    const onCancel = mockOpenConfirmModal.mock.calls[0][0].onCancel;

    // Execute the cancel function
    onCancel();

    // Verify the expected behaviors on cancel
    expect(mockCloseConfirmModal).toHaveBeenCalledTimes(1);
    expect(onLoadingMock).toHaveBeenCalledWith(false);
    expect(onDeletedMock).not.toHaveBeenCalled();
  });

  it('should handle the confirm action and delete successfully', async () => {
    const { result } = renderHook(() => useArticleDeleteConfirmation(), {
      wrapper: customWrapper,
    });

    const onDeletedMock = vi.fn();
    const onLoadingMock = vi.fn();

    // Mock successful API response
    mockHandleApiRequest.mockImplementation((_promise, options) => {
      options.successCallback?.();
      return Promise.resolve({ status: '200', data: { success: true } });
    });

    result.current.confirmThenDeleteArticle(COMPLETE_MOCK_ARTICLE, onDeletedMock, onLoadingMock);

    // Extract and execute the onConfirm function from the mock call
    const onConfirm = mockOpenConfirmModal.mock.calls[0][0].onConfirm;
    await onConfirm();

    // Verify expected behaviors on successful delete
    expect(onLoadingMock).toHaveBeenCalledWith(true);
    expect(mockCloseConfirmModal).toHaveBeenCalledTimes(1);
    expect(mockHandleApiRequest).toHaveBeenCalledWith(
      expect.any(Promise),
      expect.objectContaining({
        successCallback: expect.any(Function),
        errorCallback: expect.any(Function),
      })
    );
    expect(onDeletedMock).toHaveBeenCalledTimes(1);
    expect(onLoadingMock).toHaveBeenCalledWith(false);
  });

  it('should handle errors when deleting fails', async () => {
    const { result } = renderHook(() => useArticleDeleteConfirmation(), {
      wrapper: customWrapper,
    });

    const onDeletedMock = vi.fn();
    const onLoadingMock = vi.fn();

    // Mock failed API response
    mockHandleApiRequest.mockImplementation((_promise, options) => {
      // Call the error callback but don't reject the promise
      options.errorCallback?.();
      // Return a resolved promise with an error status instead
      return Promise.resolve({ status: 'error', data: null });
    });

    result.current.confirmThenDeleteArticle(COMPLETE_MOCK_ARTICLE, onDeletedMock, onLoadingMock);

    // Extract and execute the onConfirm function from the mock call
    const onConfirm = mockOpenConfirmModal.mock.calls[0][0].onConfirm;
    await onConfirm();

    // Verify expected behaviors on failed delete
    expect(onLoadingMock).toHaveBeenCalledWith(true);
    expect(mockCloseConfirmModal).toHaveBeenCalledTimes(1); // Called only in onConfirm
    expect(mockHandleApiRequest).toHaveBeenCalledWith(
      expect.any(Promise),
      expect.objectContaining({
        successCallback: expect.any(Function),
        errorCallback: expect.any(Function),
      })
    );
    expect(onDeletedMock).not.toHaveBeenCalled();
    expect(onLoadingMock).toHaveBeenLastCalledWith(false);
  });
});
