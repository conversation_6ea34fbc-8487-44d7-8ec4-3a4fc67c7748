import { useArticleDetailStyles, useArticleViewerStyles } from '@/hooks/useArticleStyles';
import { AllTheProviders, mockLibraries } from '@/utils/unitTest';
import { renderHook } from '@testing-library/react';

// Initialize library mocks
mockLibraries();

describe('useArticleDetailStyles', () => {
  it('should return classes object with expected properties', () => {
    const { result } = renderHook(() => useArticleDetailStyles(), {
      wrapper: AllTheProviders,
    });

    expect(result.current.classes).toBeDefined();
    expect(result.current.classes.articleWrapper).toBeDefined();
    expect(result.current.classes.articleSection).toBeDefined();
    expect(result.current.classes.articleTitle).toBeDefined();
    expect(result.current.classes.articleHeader).toBeDefined();
    expect(result.current.classes.articleContent).toBeDefined();
    expect(result.current.classes.articleEditButton).toBeDefined();
    expect(result.current.classes.articleFormFieldLabel).toBeDefined();
    expect(result.current.classes.articleErrorMessage).toBeDefined();
    expect(result.current.classes.articleNotFound).toBeDefined();
  });
});

describe('useArticleViewerStyles', () => {
  it('should return classes with expected properties when withSidebar is true', () => {
    const drawerWidth = 800;
    const { result } = renderHook(
      () => useArticleViewerStyles({ drawerWidth, withSidebar: true }),
      {
        wrapper: AllTheProviders,
      }
    );

    expect(result.current.classes).toBeDefined();
    expect(result.current.classes.articleDrawer).toBeDefined();
    expect(result.current.classes.drawerFullView).toBeDefined();
    expect(result.current.classes.bodyFullView).toBeDefined();
  });

  it('should return classes with expected properties when withSidebar is false', () => {
    const drawerWidth = 800;
    const { result } = renderHook(
      () => useArticleViewerStyles({ drawerWidth, withSidebar: false }),
      {
        wrapper: AllTheProviders,
      }
    );

    expect(result.current.classes).toBeDefined();
    expect(result.current.classes.articleDrawer).toBeDefined();
    expect(result.current.classes.drawerFullView).toBeDefined();
    expect(result.current.classes.bodyFullView).toBeDefined();
  });
});
