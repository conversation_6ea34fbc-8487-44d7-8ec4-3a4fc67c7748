import { EntityType } from '@/types/entities';
import { HTTP_ERROR_STATUS } from '@resola-ai/shared-constants';
import { renderHook } from '@testing-library/react';
import type { AxiosError, AxiosResponse } from 'axios';
import { BrowserRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useApiHandler } from '../useApiHandler';

// Create mock functions that we can spy on
const mockNotifyError = vi.fn();
const mockNotifyMessage = vi.fn();
const mockHandleError = vi.fn();
const mockNavigate = vi.fn();

// Mock dependencies
vi.mock('react-router-dom', () => ({
  ...vi.importActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({
    pathname: '/test-path',
    search: '?query=test',
  }),
  BrowserRouter: ({ children }) => children,
}));

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => key, // Return the key itself for easy testing
  }),
}));

vi.mock('@/hooks/useNotifications', () => ({
  useNotifications: () => ({
    notifyError: mockNotifyError,
    notifyMessage: mockNotifyMessage,
  }),
}));

vi.mock('@/hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    handleError: mockHandleError,
  }),
}));

// This wrapper component provides the Router context needed for the hook
const wrapper = ({ children }) => <BrowserRouter>{children}</BrowserRouter>;

describe('useApiHandler', () => {
  const TEST_ID = '01j0whnq70jzbegecbbjwh3ndh';

  const TEST_API_URLS = {
    DOCUMENT: `/api/documents/${TEST_ID}`,
    FOLDER: `/api/folders/${TEST_ID}`,
    BASE: `/api/bases/${TEST_ID}`,
    JOB: `/api/jobs/${TEST_ID}`,
    TEMPLATE: `/api/templates/${TEST_ID}`,
    ARTICLE: `/api/articles/${TEST_ID}`,
  } as const;

  // Mock data
  const mockSuccessResponse = {
    data: { message: 'Success' },
    status: 200,
  } as AxiosResponse;

  const createAxiosError = (status: number, message: string, url?: string): AxiosError =>
    ({
      response: {
        status,
        data: { message },
      },
      config: url ? { url } : undefined,
      isAxiosError: true,
      name: 'AxiosError',
      message: 'Error',
      toJSON: () => ({}),
    }) as AxiosError;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('handleApiRequest', () => {
    it('should return data on successful request', async () => {
      const { result } = renderHook(() => useApiHandler(), { wrapper });
      const successPromise = Promise.resolve(mockSuccessResponse);

      const response = await result.current.handleApiRequest(successPromise);
      expect(response).toEqual({ message: 'Success' });
    });

    it('should handle generic error response', async () => {
      const { result } = renderHook(() => useApiHandler(), { wrapper });
      const errorPromise = Promise.reject(createAxiosError(500, 'Internal Server Error'));

      try {
        await result.current.handleApiRequest(errorPromise);
        expect.fail('Should have thrown an error');
      } catch (error: any) {
        expect(error).toEqual({
          status: 'error',
          statusCode: 500,
          message: 'Internal Server Error',
          entityType: undefined,
          error: expect.any(Object),
        });
      }
    });

    it('should handle 404 error with document entity type', async () => {
      const { result } = renderHook(() => useApiHandler(), { wrapper });
      const errorPromise = Promise.reject(
        createAxiosError(HTTP_ERROR_STATUS.NOT_FOUND, 'Document not found', '/api/documents/123')
      );

      try {
        await result.current.handleApiRequest(errorPromise);
        expect.fail('Should have thrown an error');
      } catch (error: any) {
        expect(error).toEqual({
          status: 'error',
          statusCode: HTTP_ERROR_STATUS.NOT_FOUND,
          message: 'Document not found',
          entityType: EntityType.DOCUMENT,
          error: expect.any(Object),
        });
      }
    });

    it('should handle error without message and use translation key', async () => {
      const { result } = renderHook(() => useApiHandler(), { wrapper });
      const errorPromise = Promise.reject(createAxiosError(403, ''));

      try {
        await result.current.handleApiRequest(errorPromise);
        expect.fail('Should have thrown an error');
      } catch (error: any) {
        expect(error).toEqual({
          status: 'error',
          statusCode: 403,
          message: '',
          entityType: undefined,
          error: expect.any(Object),
        });
      }
    });

    it('should detect different entity types from URLs', async () => {
      const { result } = renderHook(() => useApiHandler(), { wrapper });
      const entityUrls = [
        { url: TEST_API_URLS.DOCUMENT, type: EntityType.DOCUMENT },
        { url: TEST_API_URLS.FOLDER, type: EntityType.FOLDER },
        { url: TEST_API_URLS.BASE, type: EntityType.BASE },
        { url: TEST_API_URLS.JOB, type: EntityType.JOB },
        { url: TEST_API_URLS.TEMPLATE, type: EntityType.TEMPLATE },
        { url: TEST_API_URLS.ARTICLE, type: EntityType.ARTICLE },
      ];

      for (const { url, type } of entityUrls) {
        const errorPromise = Promise.reject(createAxiosError(404, 'Not found', url));

        try {
          await result.current.handleApiRequest(errorPromise);
          expect.fail('Should have thrown an error');
        } catch (error: any) {
          expect(error).toEqual({
            status: 'error',
            statusCode: 404,
            message: 'Not found',
            entityType: type,
            error: expect.any(Object),
          });
        }
      }
    });

    // Add test for success callback
    it('should call successCallback when provided and request is successful', async () => {
      const successCallback = vi.fn();
      const { result } = renderHook(() => useApiHandler(), { wrapper });
      const successPromise = Promise.resolve(mockSuccessResponse);

      await result.current.handleApiRequest(successPromise, { successCallback });
      expect(successCallback).toHaveBeenCalledTimes(1);
    });

    // Add test for error callback
    it('should call errorCallback when provided and request fails', async () => {
      const errorCallback = vi.fn();
      const { result } = renderHook(() => useApiHandler(), { wrapper });
      const errorPromise = Promise.reject(createAxiosError(500, 'Error'));

      try {
        await result.current.handleApiRequest(errorPromise, { errorCallback });
      } catch (error) {
        // Expected
      }
      expect(errorCallback).toHaveBeenCalledTimes(1);
    });

    // Fixed test for success notification
    it('should show success notification when successMessage and successTitle are provided', async () => {
      const { result } = renderHook(() => useApiHandler(), { wrapper });
      const successPromise = Promise.resolve(mockSuccessResponse);
      const successTitle = 'Success title';
      const successMessage = 'Success message';

      await result.current.handleApiRequest(successPromise, {
        successMessage,
        successTitle,
      });

      // Check that notifyMessage was called with the correct arguments
      expect(mockNotifyMessage).toHaveBeenCalledTimes(1);
      expect(mockNotifyMessage).toHaveBeenCalledWith(successTitle, successMessage);
    });

    // Add a test for error notification
    it('should show error notification when request fails', async () => {
      const { result } = renderHook(() => useApiHandler(), { wrapper });
      const errorMessage = 'Error occurred';
      const errorPromise = Promise.reject(createAxiosError(500, errorMessage));

      try {
        await result.current.handleApiRequest(errorPromise);
      } catch (error) {
        // Expected to throw
      }

      // Check that notifyError was called
      expect(mockNotifyError).toHaveBeenCalledTimes(1);
      // The first argument should be a title (from translation)
      // The second argument should be the error message
      expect(mockNotifyError.mock.calls[0][1]).toEqual(errorMessage);
    });

    // Add test for conflict error handling
    it('should handle conflict errors with error codes', async () => {
      const { result } = renderHook(() => useApiHandler(), { wrapper });
      const errorCode = 'DUPLICATE_RESOURCE';
      const errorPromise = Promise.reject({
        response: {
          status: HTTP_ERROR_STATUS.CONFLICT,
          data: {
            message: 'Resource conflict',
            error: { code: errorCode },
          },
        },
        config: { url: TEST_API_URLS.DOCUMENT },
        isAxiosError: true,
        name: 'AxiosError',
        message: 'Error',
        toJSON: () => ({}),
      });

      try {
        await result.current.handleApiRequest(errorPromise);
        expect.fail('Should have thrown an error');
      } catch (error) {
        // Expected
      }

      // Verify handleError was called with the error code
      expect(mockHandleError).toHaveBeenCalledTimes(1);
      expect(mockHandleError).toHaveBeenCalledWith({ code: errorCode });
    });

    // Test for navigation to 404 page
    it('should navigate to 404 page when resource is not found for supported entity types', async () => {
      // Reset mock before test
      mockNavigate.mockClear();

      const { result } = renderHook(() => useApiHandler(), { wrapper });

      // Test each redirectable entity type
      const redirectableEntities = [
        TEST_API_URLS.DOCUMENT,
        TEST_API_URLS.FOLDER,
        TEST_API_URLS.BASE,
        TEST_API_URLS.JOB,
        TEST_API_URLS.TEMPLATE,
      ];

      for (const url of redirectableEntities) {
        const errorPromise = Promise.reject(
          createAxiosError(HTTP_ERROR_STATUS.NOT_FOUND, 'Not found', url)
        );

        try {
          await result.current.handleApiRequest(errorPromise);
          expect.fail('Should have thrown an error');
        } catch (error) {
          // Expected
        }

        // Verify navigation happened - add more detailed debugging
        expect(mockNavigate).toHaveBeenCalled();
        mockNavigate.mockClear();
      }
    });

    // Test that article entity type doesn't trigger navigation on 404
    it('should not navigate to 404 page for non-redirectable entity types', async () => {
      // Reset mock before test
      mockNavigate.mockClear();

      const { result } = renderHook(() => useApiHandler(), { wrapper });
      const errorPromise = Promise.reject(
        createAxiosError(HTTP_ERROR_STATUS.NOT_FOUND, 'Not found', TEST_API_URLS.ARTICLE)
      );

      try {
        await result.current.handleApiRequest(errorPromise);
        expect.fail('Should have thrown an error');
      } catch (error) {
        // Expected
      }

      // Verify navigation did not happen
      expect(mockNavigate).not.toHaveBeenCalled();
    });

    // Test for fallback message and title
    it('should use fallback message and title when provided', async () => {
      const { result } = renderHook(() => useApiHandler(), { wrapper });
      const errorPromise = Promise.reject(createAxiosError(500, ''));
      const fallbackMessage = 'Custom error message';
      const fallbackTitle = 'Custom error title';

      try {
        await result.current.handleApiRequest(errorPromise, {
          fallbackMessage,
          fallbackTitle,
        });
        expect.fail('Should have thrown an error');
      } catch (error) {
        // Expected
      }

      // Verify the notification used our fallback values
      expect(mockNotifyError).toHaveBeenCalledWith(fallbackTitle, fallbackMessage);
    });
  });
});
