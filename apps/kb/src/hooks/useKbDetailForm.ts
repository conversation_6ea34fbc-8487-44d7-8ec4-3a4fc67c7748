import { ROOT_PATH } from '@/constants/folder';
import { kbSchema } from '@/schemas';
import {
  ACCESS_LEVEL,
  type AccessLevel,
  KB_TYPE,
  type KnowledgeBase,
  type KnowledgeBaseType,
  PROCESS_TYPE,
} from '@/types';
import { zodResolver } from '@hookform/resolvers/zod';
import { useTranslate } from '@tolgee/react';
import { type SubmitHandler, useForm } from 'react-hook-form';

type KbFormInputs = {
  name: string;
  description: string;
  baseType: KnowledgeBaseType;
  accessLevel: AccessLevel;
};

interface UseKbDetailFormrops {
  currentKnowledgeBase?: KnowledgeBase;
  onSubmitted?: (data: KbFormInputs) => void;
}

export const useKbDetailForm = ({ currentKnowledgeBase, onSubmitted }: UseKbDetailFormrops) => {
  const { t } = useTranslate('kb');
  const {
    handleSubmit,
    control,
    reset,
    formState: { errors },
    trigger,
    getValues,
    watch,
  } = useForm({
    defaultValues: {
      name: currentKnowledgeBase?.name || '',
      description: currentKnowledgeBase?.description || '',
      baseType: currentKnowledgeBase?.baseType || (KB_TYPE.article as KnowledgeBaseType),
      parentDirId: currentKnowledgeBase?.parentDirId || ROOT_PATH,
      accessLevel: currentKnowledgeBase?.accessLevel || (ACCESS_LEVEL.private as AccessLevel),
      processType: currentKnowledgeBase?.processType || PROCESS_TYPE.none,
    },
    mode: 'onChange',
    resolver: zodResolver(kbSchema(t)),
  });

  const onSubmit: SubmitHandler<KbFormInputs> = (data) => {
    onSubmitted?.(data);
  };

  return {
    handleSubmit: handleSubmit(onSubmit),
    onSubmit,
    control,
    reset,
    trigger,
    getValues,
    errors,
    watch,
  };
};
