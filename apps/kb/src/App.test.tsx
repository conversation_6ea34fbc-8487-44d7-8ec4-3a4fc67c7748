import { render, screen, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Mock the Tolgee hooks
vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(),
  useTolgee: vi.fn(),
}));

// Mock useSetDefaultLang hook
vi.mock('@resola-ai/ui/hooks', () => ({
  useSetDefaultLang: vi.fn(),
}));

import { useSetDefaultLang } from '@resola-ai/ui/hooks';
import { useTolgee, useTranslate } from '@tolgee/react';
// Import App after mocks
import App from './App';

// Mock functions that can be accessed in tests
const mockChangeLanguage = vi.fn();
const mockGetLanguage = vi.fn();
const mockT = vi.fn();

// Mock ErrorPage component
vi.mock('@resola-ai/ui/pages', () => ({
  ErrorPage: ({ appName }: { appName: string }) => (
    <div data-testid='error-page'>Error Page - {appName}</div>
  ),
}));

// Mock all page components
vi.mock('./pages', () => ({
  HomePage: () => <div data-testid='home-page'>Home Page</div>,
  DetailPage: () => <div data-testid='detail-page'>Detail Page</div>,
  GeneratePage: () => <div data-testid='generate-page'>Generate Page</div>,
  QnAPage: () => <div data-testid='qna-page'>QnA Page</div>,
  QnADetailPage: () => <div data-testid='qna-detail-page'>QnA Detail Page</div>,
  JobsPage: () => <div data-testid='jobs-page'>Jobs Page</div>,
  JobDetailPage: () => <div data-testid='job-detail-page'>Job Detail Page</div>,
  JobResultPage: () => <div data-testid='job-result-page'>Job Result Page</div>,
  FolderPage: () => <div data-testid='folder-page'>Folder Page</div>,
  TemplatesPage: () => <div data-testid='templates-page'>Templates Page</div>,
  TemplateDetailPage: () => <div data-testid='template-detail-page'>Template Detail Page</div>,
  DocumentPage: () => <div data-testid='document-page'>Document Page</div>,
  NotFoundPage: () => <div data-testid='not-found-page'>Not Found Page</div>,
}));

// Test constants
const TEST_CONSTANTS = {
  LANGUAGES: {
    DEFAULT: 'en',
    CURRENT: 'ja',
    UPDATED: 'en',
  },
  TRANSLATION_KEY: 'home:kb',
  KB_APP_NAME: 'KB App',
  PATHS: {
    HOME: '/kb',
    DETAIL: '/kb/test-kb-id',
    FOLDER: '/kb/folder/test-folder-id',
    JOBS: '/kb/jobs',
    JOB_DETAIL: '/kb/jobs/test-job-id',
    JOB_RESULT: '/kb/jobs/test-job-id/result',
    QNA: '/kb/qna',
    QNA_DETAIL: '/kb/qna/test-kb-id',
    GENERATE: '/kb/qna/test-kb-id/generate',
    DOCUMENT: '/kb/document/test-doc-id',
    TEMPLATES: '/kb/settings/templates',
    TEMPLATE_DETAIL: '/kb/settings/templates/test-template-id',
    UNAUTHORIZED: '/kb/unauthorized',
    NOT_FOUND: '/kb/404',
    NOT_FOUND_WITH_ENTITY: '/kb/404/document',
  },
};

describe('App', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Setup mock implementations
    vi.mocked(useTranslate).mockReturnValue({
      t: mockT.mockReturnValue(TEST_CONSTANTS.KB_APP_NAME),
      isLoading: false,
    });

    vi.mocked(useTolgee).mockReturnValue({
      changeLanguage: mockChangeLanguage,
      getLanguage: mockGetLanguage,
      isLoading: false,
      stop: vi.fn(),
      wrap: vi.fn(),
      highlight: vi.fn(),
      getPendingLanguage: vi.fn(),
      getAllLanguages: vi.fn(),
      addActiveNamespace: vi.fn(),
      removeActiveNamespace: vi.fn(),
      addStaticData: vi.fn(),
      on: vi.fn(),
      setEmitterActive: vi.fn(),
      isRunning: vi.fn(),
      run: vi.fn(),
      getInitialOptions: vi.fn(),
      getLanguageStorage: vi.fn(),
      getTranslationStorage: vi.fn(),
      updateOptions: vi.fn(),
      getRecord: vi.fn(),
      isInitialLoading: vi.fn(),
      findPositions: vi.fn(),
      findPositionsAll: vi.fn(),
      getRequiredRecords: vi.fn(),
      loadRecord: vi.fn(),
      loadRecords: vi.fn(),
      changeTranslation: vi.fn(),
      unregisterElement: vi.fn(),
      registerElement: vi.fn(),
      getTranslation: vi.fn(),
      off: vi.fn(),
      once: vi.fn(),
      emit: vi.fn(),
      getObserver: vi.fn(),
      overrideCredentials: vi.fn(),
    } as any);

    vi.mocked(useSetDefaultLang).mockReturnValue(TEST_CONSTANTS.LANGUAGES.DEFAULT);
    mockGetLanguage.mockReturnValue(TEST_CONSTANTS.LANGUAGES.DEFAULT);
  });

  describe('Component Rendering', () => {
    it('should render without crashing', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.HOME]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('home-page')).toBeInTheDocument();
    });

    it('should render Routes component', () => {
      const { container } = render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.HOME]}>
          <App />
        </MemoryRouter>
      );

      // Check that the Routes component is rendered (it should contain the route content)
      expect(container.firstChild).toBeInTheDocument();
    });
  });

  describe('Hook Integration', () => {
    it('should call useTranslate with correct namespace', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.HOME]}>
          <App />
        </MemoryRouter>
      );

      expect(useTranslate).toHaveBeenCalledWith('home');
    });

    it('should call useSetDefaultLang', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.HOME]}>
          <App />
        </MemoryRouter>
      );

      expect(useSetDefaultLang).toHaveBeenCalled();
    });

    it('should call useTolgee', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.HOME]}>
          <App />
        </MemoryRouter>
      );

      expect(useTolgee).toHaveBeenCalled();
    });

    it('should call tolgee.getLanguage', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.HOME]}>
          <App />
        </MemoryRouter>
      );

      expect(mockGetLanguage).toHaveBeenCalled();
    });
  });

  describe('Language Management', () => {
    it('should change language when default language differs from current language', async () => {
      // Mock different languages
      vi.mocked(useSetDefaultLang).mockReturnValue(TEST_CONSTANTS.LANGUAGES.DEFAULT);
      mockGetLanguage.mockReturnValue(TEST_CONSTANTS.LANGUAGES.CURRENT);

      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.HOME]}>
          <App />
        </MemoryRouter>
      );

      await waitFor(() => {
        expect(mockChangeLanguage).toHaveBeenCalledWith(TEST_CONSTANTS.LANGUAGES.DEFAULT);
      });
    });

    it('should not change language when default language matches current language', async () => {
      // Mock same languages
      vi.mocked(useSetDefaultLang).mockReturnValue(TEST_CONSTANTS.LANGUAGES.DEFAULT);
      mockGetLanguage.mockReturnValue(TEST_CONSTANTS.LANGUAGES.DEFAULT);

      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.HOME]}>
          <App />
        </MemoryRouter>
      );

      // Wait a bit to ensure useEffect has run
      await new Promise((resolve) => setTimeout(resolve, 100));

      expect(mockChangeLanguage).not.toHaveBeenCalled();
    });

    it('should change language when language props change', async () => {
      // Mock different language from the start
      vi.mocked(useSetDefaultLang).mockReturnValue(TEST_CONSTANTS.LANGUAGES.UPDATED);
      mockGetLanguage.mockReturnValue(TEST_CONSTANTS.LANGUAGES.CURRENT);

      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.HOME]}>
          <App />
        </MemoryRouter>
      );

      await waitFor(() => {
        expect(mockChangeLanguage).toHaveBeenCalledWith(TEST_CONSTANTS.LANGUAGES.UPDATED);
      });
    });
  });

  describe('Route Configuration', () => {
    it('should render HomePage for root KB path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.HOME]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('home-page')).toBeInTheDocument();
    });

    it('should render DetailPage for KB detail path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.DETAIL]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('detail-page')).toBeInTheDocument();
    });

    it('should render FolderPage for folder path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.FOLDER]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('folder-page')).toBeInTheDocument();
    });

    it('should render JobsPage for jobs path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.JOBS]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('jobs-page')).toBeInTheDocument();
    });

    it('should render JobDetailPage for job detail path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.JOB_DETAIL]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('job-detail-page')).toBeInTheDocument();
    });

    it('should render JobResultPage for job result path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.JOB_RESULT]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('job-result-page')).toBeInTheDocument();
    });

    it('should render QnAPage for QnA path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.QNA]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('qna-page')).toBeInTheDocument();
    });

    it('should render QnADetailPage for QnA detail path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.QNA_DETAIL]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('qna-detail-page')).toBeInTheDocument();
    });

    it('should render GeneratePage for generate path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.GENERATE]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('generate-page')).toBeInTheDocument();
    });

    it('should render DocumentPage for document path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.DOCUMENT]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('document-page')).toBeInTheDocument();
    });

    it('should render TemplatesPage for templates path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.TEMPLATES]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('templates-page')).toBeInTheDocument();
    });

    it('should render TemplateDetailPage for template detail path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.TEMPLATE_DETAIL]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('template-detail-page')).toBeInTheDocument();
    });

    it('should render ErrorPage for unauthorized path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.UNAUTHORIZED]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('error-page')).toBeInTheDocument();
      expect(screen.getByText(`Error Page - ${TEST_CONSTANTS.KB_APP_NAME}`)).toBeInTheDocument();
    });

    it('should render NotFoundPage for 404 path', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.NOT_FOUND]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('not-found-page')).toBeInTheDocument();
    });

    it('should render NotFoundPage for 404 path with entity type', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.NOT_FOUND_WITH_ENTITY]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByTestId('not-found-page')).toBeInTheDocument();
    });
  });

  describe('Translation Integration', () => {
    it('should call translation function for KB app name', () => {
      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.UNAUTHORIZED]}>
          <App />
        </MemoryRouter>
      );

      expect(mockT).toHaveBeenCalledWith('kb');
    });

    it('should pass translated app name to ErrorPage', () => {
      const customAppName = 'Custom KB App';
      mockT.mockReturnValue(customAppName);

      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.UNAUTHORIZED]}>
          <App />
        </MemoryRouter>
      );

      expect(screen.getByText(`Error Page - ${customAppName}`)).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle invalid paths gracefully', () => {
      render(
        <MemoryRouter initialEntries={['/invalid-path']}>
          <App />
        </MemoryRouter>
      );

      // Should not crash and render nothing for invalid paths
      expect(screen.queryByTestId('home-page')).not.toBeInTheDocument();
      expect(screen.queryByTestId('detail-page')).not.toBeInTheDocument();
    });

    it('should handle empty language values', async () => {
      vi.mocked(useSetDefaultLang).mockReturnValue('');
      mockGetLanguage.mockReturnValue('');

      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.HOME]}>
          <App />
        </MemoryRouter>
      );

      // Should not crash with empty language values
      expect(screen.getByTestId('home-page')).toBeInTheDocument();
    });

    it('should handle undefined language values', async () => {
      vi.mocked(useSetDefaultLang).mockReturnValue(undefined as any);
      mockGetLanguage.mockReturnValue(undefined as any);

      render(
        <MemoryRouter initialEntries={[TEST_CONSTANTS.PATHS.HOME]}>
          <App />
        </MemoryRouter>
      );

      // Should not crash with undefined language values
      expect(screen.getByTestId('home-page')).toBeInTheDocument();
    });
  });
});
