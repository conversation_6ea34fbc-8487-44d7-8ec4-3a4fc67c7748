import type { IPaginationRequestParams } from './api';
import type { User } from './user';

export const JOB_STATUS = {
  queued: 'queued',
  running: 'running',
  succeeded: 'succeeded',
  failed: 'failed',
  aborted: 'aborted',
  retry: 'retry',
} as const;

export type JobStatus = keyof typeof JOB_STATUS;

export enum JobType {
  ContentGeneration = 'content-generation',
  DocumentCollectionIndexing = 'document-collection-indexing',
  ArticleCollectionIndexing = 'article-collection-indexing',
  DataSourceIndexing = 'datasource-indexing',
  DataRemoval = 'data-removal',
  DocumentProcessing = 'document-processing',
  DocumentRemoving = 'document-removing',
  ArticleExport = 'article-download',
}

export type Job = {
  id?: string;
  createdAt: Date;
  updatedAt?: Date;
  name: string;
  status: JobStatus;
  initiatedBy: string;
  startedAt?: Date;
  stoppedAt?: Date;
  config: {
    schedule: object;
    retriable: boolean;
    timeoutAfter?: number;
  };
  metadata: JobMetadata;
  data: object;
  baseName: string;
  jobType: JobType;
  error: string;
  createdBy: User;
};

export type JobDocumentReference = {
  documentId: string;
  documentName: string;
};

export type JobArticle = {
  id: string;
  jobId: string;
  title: string;
  content: string;
  contentRaw: string;
  createdAt: Date;
  updatedAt: Date;
  orgId: string;
  document: JobDocument;
  metadata: {
    references: JobDocumentReference[];
  };
};

export type JobDetailResultResponse = JobArticle[];

export type IJobsRequestParams = IPaginationRequestParams & {
  jobType?: JobType | JobType[];
};

export type Prompt = {
  id: string;
  title: string;
  content: string;
  createdAt?: string;
  updatedAt?: string;
};

export type PromptPayload = Omit<Prompt, 'id'>;

export type JobDocument = {
  id: string;
  name: string;
};

export type JobMetadata = {
  documentIds: string[];
  customPrompt?: PromptPayload;
  documents?: JobDocument[];
  locationFilter?: {
    baseIds: string[];
    folderIds: string[];
  };
  fileType?: 'csv' | 'excel';
};

export type GenerateJobPayload = {
  jobType: JobType;
  metadata: JobMetadata;
};

export type ArticleGeneratorActivity = {
  id: string;
  action: string;
  createdAt: string;
  generatedArticleId: string;
  kbId: string;
  kbName: string;
  timestamp: string;
  updatedAt: string;
  userAvatar: string;
  userEmail: string;
  userId: string;
  userName: string;
};

export type JobArticleDownloadFormat = 'excel' | 'csv';

export type JobExportResult = {
  downloadUrl?: string;
  // Add other properties that might be relevant for export results
};
