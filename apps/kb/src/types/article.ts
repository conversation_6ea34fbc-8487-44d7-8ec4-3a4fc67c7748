import type { IPaginationNextPrevious } from '@resola-ai/models';
import type { KnowledgeBase } from './kb';
import type { KBTemplateField } from './template';
import type { User } from './user';

export enum ArticleDetailLayout {
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical',
}

export type Article = {
  id: string;
  baseId: string;
  content: string;
  contentRaw: string;
  title: string;
  status: string;
  updatedAt: Date;
  createdAt: Date;
  createdBy: User;
  keywords: string[];
  likeCount?: number;
  dislikeCount?: number;
  customData?: Partial<KBTemplateField>[];
  relatedArticles?: Article[];
  isShortcut?: boolean;
  originArticleId?: string;
  base?: KnowledgeBase;
  shortcutArticleIds?: string[];
};

export type ArticleCollection = {
  data: Article[] | undefined;
  pagination?: IPaginationNextPrevious;
};

export type PartOfArticle = Partial<Article>;

export type ArticlePayload = Omit<Partial<Article>, 'relatedArticles'> & {
  relatedArticles?: string[];
};

export type Comment = {
  id: string;
  articleId: string;
  text: string;
  createdBy: User;
  createdAt: string;
  updatedAt: string;
};

export type PartOfComment = Partial<Comment>;

export type ArticleFileUpload = {
  imageUrl: string;
};

export type ArticleVote = {
  id: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  articleId: string;
  feedbackType?: VoteType;
  likeCount?: number;
  dislikeCount?: number;
};

export enum VoteType {
  LIKE = 'like',
  DISLIKE = 'dislike',
  NEUTRAL = 'neutral',
}

export enum ArticleAnalyticPeriod {
  LAST_7_DAYS = 'last7days',
  LAST_28_DAYS = 'last28days',
  LAST_90_DAYS = 'last90days',
  ALL_TIME = 'allTime',
}

export type ArticleDateRangeValue = [Date | null, Date | null];

export type ArticleAnalyticDateRangeParams = {
  from: string | null;
  to: string | null;
};

export enum ArticleAnalyticProduct {
  CHATBOT = 'chatbot',
  FAQ = 'faq',
  MANAGEMENT = 'management',
  AI_WIDGET = 'aiwidget',
  CHAT_WINDOW = 'chatwindow',
}

export type ArticleAnalyticItem = {
  id: string;
  product: ArticleAnalyticProduct;
  hour: string;
  viewCount: number;
  feedbackCount: number;
  likeCount: number;
  dislikeCount: number;
};

export type ArticleAnalyticData = Array<ArticleAnalyticItem>;

export type ArticleAnalyticSummaryByProduct = {
  product: ArticleAnalyticProduct;
  viewCount: number;
  feedbackCount: number;
  likeCount: number;
  dislikeCount: number;
};

export type ArticleAnalyticSummary = {
  totalViewCount: number;
  totalFeedbackCount: number;
  totalLikeCount: number;
  totalDislikeCount: number;
  products: Record<ArticleAnalyticProduct, ArticleAnalyticSummaryByProduct> | object;
};
