import type { Article, DocumentFile, Folder, KBDirectionQueryEnum, KnowledgeBase } from '@/types';
import type { IPaginationNextPrevious } from '@resola-ai/models';

export interface IBaseSelectionState {
  bases: KnowledgeBase[];
  basesLoading: boolean;
  basesLoadingMore: boolean;
  selectedBase: string;
  payloadCapture: {
    parentDirId: string;
    direction: KBDirectionQueryEnum;
    pagination: IPaginationNextPrevious;
  };
}

export interface IFolderSelectionState {
  folders: Folder[];
  selectedFolder: string;
  foldersLoading: boolean;
}

export interface IArticleSelectionState {
  articles: Article[];
  selectedArticles: string[];
  articlesLoading: boolean;
  articlesLoadingMore: boolean;
  payloadCapture: {
    kbId: string;
    direction: KBDirectionQueryEnum;
    pagination: IPaginationNextPrevious;
  };
}

export interface IDocumentSelectionState {
  documents: DocumentFile[];
  selectedDocuments: string[];
  documentsLoading: boolean;
  documentsLoadingMore: boolean;
  payloadCapture: {
    parentDirId: string;
    direction: KBDirectionQueryEnum;
    pagination: IPaginationNextPrevious;
  };
}
