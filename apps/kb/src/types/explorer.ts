import type { IPaginationNextPrevious } from '@resola-ai/models';
import type { DocumentFile } from './file';
import type { KnowledgeBase } from './kb';

export enum ExplorerEntityType {
  KNOWLEDGEBASE = 'knowledgebase',
  DOCUMENT = 'document',
}

export type Explorer = (KnowledgeBase | DocumentFile) & {
  entityType?: ExplorerEntityType;
};

export type ExplorerResponse = {
  data: Explorer[];
  pagination: IPaginationNextPrevious;
};
