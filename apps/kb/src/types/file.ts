import type { AccessLevel, KnowledgeBaseType } from './kb';
import type { User } from './user';

export enum FILE_UPLOAD_STATUS {
  uploaded = 'uploaded',
  failed = 'failed',
  unknown = 'unknown',
}

export const FILE_CONTENT_TYPE = {
  csv: 'text/csv',
  pdf: 'application/pdf',
  txt: 'text/plain',
  presentation: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  msword: 'application/msword',
  document: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  markdown: 'text/markdown',
  xrst: 'text/x-rst',
  rtf: 'application/rtf',
  tab: 'text/tab-separated-values',
  sheet: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  mspowerpoint: 'application/vnd.ms-powerpoint',
  msexcel: 'application/vnd.ms-excel',
  unknown: 'unknown',
  image: 'image/*',
};

export const FILE_CONTENT_TYPE_EXTENSIONS = {
  [FILE_CONTENT_TYPE.txt]: ['.txt', '.md', '.markdown', '.mdown', '.mkdn'],
  [FILE_CONTENT_TYPE.markdown]: ['.md', '.markdown', '.mdown', '.mkdn'],
  [FILE_CONTENT_TYPE.csv]: [],
  [FILE_CONTENT_TYPE.pdf]: [],
  [FILE_CONTENT_TYPE.presentation]: [],
  [FILE_CONTENT_TYPE.msword]: [],
  [FILE_CONTENT_TYPE.document]: [],
  [FILE_CONTENT_TYPE.image]: ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'],
};

export type FileContentType = keyof typeof FILE_CONTENT_TYPE;

export type FileMetadata = {
  name: string;
  contentLength: number;
  contentType: string;
  uploadUrl?: string;
  downloadUrl?: string;
  downloadUrlExpires?: Date | null;
  uploadStatus?: FILE_UPLOAD_STATUS;
  createdBy?: string | User;
  tokenUsage?: number;
};

export type DocumentFile = {
  id: string;
  orgId: string;
  folderId: string;
  type: KnowledgeBaseType;
  metadata: FileMetadata;
  accessLevel?: AccessLevel;
  parentDirBreadcrumbArray?: string[];
  parentDirPath?: string;
  parentDirBreadcrumb?: string;
  parentDirId?: string;
  createdAt?: Date;
  updatedAt?: Date;
};

export type PresignedURLResult = { fileRecord: DocumentFile };

// TODO: Remove this after the new KB Document all supported
export type FileMetadataV1 = {
  fileName: string;
  contentLength: number;
  contentType: FileContentType;
};

export type DocumentFileV1 = {
  id: string;
  orgId: string;
  kbId: string;
  uploadUrl?: string;
  downloadUrl?: string;
  uploadStatus?: FILE_UPLOAD_STATUS;
  downloadUrlExpires: Date;
  metadata: FileMetadataV1;
  tokenUsage?: number;
  creator?: string;
  createdAt?: Date;
  updatedAt?: Date;
};

export type PresignedURLResultV1 = { fileRecord: DocumentFileV1 };

export type FileLink = {
  status: string;
  error: string;
  downloadUrl: string;
  downloadUrlExpires: string;
};

export type FileUploaderInstance = {
  fileId: string;
  status: FILE_UPLOAD_STATUS;
  percentage: number;
  uploadURL: string;
  extraDataFile?: ExtraDataFile;
  upload: (onCompleted: (fileId: string, status: string, percentage: number) => void) => void;
  updateProgress: (percentage: number) => void;
};

export type FileChangeStatus = {
  fileId: string;
  status: FILE_UPLOAD_STATUS;
  percentage: number;
};

export type ExtraDataFile = {
  name: string;
  size: number;
  accessLevel: AccessLevel;
};
