import type { Article } from './article';
import type { DocumentFile } from './file';
import type { KnowledgeBase } from './kb';
import type { Folder } from './tree';

export enum SearchTypeEnums {
  base = 'base',
  article = 'article',
  document = 'document',
  folder = 'folder',
}

export type BaseSearchData = Partial<KnowledgeBase>;
export type ArticleSearchData = Partial<Article> & {
  base?: BaseSearchData;
};
export type DocumentSearchData = Partial<DocumentFile> & {
  previewText: string;
};
export type FolderSearchData = Partial<Folder>;

export type SearchType = keyof typeof SearchTypeEnums;
export type SearchResult = {
  type: SearchType;
  data: {
    base: BaseSearchData;
    article: ArticleSearchData;
    document: DocumentSearchData;
    folder: FolderSearchData;
  }[SearchType];
};

export type SearchFilters = {
  customDataKeys: string[];
  customDatas: {
    [key: string]: string;
  }[];
  fields: {
    [key: string]: string | undefined;
  };
};

export type SearchPayload = {
  dirId: string;
  query: string;
  entities: SearchType[];
  filters: SearchFilters;
};

export type SearchHistory = {
  id: string;
  createdAt: string;
  createdBy: string;
  dirId: string;
  entities: SearchType[];
  query: string;
  updatedAt: string;
  metadata: {
    dirId: string;
    query: string;
    entities: SearchType[];
    filters: SearchFilters | null;
  };
};
