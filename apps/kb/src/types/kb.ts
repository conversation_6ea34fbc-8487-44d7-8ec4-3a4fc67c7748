import type { QnAJobStatus } from './qna';

export const KB_TYPE = {
  document: 'document',
  article: 'article',
  qna: 'qna',
  dataSource: 'from_datasource',
  folder: 'folder',
  unknown: 'unknown',
} as const;

export type KnowledgeBaseType = (typeof KB_TYPE)[keyof typeof KB_TYPE];

export const ACCESS_LEVEL = {
  public: 'public',
  private: 'private',
};
export type AccessLevel = keyof typeof ACCESS_LEVEL;

export const PROCESS_TYPE = {
  processing: 'access_level_transform_processing',
  done: 'access_level_transform_processing_done',
  failed: 'access_level_transform_processing_failed',
  none: 'none',
};

export type ProcessType = keyof typeof PROCESS_TYPE;

export type KnowledgeBase = {
  id: string;
  org_id?: string;
  name: string;
  description: string;
  baseType: KnowledgeBaseType;
  type: KnowledgeBaseType; // TODO: remove this after apply KB all new
  currentQaGenStatus?: QnAJobStatus;
  createdAt?: Date;
  updatedAt?: Date;
  articleId?: string;
  documentId?: string;
  dataSourceId?: string;
  parentDirId?: string;
  parentDirBreadcrumb?: string;
  parentDirBreadcrumbArray?: Array<string>;
  parentDirPath?: string;
  accessLevel?: AccessLevel;
  processType?: ProcessType;
};
