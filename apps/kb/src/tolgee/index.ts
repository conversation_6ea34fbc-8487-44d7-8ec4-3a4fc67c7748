import { DEFAULT_LANGUAGE } from '@resola-ai/shared-constants';
import { FALLBACK_LANGUAGE } from '@resola-ai/ui/constants';
import { Tolgee } from '@tolgee/react';
import { InContextTools } from '@tolgee/web/tools';

import { AppConfig } from '@/configs';

// Import all locales from the new structure
import {
  article_en,
  article_ja,
  common_en,
  common_ja,
  details_en,
  details_ja,
  export_en,
  export_ja,
  home_en,
  home_ja,
  job_en,
  job_ja,
  kb_en,
  kb_ja,
  settings_en,
  settings_ja,
} from '@/locales';

const tolgee = Tolgee()
  .use(AppConfig.TOLGEE_TOOLS_ENABLED ? InContextTools() : undefined)
  .init({
    language: DEFAULT_LANGUAGE,
    fallbackLanguage: FALLBACK_LANGUAGE,
    defaultNs: 'common',
    ns: ['home', 'kb', 'job', 'settings', 'export', 'details', 'common', 'article'],

    // for development
    apiUrl: AppConfig.TOLGEE_URL,
    apiKey: AppConfig.TOLGEE_KEY,

    // for production
    staticData: {
      'en:home': home_en,
      'ja:home': home_ja,
      'en:kb': kb_en,
      'ja:kb': kb_ja,
      'en:job': job_en,
      'ja:job': job_ja,
      'en:settings': settings_en,
      'ja:settings': settings_ja,
      'en:export': export_en,
      'ja:export': export_ja,
      'en:details': details_en,
      'ja:details': details_ja,
      'en:common': common_en,
      'ja:common': common_ja,
      'en:article': article_en,
      'ja:article': article_ja,
    },
    onFormatError: (error) => {
      console.error('KB Tolgee translate error', error);
      return error;
    },
  });

export { tolgee };
