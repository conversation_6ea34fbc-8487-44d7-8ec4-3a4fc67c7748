import { AppConfig } from '@/configs';
import { DEFAULT_LANGUAGE } from '@resola-ai/shared-constants';
import { FALLBACK_LANGUAGE } from '@resola-ai/ui/constants';
import { describe, expect, it, vi } from 'vitest';
import { tolgee } from './index';

// Mock the locales index file with the new structure
vi.mock('@/locales', () => ({
  article_en: { test: 'Test EN Article' },
  article_ja: { test: 'Test JA Article' },
  common_en: { test: 'Test EN Common' },
  common_ja: { test: 'Test JA Common' },
  details_en: { test: 'Test EN Details' },
  details_ja: { test: 'Test JA Details' },
  export_en: { test: 'Test EN Export' },
  export_ja: { test: 'Test JA Export' },
  home_en: { test: 'Test EN Home' },
  home_ja: { test: 'Test JA Home' },
  job_en: { test: 'Test EN Job' },
  job_ja: { test: 'Test JA Job' },
  kb_en: { test: 'Test EN KB' },
  kb_ja: { test: 'Test JA KB' },
  settings_en: { test: 'Test EN Settings' },
  settings_ja: { test: 'Test JA Settings' },
}));

vi.mock('@tolgee/react', () => {
  const actual = vi.importActual<typeof import('@tolgee/react')>('@tolgee/react');
  return {
    ...actual,
    FormatSimple: vi.fn(() => ({
      use: vi.fn().mockReturnThis(),
      init: vi.fn().mockReturnThis(),
    })),
    Tolgee: vi.fn(() => ({
      use: vi.fn().mockReturnThis(),
      init: vi.fn().mockReturnThis(),
      getInitialOptions: vi.fn(() => ({
        language: DEFAULT_LANGUAGE,
        fallbackLanguage: FALLBACK_LANGUAGE,
        apiUrl: AppConfig.TOLGEE_URL,
        apiKey: AppConfig.TOLGEE_KEY,
        staticData: {
          'en:home': { test: 'Test EN Home' },
          'ja:home': { test: 'Test JA Home' },
          'en:common': { test: 'Test EN Common' },
          'ja:common': { test: 'Test JA Common' },
          'en:kb': { test: 'Test EN KB' },
          'ja:kb': { test: 'Test JA KB' },
          'en:article': { test: 'Test EN Article' },
          'ja:article': { test: 'Test JA Article' },
          'en:details': { test: 'Test EN Details' },
          'ja:details': { test: 'Test JA Details' },
          'en:job': { test: 'Test EN Job' },
          'ja:job': { test: 'Test JA Job' },
          'en:settings': { test: 'Test EN Settings' },
          'ja:settings': { test: 'Test JA Settings' },
          'en:export': { test: 'Test EN Export' },
          'ja:export': { test: 'Test JA Export' },
        },
        onFormatError: (error: string) => {
          console.error('KB Tolgee translate error', error);
          return error;
        },
      })),
    })),
  };
});

describe('KB Tolgee Configuration', () => {
  it('should initialize with correct default settings', () => {
    const options = tolgee.getInitialOptions();

    expect(options.language).toBe(DEFAULT_LANGUAGE);
    expect(options.fallbackLanguage).toBe(FALLBACK_LANGUAGE);
  });

  it('should have correct API configuration', () => {
    const options = tolgee.getInitialOptions();

    expect(options.apiUrl).toBe(AppConfig.TOLGEE_URL);
    expect(options.apiKey).toBe(AppConfig.TOLGEE_KEY);
  });

  it('should have all required English static data configurations', () => {
    const options = tolgee.getInitialOptions();
    const staticData = options.staticData;

    expect(staticData).toBeDefined();
    if (staticData) {
      expect(staticData).toHaveProperty('en:home');
      expect(staticData).toHaveProperty('en:common');
      expect(staticData).toHaveProperty('en:kb');
      expect(staticData).toHaveProperty('en:article');
      expect(staticData).toHaveProperty('en:details');
      expect(staticData).toHaveProperty('en:job');
      expect(staticData).toHaveProperty('en:settings');
      expect(staticData).toHaveProperty('en:export');
    }
  });

  it('should have all required Japanese static data configurations', () => {
    const options = tolgee.getInitialOptions();
    const staticData = options.staticData;

    expect(staticData).toBeDefined();
    if (staticData) {
      expect(staticData).toHaveProperty('ja:home');
      expect(staticData).toHaveProperty('ja:common');
      expect(staticData).toHaveProperty('ja:kb');
      expect(staticData).toHaveProperty('ja:article');
      expect(staticData).toHaveProperty('ja:details');
      expect(staticData).toHaveProperty('ja:job');
      expect(staticData).toHaveProperty('ja:settings');
      expect(staticData).toHaveProperty('ja:export');
    }
  });

  it('should handle format errors correctly', () => {
    const options = tolgee.getInitialOptions();
    const error = 'Test error';
    const consoleSpy = vi.spyOn(console, 'error');

    if (typeof options.onFormatError === 'function') {
      const result = options.onFormatError(error, { key: 'test.key' });
      expect(consoleSpy).toHaveBeenCalledWith('KB Tolgee translate error', error);
      expect(result).toBe(error);
    }

    consoleSpy.mockRestore();
  });
});
