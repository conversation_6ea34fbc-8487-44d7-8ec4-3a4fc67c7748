import { ArticleCustomContextProvider, ArticleDetailContextProvider } from '@/contexts';
import {
  type ComponentType,
  type ForwardRefExoticComponent,
  type ForwardRefRenderFunction,
  type PropsWithoutRef,
  type RefAttributes,
  forwardRef,
} from 'react';

export const withArticleDetailProviders = <P extends object, T = any>(
  WrappedComponent: ComponentType<P> | ForwardRefExoticComponent<P & RefAttributes<T>>
) => {
  const WithArticleDetailProviders: ForwardRefRenderFunction<T, PropsWithoutRef<P>> = (
    props,
    ref
  ) => {
    // Check if the WrappedComponent is a forwardRef component or not
    const isForwardRef =
      typeof WrappedComponent === 'object' &&
      (WrappedComponent as ForwardRefExoticComponent<P>)?.$$typeof?.toString() ===
        'Symbol(react.forward_ref)';

    const Component = WrappedComponent as ComponentType<P & RefAttributes<T>>;

    return (
      <ArticleDetailContextProvider>
        <ArticleCustomContextProvider>
          {isForwardRef ? (
            <Component {...(props as P)} ref={ref} />
          ) : (
            <Component {...(props as P)} />
          )}
        </ArticleCustomContextProvider>
      </ArticleDetailContextProvider>
    );
  };

  const WithForwardRef = forwardRef(WithArticleDetailProviders);

  WithForwardRef.displayName = `WithArticleDetailProviders(${
    WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })`;

  return WithForwardRef;
};

export default withArticleDetailProviders;
