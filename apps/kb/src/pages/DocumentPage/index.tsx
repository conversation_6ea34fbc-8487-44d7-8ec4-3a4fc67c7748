import { KBBreadcrumbs } from '@/components';
import { AccessIcon, FileIcon, FileStatusIcon } from '@/components/common';
import { AVATAR_URL } from '@/constants/file';
import { NAVBAR_WIDTH } from '@/constants/ui';
import { useAppContext, useDocumentContext } from '@/contexts';
import { useApiHandler, useExplorer, useKbAccessControl } from '@/hooks';
import { ACCESS_LEVEL, type AccessLevel, type DocumentFile, FILE_UPLOAD_STATUS } from '@/types';
import { fileSize, getUserName, scrollToTop } from '@/utils';
import { Avatar, Box, Button, Flex, Grid, Group, Radio, Text, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type { BreadcrumbItem } from '@resola-ai/ui';
import { formatDateTime } from '@resola-ai/ui/utils/dateTime';
import { getPublicUrl } from '@resola-ai/utils';
import { IconDownload, IconTrash } from '@tabler/icons-react';
import { useTolgee, useTranslate } from '@tolgee/react';
import isObject from 'lodash/isObject';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Controller, get, useForm } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router-dom';

const useStyles = createStyles((theme) => ({
  container: {
    width: rem('100%'),
    height: rem('100%'),
    marginLeft: rem(NAVBAR_WIDTH),
    padding: rem(40),
    paddingTop: 0,
  },
  boxContainer: {
    backgroundColor: theme.colors.decaLight[1],
    padding: rem(20),
    borderRadius: rem(10),
    width: '100%',
  },
  containerExpanded: {
    marginLeft: rem(300),
  },
  title: {
    wordBreak: 'break-word',
    color: theme.colors.decaNavy[5],
    flex: 1,
  },
  buttonGroup: {
    width: '100%',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingTop: theme.spacing.md,
  },
  typeGroup: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    gap: rem(10),
  },
  typeLabel: {
    fontWeight: 500,
    fontSize: theme.fontSizes.sm,
    color: theme.colors.decaNavy[5],
  },
  typeDescription: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.decaGrey[6],
  },
  iconFile: {
    width: rem(30),
    height: rem(30),
  },
  iconBox: {
    width: rem(32),
    height: rem(32),
    borderRadius: rem('50%'),
    backgroundColor: theme.colors.decaLight[1],
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    '& svg': {
      color: theme.colors.decaGrey[3],
    },
  },
  accessLevelRadio: {
    marginTop: rem(6),
  },
  actionIconContainer: {
    position: 'relative',
    float: 'right',
    marginTop: rem(20),
  },
  deleteButton: {
    display: 'flex',
    alignItems: 'center',
    gap: rem(10),
    border: `1px solid ${theme.colors.gray[3]}`,
    borderRadius: rem(50),
    backgroundColor: theme.colors.white,
    color: theme.colors.gray[9],
    '&:hover': {
      backgroundColor: theme.colors.gray[0],
    },
  },
  notFoundBox: {
    width: '100%',
    height: '60vh',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  },
}));

const DocumentPage: React.FC = () => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate(['kb', 'home', 'common']);
  const tolgee = useTolgee();
  const { expanded } = useExplorer();
  const { documentId = '' } = useParams();
  const { permDocument } = useKbAccessControl();
  const { getDocument, deleteDocument, updateDocument, downloadDocument, isDownloading } =
    useDocumentContext();
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const navigate = useNavigate();
  const [document, setDocument] = useState<DocumentFile>();
  const { control, setValue } = useForm({
    defaultValues: {
      accessLevel: document?.accessLevel || (ACCESS_LEVEL.private as AccessLevel),
    },
  });
  const { handleApiRequest } = useApiHandler();

  /**
   * Define the Current Breadcrumbs
   * @type {BreadcrumbsItems[]}
   * @dependencies t: i18n function, kb: KnowledgeBase, kbId: string
   */
  const currentBreadcrumbs = useMemo(() => {
    const items = document?.parentDirBreadcrumbArray ?? [];
    const ids = document?.parentDirPath?.split('/').filter(Boolean) ?? [];

    return [
      { title: t('tree.root', { ns: 'home' }), href: '/kb/' },
      ...items.map((item, index) =>
        index === 0
          ? undefined
          : {
              title: item,
              href: `/kb/folder/${ids[index]}`,
            }
      ),
      { title: document?.metadata?.name || '', href: `/kb/${documentId}` },
    ].filter(Boolean) as BreadcrumbItem[];
  }, [document, documentId, t]);

  // Get document author name by order: displayName, email, name
  const documentAuthorName = useMemo(() => {
    return document?.metadata?.createdBy && isObject(document?.metadata?.createdBy)
      ? getUserName(document?.metadata?.createdBy, tolgee.getLanguage())
      : t('unknown', { ns: 'common' });
  }, [document, tolgee, t]);

  const fetchDocument = async () => {
    const res = await handleApiRequest(getDocument(documentId));
    setDocument(res.data);
  };

  const handleDelete = useCallback(() => {
    openConfirmModal({
      onConfirm: async () => {
        await deleteDocument(documentId);
        if (document?.parentDirId && document?.parentDirId !== '/root') {
          navigate(`/kb/folder/${document?.parentDirId}`);
        } else {
          navigate('/kb/');
        }
        closeConfirmModal();
      },
      title: t('document.detail.deleteConfirmTitle'),
      options: {
        isRemoving: true,
      },
    });
  }, [
    documentId,
    document?.parentDirId,
    deleteDocument,
    openConfirmModal,
    t,
    closeConfirmModal,
    navigate,
  ]);

  const handleChangeAccessLevel = useCallback((value: string) => {
    openConfirmModal({
      onConfirm: () => {
        setValue('accessLevel', value as AccessLevel);
        closeConfirmModal();
        updateDocument(documentId, { accessLevel: value as AccessLevel });
      },
      onCancel: () => {
        closeConfirmModal();
      },
      title: t('document.detail.updateAccessLevelTitle', {
        accessLevel: t(`accessLevel.${value}.label`),
      }),
      options: {
        isRemoving: false,
        isRowReverse: true,
      },
    });
  }, []);

  const handleDownload = useCallback(() => {
    if (document) {
      downloadDocument(document.id, document.metadata?.name);
    }
  }, [document?.id, downloadDocument]);

  useEffect(() => {
    fetchDocument();
    scrollToTop();
  }, [documentId]);

  useEffect(() => {
    setValue('accessLevel', document?.accessLevel || (ACCESS_LEVEL.private as AccessLevel));
  }, [document]);

  return (
    <Box className={cx(classes.container, expanded && classes.containerExpanded)}>
      <KBBreadcrumbs items={currentBreadcrumbs} expanded={expanded} />
      <Flex justify='space-between' align='center' mt={rem(90)} mb={rem(20)} gap={rem(20)}>
        <Title order={2} className={classes.title}>
          {document?.metadata?.name}
        </Title>
        {document?.metadata?.uploadStatus === FILE_UPLOAD_STATUS.uploaded && (
          <Button onClick={handleDownload} loading={isDownloading}>
            <IconDownload size={20} />
            <Text ml={rem(10)}>{t('menu.download')}</Text>
          </Button>
        )}
      </Flex>
      <form>
        <Flex justify='space-between' direction='row' gap={rem(20)}>
          <Box className={classes.boxContainer}>
            <Grid>
              <Grid.Col span={6}>
                <Group className={classes.typeGroup}>
                  <Text fz='md'>{t('document.detail.contentType')}</Text>
                  <FileIcon
                    className={classes.iconFile}
                    contentType={document?.metadata?.contentType}
                  />
                </Group>
              </Grid.Col>

              <Grid.Col span={6}>
                <Group className={classes.typeGroup}>
                  <Text fz='md'>{t('document.detail.createdAt')}</Text>
                  <Text fz='md' className={classes.typeLabel}>
                    {formatDateTime(document?.createdAt || '')}
                  </Text>
                </Group>
              </Grid.Col>

              <Grid.Col span={6}>
                <Group className={classes.typeGroup}>
                  <Text fz='md'>{t('document.detail.size')}</Text>
                  <Text fz='md' className={classes.typeLabel}>
                    {fileSize(document?.metadata?.contentLength || 0)}
                  </Text>
                </Group>
              </Grid.Col>

              <Grid.Col span={6}>
                <Group className={classes.typeGroup}>
                  <Text fz='md'>{t('document.detail.status')}</Text>
                  <FileStatusIcon status={document?.metadata?.uploadStatus} showLabel />
                </Group>
              </Grid.Col>
              <Grid.Col span={6}>
                <Group className={classes.typeGroup}>
                  <Text fz='md'>{t('document.detail.createdBy')}</Text>
                  <Flex align={'center'} gap={rem(10)}>
                    <Avatar
                      radius='xl'
                      src={get(document?.metadata?.createdBy, 'picture', getPublicUrl(AVATAR_URL))}
                    />
                    <Text fz='md' className={classes.typeLabel}>
                      {documentAuthorName}
                    </Text>
                  </Flex>
                </Group>
              </Grid.Col>
            </Grid>
          </Box>
          <Box className={classes.boxContainer}>
            <Controller
              name='accessLevel'
              key='accessLevel'
              control={control}
              render={({ field }) => (
                <Radio.Group
                  label={
                    <Flex direction={'column'} gap={rem(13)}>
                      <Text fz='md' className={classes.typeLabel}>
                        {t('accessLevel.label')}
                      </Text>
                    </Flex>
                  }
                  key={field.name}
                  // error={errors.accessLevel?.message}
                  {...field}
                  onChange={handleChangeAccessLevel}
                >
                  <Group className={classes.typeGroup}>
                    <Text color='decaGrey.5'>{t('accessLevel.description')}</Text>
                    {[ACCESS_LEVEL.private, ACCESS_LEVEL.public].map((level) => (
                      <Radio
                        classNames={{ inner: classes.accessLevelRadio }}
                        key={level}
                        value={level}
                        disabled={!permDocument.canEditAccessLevel}
                        data-testid={`access-level-radio-${level}`}
                        label={
                          <Box>
                            <Flex gap={rem(9)} align={'center'}>
                              <Box className={classes.iconBox}>
                                <AccessIcon accessLevel={level as AccessLevel} />
                              </Box>
                              <Text fz='md' className={classes.typeLabel}>
                                {t(`accessLevel.${level}.label`)}
                              </Text>
                            </Flex>
                            <Text fz='sm' className={classes.typeDescription}>
                              {t(`accessLevel.${level}.description`)}
                            </Text>
                          </Box>
                        }
                      />
                    ))}
                  </Group>
                </Radio.Group>
              )}
            />
          </Box>
        </Flex>
      </form>
      {permDocument.canDelete && (
        <Box className={classes.actionIconContainer}>
          <Button variant='outline' onClick={handleDelete} className={classes.deleteButton}>
            <IconTrash size={20} />
            <Text ml={rem(10)}>{t('menu.delete')}</Text>
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default DocumentPage;
