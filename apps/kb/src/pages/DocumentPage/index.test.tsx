import { useAppContext, useDocumentContext } from '@/contexts';
import { use<PERSON><PERSON><PERSON><PERSON><PERSON>, useExplorer, useKbAccessControl } from '@/hooks';
import { ACCESS_LEVEL, FILE_UPLOAD_STATUS } from '@/types';
import { mockLibraries, mockNavigate, mockParams, renderWithProviders } from '@/utils/unitTest';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import DocumentPage from './index';

// Mock window.scrollTo
global.window.scrollTo = vi.fn();

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, options?: any) => {
      let translationKey = key;
      if (key.includes(':')) {
        translationKey = key.split(':')[1];
      }
      if (options && typeof options === 'object') {
        let result = translationKey;
        Object.entries(options).forEach(([optionKey, value]) => {
          result = result.replace(`{{${optionKey}}}`, String(value));
        });
        return result;
      }
      return translationKey;
    },
  }),
  useTolgee: () => ({
    getLanguage: () => 'en',
    t: (key: string, options?: any) => {
      let translationKey = key;
      if (key.includes(':')) {
        translationKey = key.split(':')[1];
      }
      if (options && typeof options === 'object') {
        let result = translationKey;
        Object.entries(options).forEach(([optionKey, value]) => {
          result = result.replace(`{{${optionKey}}}`, String(value));
        });
        return result;
      }
      return translationKey;
    },
  }),
}));

// Mock hooks
vi.mock('@/hooks', () => ({
  useExplorer: vi.fn(),
  useKbAccessControl: vi.fn(),
  useApiHandler: vi.fn(),
  useFileUpload: () => ({
    isUploading: false,
    error: null,
    uploadFiles: vi.fn(),
    uploaders: {},
    validateFiles: vi.fn(),
  }),
}));

// Mock necessary hooks and components are handled by unit test utilities

vi.mock('@/contexts', () => ({
  useDocumentContext: vi.fn(),
  useAppContext: vi.fn(),
  UploaderContextProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

vi.mock('@/components/KBBreadcrumbs', () => ({
  default: ({ items }: { items: any[] }) => (
    <div data-testid='kb-breadcrumbs'>
      {items.map((item, index) => (
        <span key={item.title || index} data-testid={`breadcrumb-${index}`}>
          {item.title}
        </span>
      ))}
    </div>
  ),
}));

vi.mock('@/components/common/AccessIcon', () => ({
  default: () => <div data-testid='access-icon' />,
}));

vi.mock('@/components/common/FileIcon', () => ({
  default: () => <div data-testid='file-icon' />,
}));

vi.mock('@/components/common/FileStatusIcon', () => ({
  default: () => <div data-testid='file-status-icon' />,
}));

describe('DocumentPage', () => {
  const mockDocument = {
    id: 'test-doc-id',
    metadata: {
      name: 'Test Document',
      createdBy: {
        displayName: 'Test User',
        email: '<EMAIL>',
      },
      uploadStatus: FILE_UPLOAD_STATUS.uploaded,
      size: 1024,
      createdAt: '2024-04-03T10:00:00Z',
      updatedAt: '2024-04-03T10:00:00Z',
    },
    accessLevel: ACCESS_LEVEL.private,
    parentDirId: '/root',
    parentDirBreadcrumbArray: ['Root', 'Folder'],
    parentDirPath: '/root/folder',
  };

  const mockGetDocument = vi.fn();
  const mockDeleteDocument = vi.fn();
  const mockUpdateDocument = vi.fn();
  const mockDownloadDocument = vi.fn();
  const mockOpenConfirmModal = vi.fn();
  const mockCloseConfirmModal = vi.fn();
  const mockHandleApiRequest = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    mockLibraries();

    // Setup default mock implementations
    Object.assign(mockParams, { documentId: 'test-doc-id' });
    mockNavigate.mockImplementation(vi.fn());

    vi.mocked(useDocumentContext).mockReturnValue({
      getDocument: mockGetDocument,
      deleteDocument: mockDeleteDocument,
      updateDocument: mockUpdateDocument,
      downloadDocument: mockDownloadDocument,
      isDownloading: false,
    } as any);

    vi.mocked(useAppContext).mockReturnValue({
      openConfirmModal: mockOpenConfirmModal,
      closeConfirmModal: mockCloseConfirmModal,
    } as any);

    vi.mocked(useExplorer).mockReturnValue({
      expanded: false,
    } as any);

    vi.mocked(useKbAccessControl).mockReturnValue({
      permDocument: true,
    } as any);

    vi.mocked(useApiHandler).mockReturnValue({
      handleApiRequest: mockHandleApiRequest,
    } as any);

    mockHandleApiRequest.mockResolvedValue({ data: mockDocument });
  });

  const renderPage = () => {
    return renderWithProviders(<DocumentPage />);
  };

  it('renders document details correctly', async () => {
    renderPage();

    await waitFor(() => {
      // Check for the title
      const title = screen.getByRole('heading', { name: 'Test Document' });
      expect(title).toBeInTheDocument();

      // Check for the author name
      expect(screen.getByText('Test User')).toBeInTheDocument();

      // Check for breadcrumbs
      expect(screen.getByTestId('kb-breadcrumbs')).toBeInTheDocument();
      expect(screen.getByTestId('breadcrumb-0')).toHaveTextContent('tree.root');
      expect(screen.getByTestId('breadcrumb-1')).toHaveTextContent('Folder');
      expect(screen.getByTestId('breadcrumb-2')).toHaveTextContent('Test Document');
    });
  });

  it('renders breadcrumbs correctly', async () => {
    renderPage();

    await waitFor(() => {
      expect(screen.getByTestId('kb-breadcrumbs')).toBeInTheDocument();
      expect(screen.getByTestId('breadcrumb-0')).toHaveTextContent('tree.root');
      expect(screen.getByTestId('breadcrumb-1')).toHaveTextContent('Folder');
      expect(screen.getByTestId('breadcrumb-2')).toHaveTextContent('Test Document');
    });
  });

  it('handles document download', async () => {
    renderPage();

    await waitFor(() => {
      const downloadButton = screen.getByText('menu.download');
      fireEvent.click(downloadButton);
      expect(mockDownloadDocument).toHaveBeenCalledWith('test-doc-id', 'Test Document');
    });
  });

  it('handles document deletion', async () => {
    vi.mocked(useKbAccessControl).mockReturnValue({
      permDocument: {
        canDelete: true,
      },
    } as any);
    renderPage();

    await waitFor(() => {
      const deleteButton = screen.getByText('menu.delete');
      fireEvent.click(deleteButton);
      expect(mockOpenConfirmModal).toHaveBeenCalledWith(
        expect.objectContaining({
          title: 'document.detail.deleteConfirmTitle',
          options: { isRemoving: true },
        })
      );
    });
  });
  it('Hides delete button and disable access level change when user does not have permission', async () => {
    vi.mocked(useKbAccessControl).mockReturnValue({
      permDocument: {
        canDelete: false,
        canUpdateAccessLevel: false,
      },
    } as any);
    renderPage();

    await waitFor(() => {
      const deleteButton = screen.queryByText('menu.delete');
      expect(deleteButton).not.toBeInTheDocument();
      const publicRadio = screen.getByTestId('access-level-radio-public');
      expect(publicRadio).toBeDisabled();
      const privateRadio = screen.getByTestId('access-level-radio-private');
      expect(privateRadio).toBeDisabled();
    });
  });

  it('handles access level change', async () => {
    renderPage();

    await waitFor(() => {
      const publicRadio = screen.getByTestId('access-level-radio-public');
      fireEvent.click(publicRadio);
      expect(mockOpenConfirmModal).toHaveBeenCalled();
    });

    await waitFor(() => {
      const privateRadio = screen.getByTestId('access-level-radio-private');
      fireEvent.click(privateRadio);
      expect(mockOpenConfirmModal).toHaveBeenCalled();
    });
  });

  // it('handles error state when document fetch fails', async () => {
  //   mockHandleApiRequest.mockRejectedValue(new Error('Failed to fetch document'));
  //   renderPage();

  //   await waitFor(() => {
  //     expect(screen.getByText('error.fetchingDocument')).toBeInTheDocument();
  //   });
  // });
});
