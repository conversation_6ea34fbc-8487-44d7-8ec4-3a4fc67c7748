import { Box, Flex, LoadingOverlay, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure, useIntersection } from '@mantine/hooks';
import { usePathName } from '@resola-ai/ui/hooks';
import { useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import isNil from 'lodash/isNil';
import type React from 'react';
import { useCallback, useEffect, useState } from 'react';

import { GridView, KBContextMenu, KBSearch, RecentView } from '@/components';
import { GridEmpty } from '@/components/common';
import { HOME_PATH, ROOT_PATH } from '@/constants/folder';
import {
  SearchContextProvider,
  useAppContext,
  useDocumentContext,
  useFolderContext,
  useKnowledgeBaseContext,
} from '@/contexts';
import { useExplorerContext } from '@/contexts/ExplorerContext';
import { useExplorer, useKbSearch } from '@/hooks';
import type { DocumentFile, Explorer, Folder, KnowledgeBaseDirectionQuery, Recent } from '@/types';
import { isDocument, isFolder } from '@/utils/tree';
import { AdvancedSearchBox } from '@resola-ai/ui';

const useStyles = createStyles((theme) => ({
  container: {
    width: `calc(${rem('100%')} - ${rem(56)})`,
    marginLeft: rem(56),
  },
  innerContainer: {
    display: 'flex',
    width: rem('100%'),
    position: 'relative',
  },
  wrapper: {
    padding: rem(40),
    width: rem('100%'),
  },
  wrapperExpanded: {
    marginLeft: rem(244),
  },
  wrapperEmptyExpanded: {
    width: 'unset',
    marginLeft: rem(244),
  },
  title: {
    fontWeight: 700,
    fontSize: rem(32),
    lineHeight: rem(48),
    color: theme.colors.decaNavy[5],
  },
  searchBox: {
    width: rem(400),
    '& input': {
      width: rem(400),
      height: rem(38),
    },
  },
  gridWrapper: {
    marginBottom: rem(24),
  },
  loadingOverlay: {
    position: 'fixed',
  },
  bottomTarget: {
    height: rem(10),
  },
}));

const HomePage: React.FC = () => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate(['home', 'common']);
  const { expanded, setIsSearching, triggerRefetch, setTriggerRefetch } = useExplorer();
  const { deleteKnowledgeBase, isLoading } = useKnowledgeBaseContext();
  const { deleteDocument, downloadDocument } = useDocumentContext();
  const { getFolders, getRecentlyViewed, deleteFolder, shouldRefresh, setShouldRefresh } =
    useFolderContext();
  const { explorers, fetchMoreExplorers, getExplorers } = useExplorerContext();
  const { openConfirmModal, closeConfirmModal, openNoticeModal } = useAppContext();
  const [folders, setFolders] = useState<Folder[]>();
  const [searchKeyword, setSearchKeyword] = useState<string | undefined>(undefined);
  const [searchFilter, setSearchFilter] = useState<string | undefined>(undefined);

  const [recentlyViewed, setRecentlyViewed] = useState<Recent[]>([]);
  const [opened, { open, close }] = useDisclosure(false);
  const [selectedItem, setSelectedItem] = useState<Folder | Explorer>();
  const pathName = usePathName();
  const { ref: bottomRef, entry: bottomEntry } = useIntersection({
    root: null,
    threshold: 1,
  });
  const { getSuggestions, adjustSearchQuery, renderSearchOptions } = useKbSearch();

  /**
   * Fetch explorers with direction and cursor
   * @param direction
   * @param cursor
   * @returns void
   */
  const fetchExplorers = useCallback(
    (direction: KnowledgeBaseDirectionQuery = 'backward', cursor = '') => {
      getExplorers(ROOT_PATH, cursor, direction);
    },
    [getExplorers]
  );

  /**
   * Fetch folders from root path
   * @returns void
   */
  const fetchFolders = useCallback(async () => {
    const response = await getFolders(ROOT_PATH, 1);

    if (response) {
      setFolders(response.data ?? []);
      setShouldRefresh(false);
      setTriggerRefetch(false);
    }
  }, [getFolders]);

  /**
   * Fetch recently viewed
   * @returns void
   */
  const fetchRecentlyViewed = useCallback(async () => {
    const response = await getRecentlyViewed();

    if (response) {
      setRecentlyViewed(response.data?.reverse() ?? []);
    }
  }, [getRecentlyViewed]);

  /**
   * Fetch all data (knowledge bases, folders, recently viewed)
   * @returns void
   */
  const fetchAll = useCallback(() => {
    fetchExplorers();
    fetchFolders();
    fetchRecentlyViewed();
  }, [fetchExplorers, fetchFolders, fetchRecentlyViewed]);

  /**
   * Confirm delete item and delete it
   * @param item
   * @returns void
   */
  const onConfirmDelete = useCallback(async (item: Folder | Explorer) => {
    if (isFolder(item)) {
      await deleteFolder(item.id);
    } else if (isDocument(item)) {
      await deleteDocument(item.id);
    } else {
      await deleteKnowledgeBase(item.id);
    }
    setShouldRefresh(true);
    closeConfirmModal();

    if (
      (isFolder(item) && (item as Folder).childFolderCount > 0) ||
      (item as Folder).childKbCount > 0
    ) {
      openNoticeModal({
        title: t('modal.notice.folder.childProcess.title'),
        content: t('modal.notice.folder.childProcess.content'),
        showLoader: true,
      });
    }
  }, []);

  /**
   * Delete item with confirm modal
   * @param item
   * @returns void
   */
  const onDelete = useCallback((item: Folder | Explorer) => {
    openConfirmModal({
      onConfirm: () => onConfirmDelete(item),
      title: t(isFolder(item) ? 'modal.confirmFolderDelete' : 'modal.confirmKnowledgeBaseDelete'),
      confirmText: t('modal.delete'),
      options: { isRemoving: true },
    });
  }, []);

  const onDownload = useCallback((item: Folder | Explorer) => {
    if (isDocument(item)) {
      const document = item as DocumentFile;
      downloadDocument(document.id, document.metadata?.name);
    }
  }, []);

  /**
   * Open edit modal
   * @param item
   * @returns void
   */
  const onEdit = useCallback((item: Folder | Explorer) => {
    setSelectedItem(item);
    open();
  }, []);

  /**
   * Submit context menu: refresh data
   * @returns void
   */
  const onContextSubmit = useCallback(() => {
    close();
    fetchAll();
  }, []);

  useEffect(() => {
    fetchAll();
  }, []);

  useEffect(() => {
    if ((shouldRefresh || triggerRefetch) && pathName === HOME_PATH) {
      fetchAll();
    }
  }, [shouldRefresh, triggerRefetch]);

  useEffect(() => {
    setIsSearching(Boolean(searchKeyword));
  }, [searchKeyword]);

  useEffect(() => {
    if (bottomEntry?.isIntersecting && !isLoading) {
      fetchMoreExplorers();
    }
  }, [bottomEntry]);

  if (isNil(explorers) || isNil(folders)) return <LoadingOverlay visible />;

  return isEmpty(explorers) && isEmpty(folders) ? (
    <Box className={classes.container}>
      <Box className={cx(classes.wrapper, expanded && classes.wrapperEmptyExpanded)}>
        <Title className={classes.title} ta='center' mb={rem(60)}>
          {t('pageTitle')}
        </Title>
        <GridEmpty isNewVersion onFetch={fetchAll} />
      </Box>
    </Box>
  ) : (
    <Box className={classes.container}>
      <Box className={classes.innerContainer}>
        <Box className={cx(classes.wrapper, expanded && classes.wrapperExpanded)}>
          <Flex
            direction={'column'}
            w={'100%'}
            align={'center'}
            justify={'center'}
            gap={rem(24)}
            mb={rem(60)}
          >
            <Title className={classes.title}>{t('pageTitle')}</Title>
            <AdvancedSearchBox
              placeholder={t('searchPlaceholder', { ns: 'common' })}
              className={classes.searchBox}
              optionsRenderer={renderSearchOptions}
              formatKeyword={adjustSearchQuery}
              getSuggestions={getSuggestions}
              onSearch={({ keyword, filter }) => {
                setSearchKeyword(keyword);
                setSearchFilter(filter);
              }}
            />
          </Flex>
          {!isNil(searchKeyword) && (
            <SearchContextProvider>
              <KBSearch keyword={searchKeyword} searchFilter={searchFilter} />
            </SearchContextProvider>
          )}
          {isNil(searchKeyword) && (
            <>
              <RecentView
                recentList={recentlyViewed}
                isLayoutChange={expanded}
                onEdit={onEdit}
                onDelete={onDelete}
              />
              <Box className={classes.gridWrapper}>
                <LoadingOverlay className={classes.loadingOverlay} visible={isLoading} />
                <GridView
                  folders={folders}
                  explorers={explorers}
                  onFetch={() => fetchAll()}
                  isLayoutChange={expanded}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onDownload={onDownload}
                />
              </Box>
              <Box className={classes.bottomTarget} ref={bottomRef} />
            </>
          )}
        </Box>
        <KBContextMenu
          opened={opened}
          onClose={close}
          onSubmitted={onContextSubmit}
          item={selectedItem}
        />
      </Box>
    </Box>
  );
};

export default HomePage;
