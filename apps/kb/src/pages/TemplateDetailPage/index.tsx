import { KBSettingLayout, TemplateDetail } from '@/components';
import { TemplateDetailContextProvider } from '@/contexts/TemplateDetailContext';
import { Box, Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useParams } from 'react-router-dom';

const useStyles = createStyles(() => ({
  container: {
    width: '100%',
    position: 'relative',
    padding: `${rem(16)} ${rem(32)}`,
  },
}));

const TemplateDetailPage: React.FC = () => {
  const { classes } = useStyles();
  const { templateId } = useParams();

  return (
    <KBSettingLayout>
      <TemplateDetailContextProvider>
        <Box className={classes.container}>
          <Flex direction='column' gap={rem(16)} pb={rem(36)}>
            <TemplateDetail templateId={templateId} />
          </Flex>
        </Box>
      </TemplateDetailContextProvider>
    </KBSettingLayout>
  );
};

export default TemplateDetailPage;
