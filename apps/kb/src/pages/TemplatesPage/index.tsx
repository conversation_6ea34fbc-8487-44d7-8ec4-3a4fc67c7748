import { KBSettingLayout, TemplateGrid } from '@/components';
import { TemplatesContextProvider } from '@/contexts/TemplatesContext';
import { Box, Flex, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';

const useStyles = createStyles(() => ({
  container: {
    width: '100%',
    position: 'relative',
    padding: `${rem(24)} ${rem(32)}`,
  },
}));

const TemplatesPage: React.FC = () => {
  const { classes } = useStyles();
  const { t } = useTranslate('settings');

  return (
    <KBSettingLayout>
      <TemplatesContextProvider>
        <Box className={classes.container}>
          <Flex direction='column' gap={rem(36)} pb={rem(36)}>
            <Title order={2} c='decaNavy.5'>
              {t('templates.title')}
            </Title>
            <TemplateGrid />
          </Flex>
        </Box>
      </TemplatesContextProvider>
    </KBSettingLayout>
  );
};

export default TemplatesPage;
