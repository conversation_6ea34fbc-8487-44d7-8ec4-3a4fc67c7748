import { JobResult } from '@/components/KBJobResult';
import { NAVBAR_WIDTH } from '@/constants/ui';
import { JobDetailProvider } from '@/contexts/JobDetailContext';
import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useParams } from 'react-router-dom';

const useStyles = createStyles(() => ({
  pageContainer: {
    position: 'relative',
    width: '100%',
    marginLeft: rem(NAVBAR_WIDTH),
    padding: `${rem(24)} ${rem(40)} ${rem(40)}`,
  },
}));

const JobResultPage = () => {
  const { jobId } = useParams<{ jobId: string }>();
  const { classes } = useStyles();

  if (!jobId) return null;

  return (
    <JobDetailProvider>
      <Box className={classes.pageContainer}>
        <JobResult jobId={jobId} />
      </Box>
    </JobDetailProvider>
  );
};

export default JobResultPage;
