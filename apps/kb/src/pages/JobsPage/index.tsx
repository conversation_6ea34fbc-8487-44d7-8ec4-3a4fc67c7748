import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>JobButton } from '@/components';
import { PageTitle } from '@/components/common';
import { NAVBAR_WIDTH } from '@/constants/ui';
import { JobContext, JobContextProvider } from '@/contexts';
import useKbAccessControl from '@/hooks/useKbAccessControl';
import { Box, Flex, Grid, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';

const useStyles = createStyles((theme) => ({
  container: {
    position: 'relative',
    width: '100%',
    marginLeft: rem(NAVBAR_WIDTH),
    padding: rem(40),
  },
  description: {
    fontSize: theme.fontSizes.md,
    lineHeight: theme.other.lineHeight?.md,
    color: theme.colors.decaGrey[6],
  },
}));

const JobsPage: React.FC = () => {
  const { classes } = useStyles();
  const { permJob } = useKbAccessControl();
  const { t } = useTranslate('job');

  return (
    <JobContextProvider>
      <Box className={classes.container}>
        <Grid justify='space-between'>
          <Grid.Col span={8}>
            <Flex direction='column' gap={rem(20)} mb={rem(30)}>
              <PageTitle title={t('title')} />
              <Text className={classes.description}>{t('description')}</Text>
            </Flex>
          </Grid.Col>
          <Grid.Col span={4} ta='right' pt={rem(20)}>
            {permJob.canCreate && (
              <JobContext.Consumer>
                {(context) =>
                  context ? <RunJobButton afterGenerated={context.fetchJobs} /> : null
                }
              </JobContext.Consumer>
            )}
          </Grid.Col>
        </Grid>
        <KBJobs />
      </Box>
    </JobContextProvider>
  );
};

export default JobsPage;
