import { Container, Flex, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import type React from 'react';

import KBGrid from '@/components/KBGrid';

const useStyles = createStyles((theme) => ({
  container: {
    width: rem('100%'),
    maxWidth: rem(1200),
    paddingTop: rem(40),
  },
  title: {
    fontWeight: 700,
    fontSize: rem(20),
    lineHeight: rem(31),
    color: theme.colors.decaNavy[5],
  },
}));

const QnAPage: React.FC = () => {
  const { classes } = useStyles();
  const { t } = useTranslate('home');

  return (
    <Container className={classes.container} size='lg'>
      <Flex w={'100%'} align={'center'} h={rem(50)}>
        <Title className={classes.title}>{t('pageTitle')}</Title>
      </Flex>
      <KBGrid />
    </Container>
  );
};

export default QnAPage;
