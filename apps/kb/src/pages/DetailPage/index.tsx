import {
  ArticleCollection,
  KBBasicInfo,
  KBBreadcrumbs,
  KBDetailForm,
  KBDocuments,
  Modal,
} from '@/components';
import { NAVBAR_WIDTH } from '@/constants/ui';
import { ArticleContextProvider, useKnowledgeBaseContext } from '@/contexts';
import { useApiHandler, useExplorer, useKbAccessControl } from '@/hooks';
import { KB_TYPE, type KnowledgeBase, type KnowledgeBaseType } from '@/types';
import { scrollToTop } from '@/utils';
import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import type { BreadcrumbItem } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

const useStyles = createStyles(() => ({
  container: {
    width: rem('100%'),
    height: rem('100%'),
    marginLeft: rem(NAVBAR_WIDTH),
    padding: rem(40),
    paddingTop: 0,
  },
  containerExpanded: {
    marginLeft: rem(300),
  },
}));

const DetailPage: React.FC = () => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate(['kb', 'home']);
  const { kbId } = useParams();
  const { expanded } = useExplorer();
  const { getKnowledgeBase } = useKnowledgeBaseContext();
  const { permKb } = useKbAccessControl();
  const [opened, { open, close }] = useDisclosure(false);
  const [kb, setKb] = useState<KnowledgeBase>();
  const { handleApiRequest } = useApiHandler();

  const fetchKbDetail = useCallback(async () => {
    if (kbId) {
      const responseData = await handleApiRequest(getKnowledgeBase(kbId));
      if (responseData) {
        setKb(responseData as KnowledgeBase);
      }
    }
  }, [getKnowledgeBase, kbId, handleApiRequest]);

  /**
   * Define the Current Breadcrumbs
   * @type {BreadcrumbsItems[]}
   * @dependencies t: i18n function, kb: KnowledgeBase, kbId: string
   */
  const currentBreadcrumbs = useMemo(() => {
    const items = kb?.parentDirBreadcrumbArray ?? [];
    const ids = kb?.parentDirPath?.split('/').filter(Boolean) ?? [];

    return [
      { title: t('tree.root', { ns: 'home' }), href: '/kb/' },
      ...items.map((item, index) =>
        index === 0
          ? undefined
          : {
              title: item,
              href: `/kb/folder/${ids[index]}`,
            }
      ),
      { title: kb?.name ?? '', href: `/kb/${kbId}` },
    ].filter(Boolean) as BreadcrumbItem[];
  }, [kb, kbId, t]);

  /**
   * Render Knowledge Base Detail
   * @param {KnowledgeBaseType} kbType
   * @param {KnowledgeBase} kbData
   * @returns {JSX.Element}
   */
  const kbDetailRender = useCallback((kbType: KnowledgeBaseType, kbData: KnowledgeBase) => {
    switch (kbType) {
      case KB_TYPE.document:
        return <KBDocuments kb={kbData} />;

      case KB_TYPE.article:
        return (
          <ArticleContextProvider>
            <ArticleCollection kb={kbData} />
          </ArticleContextProvider>
        );

      default:
        return null;
    }
  }, []);

  const onSubmitted = useCallback(() => {
    fetchKbDetail();
    close();
  }, []);

  useEffect(() => {
    fetchKbDetail();
    scrollToTop();
  }, []);

  return (
    <Box className={cx(classes.container, expanded && classes.containerExpanded)}>
      <KBBreadcrumbs items={currentBreadcrumbs} expanded={expanded} />
      {kb ? (
        <>
          <KBBasicInfo
            name={kb.name}
            description={kb.description}
            onEdit={permKb.canUpdate ? open : undefined}
            type={kb.baseType}
          />
          {kbDetailRender(kb.baseType, kb)}
        </>
      ) : null}
      <Modal opened={opened} onClose={close} title={t('kbCreateModalTitle', { ns: 'kb' })}>
        <KBDetailForm onSubmitted={onSubmitted} onCancel={close} isEdit knowledgeBase={kb} />
      </Modal>
    </Box>
  );
};

export default DetailPage;
