import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { memo } from 'react';
import { useParams } from 'react-router-dom';

import { JobDetail } from '@/components';
import { NAVBAR_WIDTH } from '@/constants/ui';
import { JobDetailProvider } from '@/contexts';

const useStyles = createStyles(() => ({
  pageContainer: {
    position: 'relative',
    width: '100%',
    marginLeft: rem(NAVBAR_WIDTH),
    padding: `${rem(24)} ${rem(40)} ${rem(40)}`,
  },
}));

const JobDetailPage = () => {
  const { classes } = useStyles();
  const { jobId } = useParams<{ jobId: string }>();

  if (!jobId) {
    return null;
  }

  return (
    <JobDetailProvider>
      <Box className={classes.pageContainer}>
        <JobDetail jobId={jobId} />
      </Box>
    </JobDetailProvider>
  );
};

export default memo(JobDetailPage);
