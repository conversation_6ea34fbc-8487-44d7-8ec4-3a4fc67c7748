import {
  GridEmpty,
  GridView,
  KBBreadcrumbs,
  KBContextMenu,
  KBCreateModal,
  KBSearch,
  SearchBox,
} from '@/components';
import { ExportJobByEntityButton } from '@/components/KBExport/components/ExportJobByEntityButton';
import { NAVBAR_WIDTH } from '@/constants/ui';
import {
  SearchContextProvider,
  useAppContext,
  useDocumentContext,
  useFolderContext,
  useKnowledgeBaseContext,
} from '@/contexts';
import { useExplorerContext } from '@/contexts/ExplorerContext';
import { useApiHandler, useExplorer } from '@/hooks';
import type { DocumentFile, Explorer, Folder } from '@/types';
import { EntityType } from '@/types/entities';
import { scrollToTop } from '@/utils';
import { isDocument, isFolder } from '@/utils/tree';
import { Box, Flex, Group, LoadingOverlay, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure, useIntersection } from '@mantine/hooks';
import type { BreadcrumbItem } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import isEmpty from 'lodash/isEmpty';
import isNil from 'lodash/isNil';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

const useStyles = createStyles(() => ({
  container: {
    width: rem('100%'),
    marginLeft: rem(NAVBAR_WIDTH),
    display: 'flex',
  },
  wrapper: {
    padding: rem(40),
    paddingTop: 0,
    width: rem('100%'),
  },
  wrapperExpanded: {
    marginLeft: rem(244),
  },
  searchBox: {
    width: rem(400),
    '& input': {
      width: rem(400),
      height: rem(38),
    },
  },
  loadingOverlay: {
    position: 'fixed',
  },
  bottomTarget: {
    height: rem(10),
  },
}));

const FolderPage: React.FC = () => {
  const { classes, cx } = useStyles();
  const { folderId } = useParams();
  const { t } = useTranslate(['kb', 'home']);
  const [folders, setFolders] = useState<Folder[]>([]);
  const { expanded, setIsSearching, triggerRefetch, setTriggerRefetch } = useExplorer();
  const { isLoading, deleteKnowledgeBase } = useKnowledgeBaseContext();
  const { explorers, getExplorers, fetchMoreExplorers } = useExplorerContext();
  const { deleteDocument, downloadDocument } = useDocumentContext();
  const { getFolders, getFolder, deleteFolder, shouldRefresh, setShouldRefresh } =
    useFolderContext();
  const { openConfirmModal, closeConfirmModal, openNoticeModal } = useAppContext();
  const { ref: bottomRef, entry: bottomEntry } = useIntersection({
    root: null,
    threshold: 1,
  });
  const { handleApiRequest } = useApiHandler();

  const [mainFolder, setMainFolder] = useState<Folder>();
  const [searchKeyword, setSearchKeyword] = useState<string>('');
  const [opened, { open, close }] = useDisclosure(false);
  const [selectedItem, setSelectedItem] = useState<Folder | Explorer>();

  const fetchExplorers = useCallback(
    async (currentFolderId: string) => {
      if (currentFolderId) {
        getExplorers(currentFolderId);
      }
    },
    [getExplorers]
  );

  const fetchFolders = useCallback(
    async (currentFolderId: string) => {
      if (currentFolderId) {
        const response = await getFolders(currentFolderId, 1);

        if (response) {
          setFolders((response.data ?? []).filter((folder) => folder.id !== currentFolderId));
          setShouldRefresh(false);
          setTriggerRefetch(false);
        }
      }
    },
    [getFolders]
  );

  const fetchMainFolder = useCallback(async () => {
    if (folderId) {
      const response = await handleApiRequest(getFolder(folderId));

      if (response) {
        setMainFolder(response.data);
      }
    }
  }, [folderId]);

  const fetchAll = useCallback(
    async (currentFolderId?: string) => {
      if (!currentFolderId) return;
      fetchExplorers(currentFolderId);
      fetchFolders(currentFolderId);
    },
    [fetchExplorers, fetchFolders]
  );

  const breadcrumbs = useMemo(() => {
    if (!mainFolder) return [];

    const items = mainFolder.breadcrumbArray || [];
    const ids = mainFolder.path?.split('/').filter(Boolean) ?? [];
    return [
      { title: t('tree.root', { ns: 'home' }), href: '/kb/' },
      ...items.map((item, index) =>
        index === 0
          ? undefined
          : {
              title: item,
              href: `/kb/folder/${ids[index]}`,
            }
      ),
    ].filter(Boolean) as BreadcrumbItem[];
  }, [mainFolder]);

  useEffect(() => {
    fetchMainFolder();
    fetchAll(folderId);
    scrollToTop();
  }, [folderId]);

  useEffect(() => {
    if ((shouldRefresh || triggerRefetch) && folderId) {
      fetchAll(folderId);
    }
  }, [shouldRefresh, triggerRefetch]);

  useEffect(() => {
    setIsSearching(Boolean(searchKeyword));
  }, [searchKeyword]);

  useEffect(() => {
    if (bottomEntry?.isIntersecting && !isLoading) {
      fetchMoreExplorers();
    }
  }, [bottomEntry]);

  const onConfirmDelete = useCallback(async (item: Folder | Explorer) => {
    if (isFolder(item)) {
      await deleteFolder(item.id);
    } else if (isDocument(item)) {
      await deleteDocument(item.id);
    } else {
      await deleteKnowledgeBase(item.id);
    }

    setShouldRefresh(true);
    closeConfirmModal();

    if (
      (isFolder(item) && (item as Folder).childFolderCount > 0) ||
      (item as Folder).childKbCount > 0
    ) {
      openNoticeModal({
        title: t('modal.notice.folder.childProcess.title', { ns: 'home' }),
        content: t('modal.notice.folder.childProcess.content', { ns: 'home' }),
        showLoader: true,
      });
    }
  }, []);

  const onDelete = useCallback((item: Folder | Explorer) => {
    openConfirmModal({
      onConfirm: () => onConfirmDelete(item),
      title: t(isFolder(item) ? 'modal.confirmFolderDelete' : 'modal.confirmKnowledgeBaseDelete', {
        ns: 'home',
      }),
      confirmText: t('modal.delete', { ns: 'home' }),
      options: { isRemoving: true },
    });
  }, []);

  const onDownload = useCallback((item: Folder | Explorer) => {
    if (isDocument(item)) {
      const document = item as DocumentFile;
      downloadDocument(document.id, document.metadata?.name);
    }
  }, []);

  const onEdit = useCallback((item: Folder | Explorer) => {
    setSelectedItem(item);
    open();
  }, []);

  const onContextSubmit = useCallback((currentFolderId?: string) => {
    close();
    fetchAll(currentFolderId);
  }, []);

  if (isNil(explorers) || isNil(folders)) return <LoadingOverlay visible />;

  return mainFolder ? (
    <Box className={classes.container}>
      <Box className={cx(classes.wrapper, expanded && classes.wrapperExpanded)}>
        <KBBreadcrumbs items={breadcrumbs} expanded={expanded} />
        <Title order={2} c='decaNavy.5' mt={rem(103)}>
          {mainFolder.name}
        </Title>
        {isEmpty(explorers) && isEmpty(folders) ? (
          <Box mt={rem(40)} h={'100%'}>
            <GridEmpty
              isNewVersion
              parentDirId={folderId}
              onFetch={() => fetchAll(folderId)}
              folderLevel={breadcrumbs.length}
            />
          </Box>
        ) : (
          <Box pb={rem(30)}>
            <Flex gap={rem(10)} justify='space-between' align='center' my={rem(40)}>
              <Box>
                <SearchBox className={classes.searchBox} onSearch={setSearchKeyword} />
              </Box>
              <Group gap={rem(12)}>
                {folderId && (
                  <ExportJobByEntityButton
                    entityId={folderId}
                    entityType={EntityType.FOLDER}
                    onSuccess={() => fetchAll(folderId)}
                  />
                )}
                <KBCreateModal
                  isNewVersion
                  onClose={() => fetchAll(folderId)}
                  parentDirId={folderId}
                  folderLevel={breadcrumbs.length}
                />
              </Group>
            </Flex>
            {searchKeyword && (
              <SearchContextProvider>
                <KBSearch folderId={folderId} keyword={searchKeyword} />
              </SearchContextProvider>
            )}
            {!searchKeyword && (
              <>
                <LoadingOverlay className={classes.loadingOverlay} visible={isLoading} />
                <GridView
                  folders={folders}
                  explorers={explorers}
                  onFetch={() => fetchAll(folderId)}
                  isLayoutChange={expanded}
                  isFolderLayout
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onDownload={onDownload}
                />
                <Box className={classes.bottomTarget} ref={bottomRef} />
              </>
            )}
          </Box>
        )}
      </Box>
      <KBContextMenu
        opened={opened}
        onClose={close}
        onSubmitted={() => onContextSubmit(folderId)}
        item={selectedItem}
      />
    </Box>
  ) : null;
};

export default FolderPage;
