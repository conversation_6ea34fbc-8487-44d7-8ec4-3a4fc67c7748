import { Box, Container, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';

import { BackToLink, CustomStepper, KBQnASteps } from '@/components';
import { useKbContext } from '@/contexts/KbContext';
import type { KnowledgeBase } from '@/types';
import { type QnAGenerateRequest, QnAGenerateStep, type QnAJob } from '@/types/qna';
import { getQnAJobStatusMapping } from '@/utils/qna';
import { generateStep } from '@/utils/stepper';

const useStyles = createStyles((theme) => ({
  container: {
    width: rem('100%'),
    height: rem('100%'),
    maxWidth: rem(1200),
    paddingTop: rem(48),
  },
  title: {
    fontWeight: 700,
    fontSize: rem(20),
    lineHeight: rem(31),
    color: theme.colors.decaNavy[5],
    marginBottom: rem(30),
    padding: `${rem(5)} ${rem(10)}`,
  },
  wrapper: {
    padding: `0 ${rem(170)}`,
    display: 'flex',
    flexDirection: 'column',
    gap: rem(40),
  },
}));

const GeneratePage: React.FC<any> = () => {
  const navigate = useNavigate();
  const { classes } = useStyles();
  const { t } = useTranslate(['common', 'kb']);
  const { kbId } = useParams();
  const { getKnowledgeBase, getQnAJobStatus, generateQnA, cancelGenerateQnA } = useKbContext();
  const [kb, setKb] = useState<KnowledgeBase>();
  const [step, setStep] = useState<number>(QnAGenerateStep.DOCUMENT_SELECTION);
  const [job, setJob] = useState<QnAJob>();

  const fetchKbDetail = useCallback(async () => {
    if (kbId) {
      const res = await getKnowledgeBase(kbId);
      const jobStatus = await getQnAJobStatus(kbId);

      if (res?.type === 'document') {
        navigate(`/kb/qna/${kbId}`);
      }
      setStep(
        jobStatus?.status
          ? getQnAJobStatusMapping(jobStatus.status).step
          : QnAGenerateStep.DOCUMENT_SELECTION
      );
      setJob(jobStatus);
      setKb(res);
    }
  }, [getKnowledgeBase, getQnAJobStatus, kbId, navigate]);

  const onGenerateQnA = useCallback(
    async (data: QnAGenerateRequest) => {
      const res = await generateQnA(data);

      if (res?.status === 'success') {
        setStep(getQnAJobStatusMapping(res.data.status).step);
      }
    },
    [generateQnA]
  );

  const onCancelGenerateQnA = useCallback(async () => {
    if (!kbId) return;
    const res = await cancelGenerateQnA(kbId);

    if (res?.status === 'success') {
      setStep(QnAGenerateStep.DOCUMENT_SELECTION);
    }
  }, [kbId, cancelGenerateQnA]);

  useEffect(() => {
    fetchKbDetail();
  }, []);

  const backToKBDetail = () => {
    navigate(kb ? `/kb/qna/${kb.id}` : '/kb');
  };

  if (!kb) {
    return null;
  }

  return (
    <Container size='lg' className={classes.container}>
      <BackToLink
        to={`/kb/qna/${kb.id}`}
        text={t('backToKBDetail', { kbName: kb.name || t('unknownKBName', { ns: 'kb' }) })}
      />
      <Title className={classes.title}>{t('generatePageTitle', { ns: 'kb' })}</Title>
      <Box className={classes.wrapper}>
        <CustomStepper steps={generateStep(t)} activeStep={step} setActiveStep={setStep} />
        <KBQnASteps
          kb={kb}
          job={job}
          activeStep={step}
          onGenerateQnA={onGenerateQnA}
          onCancelGenerateQnA={onCancelGenerateQnA}
          onSaveAsDraft={() => setStep(step + 1)}
          backToKBDetail={backToKBDetail}
        />
      </Box>
    </Container>
  );
};

export default GeneratePage;
