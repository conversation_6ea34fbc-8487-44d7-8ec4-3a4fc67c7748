import BaseLayout from '@/components/BaseLayout';
import NavbarContainer from '@/components/Navbar';
import { NotFound } from '@/components/common/NotFound';
import type { EntityType } from '@/types/entities';
import { Box, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import type React from 'react';
import { useMemo } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';

const useStyles = createStyles((_theme, _, u) => ({
  container: {
    display: 'flex',
    width: '100%',
    height: '100%',
  },
  content: {
    flex: 1,
    marginLeft: rem(64),
    padding: rem(24),
    [u.smallerThan('md')]: {
      marginLeft: 0,
    },
  },
}));

const NotFoundPage: React.FC = () => {
  const { classes } = useStyles();
  const { entityType } = useParams();
  const [searchParams] = useSearchParams();
  const fromUrl = useMemo(() => searchParams.get('fromUrl') ?? '', [searchParams]);

  return (
    <BaseLayout navigationMobile={<NavbarContainer />}>
      <Box className={classes.content}>
        <NotFound entityType={entityType as EntityType} fromUrl={fromUrl} />
      </Box>
    </BaseLayout>
  );
};

export default NotFoundPage;
