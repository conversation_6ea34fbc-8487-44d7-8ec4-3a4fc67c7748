import { EntityType } from '@/types/entities';
import {
  mockLibraries,
  mockParams,
  renderWithProviders,
  updateMockSearchParams,
} from '@/utils/unitTest';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import NotFoundPage from './index';

// React router mocks are handled by unitTest.tsx

vi.mock('@/components/BaseLayout', () => ({
  default: ({ children }: { children: React.ReactNode }) => (
    <div data-testid='base-layout'>
      <div data-testid='navbar'>Navbar</div>
      {children}
    </div>
  ),
}));

vi.mock('@/components/common/NotFound', () => ({
  NotFound: ({ entityType, fromUrl }: { entityType?: EntityType; fromUrl?: string }) => (
    <div data-testid='not-found'>
      <span data-testid='entity-type'>{entityType || ''}</span>
      <span data-testid='from-url'>{fromUrl || ''}</span>
    </div>
  ),
}));

describe('NotFoundPage', () => {
  const TEST_ID = '01j0whnq70jzbegecbbjwh3ndh';

  const TEST_URLS = {
    DOCUMENT: `/documents/${TEST_ID}`,
    FOLDER: `/folders/${TEST_ID}`,
    BASE: `/bases/${TEST_ID}`,
    JOB: `/jobs/${TEST_ID}`,
    TEMPLATE: `/templates/${TEST_ID}`,
    DEFAULT: '/test-path',
    EMPTY: '',
  } as const;

  // Helper function to setup mocks for each test
  const setupMocks = (
    entityType: string = EntityType.DOCUMENT,
    fromUrl: string = TEST_URLS.DEFAULT
  ) => {
    // Update global mock objects
    mockParams.entityType = entityType;

    // Create new URLSearchParams for this test
    const searchParams = new URLSearchParams();
    if (fromUrl) {
      searchParams.set('fromUrl', fromUrl);
    }

    // Update the global mock to return the new URLSearchParams
    updateMockSearchParams(searchParams);
  };

  beforeEach(() => {
    vi.clearAllMocks();
    mockLibraries();
  });

  const renderPage = () => {
    return renderWithProviders(<NotFoundPage />);
  };

  it('renders the NotFoundPage with document entity type', () => {
    setupMocks(EntityType.DOCUMENT, TEST_URLS.DOCUMENT);

    renderPage();

    expect(screen.getByTestId('base-layout')).toBeInTheDocument();
    expect(screen.getByTestId('navbar')).toBeInTheDocument();
    expect(screen.getByTestId('not-found')).toBeInTheDocument();
    expect(screen.getByTestId('entity-type')).toHaveTextContent(EntityType.DOCUMENT);
    expect(screen.getByTestId('from-url')).toHaveTextContent(TEST_URLS.DOCUMENT);
  });

  it('renders the NotFoundPage with folder entity type', () => {
    setupMocks(EntityType.FOLDER, TEST_URLS.FOLDER);

    renderPage();

    expect(screen.getByTestId('entity-type')).toHaveTextContent(EntityType.FOLDER);
    expect(screen.getByTestId('from-url')).toHaveTextContent(TEST_URLS.FOLDER);
  });

  it('handles empty fromUrl parameter', () => {
    setupMocks(EntityType.DOCUMENT, TEST_URLS.EMPTY);

    renderPage();

    expect(screen.getByTestId('entity-type')).toHaveTextContent(EntityType.DOCUMENT);
    expect(screen.getByTestId('from-url')).toHaveTextContent(TEST_URLS.EMPTY);
  });

  it('renders with all required components', () => {
    setupMocks();

    renderPage();

    expect(screen.getByTestId('base-layout')).toBeInTheDocument();
    expect(screen.getByTestId('navbar')).toBeInTheDocument();
    expect(screen.getByTestId('not-found')).toBeInTheDocument();
  });

  it('handles different entity types', () => {
    const entityTypes = [
      EntityType.DOCUMENT,
      EntityType.FOLDER,
      EntityType.BASE,
      EntityType.JOB,
      EntityType.TEMPLATE,
    ];

    entityTypes.forEach((type) => {
      const urlKey = type.toUpperCase() as keyof typeof TEST_URLS;
      setupMocks(type, TEST_URLS[urlKey]);
      const { unmount } = renderPage();

      expect(screen.getByTestId('entity-type')).toHaveTextContent(type);
      expect(screen.getByTestId('from-url')).toHaveTextContent(TEST_URLS[urlKey]);

      unmount();
    });
  });
});
