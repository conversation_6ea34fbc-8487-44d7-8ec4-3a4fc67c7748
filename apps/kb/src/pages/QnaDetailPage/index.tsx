import { Container, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import { BackToLink, KBBasicForm, KBBasicInfo, KBDocuments, KBQnA, Modal } from '@/components';
import { useKbContext } from '@/contexts/KbContext';
import useKbAccessControl from '@/hooks/useKbAccessControl';
import { KB_TYPE, type KnowledgeBase } from '@/types';

const useStyles = createStyles(() => ({
  container: {
    width: rem('100%'),
    height: rem('100%'),
    maxWidth: rem(1200),
    paddingTop: rem(48),
  },
}));

const QnADetailPage: React.FC<any> = () => {
  const { classes } = useStyles();
  const { t } = useTranslate(['common', 'kb']);
  const { kbId } = useParams();
  const { getKnowledgeBase } = useKbContext();
  const { permKb } = useKbAccessControl();
  const [opened, { open, close }] = useDisclosure(false);
  const [kb, setKb] = useState<KnowledgeBase>();

  const fetchKbDetail = useCallback(async () => {
    if (kbId) {
      const res = await getKnowledgeBase(kbId);
      setKb(res);
    }
  }, [getKnowledgeBase, kbId]);

  useEffect(() => {
    fetchKbDetail();
  }, []);

  const onSubmitted = useCallback(() => {
    fetchKbDetail();
    close();
  }, []);

  return (
    <Container size='lg' className={classes.container}>
      <BackToLink to={'/kb/qna/'} text={t('backToKBList')} />
      {kb ? (
        <>
          <KBBasicInfo
            name={kb.name}
            description={kb.description}
            onEdit={permKb.canUpdate ? open : undefined}
          />
          {kb.type === KB_TYPE.document ? <KBDocuments kb={kb} /> : <KBQnA kb={kb} />}
        </>
      ) : null}
      <Modal opened={opened} onClose={close} title={t('kbCreateModalTitle', { ns: 'kb' })}>
        <KBBasicForm onSubmitted={onSubmitted} onCancel={close} isEdit knowledgeBase={kb} />
      </Modal>
    </Container>
  );
};

export default QnADetailPage;
