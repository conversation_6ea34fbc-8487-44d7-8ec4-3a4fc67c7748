import type { JobArticleDownloadFormat } from '@/types';
export const DEFAULT_DOCUMENT_RETRIEVE_LIMIT = 10;
export const MAX_DOCUMENT_SIZE = 150;
export const SPECIAL_FILE_TYPES = {
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx',
  'application/msword': 'doc',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'pptx',
  'application/vnd.ms-powerpoint': 'ppt',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
  'application/vnd.ms-excel': 'xls',
};

export const DOCUMENT_ACCEPT_TYPES = [
  'application/pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-excel',
];

export const STORAGE_UNITS = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

export const AVATAR_URL = 'images/avatar.svg';

export const JOB_ARTICLE_DOWNLOAD_FORMATS = {
  csv: {
    type: 'csv' as JobArticleDownloadFormat,
    extension: 'csv',
    mimeType: 'text/csv;charset=utf-8;',
  },
  excel: {
    type: 'excel' as JobArticleDownloadFormat,
    extension: 'xlsx',
    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  },
};
