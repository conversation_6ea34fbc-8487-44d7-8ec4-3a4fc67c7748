import type { Article } from '@/types';
import type { ReactNode } from 'react';
import { type Mock, vi } from 'vitest';

// Constants - keeping only what's used in tests
export const TEST_IDS = {
  ARTICLE_ID: 'test-article-id',
  BASE_ID: 'test-base-id',
  USER_ID: 'user-123',
};

// Mock data - keeping only what's used
export const MOCK_ARTICLE = {
  id: TEST_IDS.ARTICLE_ID,
  baseId: TEST_IDS.BASE_ID,
  title: 'Test Article',
  content: 'Test content',
  createdBy: {
    id: TEST_IDS.USER_ID,
    name: 'Test User',
  },
};

// Create a complete mock article that satisfies the required type
export const COMPLETE_MOCK_ARTICLE: Article = {
  ...MOCK_ARTICLE,
  contentRaw: 'Raw content',
  updatedAt: new Date('2023-01-01'),
  createdAt: new Date('2023-01-01'),
  keywords: ['test'],
  status: 'published',
  createdBy: {
    ...MOCK_ARTICLE.createdBy,
    orgId: 'org-123',
    createdAt: '2023-01-01',
    displayName: 'Display Name',
    email: '<EMAIL>',
    familyName: 'User',
    givenName: 'Test',
    picture: '',
    updatedAt: '2023-01-01',
  },
};

// Types - keeping as they define the API structure
export type MockedArticleAPI = {
  get: Mock;
  update: Mock;
  delete: Mock;
  uploadFile: Mock;
  uploadFileWhenCreating: Mock;
};

// Mock API services - keeping only what's needed for file upload tests
vi.mock('@/services/api/v2', () => ({
  ArticleAPI: {
    get: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    uploadFile: vi.fn(),
    uploadFileWhenCreating: vi.fn(),
  },
}));

// Mock UploaderContext - keeping as it's used in the wrapper
export const mockUploaderContext = {
  useUploader: () => ({
    uploadFiles: vi.fn(),
    uploaders: [],
    validateFiles: vi.fn(),
    openedUploadStatus: false,
    openUploadStatus: vi.fn(),
    closeUploadStatus: vi.fn(),
  }),
  UploaderContextProvider: ({ children }: { children: ReactNode }) => children,
};
