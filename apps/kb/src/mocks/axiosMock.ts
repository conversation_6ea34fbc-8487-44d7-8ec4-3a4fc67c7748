import type { AxiosRequestHeaders, InternalAxiosRequestConfig } from 'axios';

/**
 * Mock Axios configuration for testing
 * Contains all required properties of an Axios request config
 */
export const mockAxiosConfig: InternalAxiosRequestConfig = {
  headers: {} as AxiosRequestHeaders,
  url: '',
  method: 'get',
  baseURL: '',
  transformRequest: [],
  transformResponse: [],
  timeout: 0,
  adapter: undefined as any,
  xsrfCookieName: '',
  xsrfHeaderName: '',
  maxContentLength: 0,
  validateStatus: () => true,
  data: {},
  transitional: {
    silentJSONParsing: true,
    forcedJSONParsing: true,
    clarifyTimeoutError: false,
  },
  signal: undefined as any,
  env: undefined as any,
  maxBodyLength: 0,
  maxRedirects: 0,
  beforeRedirect: undefined as any,
};

/**
 * Create a clone of the mock Axios config with custom properties
 * @param customProps - Properties to override in the mock config
 * @returns A new mock Axios config with the custom properties
 */
export const createMockAxiosConfig = (
  customProps: Partial<InternalAxiosRequestConfig> = {}
): InternalAxiosRequestConfig => {
  return {
    ...mockAxiosConfig,
    ...customProps,
  };
};
