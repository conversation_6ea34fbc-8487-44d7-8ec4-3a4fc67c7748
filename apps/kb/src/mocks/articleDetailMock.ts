import type { Article, KBTemplateField, User } from '@/types';
import { vi } from 'vitest';

// Constants
export const TEST_ARTICLE_ID = 'article-123';
export const TEST_BASE_ID = 'base-123';
export const TEST_ARTICLE_TITLE = 'Test Article';
export const TEST_ARTICLE_CONTENT = 'Test content';
export const TEST_USER_ID = 'user-123';
export const SUCCESS_STATUS = 'success';

// Mock user data
export const mockUser: User = {
  id: TEST_USER_ID,
  orgId: 'org-123',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  displayName: 'Test User',
  email: '<EMAIL>',
  familyName: 'User',
  givenName: 'Test',
  picture: 'https://example.com/avatar.jpg',
};

// Mock article data
export const mockArticle: Article = {
  id: TEST_ARTICLE_ID,
  baseId: TEST_BASE_ID,
  title: TEST_ARTICLE_TITLE,
  content: TEST_ARTICLE_CONTENT,
  contentRaw: TEST_ARTICLE_CONTENT,
  status: 'published',
  keywords: ['test'],
  relatedArticles: [],
  customData: [] as Partial<KBTemplateField>[],
  createdAt: new Date(),
  updatedAt: new Date(),
  createdBy: mockUser,
  isShortcut: false,
};

// Create mockArticleDetailContext
export const createMockArticleDetailContext = () => ({
  currentArticle: null,
  isDetailLoading: false,
  isArticleVoting: false,
  articleVote: null,
  getArticleById: vi.fn().mockResolvedValue({ data: null }),
  updateArticle: vi.fn(),
  deleteArticle: vi.fn(),
  uploadFileInArticle: vi.fn(),
  uploadFileWhenCreatingArticle: vi.fn(),
  updateArticleVote: vi.fn(),
  setIsDetailLoading: vi.fn(),
  articleViewer: {
    scrollToTopOfArticle: vi.fn(),
    topOfArticleRef: { current: null },
    articleViewerScrollableRef: { current: null },
  },
});

// Type for the mock context
export type MockArticleDetailContextType = ReturnType<typeof createMockArticleDetailContext>;
