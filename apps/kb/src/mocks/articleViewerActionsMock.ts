import { VoteType } from '@/types';
import { vi } from 'vitest';

// Constants for test data
export const TEST_ARTICLE = {
  id: 'article-123',
  baseId: 'base-123',
  title: 'Test Article',
  isShortcut: false,
  originArticleId: null,
  status: 'published',
  base: {
    parentDirId: 'parent-dir-123',
  },
};

export const TEST_SHORTCUT_ARTICLE = {
  ...TEST_ARTICLE,
  isShortcut: true,
  originArticleId: 'original-article-123',
};

export const TEST_ARTICLE_VOTE = {
  feedbackType: VoteType.NEUTRAL,
  likeCount: 5,
  dislikeCount: 2,
};

// Setup translations
export const TRANSLATIONS = {
  edit: 'Edit',
  'articleShortcut.viewOriginal': 'View Original',
  'articleCollection.vote.success.title': 'Vote Success',
  'articleCollection.vote.success.description': 'Your vote has been recorded',
  'articleCollection.vote.failed.title': 'Vote Failed',
  'articleCollection.vote.failed.description': 'Failed to record your vote',
  'articleCollection.delete.confirmTitle': 'Confirm Delete',
  'articleCollection.delete.success.title': 'Delete Success',
  'articleCollection.delete.success.description': 'Article deleted successfully',
  'articleCollection.delete.failed.title': 'Delete Failed',
  'articleCollection.delete.failed.description': 'Failed to delete article',
  'articleShortcut.creating.title': 'Create Shortcut',
  'articleShortcut.creating.description': 'Select a knowledge base',
  'articleShortcut.creating.successTitle': 'Shortcut Created',
  'articleShortcut.creating.successDescription': 'Shortcut created successfully',
  'articleShortcut.creating.failedTitle': 'Creation Failed',
  'articleShortcut.creating.failedDescription': 'Failed to create shortcut',
};

// Setup function for ArticleViewerActions tests
export const setupMockHooks = () => {
  // Mock article context
  const updateArticleVote = vi.fn(() => {
    return Promise.resolve({ success: true });
  });
  const deleteArticle = vi.fn().mockResolvedValue({ success: true });
  const createShortcut = vi.fn(() => {
    return Promise.resolve({ success: true });
  });
  const updateArticle = vi.fn(() => {
    return Promise.resolve({ success: true });
  });
  const getArticleById = vi.fn(() => {
    return Promise.resolve({ success: true });
  });
  const setIsArticleVoting = vi.fn();

  // Mock modal hooks
  const openArticleMovingModal = vi.fn();
  const openKBSelectionModal = vi.fn(({ onSelect }) => {
    // Store the onSelect callback so we can call it in tests
    (openKBSelectionModal as any).onSelect = onSelect;
  });
  const closeKBSelectionModal = vi.fn();

  // Mock confirm modal
  const openConfirmModal = vi.fn(({ onConfirm }) => {
    // Store the onConfirm callback so we can call it in tests
    (openConfirmModal as any).onConfirm = onConfirm;
  });
  const closeConfirmModal = vi.fn();

  return {
    mockArticleDetail: {
      currentArticle: TEST_ARTICLE,
      articleVote: TEST_ARTICLE_VOTE,
      isArticleVoting: false,
      setIsArticleVoting,
      updateArticleVote,
      deleteArticle,
      createShortcut,
      updateArticle,
      getArticleById,
    },
    mockAppContext: {
      openConfirmModal,
      closeConfirmModal,
    },
    mockKbAccessControl: {
      permArticle: {
        canUpdate: true,
        canShortcut: true,
        canMove: true,
        canDelete: true,
      },
      permVoting: {
        canView: true,
        canVote: true,
      },
    },
    mockModals: {
      openArticleMovingModal,
      openKBSelectionModal,
      closeKBSelectionModal,
    },
    mockApiHandler: {
      handleApiRequest: vi.fn(async (promise) => {
        const result = await promise;
        return { status: 'success', data: result };
      }),
      API_RESPONSE_STATUS: {
        SUCCESS: 'success',
        ERROR: 'error',
      },
    },
  };
};
