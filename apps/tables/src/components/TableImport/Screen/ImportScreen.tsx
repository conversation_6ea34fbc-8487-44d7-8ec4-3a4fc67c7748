import { Flex, Group, rem, Stack, Text } from '@mantine/core';
import { IconChevronDown } from '@tabler/icons-react';
import { Form<PERSON><PERSON>ider, SubmitHandler, useForm } from 'react-hook-form';
import { Select, TextInput } from 'react-hook-form-mantine';
import { useTranslate } from '@tolgee/react';
import UploadFileButton from '../UploadFileButton';
import { DecaButton, DecaRadio, HFRadio, HFSwitch } from '@resola-ai/ui';
import { zodResolver } from '@hookform/resolvers/zod';
import { TableImportScreen, TableImportType } from '../constants';
import LoaderText from '../LoaderText';
import { useCallback, useMemo, useState } from 'react';
import { useTablesQuery } from '@/hooks';
import { useParams } from 'react-router-dom';
import { importScreenFormSchema } from './schema';
import { ImportScreenFormData } from './schema';
import { createStyles } from '@mantine/emotion';
import { notifications } from '@/components/Common';
import { csvToJson } from '@/utils';
import { useTableImportContext } from '../TableImportContext';

const useStyles = createStyles(theme => ({
  uploadImage: {
    width: rem(200),
  },
  radioButton: {
    padding: rem(20),
    height: rem(78),
    borderRadius: rem(12),
    border: `1px solid ${theme.colors.decaLight[2]}`,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  borderLeft: {
    borderLeft: `1px dashed ${theme.colors.decaLight[2]}`,
    borderRadius: rem(4),
  },
  borderTop: {
    borderTop: `1px solid ${theme.colors.decaLight[2]}`,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#fff',
    zIndex: 100,
  },
  selectOption: {
    '&[data-checked="true"]': {
      backgroundColor: theme.colors.decaNavy[5],
      color: theme.white,
    },
  },
  selectIcon: {
    width: rem(20),
    height: rem(20),
  },
}));

export type ImportScreenSubmitCallback = (
  data: ImportScreenFormData,
  onProgress?: (progress: number) => void
) => Promise<void>;

const ImportScreen = () => {
  const { t } = useTranslate('common');
  const { classes } = useStyles();
  const [progress, setProgress] = useState(0);
  const { baseId } = useParams();
  const { setImportData, setActiveScreen, onClose } = useTableImportContext();
  const { tables } = useTablesQuery(baseId);
  const hasExistingTables = tables && tables.length > 0;
  const firstTableId = tables?.[0]?.id;

  const methods = useForm<ImportScreenFormData>({
    mode: 'onChange',
    resolver: zodResolver(importScreenFormSchema),
    defaultValues: {
      importType: hasExistingTables
        ? TableImportType.IMPORT_TO_EXISTING_TABLE
        : TableImportType.CREATE_NEW_TABLE,
      options: hasExistingTables
        ? {
            table: {
              id: firstTableId,
            },
          }
        : {
            table: {
              name: '',
            },
          },
    },
  });
  const {
    control,
    watch,
    setValue,
    handleSubmit,
    formState: { isSubmitting, isValid },
  } = methods;
  const tableOptions = useMemo(() => {
    if (!tables) return [];
    return tables.map(table => ({ value: table.id, label: table.name }));
  }, [tables]);
  const importType = watch('importType');
  const isDisabledSubmit = isSubmitting || !isValid;

  const onSubmit: SubmitHandler<ImportScreenFormData> = async data => {
    try {
      const { columns, rows } = await csvToJson(data.file, {
        timeout: 1000,
        onProgress: setProgress,
      });

      const tableId = data.options?.table?.id;
      const tableName = tableId
        ? tables.find(table => table.id === tableId)?.name || ''
        : data.options?.table?.name;

      setImportData({
        isCreate: data.importType === TableImportType.CREATE_NEW_TABLE,
        table: { id: tableId, name: tableName },
        uploadFile: {
          rows,
          columns,
          file: data.file,
        },
        options: {
          useFirstRowAsHeader: data.options?.useFirstRowAsHeader ?? false,
          createNewFieldsFromMissingOptions:
            data.options?.createNewFieldsForMissingOptions ?? false,
        },
      });
    } catch (error) {
      console.error(error);
      notifications.show({
        message: t('csvImport.notification.parseCSVError'),
        status: 'error',
      });
    } finally {
      setActiveScreen(TableImportScreen.FIELD_MAPPING_SCREEN);
    }
  };

  const onRadioChange = useCallback(
    (value: string) => {
      switch (value) {
        case TableImportType.IMPORT_TO_EXISTING_TABLE: {
          setValue('options.table', { id: firstTableId, name: '' }, { shouldValidate: true });
          break;
        }
        case TableImportType.CREATE_NEW_TABLE: {
          setValue('options.table', { id: undefined, name: '' });
          break;
        }
      }
    },
    [setValue, firstTableId]
  );

  return (
    <FormProvider {...methods}>
      <Stack gap={0}>
        <Flex pos='relative' className={classes.borderTop} mih={rem(366)}>
          {isSubmitting ? (
            <Flex align='center' justify='center' className={classes.loadingOverlay}>
              <LoaderText text={t('csvImport.loadingData', { progress })} />
            </Flex>
          ) : null}

          <Stack gap='xl' align='center' justify='center' sx={{ flexBasis: '40%' }} p='xl'>
            <UploadFileButton
              onUpload={file => {
                setValue('file', file, { shouldValidate: true });
              }}
            />
          </Stack>
          <Stack gap='xl' sx={{ flex: 1 }} className={classes.borderLeft} p='xl'>
            <Text>{t('csvImport.chooseTableHeader')}</Text>
            <HFRadio.Group control={control} name='importType' onChange={onRadioChange}>
              <Stack gap='xl'>
                <Flex className={classes.radioButton}>
                  <DecaRadio
                    value={TableImportType.IMPORT_TO_EXISTING_TABLE}
                    label={t('csvImport.importExistingTable')}
                    disabled={!hasExistingTables}
                  />

                  {importType === TableImportType.IMPORT_TO_EXISTING_TABLE ? (
                    <Select
                      control={control}
                      name='options.table.id'
                      placeholder={t('csvImport.chooseTable')}
                      allowDeselect={false}
                      withCheckIcon={false}
                      w={rem(280)}
                      rightSectionWidth={40}
                      rightSection={<IconChevronDown className={classes.selectIcon} />}
                      data={tableOptions}
                      comboboxProps={{
                        withinPortal: true,
                      }}
                      classNames={{
                        option: classes.selectOption,
                      }}
                    />
                  ) : null}
                </Flex>
                <Flex className={classes.radioButton}>
                  <DecaRadio
                    value={TableImportType.CREATE_NEW_TABLE}
                    label={t('csvImport.createNewTable')}
                  />

                  {importType === TableImportType.CREATE_NEW_TABLE ? (
                    <TextInput
                      control={control}
                      name='options.table.name'
                      w={rem(280)}
                      placeholder={t('csvImport.enterTableName')}
                    />
                  ) : null}
                </Flex>
              </Stack>
            </HFRadio.Group>

            <Stack gap='lg'>
              <HFSwitch
                name='options.useFirstRowAsHeader'
                label={t('csvImport.useFirstRowAsHeader')}
                defaultChecked={false}
                defaultValue={false}
              />
              {importType === TableImportType.IMPORT_TO_EXISTING_TABLE ? (
                <HFSwitch
                  name='options.createNewFieldsForMissingOptions'
                  label={t('csvImport.createNewFieldsForMissingOptions')}
                  defaultChecked={false}
                  defaultValue={false}
                />
              ) : null}
            </Stack>
          </Stack>
        </Flex>
        <Group
          justify='flex-end'
          gap='md'
          p='md'
          pos='sticky'
          bottom={0}
          bg='#fff'
          className={classes.borderTop}
          sx={{ zIndex: 100 }}
        >
          <DecaButton type='button' onClick={onClose} variant='neutral'>
            {t('csvImport.cancelBtn')}
          </DecaButton>
          <DecaButton disabled={isDisabledSubmit} onClick={handleSubmit(onSubmit)}>
            {t('csvImport.nextBtn')}
          </DecaButton>
        </Group>
      </Stack>
    </FormProvider>
  );
};

export default ImportScreen;
