import { Group, rem, Box } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { SubmitHandler, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { createStyles } from '@mantine/emotion';
import { Select } from 'react-hook-form-mantine';
import { useTablesQuery } from '@/hooks';
import { useMemo } from 'react';
import { primaryFieldFormSchema } from './schema';
import { FieldAPIUpdatePayload } from '@/services/api';
import { SUPPORTED_PRIMARY_FIELD_TYPES } from '@/constants/table';

const useStyles = createStyles(theme => ({
  bodyContent: {
    padding: `${rem(16)} ${rem(24)}`,
    borderTop: `1px solid ${theme.colors.decaLight[2]}`,
    display: 'flex',
    flexDirection: 'column',
    gap: rem(8),
  },
  bodyFooter: {
    padding: `${rem(16)} ${rem(24)}`,
    borderTop: `1px solid ${theme.colors.decaLight[2]}`,
  },
  select: {
    '& > .mantine-Select-label': {
      color: theme.colors.decaGrey[9],
      marginBottom: rem(8),
    },
    '& > .mantine-Select-wrapper > input': {
      '--input-height': rem(38),
      '--input-size': rem(38),
    },
  },
}));

export type PrimaryFieldFormSubmitCallback = ({
  formData,
  closeModal,
}: {
  formData: {
    fieldId: string;
    payload: FieldAPIUpdatePayload;
  };
  closeModal: () => void;
}) => Promise<void>;

export interface PrimaryFieldFormProps {
  baseId?: string;
  tableId?: string;
  onSubmit: PrimaryFieldFormSubmitCallback;
  onCancel: () => void;
  defaultValues?: Partial<FormInput>;
}

export type FormInput = z.infer<typeof primaryFieldFormSchema>;

const PrimaryFieldForm = (props: PrimaryFieldFormProps) => {
  const { onSubmit: handleOnSubmit, onCancel: closeModal, defaultValues, baseId, tableId } = props;
  const { t } = useTranslate('table');
  const { classes } = useStyles();

  const { tables } = useTablesQuery(baseId);

  const activeTable = useMemo(() => {
    return tables?.find(table => table.id === tableId);
  }, [tables, tableId]);

  const options = useMemo(() => {
    return activeTable?.fields
      .filter(field => SUPPORTED_PRIMARY_FIELD_TYPES.includes(field.type))
      .map(field => ({
        value: field.id,
        label: field.name,
      }));
  }, [activeTable]);

  const {
    handleSubmit,
    control,
    formState: { isSubmitting, isValid },
    watch,
  } = useForm({
    mode: 'onChange',
    defaultValues: { fieldId: '', ...defaultValues },
    resolver: zodResolver(primaryFieldFormSchema),
  });

  const fieldId = watch('fieldId');
  const isDisabled = !isValid || fieldId === defaultValues?.fieldId;

  const onSubmit: SubmitHandler<FormInput> = async formData => {
    if (!handleOnSubmit) return;
    const fieldId = formData.fieldId;
    const currentFieldPayload = activeTable?.fields.find(field => field.id === fieldId);
    if (!currentFieldPayload) return;

    if (currentFieldPayload.isPrimary) {
      closeModal();
      return;
    }

    try {
      const { id, ...payloadWithoutId } = currentFieldPayload;
      await handleOnSubmit({
        formData: {
          fieldId,
          payload: payloadWithoutId,
        },
        closeModal,
      });
    } catch (error: any) {
      console.log(error);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box className={classes.bodyContent}>
        <Select
          name='fieldId'
          control={control}
          label={t('primaryFieldModal.selectLabel')}
          placeholder={t('primaryFieldModal.selectPlaceholder')}
          data={options}
          size='md'
          className={classes.select}
          withCheckIcon={false}
          allowDeselect={false}
        />
      </Box>

      <Group justify='flex-end' gap='lg' className={classes.bodyFooter}>
        <DecaButton
          size='sm'
          type='button'
          variant='neutral'
          onClick={closeModal}
          aria-label={t('primaryFieldModal.cancelBtn')}
        >
          {t('primaryFieldModal.cancelBtn')}
        </DecaButton>
        <DecaButton
          size='sm'
          type='submit'
          variant='primary'
          loading={isSubmitting}
          disabled={isDisabled}
          aria-label={t('primaryFieldModal.submitBtn')}
        >
          {t('primaryFieldModal.submitBtn')}
        </DecaButton>
      </Group>
    </form>
  );
};

export default PrimaryFieldForm;
