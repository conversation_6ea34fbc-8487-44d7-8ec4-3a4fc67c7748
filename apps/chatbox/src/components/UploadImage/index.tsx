import { useEffect, useMemo, useRef, useState } from 'react';
import { Box, Button, FileButton, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconUpload, IconX } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useObjectUrls } from '@/hooks';
import { CustomImage } from '@resola-ai/ui';
import { getPublicUrl } from '@resola-ai/utils';
import CustomImageBackground from '../CustomImageBackground';

const useStyles = createStyles((theme) => ({
  container: {
    display: 'flex',
    alignItems: 'center',
    gap: rem(8),
  },
  button: {
    fontSize: rem(16),
    fontWeight: 500,
    color: theme.colors.silverFox[9],
    backgroundColor: theme.colors.silverFox[3],
    '[data-position="left"]': {
      marginInlineEnd: '4px',
    },
    '&:hover': {
      backgroundColor: theme.colors.silverFox[4],
    },
  },
  preview: {
    width: rem(60),
    height: rem(60),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    img: {
      height: rem(34),
    },
    '&:hover': {
      '.clearButton': {
        display: 'flex',
      },
    },
  },
  circlePreview: {
    borderRadius: '100%',
    backgroundColor: theme.colors.silverFox[0],
    border: `1px solid ${theme.colors.silverFox[5]}`,
    img: {
      height: rem(34),
    },
  },
  clearImageControl: {
    position: 'absolute',
    top: 0,
    right: 0,
    cursor: 'pointer',
    color: theme.colors.silverFox[5],
    width: rem(16),
    height: rem(16),
    backgroundColor: theme.colors.decaNavy[5],
    borderRadius: '100%',
    border: `1px solid ${theme.colors.decaNavy[5]}`,
    display: 'none',
    alignItems: 'center',
    justifyContent: 'center',
  },
}));

interface UploadImageProps {
  defaultValue?: string | File | null;
  circlePreview?: boolean;
  rounded?: boolean;
  onChange?: (file: File | null) => void;
  staticUrl?: string;
  smallSize?: boolean;
  isLauncherSection?: boolean;
  useChatboxCDNUrl?: boolean;
}

interface IconXProps {
  handleClearFile: () => void;
}

const ClearImageControl = ({ handleClearFile }: IconXProps) => {
  const { classes, cx } = useStyles();

  return (
    <Box className={cx(classes.clearImageControl, 'clearButton')} onClick={handleClearFile}>
      <IconX stroke={2.5} />
    </Box>
  );
};

export default function UploadImage({
  circlePreview = false,
  onChange,
  defaultValue,
  rounded = false,
  staticUrl,
  smallSize = false,
  isLauncherSection = false,
  useChatboxCDNUrl = false,
}: UploadImageProps) {
  const { t } = useTranslate('stylingSetting');
  const { classes, cx } = useStyles();
  const getObjectUrl = useObjectUrls();
  const [file, setFile] = useState<File | null>(null);

  const resetRef = useRef<() => void>(null);

  const clearFile = () => {
    setFile(null);
    resetRef.current?.();
  };

  useEffect(() => {
    if (typeof defaultValue === 'string') {
      clearFile();
    }
  }, [defaultValue]);

  const handleChange = (file: File | null) => {
    if (!file) return;
    setFile(file);
    onChange?.(file);
  };

  const previewUrl = useMemo(() => {
    if (!file) return undefined;
    return getObjectUrl(file);
  }, [file, getObjectUrl]);

  const handleClearFile = () => {
    clearFile();
    onChange?.(null);
  };

  return (
    <Box className={classes.container}>
      {(defaultValue || staticUrl) && !file && (
        <Box
          className={cx(classes.preview, {
            [classes.circlePreview]: circlePreview,
          })}
        >
          {staticUrl ? (
            <>
              <CustomImageBackground
                url={staticUrl}
                rounded={rounded}
                width={smallSize ? rem(34) : undefined}
                height={smallSize ? rem(34) : undefined}
              />
              {isLauncherSection && <ClearImageControl handleClearFile={handleClearFile} />}
            </>
          ) : (
            <>
              {rounded ? (
                <CustomImageBackground url={getPublicUrl(defaultValue as string)} rounded />
              ) : (
                <CustomImage url={defaultValue as string} useChatboxCDNUrl={useChatboxCDNUrl} />
              )}
            </>
          )}
        </Box>
      )}
      {file && (
        <Box
          className={cx(classes.preview, {
            [classes.circlePreview]: circlePreview,
          })}
        >
          <CustomImageBackground
            url={previewUrl}
            rounded={rounded}
            width={smallSize ? rem(34) : undefined}
            height={smallSize ? rem(34) : undefined}
          />
          {isLauncherSection && <ClearImageControl handleClearFile={handleClearFile} />}
        </Box>
      )}

      <FileButton onChange={handleChange} accept='image/png,image/jpeg' resetRef={resetRef}>
        {(props) => (
          <Button {...props} className={classes.button} leftSection={<IconUpload />}>
            {t('uploadYourImage')}
          </Button>
        )}
      </FileButton>
    </Box>
  );
}
