import { replaceLineBreaks, transformText } from '@/utils';
import { randomId } from '@/utils/id';
import type { StyledOptions } from '@emotion/styled';
import ColorScheme from 'color-scheme';
import { getPublicUrl } from '@resola-ai/utils';
import { ratio as WCAGRatio, score as WCAGScore } from 'wcag-color';
import type { BackgroundProps, BrandProps, ChatBoxUIAppProps, NavigatorType } from '../models';
import type { ChatResponse, DataActionsResponses, DataTextResponses } from './../models';
import {
  ALLOWED_TYPES,
  BOX_MESSAGES_KEY,
  CHATBOT_SYSTEM_TYPES,
  DEFAULT_COLOR_NAVIGATOR,
} from '../constants';
import { getTimeStampMessage } from './dateTime';

export * from './events';
export * from './websocket';
export * from './userAgent';
export * from './message';
export * from './dateTime';

export const getBackgroundCss = (backgroundProps: BackgroundProps) => {
  const { backgroundType, backgroundValue, fadeBackgroundToWhite } = backgroundProps;
  if (fadeBackgroundToWhite) {
    if (backgroundType === 'image') {
      const fadeBackgroundToWhiteColor = `linear-gradient(180deg, ${'transparent'} 45%, #FFFFFF 55%)`;
      return {
        background: ` ${fadeBackgroundToWhiteColor}, url(${backgroundValue})  no-repeat center`,
        backgroundSize: 'cover',
      };
    }
    if (backgroundType === 'gradient') {
      const fadeBackgroundToWhiteColor = `linear-gradient(180deg, ${backgroundValue.color1} 45%, #FFFFFF 55%)`;
      // return `${fadeBackgroundToWhiteColor}, linear-gradient(180deg, ${backgroundValue.color2} 45%, #FFFFFF 55%)`;
      return {
        backgroundImage: `${fadeBackgroundToWhiteColor}, linear-gradient(180deg, ${backgroundValue.color2} 45%, #FFFFFF 55%)`,
      };
    }
    const fadeBackgroundToWhiteColor = `linear-gradient(180deg, ${backgroundValue} 45%, #FFFFFF 55%)`;
    // return fadeBackgroundToWhiteColor;
    return {
      backgroundImage: `${fadeBackgroundToWhiteColor}`,
    };
  }
  const color1 = backgroundType === 'gradient' ? backgroundValue.color1 : backgroundValue;
  const color2 = backgroundType === 'gradient' ? backgroundValue.color2 : backgroundValue;
  switch (backgroundType) {
    case 'color':
      // return backgroundValue;
      return {
        backgroundColor: backgroundValue,
      };
    case 'gradient':
      // return `linear-gradient(180deg, ${color1} 45%, ${color2} 55%)`;
      return {
        backgroundImage: `linear-gradient(180deg, ${color1} 45%, ${color2} 55%)`,
      };
    case 'image':
      return {
        background: `url(${backgroundValue}) no-repeat center`,
        backgroundSize: 'cover',
      };
  }
};

export const getNavigatorColor = (
  selectedPage: NavigatorType,
  currentPage: NavigatorType,
  brand: BrandProps
) => {
  if (selectedPage === currentPage) {
    return brand.actionColor;
  }
  return DEFAULT_COLOR_NAVIGATOR;
};

export const getStyledOptions = (): StyledOptions<any> => {
  const forwardLists = ['currentPage'];
  return {
    shouldForwardProp: (propName) => {
      return !forwardLists.includes(propName);
    },
  };
};

const generateRandomHue = () => {
  return Math.floor(Math.random() * 360);
};
// const LIST_OF_COLORS = Object.keys(colors);
const LIST_OF_COLORS = ['#ffffff', '#000000'];

export const getWCAGColorFromSingle = (inputColor: string) => {
  const okList: {
    color: string;
    ratio: number;
  }[] = [];
  // use a performant way to get the okList
  LIST_OF_COLORS.forEach((color) => {
    const score = WCAGScore(color, inputColor);
    const ratio = WCAGRatio(color, inputColor);
    if (score !== 'Fail') {
      okList.push({
        color,
        ratio,
      });
    }
  });

  // use a performant way to get the max
  const maxRatio = Math.max(...okList.map((item) => item.ratio));
  const maxColor = okList.find((item) => item.ratio === maxRatio);
  return maxColor?.color;
};

export const getWCAColorFromDouble = (inputColor1: string, inputColor2: string) => {
  for (const color1 of LIST_OF_COLORS) {
    for (const color2 of LIST_OF_COLORS) {
      const score = WCAGScore(color1, inputColor1);
      const score2 = WCAGScore(color2, inputColor2);
      if (score !== 'Fail' && score2 !== 'Fail') {
        return [color1, color2];
      }
    }
  }
};

export const generateRandomColor = (inputColor: string, hueNumber = 21) => {
  const scheme = new ColorScheme();
  scheme.from_hue(hueNumber).scheme('contrast').variation('default');
  const colors = scheme.colors();
  // check for any color that passes WCAG, then take that one, if can not then generate new hue and generate new color

  for (let i = 0; i < colors.length; i++) {
    const score = WCAGScore(colors[i], inputColor);
    if (score !== 'Fail') {
      return colors[i];
    }
  }
  const newHueNumber = generateRandomHue();
  return generateRandomColor(inputColor, newHueNumber);
};

export const getWCAGColor = (backgroundProps: BackgroundProps, brand: BrandProps): string => {
  // depend on color or gradient, we will have different logic (single or double wcag)
  const { backgroundType, backgroundValue } = backgroundProps;
  if (backgroundType === 'color') {
    return getWCAGColorFromSingle(backgroundValue)!;
  }
  // if (backgroundType === 'gradient') {
  //   return getWCAColorFromDouble(backgroundValue.color1, backgroundValue.color2);
  // }
  return getWCAGColorFromSingle(brand.actionColor)!;
};

export const getWCAGColorV2 = (backgroundColor: string) => {
  return getWCAGColorFromSingle(backgroundColor);
};

export const getCurrentPage = (props: ChatBoxUIAppProps): NavigatorType => {
  if (props.chatbox.displayComponents.includes('home')) {
    return 'home';
  }
  if (props.chatbox.displayComponents.includes('message')) {
    return 'message';
  }

  return 'home';
};

const allowedTypesSet = new Set(ALLOWED_TYPES);

const isAllowedMessage = ({ type, data }: ChatResponse) => {
  return (
    allowedTypesSet.has(type) &&
    !(type === 'system' && data && data?.type === CHATBOT_SYSTEM_TYPES.EXECUTION) // remove system message type chatbot.finished
  );
};

export const normalizeMessages = (data: ChatResponse[], botAvatar?: string) => {
  const defaultAvatar = getPublicUrl('svg/default-bot.svg');
  const avatarChatbot = botAvatar ?? defaultAvatar;

  return data.filter(isAllowedMessage).map((message) => {
    const { from, data: messageData } = message;
    let avatarSrc = '';
    let avatarAlt = '';
    let src = messageData?.src ?? '';
    const target = messageData?.href ? '_blank' : '';

    // Determine avatar source and alt text based on sender
    if (from === 'bot') {
      avatarSrc = avatarChatbot;
      avatarAlt = 'Bot avatar';
    } else if (from === 'user') {
      avatarSrc = getPublicUrl('svg/default-user.svg');
      avatarAlt = 'User default avatar';
    } else {
      avatarSrc = messageData?.metadata?.sender?.iconUrl ?? defaultAvatar;
    }

    // Handle non-https image sources
    if (src && !src.startsWith('https')) {
      src = getPublicUrl(src);
    }

    // Update text with transformations
    const updatedText = (messageData as DataTextResponses)?.text
      ? transformText((messageData as DataTextResponses).text)
      : '';

    // Update button actions and handle line breaks
    const updatedActions = (messageData as DataActionsResponses)?.actions?.map((action) => ({
      ...action,
      text: replaceLineBreaks(action.text),
    }));

    // if message type is loader, we do not need to show the created time
    const created = message.type === 'loader' ? '' : getTimeStampMessage(message.created);

    return {
      ...message,
      data: {
        ...messageData,
        created,
        src,
        target,
        text: updatedText,
        actions: updatedActions,
        avatar: {
          src: avatarSrc,
          alt: avatarAlt,
        },
      },
    };
  });
};

export const getBoxMessagesKey = (botId: string, boxId: string, userId: string) => {
  return BOX_MESSAGES_KEY.replace('{botId}', botId)
    .replace('{boxId}', boxId)
    .replace('{userId}', userId);
};

export const generateLoaderMessage = (color: string): ChatResponse => {
  return {
    id: randomId(),
    created: new Date().toISOString(),
    from: 'bot',
    type: 'loader',
    data: {
      color: color,
      size: '28px',
      variant: 'dots',
    },
  };
};

export const getMessages = (botId: string, boxId: string, userId: string) => {
  // when first load the message box, get the messages from local storage first
  const cachedMessages = localStorage.getItem(getBoxMessagesKey(botId, boxId, userId));
  if (cachedMessages) {
    return JSON.parse(cachedMessages);
  }
  return [];
};

export const trimText = (text: string) => {
  if (!text) return '';
  return text
    .replace(/^(\s|&nbsp;|\\n)+/g, '') // Remove unwanted characters at the beginning
    .replace(/(\s|&nbsp;|\\n)+$/g, '') // Remove unwanted characters at the end
    .trim(); // Ensure no extra leading/trailing spaces remain
};

export const isEmptyTextAfterTrim = (text: string) => {
  if (!text) return true;
  const trimmedText = trimText(text);

  return trimmedText === '';
};
