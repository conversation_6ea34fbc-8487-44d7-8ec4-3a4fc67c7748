import axios from 'axios';
import { notifications } from '@mantine/notifications';
import { tolgee } from '../../tolgee';
import { ISuccessResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';

interface PresignedUrlRequest {
  feature: string;
  mimeType: string;
  size: number;
  name: string;
}

interface FileInfo {
  id: string;
  name: string;
  description: string;
  status: string;
  appId: string;
  resourceId: string;
  mimeType: string;
  size: number;
  target: string;
  path: string;
  meta: Record<string, any>;
  created: string;
  updated: string;
}

interface PresignedUrlResponse {
  file: FileInfo;
  uploadUrl: string;
}

interface FileMetadata {
  size: number;
  name: string;
  type: string;
}

export class PresignedUploadService {
  private static processingFiles = new Map<string, Promise<string>>();

  private static getFileKey(file: FileMetadata): string {
    return `${file.name}-${file.size}-${file.type}`;
  }

  static async getPresignedUrl(
    data: PresignedUrlRequest,
    orgId: string
  ): Promise<PresignedUrlResponse> {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<PresignedUrlResponse>>(
        '/presigned-url',
        data,
        {
          headers: {
            'X-Org-id': orgId ?? '',
          },
        }
      );
      return response.data.data;
    } catch (error: any) {
      logger.error('Error getting presigned URL:', error);
      notifications.show({
        message: tolgee.t(`common:errors.${error.response?.data?.detail?.type ?? 'unknown'}`),
        color: 'red',
        title: tolgee.t('common:errors.title'),
      });
      throw error;
    }
  }

  static async getLiveChatPresignedUrl(
    file: File,
    orgId: string,
    conversationId: string
  ): Promise<{ uploadUrl: string; filePath: string }> {
    const response = await this.getPresignedUrl(
      {
        feature: `livechat/assets/${orgId}/conversation/${conversationId}`,
        mimeType: file.type,
        size: file.size,
        name: file.name,
      },
      orgId
    );

    return {
      uploadUrl: response.uploadUrl,
      filePath: response.file.path,
    };
  }

  static async uploadToS3(file: File, presignedUrl: string): Promise<void> {
    await axios.put(presignedUrl, file, {
      headers: {
        'Content-Type': file.type,
      },
    });
  }

  static async uploadFile(
    file: File,
    orgId: string = '',
    conversationId: string = ''
  ): Promise<string> {
    const fileMetadata: FileMetadata = {
      size: file.size,
      name: file.name,
      type: file.type,
    };

    const fileKey = this.getFileKey(fileMetadata);

    // Return existing upload if already in progress
    if (this.processingFiles.has(fileKey)) {
      return this.processingFiles.get(fileKey)!;
    }

    const uploadPromise = (async () => {
      try {
        const { uploadUrl, filePath } = await this.getLiveChatPresignedUrl(
          file,
          orgId,
          conversationId
        );
        await this.uploadToS3(file, uploadUrl);
        return filePath;
      } finally {
        this.processingFiles.delete(fileKey);
      }
    })();

    this.processingFiles.set(fileKey, uploadPromise);
    return uploadPromise;
  }

  static async uploadMultipleFiles(
    files: File[],
    orgId: string = '',
    conversationId: string = ''
  ): Promise<string[]> {
    const uploadPromises = files.map(async (file) => {
      try {
        const fileMetadata: FileMetadata = {
          size: file.size,
          name: file.name,
          type: file.type,
        };

        const fileKey = this.getFileKey(fileMetadata);

        // Return existing upload if already in progress
        if (this.processingFiles.has(fileKey)) {
          return { success: true, url: await this.processingFiles.get(fileKey)!, file };
        }

        const uploadPromise = (async () => {
          try {
            const { uploadUrl, filePath } = await this.getLiveChatPresignedUrl(
              file,
              orgId,
              conversationId
            );

            // Don't await here, as we want to run upload in the background
            this.uploadToS3(file, uploadUrl);
            return filePath;
          } finally {
            this.processingFiles.delete(fileKey);
          }
        })();

        this.processingFiles.set(fileKey, uploadPromise);
        const filePath = await uploadPromise;
        return { success: true, url: filePath, file };
      } catch (error) {
        logger.error(`Error uploading file ${file.name}:`, error);
        return { success: false, error, file: file.name };
      }
    });

    const results = await Promise.all(uploadPromises);

    // Get file paths from successful uploads
    return results
      .filter((result) => result.success && result.url)
      .map((result) => result.url!) as string[];
  }

  static clearProcessingFiles(): void {
    this.processingFiles.clear();
  }
}
