import type { ChatBox, IConfigurationLimits } from '@/types';
import type { ISuccessListNextResponse, ISuccessResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';

export const ChatboxAPI = {
  getAll: async () => {
    try {
      const response =
        await axiosService.instance.get<ISuccessListNextResponse<ChatBox>>('/settings/all');
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  getList: async (limit = 20, next = '') => {
    try {
      const response = await axiosService.instance.get<ISuccessListNextResponse<ChatBox>>(
        `/settings?limit=${limit}&next=${next}`
      );
      return response.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  get: async (id: string) => {
    try {
      const response = await axiosService.instance.get<ISuccessResponse<ChatBox>>(`settings/${id}`);
      return response.data.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  save: async (payload) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<ChatBox>>('/settings', {
        config: payload,
        title: payload?.title,
        desc: payload?.desc,
      });
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  update: async (id: string, payload) => {
    try {
      const response = await axiosService.instance.put<ISuccessResponse<ChatBox>>(
        `/settings/${id}`,
        {
          config: payload,
          title: payload?.title,
          desc: payload?.desc,
          updated: payload?.updated,
        }
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  remove: async (id: string) => {
    try {
      const response = await axiosService.instance.delete(`/settings/${id}`);
      return response.data.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
  uploadAsset: async (id: string, file: File) => {
    const response = await axiosService.instance.post<ISuccessResponse<any>>(
      `/settings/${id}/assets`,
      {
        contentLength: file.size,
        contentType: file.type,
      }
    );

    // fetch to upload image
    const url = await fetch(response.data.data.uploadUrl, {
      method: 'PUT',
      body: file,
    })
      .then((res) => res.text())
      .then(() => response.data.data.url)
      .catch((err) => {
        logger.error(err);
        return undefined;
      });
    return url;
  },
  getConfigurationLimits: async () => {
    try {
      const response =
        await axiosService.instance.get<ISuccessResponse<IConfigurationLimits>>('/configuration');
      return response.data.data;
    } catch (error) {
      logger.error(error);
      return undefined;
    }
  },
};
