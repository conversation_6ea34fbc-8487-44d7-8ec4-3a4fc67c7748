import type { ChatBotInfor } from '@/types';
import { notifications } from '@mantine/notifications';
import { tolgee } from '../../tolgee';
import type { ISuccessListNextResponse, ISuccessResponse } from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';

export const IntegrationBotsAPI = {
  getList: async () => {
    try {
      const response =
        await axiosService.instance.get<ISuccessListNextResponse<ChatBotInfor>>('/bots');
      return response.data.data;
    } catch (error: any) {
      logger.error(error);
      notifications.show({
        message: tolgee.t(`integration:${error.response?.data?.detail?.type ?? 'notFound'}`),
        color: 'red',
        title: tolgee.t('integration:error'),
      });
      return undefined;
    }
  },
  connect: async ({
    botId,
    chatBoxId,
    newBotId,
  }: {
    botId: string;
    chatBoxId: string;
    newBotId?: string;
  }) => {
    try {
      const response = await axiosService.instance.post<ISuccessResponse<any>>(
        '/integrations/connections',
        newBotId
          ? { botId: botId, settingsId: chatBoxId, newBotId: newBotId }
          : { botId: botId, settingsId: chatBoxId }
      );
      return response.data.data;
    } catch (error: any) {
      logger.error(error);
      throw error;
    }
  },
  disconnectAll: async (payload) => {
    try {
      const response = await axiosService.instance.patch<ISuccessResponse<any>>(
        '/integrations/connections',
        {
          settingsId: payload.chatBoxId,
        }
      );
      return response.data.data;
    } catch (error: any) {
      logger.error(error);
      notifications.show({
        message: tolgee.t(`integration:${error.response?.data?.detail?.type ?? 'notFound'}`),
        title: tolgee.t('integration:error'),
        color: 'red',
      });
      return undefined;
    }
  },
};
