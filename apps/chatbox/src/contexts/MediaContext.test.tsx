import React from 'react';
import { act } from '@testing-library/react';
import { MediaProvider, useMedia, type MediaContextType } from './MediaContext';
import { useChatBoxUIContext } from '@/components/ChatBoxUI/context/chatBoxUIContext';
import { PresignedUploadService } from '../services/api/presignedUpload';
import { vi } from 'vitest';
import axios from 'axios';
import { renderWithMantine } from '../utils/unitTest';
import { useFileValidationAlert } from './FileValidationAlertContext';
import { MAX_FILES } from '../components/ChatBoxUI/constants/media';
import { validateFiles } from '../utils/fileValidation';
import { extractFilename } from '@resola-ai/utils';

// Mock dependencies
vi.mock('@/components/ChatBoxUI/context/chatBoxUIContext');
vi.mock('../services/api/presignedUpload');
vi.mock('axios');
vi.mock('@resola-ai/services-shared', () => ({
  logger: {
    error: vi.fn(),
  },
  createBaseConfig: vi.fn().mockReturnValue({
    apiUrl: 'test-api-url',
    apiKey: 'test-api-key',
    env: 'test',
  }),
}));

// Mock FileValidationAlertContext
vi.mock('./FileValidationAlertContext', () => ({
  useFileValidationAlert: vi.fn(),
}));

// Mock file validation utility
vi.mock('../utils/fileValidation', () => ({
  validateFiles: vi.fn(),
}));

// Mock utils package - use partial mock to preserve other exports
vi.mock('@resola-ai/utils', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@resola-ai/utils')>();
  return {
    ...actual,
    extractFilename: vi.fn(),
  };
});

describe('MediaContext', () => {
  const mockChatBoxUIContext = {
    // Required properties from ChatBoxUIContextType
    currentPage: 'message' as const,
    chatBoxUIState: true,
    chatBoxUIAvailability: true,
    clickLauncherHandler: vi.fn(),
    navigateHandler: vi.fn(),
    closeHandler: vi.fn(),
    updateNavigatorRef: vi.fn(),
    updateEmptyStateHeaderRef: vi.fn(),
    updateHeaderRef: vi.fn(),
    updateInputElmRef: vi.fn(),
    updateStartConversationButtonRef: vi.fn(),
    dom: {
      navigatorData: { height: 0, width: 0 },
      emptyStateHeaderData: { height: 0, width: 0 },
      headerData: { height: 0, width: 0 },
      inputElmData: { height: 0, width: 0 },
      startConversationButtonData: { height: 0, width: 0 },
      triggerFlowButtonElmData: { height: 0, width: 0 },
    },
    isDisplayDouble: true,
    messages: [],
    updateMessages: vi.fn(),
    messageNavigator: {
      isChat: vi.fn(),
      isConversation: vi.fn(),
      goToConversation: vi.fn(),
      goToChat: vi.fn(),
    },
    handleSendEventData: vi.fn(),
    setMessages: vi.fn(),
    liveChatConnected: false,
    actionColor: '#000000',
    connectLiveChatInfor: {
      name: '',
      picture: '',
      description: '',
      teamId: '',
      assigneeId: '',
    },
    setConnectLiveChatInfor: vi.fn(),
    setLiveChatConnected: vi.fn(),
    currentConversationId: 'test-conversation-id',
    setCurrentConversationId: vi.fn(),
    isConversationAssignedToOperator: false,
    setIsConversationAssignedToOperator: vi.fn(),
    isDisabledMessageInput: false,
    setIsDisabledMessageInput: vi.fn(),
    userAgent: 'test-agent',
    urlGetLiveChatHistoryMessages: '',
    urlGetLiveChatConversationList: '',
    conversationSelected: null,
    setConversationSelected: vi.fn(),
    livechatConversationList: [],
    setLivechatConversationList: vi.fn(),
    isCompletedLiveChatConversation: false,
    isConnectNotAssignedOperatorYet: false,
    isConnectAssignedOperator: false,
    isDisconnectLiveChat: true,
    wsUserId: 'test-user',
    chatbotSystemState: { type: '', text: '' },
    setChatbotSystemState: vi.fn(),
    isFinishedChatbotConversation: false,
    updateTriggerFlowButtonRef: vi.fn(),
    isKBCardMessaging: false,
    setIsKBCardMessaging: vi.fn(),
    urlGetChatbotHistoryMessages: '',
    botId: '',
    handleTriggerChatbotFlow: vi.fn(),
    actionButtonEvents: [],
    setActionButtonEvents: vi.fn(),
    submitFormMessageEvents: [],
    setSubmitFormMessageEvents: vi.fn(),
    boxId: 'test-box',
    resetChatBoxState: vi.fn(),
    articleAnalyticEventPayload: {
      event: '',
      requestId: '',
      organizationId: 'test-org-id',
      anonymousId: '',
      context: { userAgent: '', source: '' },
      properties: { itemId: '' },
      sentAt: '',
    },
    handleSendArticleAnalyticEvent: vi.fn(),
    voteCollectionStorageRef: { current: {} },
    articleAnalyticsEvents: [],
    setArticleAnalyticsEvents: vi.fn(),
    isFocusInput: false,
    setIsFocusInput: vi.fn(),
    lastLiveChatMessage: null,
    setLastLiveChatMessage: vi.fn(),
    handleSendLiveChatDisplayEvent: vi.fn(),
    isTurnOnLiveChatIntegration: false,
    selectedLiveChatTeamEvents: [],
    setSelectedLiveChatTeamEvents: vi.fn(),
    sourceInfo: {
      type: 'user',
      userId: 'test-user',
      meta: { userAgent: { raw: 'test-agent' } },
    },
    centrifugeInstance: null,
    setCentrifugeInstance: vi.fn(),
    organizationId: 'test-org-id',
  };

  const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
  const mockPresignedUrl = {
    file: {
      id: 'file-123',
      name: 'test-image.jpg',
      description: 'Test file',
      status: 'pending',
      appId: 'app-123',
      resourceId: 'resource-123',
      mimeType: 'image/jpeg',
      size: 1024,
      target: 'test-target',
      path: 'test-path',
      meta: {},
      created: '2024-01-01T00:00:00Z',
      updated: '2024-01-01T00:00:00Z',
    },
    uploadUrl: 'test-upload-url',
  };

  beforeEach(() => {
    vi.mocked(useChatBoxUIContext).mockReturnValue(mockChatBoxUIContext as any);
    vi.mocked(PresignedUploadService.getPresignedUrl).mockResolvedValue(mockPresignedUrl);

    // Add this line to mock uploadFile to return 'test-path'
    vi.mocked(PresignedUploadService.uploadFile).mockResolvedValue('test-path');
    // Mock uploadMultipleFiles to return unique paths across multiple calls
    let pathCounter = 0;
    vi.mocked(PresignedUploadService.uploadMultipleFiles).mockImplementation(async (files) => {
      return files.map(() => `test-path-${++pathCounter}`);
    });
    vi.mocked(PresignedUploadService.clearProcessingFiles).mockImplementation(() => {});

    // Instead of mocking uploadFile directly, mock the internal methods
    vi.mocked(PresignedUploadService.getLiveChatPresignedUrl).mockResolvedValue({
      uploadUrl: 'test-upload-url',
      filePath: 'test-path',
    });

    // Let uploadFile use the real implementation
    // This will ensure uploadToS3 is called, which will call axios.put
    // Mock its dependencies instead:
    vi.mocked(PresignedUploadService.getLiveChatPresignedUrl).mockResolvedValue({
      uploadUrl: 'test-upload-url',
      filePath: 'test-path',
    });

    vi.mocked(axios.put).mockResolvedValue({});

    // Mock FileValidationAlertContext
    vi.mocked(useFileValidationAlert).mockReturnValue({
      showValidationAlert: vi.fn(),
      hideValidationAlert: vi.fn(),
      validationAlert: null,
    });

    // Mock validateFiles to return valid by default
    vi.mocked(validateFiles).mockReturnValue({
      isValid: true,
      errorMessage: '',
    });

    // Mock extractFilename to return the filename from URL
    vi.mocked(extractFilename).mockImplementation((url: string) => {
      // Extract filename from URL (e.g., 'test-document.pdf' from 'https://example.com/test-document.pdf')
      return url.split('/').pop() || url;
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  // Test component to access context
  const TestComponent = () => {
    const media = useMedia();
    return <div data-testid='test-component'>{media ? 'loaded' : 'not-loaded'}</div>;
  };

  it('provides media context to children', () => {
    const { getByTestId } = renderWithMantine(
      <MediaProvider>
        <TestComponent />
      </MediaProvider>
    );
    expect(getByTestId('test-component')).toHaveTextContent('loaded');
  });

  it('throws error when used outside provider', () => {
    const consoleError = console.error;
    console.error = vi.fn();

    expect(() => renderWithMantine(<TestComponent />)).toThrow(
      'useMedia must be used within a MediaProvider'
    );

    console.error = consoleError;
  });

  describe('handleFileUploads', () => {
    it('successfully uploads multiple files', async () => {
      const mockFiles = [
        new File(['test1'], 'test1.jpg', { type: 'image/jpeg' }),
        new File(['test2'], 'test2.png', { type: 'image/png' }),
      ] as any; // Cast to FileWithPath[]

      let mediaContext!: MediaContextType;
      const TestComponent = () => {
        mediaContext = useMedia();
        return null;
      };

      renderWithMantine(
        <MediaProvider>
          <TestComponent />
        </MediaProvider>
      );

      await act(async () => {
        const result = await mediaContext.handleFileUploads(
          mockFiles,
          mockChatBoxUIContext.organizationId,
          mockChatBoxUIContext.currentConversationId
        );
        expect(result).toEqual(['test-path-1', 'test-path-2']);
      });

      expect(PresignedUploadService.uploadMultipleFiles).toHaveBeenCalledWith(
        mockFiles,
        mockChatBoxUIContext.organizationId,
        mockChatBoxUIContext.currentConversationId
      );

      // Verify that validateFiles was called with correct parameters (no existing files initially)
      expect(validateFiles).toHaveBeenCalledWith(mockFiles, undefined, 0);

      // Verify uploaded files are now tracked in uploadedFiles for validation
      expect(mediaContext.uploadedFiles).toHaveLength(2); // Files added to uploadedFiles after upload
      expect(mediaContext.canAddMoreFiles(3)).toBe(true); // 2 uploaded + 3 new = 5 <= MAX_FILES
      expect(mediaContext.canAddMoreFiles(4)).toBe(false); // 2 uploaded + 4 new = 6 > MAX_FILES
    });

    it('handles multiple file upload errors', async () => {
      const error = new Error('Multiple upload failed');
      vi.mocked(PresignedUploadService.uploadMultipleFiles).mockRejectedValueOnce(error);

      const mockFiles = [new File(['test1'], 'test1.jpg', { type: 'image/jpeg' })] as any;

      let mediaContext!: MediaContextType;
      const TestComponent = () => {
        mediaContext = useMedia();
        return null;
      };

      renderWithMantine(
        <MediaProvider>
          <TestComponent />
        </MediaProvider>
      );

      await act(async () => {
        await expect(
          mediaContext.handleFileUploads(
            mockFiles,
            mockChatBoxUIContext.organizationId,
            mockChatBoxUIContext.currentConversationId
          )
        ).rejects.toThrow('Multiple upload failed');
      });
    });

    it('handles file validation errors', async () => {
      // Mock validation to fail
      vi.mocked(validateFiles).mockReturnValueOnce({
        isValid: false,
        errorMessage: 'File validation failed',
      });

      const mockShowValidationAlert = vi.fn();
      vi.mocked(useFileValidationAlert).mockReturnValueOnce({
        showValidationAlert: mockShowValidationAlert,
        hideValidationAlert: vi.fn(),
        validationAlert: null,
      });

      const mockFiles = [new File(['test1'], 'test1.jpg', { type: 'image/jpeg' })] as any;

      let mediaContext!: MediaContextType;
      const TestComponent = () => {
        mediaContext = useMedia();
        return null;
      };

      renderWithMantine(
        <MediaProvider>
          <TestComponent />
        </MediaProvider>
      );

      await act(async () => {
        await expect(
          mediaContext.handleFileUploads(
            mockFiles,
            mockChatBoxUIContext.organizationId,
            mockChatBoxUIContext.currentConversationId
          )
        ).rejects.toThrow('File validation failed');
      });

      // Verify that validateFiles was called with correct parameters
      expect(validateFiles).toHaveBeenCalledWith(mockFiles, undefined, 0);

      // Verify that validation alert was shown
      expect(mockShowValidationAlert).toHaveBeenCalledWith(expect.any(String));
    });

    it('calls validateFiles with correct existing files count when files are already uploaded', async () => {
      let mediaContext: MediaContextType;
      const TestComponent = () => {
        mediaContext = useMedia();
        return null;
      };

      renderWithMantine(
        <MediaProvider>
          <TestComponent />
        </MediaProvider>
      );

      // First upload some files
      const firstBatch = [
        new File(['content1'], 'file1.jpg', { type: 'image/jpeg' }),
        new File(['content2'], 'file2.jpg', { type: 'image/jpeg' }),
      ];

      await act(async () => {
        await mediaContext.handleFileUploads(firstBatch, 'test-org-id', 'test-conversation-id');
      });

      // Verify first call was with 0 existing files
      expect(validateFiles).toHaveBeenCalledWith(firstBatch, undefined, 0);

      // Now upload more files
      const secondBatch = [new File(['content3'], 'file3.jpg', { type: 'image/jpeg' })];

      await act(async () => {
        await mediaContext.handleFileUploads(secondBatch, 'test-org-id', 'test-conversation-id');
      });

      // Verify second call was with 2 existing files
      expect(validateFiles).toHaveBeenCalledWith(secondBatch, undefined, 2);

      // Final verification of state
      expect(mediaContext!.uploadedFiles).toHaveLength(3);
      expect(mediaContext!.canAddMoreFiles(2)).toBe(true);
      expect(mediaContext!.canAddMoreFiles(3)).toBe(false);
    });

    it('allows uploading multiple files at once to reach exactly MAX_FILES', async () => {
      let mediaContext: MediaContextType;
      const TestComponent = () => {
        mediaContext = useMedia();
        return null;
      };

      renderWithMantine(
        <MediaProvider>
          <TestComponent />
        </MediaProvider>
      );

      // Upload files to reach exactly MAX_FILES
      const files = Array.from(
        { length: 5 },
        (_, i) => new File(['content'], `file${i}.jpg`, { type: 'image/jpeg' })
      );

      await act(async () => {
        const result = await mediaContext.handleFileUploads(
          files,
          'test-org-id',
          'test-conversation-id'
        );
        expect(result).toBeDefined(); // Should succeed
      });

      // Verify that validateFiles was called with correct parameters (no existing files initially)
      expect(validateFiles).toHaveBeenCalledWith(files, undefined, 0);

      // Should now have exactly MAX_FILES uploaded
      expect(mediaContext!.uploadedFiles).toHaveLength(5); // All files uploaded
      expect(mediaContext!.canAddMoreFiles()).toBe(false); // Exactly at MAX_FILES limit
    });

    it('handles one-by-one file dropping correctly without exceeding MAX_FILES', async () => {
      let mediaContext: MediaContextType;
      const TestComponent = () => {
        mediaContext = useMedia();
        return null;
      };

      renderWithMantine(
        <MediaProvider>
          <TestComponent />
        </MediaProvider>
      );

      // Drop files one by one, simulating user behavior
      for (let i = 1; i <= 5; i++) {
        const file = new File([`content${i}`], `file${i}.jpg`, { type: 'image/jpeg' });

        // Check that we can add this file before uploading
        expect(mediaContext!.canAddMoreFiles(1)).toBe(true);

        // Upload the file
        await act(async () => {
          await mediaContext!.handleFileUploads([file], 'test-org-id', 'test-conversation-id');
        });

        // Verify that validateFiles was called with correct existing files count
        expect(validateFiles).toHaveBeenCalledWith([file], undefined, i - 1);

        // After uploading i files, we should have (MAX_FILES - i) slots left
        const remainingSlots = MAX_FILES - i;
        expect(mediaContext!.canAddMoreFiles(remainingSlots)).toBe(true);
        if (remainingSlots > 0) {
          expect(mediaContext!.canAddMoreFiles(remainingSlots + 1)).toBe(false);
        }

        // Verify that uploadedFiles now contains the uploaded files
        expect(mediaContext!.uploadedFiles).toHaveLength(i);
      }

      // Now we should have reached MAX_FILES and cannot add more
      expect(mediaContext!.canAddMoreFiles(1)).toBe(false);

      // Send the message to clear everything
      await act(async () => {
        const mediaUrls = ['file1.jpg', 'file2.jpg', 'file3.jpg', 'file4.jpg', 'file5.jpg'];
        await mediaContext!.createMessageWithMedia('Test message', mediaUrls);
      });

      // After sending, we should be able to upload MAX_FILES again
      expect(mediaContext!.canAddMoreFiles(5)).toBe(true);
    });
  });

  describe('createMessageWithMedia', () => {
    it('creates message with text and media', async () => {
      let mediaContext: MediaContextType;
      const TestComponent = () => {
        mediaContext = useMedia();
        return null;
      };

      renderWithMantine(
        <MediaProvider>
          <TestComponent />
        </MediaProvider>
      );

      const message = 'Test message';
      const mediaUrls = ['test.jpg', 'test.mp4'];

      await act(async () => {
        await mediaContext!.createMessageWithMedia(message, mediaUrls);
      });

      expect(mockChatBoxUIContext.handleSendEventData).toHaveBeenCalledWith({
        events: expect.arrayContaining([
          expect.objectContaining({
            type: 'message',
            message: {
              type: 'text',
              data: {
                text: message,
              },
            },
            source: {
              type: 'user',
              userId: 'test-user',
              meta: {
                userAgent: 'test-agent',
              },
            },
          }),
          expect.objectContaining({
            type: 'message',
            message: {
              type: 'image',
              data: {
                text: mediaUrls[0],
              },
            },
            source: {
              type: 'user',
              userId: 'test-user',
              meta: {
                userAgent: 'test-agent',
              },
            },
          }),
          expect.objectContaining({
            type: 'message',
            message: {
              type: 'video',
              data: {
                text: mediaUrls[1],
              },
            },
            source: {
              type: 'user',
              userId: 'test-user',
              meta: {
                userAgent: 'test-agent',
              },
            },
          }),
        ]),
      });
    });

    it('creates document message with content type and filename', async () => {
      let mediaContext: MediaContextType;
      const TestComponent = () => {
        mediaContext = useMedia();
        return null;
      };

      renderWithMantine(
        <MediaProvider>
          <TestComponent />
        </MediaProvider>
      );

      // Test with a PDF document URL to test getOriginalFilename and getContentTypeFromFile

      const message = 'Document message';
      const mediaUrls = ['test-document.pdf'];

      await act(async () => {
        const result = await mediaContext!.createMessageWithMedia(message, mediaUrls);
        expect(result).toHaveLength(2); // Text message + document message
      });

      // Verify document message includes contentType and filename
      expect(mockChatBoxUIContext.handleSendEventData).toHaveBeenCalledWith({
        events: expect.arrayContaining([
          expect.objectContaining({
            message: {
              type: 'document',
              data: expect.objectContaining({
                text: mediaUrls[0],
                contentType: 'application/pdf',
                filename: 'test-document.pdf',
              }),
            },
          }),
        ]),
      });
    });

    it('handles message creation errors', async () => {
      const error = new Error('Send failed');
      vi.mocked(mockChatBoxUIContext.handleSendEventData).mockImplementationOnce(() => {
        throw error;
      });

      let mediaContext: MediaContextType;
      const TestComponent = () => {
        mediaContext = useMedia();
        return null;
      };

      renderWithMantine(
        <MediaProvider>
          <TestComponent />
        </MediaProvider>
      );

      await act(async () => {
        await expect(mediaContext!.createMessageWithMedia('Test', ['test.jpg'])).rejects.toThrow(
          'Send failed'
        );
      });
    });

    it('clears uploaded files after successfully sending message with media', async () => {
      let mediaContext: MediaContextType;
      const TestComponent = () => {
        mediaContext = useMedia();
        return null;
      };

      renderWithMantine(
        <MediaProvider>
          <TestComponent />
        </MediaProvider>
      );

      // Upload some files
      const uploadedFiles = [
        new File(['content1'], 'file1.jpg', { type: 'image/jpeg' }),
        new File(['content2'], 'file2.jpg', { type: 'image/jpeg' }),
      ];

      await act(async () => {
        await mediaContext.handleFileUploads(uploadedFiles, 'test-org-id', 'test-conversation-id');
      });

      // Verify files are uploaded
      expect(mediaContext!.uploadedFiles).toHaveLength(2);
      expect(mediaContext!.canAddMoreFiles(3)).toBe(true); // Should be able to add 3 more (2 + 3 = 5 <= 5)
      expect(mediaContext!.canAddMoreFiles(4)).toBe(false); // Should not be able to add 4 more (2 + 4 = 6 > 5)

      const message = 'Test message';
      const mediaUrls = ['test1.jpg', 'test2.jpg'];

      // Send message with media
      await act(async () => {
        await mediaContext!.createMessageWithMedia(message, mediaUrls);
      });

      // Verify that uploadedFiles are cleared after sending
      expect(mediaContext!.uploadedFiles).toHaveLength(0);
      // Verify that we can now upload up to MAX_FILES again
      expect(mediaContext!.canAddMoreFiles(5)).toBe(true);
    });
  });

  describe('file management', () => {
    it('clears processing files', () => {
      let mediaContext: MediaContextType;
      const TestComponent = () => {
        mediaContext = useMedia();
        return null;
      };

      renderWithMantine(
        <MediaProvider>
          <TestComponent />
        </MediaProvider>
      );

      // Mock the clearProcessingFiles method
      vi.mocked(PresignedUploadService.clearProcessingFiles).mockImplementation(() => {});

      // Call the method
      mediaContext!.clearProcessingFiles();

      // Verify it was called
      expect(PresignedUploadService.clearProcessingFiles).toHaveBeenCalled();
    });
  });

  describe('file validation with existing files', () => {
    it('shows validation error when uploading files would exceed max files', async () => {
      const mockShowValidationAlert = vi.fn();
      vi.mocked(useFileValidationAlert).mockReturnValue({
        showValidationAlert: mockShowValidationAlert,
        hideValidationAlert: vi.fn(),
        validationAlert: null,
      });

      let mediaContext: MediaContextType;
      const TestComponent = () => {
        mediaContext = useMedia();
        return null;
      };

      renderWithMantine(
        <MediaProvider>
          <TestComponent />
        </MediaProvider>
      );

      // Upload 5 files (max limit)
      const files = Array.from(
        { length: 5 },
        (_, i) => new File(['content'], `file${i}.jpg`, { type: 'image/jpeg' })
      );

      await act(async () => {
        await mediaContext!.handleFileUploads(files, 'test-org-id', 'test-conversation-id');
      });

      expect(mediaContext!.uploadedFiles).toHaveLength(5);
      expect(mediaContext!.canAddMoreFiles()).toBe(false);

      // Mock validation to fail when trying to add more files
      vi.mocked(validateFiles).mockReturnValueOnce({
        isValid: false,
        errorMessage: 'Maximum file limit exceeded',
      });

      // Try to upload one more file - should trigger validation error
      const extraFile = new File(['content'], 'extra.jpg', { type: 'image/jpeg' });

      await act(async () => {
        try {
          await mediaContext!.handleFileUploads([extraFile], 'test-org-id', 'test-conversation-id');
        } catch (error) {
          // Expected to throw due to validation
          expect(error).toBeInstanceOf(Error);
          expect((error as Error).message).toBe('Maximum file limit exceeded');
        }
      });

      // Should still be 5 files, not 6
      expect(mediaContext!.uploadedFiles).toHaveLength(5);

      // Verify that validateFiles was called with the existing files count (5)
      expect(validateFiles).toHaveBeenCalledWith([extraFile], undefined, 5);

      // Should show validation alert
      expect(mockShowValidationAlert).toHaveBeenCalledWith(expect.any(String));
    });
  });
});
