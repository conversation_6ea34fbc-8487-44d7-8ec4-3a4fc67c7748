import type React from 'react';
import { createContext, useCallback, useContext, useEffect, useState } from 'react';
import useSWR from 'swr';
import { IntegrationBotsAPI } from '@/services/api';
import { useSettingsContext } from './SettingsContext';
import { useTranslate } from '@tolgee/react';
import { errorHandler } from '@/utils/errorHandler';
import { useForm } from 'react-hook-form';
import { DEFAULT_INCIDENT_MESSAGE } from '@/constants';

type FormValues = {
  livechatConnected: boolean;
  livechatSettings: {
    incidentMessage: string;
  };
  chatbotSettings: {
    incidentMessage: string;
  };
};

const useIntegrations = () => {
  const { currentChatboxId, reloadChatbox, currentSettings } = useSettingsContext();
  const [loading, setLoading] = useState(false);
  const { t } = useTranslate('integration');

  const form = useForm({
    defaultValues: {
      livechatConnected: true,
      livechatSettings: {
        incidentMessage: DEFAULT_INCIDENT_MESSAGE,
      },
      chatbotSettings: {
        incidentMessage: DEFAULT_INCIDENT_MESSAGE,
      },
    } as FormValues,
  });

  useEffect(() => {
    const _settings = currentSettings?.integrationSettings;
    form.reset({
      livechatConnected: _settings?.livechatConnected,
      livechatSettings: {
        incidentMessage: _settings?.livechatSettings?.incidentMessage || DEFAULT_INCIDENT_MESSAGE,
      },
      chatbotSettings: {
        incidentMessage: _settings?.chatbotSettings?.incidentMessage || DEFAULT_INCIDENT_MESSAGE,
      },
    });
  }, [currentSettings]);

  const { data: current, isLoading } = useSWR(
    currentChatboxId ? [`bots/${currentChatboxId}`] : null,
    () => {
      return IntegrationBotsAPI.getList();
    }
  );

  const connectBot = useCallback(
    async (payload) => {
      try {
        setLoading(true);
        await IntegrationBotsAPI.connect(payload);
        reloadChatbox();
      } catch (error) {
        errorHandler(error, t);
      } finally {
        setLoading(false);
      }
    },
    [reloadChatbox]
  );

  const disconnectBots = useCallback(async () => {
    setLoading(true);
    await IntegrationBotsAPI.disconnectAll({ chatBoxId: currentChatboxId });
    setTimeout(() => {
      reloadChatbox();
    }, 2000);
    setLoading(false);
  }, [currentChatboxId, reloadChatbox]);

  return {
    listChatbots: current,
    loading: loading || isLoading,
    connectBot,
    disconnectBots,
    form,
  };
};

export type IntegrationContextType = ReturnType<typeof useIntegrations>;

const context = createContext<IntegrationContextType | null>(null);

export const IntegrationContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useIntegrations();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useIntegrationContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useIntegrationContext must be used inside IntegrationContextProvider');
  }

  return value;
};
