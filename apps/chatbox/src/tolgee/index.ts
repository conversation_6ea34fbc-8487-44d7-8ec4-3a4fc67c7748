import { Tolgee, FormatSimple } from '@tolgee/react';
import { InContextTools } from '@tolgee/web/tools';
import { FALLBACK_LANGUAGE } from '@resola-ai/ui/constants';
import { DEFAULT_LANGUAGE } from '@resola-ai/shared-constants';

import { AppConfig } from '@/configs';
import enHome from '@/locales/home/<USER>';
import jaHome from '@/locales/home/<USER>';
import enCommon from '@/locales/common/en.json';
import jaCommon from '@/locales/common/ja.json';
import enMessageClient from '@/locales/messageClient/en.json';
import jaMessageClient from '@/locales/messageClient/ja.json';
import enContentSetting from '@/locales/contentSetting/en.json';
import jaContentSetting from '@/locales/contentSetting/ja.json';
import enIntegration from '@/locales/integration/en.json';
import jaIntegration from '@/locales/integration/ja.json';
import enStylingSetting from '@/locales/stylingSetting/en.json';
import jaStylingSetting from '@/locales/stylingSetting/ja.json';
import enGeneralSetting from '@/locales/generalSetting/en.json';
import jaGeneralSetting from '@/locales/generalSetting/ja.json';

const tolgee = Tolgee()
  .use(AppConfig.TOLGEE_TOOLS_ENABLED ? InContextTools() : undefined)
  .use(FormatSimple())
  .init({
    language: DEFAULT_LANGUAGE,
    fallbackLanguage: FALLBACK_LANGUAGE,
    defaultNs: 'common',
    ns: [
      'home',
      'common',
      'messageClient',
      'contentSetting',
      'integration',
      'stylingSetting',
      'generalSetting',
    ],

    // for development
    apiUrl: AppConfig.TOLGEE_URL,
    apiKey: AppConfig.TOLGEE_KEY,

    // for production
    staticData: {
      'en:home': enHome,
      'ja:home': jaHome,
      'en:common': enCommon,
      'ja:common': jaCommon,
      'en:messageClient': enMessageClient,
      'ja:messageClient': jaMessageClient,
      'en:contentSetting': enContentSetting,
      'ja:contentSetting': jaContentSetting,
      'en:integration': enIntegration,
      'ja:integration': jaIntegration,
      'en:stylingSetting': enStylingSetting,
      'ja:stylingSetting': jaStylingSetting,
      'en:generalSetting': enGeneralSetting,
      'ja:generalSetting': jaGeneralSetting,
    },
    onFormatError: (error) => {
      console.error('Chatbox Tolgee translate error', error);
      return error;
    },
  });

export { tolgee };
