import { Stack, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import ListAvailableWidget from '../ListAvailableWidget';

const useStyles = createStyles((theme) => ({
  container: {},
  customButton: {
    marginBottom: rem(16),
    color: theme.colors.silverFox[1],
    backgroundColor: theme.colors.decaNavy[5],
    '&:hover': {
      backgroundColor: theme.colors.decaNavy[4],
    },
  },
  title: {
    color: theme.colors.silverFox[9],
    fontSize: rem(20),
  },
}));

const ContainerWidgets = () => {
  const { classes } = useStyles();
  const { t } = useTranslate('contentSetting');

  // const handleLoadMoreWidget = () => {}; comment for MVP version

  return (
    <Stack className={classes.container}>
      <Title className={classes.title}>{t('addWidgetTitle')}</Title>
      <ListAvailableWidget />
      {/* Comment for MVP Version */}
      {/* <CustomButton
        leftIcon={<IconPlus />}
        size='sm'
        onClick={handleLoadMoreWidget}
        customClass={classes.customButton}
      >
        <Text c={'silverFox.1'}>{t('moreWidget')}</Text>
      </CustomButton> */}
    </Stack>
  );
};

export default ContainerWidgets;
