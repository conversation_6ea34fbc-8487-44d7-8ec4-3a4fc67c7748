import { Stack } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import WrapperAvailableWidgets from './WrapperAvailableWidgets';
import { getPublicUrl } from '@resola-ai/utils';
import { WIDGET_TYPE } from '@/constants/widgetType';

const ListAvailableWidget = () => {
  const { t } = useTranslate('contentSetting');
  const widgetList = [
    {
      type: WIDGET_TYPE.CALL_TO_ACTION,
    },
    {
      type: WIDGET_TYPE.LARGE_IMAGE_LINK,
      properties: {
        imageSource: getPublicUrl('svg/default-image.svg'),
        title: t('smallLargeImageDefaultTitle'),
        description: t('smallLargeImageDefaultDescription'),
        link: '',
      },
    },
    {
      type: WIDGET_TYPE.SMALL_IMAGE_LINK,
      properties: {
        imageSource: getPublicUrl('svg/default-image.svg'),
        title: t('smallLargeImageDefaultTitle'),
        description: t('smallLargeImageDefaultDescription'),
        link: '',
      },
    },
    /* Comment for MVP Version */
    // { id: 4, title: 'FAQ', subTitle: 'Showing Frequently Questions', type: 'FAQ' },
    // {
    //   id: 5,
    //   title: 'Sending message',
    //   subTitle: 'Start conversation with the bot',
    //   type: 'SENDING_MESSAGE',
    // },
    // { id: 6, title: 'Quick answer', subTitle: 'Asking quick question', type: 'QUICK_ANSWER' },
  ];

  return (
    <Stack>
      {widgetList.map((widget, index) => (
        <WrapperAvailableWidgets key={`${widget.type}-${index}`} widget={widget} />
      ))}
    </Stack>
  );
};

export default ListAvailableWidget;
