import { rem, Stack, Flex, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCirclePlus } from '@tabler/icons-react';
import type { CustomWidget } from '@/types';
import { v4 as uuidV4 } from 'uuid';
import { useContentSettingsContext } from '@/pages/ContentSetting/ContentSettingsContext';
import CommonTitle from '../../../CommonTitle';
import WidgetUIComponent from '../../../WidgetUIComponent';

const useStyles = createStyles((theme) => ({
  container: {
    border: `${rem(1)} solid ${theme.colors.silverFox[5]}`,
    borderRadius: rem(8),
    backgroundColor: theme.colors.silverFox[2],
  },
  customWidget: {
    pointerEvents: 'none',
    cursor: 'auto',
  },
  iconCustom: {
    cursor: 'pointer',
  },
}));

type Props = {
  widget: CustomWidget;
};

const WrapperAvailableWidgets = ({ widget }: Props) => {
  const { classes } = useStyles();
  const mantineTheme = useMantineTheme();
  const { onAddWidget, handleResetDrawerForm } = useContentSettingsContext();

  const handleAddWidget = () => {
    const item: CustomWidget = {
      id: uuidV4(),
      type: widget.type,
    };
    onAddWidget(item);
    handleResetDrawerForm(widget.type);
  };

  return (
    <Stack gap='md' p={rem(12)} className={classes.container}>
      <Flex justify={'space-between'}>
        <CommonTitle type={widget.type} />
        <IconCirclePlus
          className={classes.iconCustom}
          size={26}
          strokeWidth={2}
          color={mantineTheme.colors.pureApple[5]}
          onClick={handleAddWidget}
        />
      </Flex>
      <WidgetUIComponent
        widgetType={widget.type}
        properties={widget.properties}
        customWidget={classes.customWidget}
      />
    </Stack>
  );
};

export default WrapperAvailableWidgets;
