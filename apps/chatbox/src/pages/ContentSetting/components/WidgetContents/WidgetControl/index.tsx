import { useEffect } from 'react';
import { Text, Drawer, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { CustomButton } from '@resola-ai/ui';
import ContainerWidgets from './ContainerWidgets';
import { useContentSettingsContext } from '@/pages/ContentSetting/ContentSettingsContext';
// import styled from '@emotion/styled';
import EditFormWidgetLayout from '../EditFormWidgetLayout';
import { useSettingsContext } from '@/contexts/SettingsContext';
import { errorHandler, ERROR_TYPES } from '@/utils/errorHandler';

// const Form = styled.form`
//   height: 100%;
// `;

const useStyles = createStyles((theme) => ({
  drawerContainer: {
    '.mantine-Drawer-body': {
      height: '100%',
    },
  },
  formContainer: {
    height: '100%',
  },
  buttonCustom: {
    '[data-position="left"]': {
      marginInlineEnd: '4px',
    },
  },
}));

const WidgetControl = () => {
  const { classes } = useStyles();
  const { t } = useTranslate('contentSetting');
  const { LIMITATION_OF_WIDGETS } = useSettingsContext();
  const { addingWidget, drawerForm, form } = useContentSettingsContext();
  const [opened, { open, close }] = useDisclosure(false);
  const [openedEditModal, { open: openEditModal, close: closeEditModal }] = useDisclosure(false);
  const { watch } = form;
  const widgets = watch('widgetList') || [];

  useEffect(() => {
    if (addingWidget) {
      close();
      openEditModal();
    }
  }, [addingWidget, close, openEditModal]);

  useEffect(() => {
    const root = document.getElementById('root');
    if (!root) return;
    if (openedEditModal) {
      root.setAttribute('aria-hidden', 'false');
    } else {
      root.removeAttribute('aria-hidden');
    }
  }, [openedEditModal]);

  const handleCloseDrawer = () => {
    closeEditModal();
    drawerForm.reset();
  };

  const handleAddWidget = () => {
    if (widgets.length >= LIMITATION_OF_WIDGETS) {
      errorHandler(ERROR_TYPES.WIDGETS_LIMITATION, t);
      return;
    }

    open();
  };

  return (
    <>
      <Drawer
        opened={opened}
        onClose={close}
        position={'right'}
        size={'md'}
        className={classes.drawerContainer}
        withCloseButton={false}
        closeOnEscape={false}
      >
        <ContainerWidgets />
      </Drawer>
      <CustomButton
        colorScheme='primary'
        leftIcon={<IconPlus />}
        size='sm'
        onClick={handleAddWidget}
        className={classes.buttonCustom}
      >
        <Text c={'silverFox.1'} size={rem(16)}>
          {t('addWidgetTitle')}
        </Text>
      </CustomButton>
      <Drawer
        opened={openedEditModal}
        onClose={handleCloseDrawer}
        position={'right'}
        size={'lg'}
        className={classes.drawerContainer}
        withCloseButton={false}
      >
        {addingWidget && (
          <form className={classes.formContainer}>
            <EditFormWidgetLayout
              widgetType={addingWidget.type}
              onCloseDrawer={handleCloseDrawer}
              widgetId={addingWidget.id}
            />
          </form>
        )}
      </Drawer>
    </>
  );
};

export default WidgetControl;
