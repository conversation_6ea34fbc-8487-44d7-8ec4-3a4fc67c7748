import { rem, Title, Stack, TextInput, Flex, Divider } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronRight, IconSearch } from '@tabler/icons-react';

const useStyles = createStyles((theme) => ({
  customClass: {
    width: rem('80%'),
    padding: rem(12),
    border: `1px solid ${theme.colors.silverFox[5]}`,
    color: theme.colors.silverFox[1],
    borderRadius: rem(8),
    backgroundColor: theme.colors.silverFox[0],
  },
  title: {
    color: theme.colors.silverFox[9],
    fontSize: rem(12),
  },
  icon: {
    color: theme.colors.silverFox[9],
    width: rem(16),
    height: rem(16),
    strokeWidth: 2,
  },
}));

const FAQWidget = () => {
  const { classes } = useStyles();

  return (
    <Stack className={classes.customClass} gap='xs'>
      <TextInput placeholder='Search' leftSection={<IconSearch size={16} />} disabled />
      <Flex justify='space-between' align='center'>
        <Title className={classes.title}>Question</Title>
        <IconChevronRight className={classes.icon} />
      </Flex>
      <Divider />
      <Flex justify='space-between' align='center'>
        <Title className={classes.title}>Question</Title>
        <IconChevronRight className={classes.icon} />
      </Flex>
      <Divider />
      <Flex justify='space-between' align='center'>
        <Title className={classes.title}>Question</Title>
        <IconChevronRight className={classes.icon} />
      </Flex>
      <Divider />
    </Stack>
  );
};
export default FAQWidget;
