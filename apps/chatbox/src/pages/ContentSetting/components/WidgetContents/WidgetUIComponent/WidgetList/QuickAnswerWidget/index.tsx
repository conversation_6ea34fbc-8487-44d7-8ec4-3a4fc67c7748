import { memo } from 'react';
import { rem, Container, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { Engine as WidgetEngine } from '@resola-ai/widget-engine';
import { getGeneralWidgetTemplates } from '@/components/ChatBoxUI/App/Widgets/templates';
import type { QuickAnswerResponse } from '@/components/ChatBoxUI/models';

const useStyles = createStyles(() => ({
  container: {
    width: rem('80%'),
    padding: 0,
    margin: 0,
  },
}));

interface QuickAnswerWidgetProps {
  customWidget?: string;
  widgetData?: QuickAnswerResponse;
  commonStyle?: any;
  styleWidgetContext?: any;
  engineConfig?: any;
}

const QuickAnswerWidget: React.FC<QuickAnswerWidgetProps> = ({
  customWidget,
  widgetData,
  commonStyle,
  styleWidgetContext,
  engineConfig,
}) => {
  const { classes, cx } = useStyles();
  const mantineTheme = useMantineTheme();

  const initStyleContext = {
    widgetTemplates: {
      commonStyle,
      quickAnswer: {
        titleColor: mantineTheme.colors.silverFox[9],
        answerItemBackgroundColor: mantineTheme.colors.decaNavy[0],
        answerItemColor: mantineTheme.colors.decaNavy[5],
      },
    },
  };
  // no jp version yet. will update later
  const initData = {
    title: 'Question',
    answerItems: ['Answer 1', 'Answer 2', 'Answer 3', 'Answer 4', 'Answer 5', 'Answer 6'],
  };

  const rawData = widgetData ?? initData;
  const rawStyleContext = styleWidgetContext ?? initStyleContext;

  const result = { ...rawData, ...rawStyleContext };

  return (
    <Container fluid className={cx(classes.container, customWidget)}>
      <WidgetEngine
        data={getGeneralWidgetTemplates(result, 'quickAnswer', result)}
        context={result}
        config={engineConfig}
      />
    </Container>
  );
};

export default memo(QuickAnswerWidget);
