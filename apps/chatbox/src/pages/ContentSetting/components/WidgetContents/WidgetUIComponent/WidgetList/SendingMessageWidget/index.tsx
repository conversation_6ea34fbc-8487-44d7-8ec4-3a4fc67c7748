import { memo } from 'react';
import { rem, Container, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { Engine as WidgetEngine } from '@resola-ai/widget-engine';
import { getGeneralWidgetTemplates } from '@/components/ChatBoxUI/App/Widgets/templates';
import type { SendingMessageResponse } from '@/components/ChatBoxUI/models';

const useStyles = createStyles(() => ({
  container: {
    width: rem('80%'),
    padding: 0,
    margin: 0,
  },
}));

interface Props {
  customWidget?: string;
  widgetData?: SendingMessageResponse;
  commonStyle?: any;
  styleWidgetContext?: any;
  engineConfig?: any;
}

const SendingMessageWidget: React.FC<Props> = ({
  customWidget,
  widgetData,
  commonStyle,
  styleWidgetContext,
  engineConfig,
}) => {
  const { classes, cx } = useStyles();
  const mantineTheme = useMantineTheme();

  const initStyleContext = {
    widgetTemplates: {
      commonStyle,
      sendingMessage: {
        titleColor: mantineTheme.colors.silverFox[9],
        description: mantineTheme.colors.silverFox[9],
        iconSendColor: mantineTheme.colors.decaNavy[5],
      },
    },
  };

  // no jp version yet. will update later
  const initData = {
    title: 'Send us a message',
    description: 'We here to help you',
  };

  const rawData = widgetData ?? initData;
  const rawStyleContext = styleWidgetContext ?? initStyleContext;

  const result = { ...rawData, ...rawStyleContext };

  return (
    <Container fluid className={cx(classes.container, customWidget)}>
      <WidgetEngine
        data={getGeneralWidgetTemplates(result, 'sendingMessage', result)}
        context={result}
        config={engineConfig}
      />
    </Container>
  );
};
export default memo(SendingMessageWidget);
