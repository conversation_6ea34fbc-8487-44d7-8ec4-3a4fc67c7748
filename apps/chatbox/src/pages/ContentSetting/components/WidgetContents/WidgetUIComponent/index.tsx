import { WIDGET_TYPE } from '@/constants/widgetType';
import {
  CallToActionWidget,
  FAQWidget,
  LargeImageLinkWidget,
  QuickAnswerWidget,
  SmallImageLinkWidget,
  WelcomeMessageWidget,
} from './WidgetList';
import type { CustomWidget, SimpleMap } from '@/types';
import { useMantineTheme } from '@mantine/core';

const COMPONENTS = {
  [WIDGET_TYPE.CALL_TO_ACTION]: CallToActionWidget,
  [WIDGET_TYPE.LARGE_IMAGE_LINK]: LargeImageLinkWidget,
  [WIDGET_TYPE.SMALL_IMAGE_LINK]: SmallImageLinkWidget,
  [WIDGET_TYPE.FAQ]: FAQWidget,
  [WIDGET_TYPE.QUICK_ANSWER]: QuickAnswerWidget,
  [WIDGET_TYPE.WELCOME_MESSAGE]: WelcomeMessageWidget,
};
type WidgetUIComponentProps = {
  widgetType: string;
  customWidget?: string;
  properties?: SimpleMap;
  widget?: CustomWidget;
  styleWidgetContext?: any;
  onSaveWidget?: (values: any) => void;
  engineConfig?: any;
};

const WidgetUIComponent = ({
  widgetType,
  customWidget,
  properties,
  widget,
  styleWidgetContext,
  onSaveWidget,
  engineConfig,
}: WidgetUIComponentProps) => {
  const mantineTheme = useMantineTheme();

  const commonStyle = {
    backgroundColor: mantineTheme.colors.silverFox[0],
    borderColor: mantineTheme.colors.silverFox[5],
    fontStyle: 'normal',
  };

  const Component = COMPONENTS[widgetType];
  return (
    <Component
      commonStyle={commonStyle}
      customWidget={customWidget}
      properties={properties}
      styleWidgetContext={styleWidgetContext}
      editMode={widgetType === WIDGET_TYPE.WELCOME_MESSAGE}
      widget={widget}
      onSaveWidget={onSaveWidget}
      engineConfig={engineConfig}
    />
  );
};

export default WidgetUIComponent;
