import { memo } from 'react';
import { rem, Container, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { getGeneralWidgetTemplates } from '@/components/ChatBoxUI/App/Widgets/templates';
import type { WelcomeMessage } from '@/components/ChatBoxUI/models';
import { Engine as WidgetEngine } from '@resola-ai/widget-engine';
import type { CustomWidget, SimpleMap } from '@/types';
import { replaceLineBreaks } from '@/utils';
import { Textarea } from 'react-hook-form-mantine';
import { useForm } from 'react-hook-form';

const useStyles = createStyles((theme) => ({
  container: {
    borderRadius: rem(4),
    border: `1px solid ${theme.colors.silverFox[5]}`,
    backgroundColor: theme.colors.silverFox[0],
    padding: `${rem(8)} ${rem(12)}`,
    wordBreak: 'break-all',
    margin: 0,
  },
  inputTextArea: {
    fontSize: rem(14),
  },
}));

interface WelcomeMessageWidgetProps {
  widgetData?: WelcomeMessage;
  customClassWelcomeWidget?: string;
  commonStyle?: any;
  styleWidgetContext?: any;
  properties?: SimpleMap;
  editMode?: boolean;
  widget?: CustomWidget;
  onSaveWidget?: (values: any) => void;
  engineConfig?: any;
}

const WelcomeMessageWidget = ({
  widgetData,
  commonStyle,
  styleWidgetContext,
  properties,
  customClassWelcomeWidget,
  editMode = false,
  widget,
  onSaveWidget,
  engineConfig,
}: WelcomeMessageWidgetProps) => {
  const { classes, cx } = useStyles();
  const mantineTheme = useMantineTheme();

  const form = useForm({
    defaultValues: {
      title: widget?.properties?.title,
    },
  });

  const { control } = form;

  const initStyleContext = {
    widgetTemplates: {
      commonStyle,
      welcomeMessage: {
        textColor: mantineTheme.colors.silverFox[9],
        fontSize: rem(14),
        minHeight: rem(21),
      },
    },
  };

  const initData = {
    text: replaceLineBreaks(properties?.title || ''),
  };

  const rawData = widgetData ?? initData;
  const rawStyleContext = styleWidgetContext ?? initStyleContext;

  const result = { ...rawData, ...rawStyleContext };

  const handleChangeMessage = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (widget) {
      onSaveWidget?.({
        ...widget.properties,
        title: event.target.value,
        type: widget.type,
        id: widget.id,
      });
    }
  };

  if (editMode) {
    return (
      <Textarea
        name='title'
        control={control}
        autosize
        minRows={1}
        classNames={{ input: classes.inputTextArea }}
        onChange={handleChangeMessage}
      />
    );
  }

  return (
    <Container fluid className={cx(classes.container, customClassWelcomeWidget)}>
      <WidgetEngine
        data={getGeneralWidgetTemplates(result, 'welcomeMessage', result)}
        context={result}
        config={engineConfig}
      />
    </Container>
  );
};

export default memo(WelcomeMessageWidget);
