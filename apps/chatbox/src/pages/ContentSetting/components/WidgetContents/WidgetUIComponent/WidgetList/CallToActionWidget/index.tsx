import { memo } from 'react';
import type { SimpleMap } from '@/types';
import { rem, Container, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { Engine as WidgetEngine } from '@resola-ai/widget-engine';
import { getGeneralWidgetTemplates } from '@/components/ChatBoxUI/App/Widgets/templates';
import type { CallToActionResponse } from '@/components/ChatBoxUI/models';
import { useTranslate } from '@tolgee/react';

const useStyles = createStyles(() => ({
  container: {
    width: rem('80%'),
    padding: 0,
    margin: 0,
  },
}));

type CallToActionProps = {
  customWidget?: string;
  properties?: SimpleMap;
  widgetData?: CallToActionResponse;
  styleWidgetContext?: any;
  engineConfig?: any;
};

const CallToActionWidget = ({
  customWidget,
  properties,
  widgetData,
  styleWidgetContext,
  engineConfig,
}: CallToActionProps) => {
  const { classes, cx } = useStyles();
  const mantineTheme = useMantineTheme();
  const { t } = useTranslate('contentSetting');

  const initStyleContext = {
    widgetTemplates: {
      callToAction: {
        backgroundColor: mantineTheme.colors.decaNavy[5],
        hoverBackgroundColor: mantineTheme.colors.decaNavy[5],
        color: mantineTheme.colors.silverFox[1],
      },
    },
  };

  const initData = {
    id: properties?.id || '',
    text: properties?.label || t('callToActionDefaultLabel'),
    href: properties?.link || '',
    behavior: properties?.behavior || 'moveToUrl',
    target: '',
    clickedEvent: properties?.clickedEvent || '',
  };

  const rawData = widgetData ?? initData;
  const rawStyleContext = styleWidgetContext ?? initStyleContext;

  const result = { ...rawData, ...rawStyleContext };

  return (
    <Container fluid className={cx(classes.container, customWidget)}>
      <WidgetEngine
        data={getGeneralWidgetTemplates(result, 'callToActionButton', result)}
        context={result}
        config={engineConfig}
      />
    </Container>
  );
};
export default memo(CallToActionWidget);
