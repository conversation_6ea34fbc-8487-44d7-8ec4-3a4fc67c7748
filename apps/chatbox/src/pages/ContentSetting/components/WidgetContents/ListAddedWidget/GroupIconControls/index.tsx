import { useEffect, useState } from 'react';
import { Drawer, Group, Switch, rem, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import {
  IconGripHorizontal,
  IconAdjustmentsHorizontal,
  IconCircleMinus,
} from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';
import { WIDGET_TYPE } from '@/constants/widgetType';
import type { CustomWidget } from '@/types';
import { useContentSettingsContext } from '@/pages/ContentSetting/ContentSettingsContext';
// import styled from '@emotion/styled';
import EditFormWidgetLayout from '../../EditFormWidgetLayout';
import RemoveWidgetModal from '../../EditFormWidgetLayout/RemoveWidgetModal';

const useStyles = createStyles((theme) => ({
  icon: {
    width: rem(26),
    height: rem(26),
    strokeWidth: 2,
    cursor: 'pointer',
  },
  drawerContainer: {
    '.mantine-Drawer-body': {
      height: '100%',
    },
  },
  formContainer: {
    height: '100%',
  },
}));

// const Form = styled.form`
//   height: 100%;
// `;

type Props = {
  widget: CustomWidget;
};

const IconControlsWelcomeMessageWidget = ({ widget }: Props) => {
  const { classes } = useStyles();
  const mantineTheme = useMantineTheme();
  const [opened, { open, close }] = useDisclosure(false);
  const [checked, setChecked] = useState(!widget.properties?.hidden);
  const { onSelectWidget, onSaveWidget } = useContentSettingsContext();

  const handleEditWidget = () => {
    if (!checked) return;
    onSelectWidget(widget);
    open();
  };

  useEffect(() => {
    onSelectWidget(widget);
    onSaveWidget({ ...widget.properties, hidden: !checked, type: widget.type, id: widget.id });
  }, [checked]);

  return (
    <>
      <Group justify='flex-end'>
        <Switch
          size='md'
          checked={checked}
          onChange={(event) => setChecked(event.currentTarget.checked)}
        />
        {/* <IconAdjustmentsHorizontal
          className={classes.icon}
          color={checked ? mantineTheme.colors.silverFox[9] : mantineTheme.colors.silverFox[5]}
          onClick={handleEditWidget}
        /> */}
        <IconGripHorizontal className={classes.icon} color={mantineTheme.colors.silverFox[5]} />
      </Group>
      <Drawer
        opened={opened}
        onClose={close}
        position={'right'}
        size={'md'}
        className={classes.drawerContainer}
        withCloseButton={false}
        closeOnEscape={false}
      >
        <form className={classes.formContainer} onSubmit={(e) => e.preventDefault()}>
          <EditFormWidgetLayout
            widgetType={WIDGET_TYPE.WELCOME_MESSAGE}
            onCloseDrawer={close}
            widgetId={widget.id}
          />
        </form>
      </Drawer>
    </>
  );
};

const IconControlsCommonWidgets = ({ widget }: Props) => {
  const { classes } = useStyles();
  const mantineTheme = useMantineTheme();
  const [opened, { open, close }] = useDisclosure(false);
  const [openedRemoveModal, { open: openRemoveModal, close: closeRemoveModal }] =
    useDisclosure(false);
  const { onSelectWidget } = useContentSettingsContext();

  const handleEditWidget = () => {
    onSelectWidget(widget);
    open();
  };

  const removeWidgetFromList = () => {
    onSelectWidget(widget);
    openRemoveModal();
  };

  return (
    <>
      <Group justify='flex-end'>
        <IconAdjustmentsHorizontal
          className={classes.icon}
          color={mantineTheme.colors.silverFox[9]}
          onClick={handleEditWidget}
        />
        <IconCircleMinus
          className={classes.icon}
          color={mantineTheme.colors.redSalsa[5]}
          onClick={removeWidgetFromList}
        />
        <IconGripHorizontal className={classes.icon} color={mantineTheme.colors.silverFox[9]} />
      </Group>
      <Drawer
        opened={opened}
        onClose={close}
        position={'right'}
        size={'lg'}
        className={classes.drawerContainer}
        withCloseButton={false}
      >
        <form className={classes.formContainer}>
          <EditFormWidgetLayout
            widgetType={widget.type}
            onCloseDrawer={close}
            widgetId={widget.id}
          />
        </form>
      </Drawer>
      <RemoveWidgetModal isOpen={openedRemoveModal} close={closeRemoveModal} />
    </>
  );
};

const GroupIconControls = ({ widget }: Props) => {
  if (widget.type === WIDGET_TYPE.WELCOME_MESSAGE) {
    return <IconControlsWelcomeMessageWidget widget={widget} />;
  }

  return <IconControlsCommonWidgets widget={widget} />;
};

export default GroupIconControls;
