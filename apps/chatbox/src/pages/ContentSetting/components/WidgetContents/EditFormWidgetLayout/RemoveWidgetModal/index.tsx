import { useCallback } from 'react';
import { Modal, Title, Text, rem, Flex, ActionIcon } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { IconX, IconAlertCircle } from '@tabler/icons-react';
import { CustomGroupButton } from '@/components';
import { useContentSettingsContext } from '@/pages/ContentSetting/ContentSettingsContext';

const useStyles = createStyles((theme) => ({
  container: {
    justifyContent: 'flex-end',
  },
  wrapAlertIcon: {
    backgroundColor: theme.colors.redSalsa[0],
    width: rem(55),
    height: rem(55),
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconX: {
    color: theme.colors.silverFox[9],
  },
  iconAlertCircle: {
    color: theme.colors.redSalsa[5],
  },
  text: {
    color: theme.colors.silverFox[9],
    fontSize: rem(14),
  },
  title: {
    color: theme.colors.silverFox[9],
    fontSize: rem(18),
  },
}));

type Props = {
  isOpen: boolean;
  close: () => void;
};

const RemoveWidgetModal = ({ isOpen, close }: Props) => {
  const { classes } = useStyles();
  const { t } = useTranslate('contentSetting');
  const { widgets, selectedWidget, form } = useContentSettingsContext();

  const handleConfirmRemove = useCallback(() => {
    const _widgets = [...widgets];
    const index = _widgets.findIndex((w) => w.id === selectedWidget?.id);
    if (index === -1) return;
    _widgets.splice(index, 1);
    form.setValue('widgetList', [..._widgets]);

    close();
  }, [close, form, selectedWidget, widgets]);

  return (
    <Modal
      opened={isOpen}
      onClose={close}
      centered
      padding={rem(24)}
      withCloseButton={false}
      closeOnClickOutside={false}
      closeOnEscape={false}
    >
      <Flex justify={'space-between'} mb={rem(24)}>
        <span className={classes.wrapAlertIcon}>
          <IconAlertCircle className={classes.iconAlertCircle} />
        </span>
        <ActionIcon color={'silverFox.3'} variant='filled' onClick={close}>
          <IconX className={classes.iconX} />
        </ActionIcon>
      </Flex>
      <Title className={classes.title} mb={rem(12)}>
        {t('removeWidgetTitle')}
      </Title>
      <Text className={classes.text} mb={rem(24)}>
        {t('removeWidgetDescription')}
      </Text>
      <CustomGroupButton
        rejectTitle={t('cancel')}
        approveTitle={t('remove')}
        onReject={close}
        onApprove={handleConfirmRemove}
        size={'sm'}
        containerCustomClass={classes.container}
      />
    </Modal>
  );
};

export default RemoveWidgetModal;
