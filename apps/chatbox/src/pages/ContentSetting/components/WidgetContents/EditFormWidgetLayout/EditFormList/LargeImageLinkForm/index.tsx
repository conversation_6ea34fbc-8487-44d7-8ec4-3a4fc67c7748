import { useCallback, useState, useMemo } from 'react';
import { rem, Stack, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate, useTolgee } from '@tolgee/react';
import { TextInput } from 'react-hook-form-mantine';
import { useContentSettingsContext } from '@/pages/ContentSetting/ContentSettingsContext';
import { CustomDropzone } from '@/components';
import { DropZonePropsDefault } from '@/constants/uploadSetting';
import { URL_PATTERN } from '@/constants';
import { BlockNoteMarkdown } from '@resola-ai/blocknote-editor';
import FormTitle from '../../FormTitle';

const useStyles = createStyles(() => ({
  editor: {
    '&.bn-container': {
      maxHeight: '700px',
      minHeight: '120px',
      scrollbarWidth: 'thin',
    },
  },
}));

const LargeImageLinkForm = () => {
  const { classes } = useStyles();
  const { t } = useTranslate('contentSetting');
  const tolgee = useTolgee();
  const {
    drawerForm: { control, setValue, watch },
    uploadImage,
    handleClearImageFile,
    selectedWidget,
  } = useContentSettingsContext();
  const [isLoading, setIsLoading] = useState(false);
  const img_src = watch('image_src');

  const initialText = useMemo(
    () => selectedWidget?.properties?.description || '',
    [selectedWidget]
  );

  const handleChangeImage = async (files: File[]) => {
    if (!files || files.length === 0) return;

    setIsLoading(true);
    const image = await uploadImage(files[0] as File);
    setValue('image_src', image);
    setIsLoading(false);
  };

  const onBlockNoteChange = useCallback(
    (markdown: string) => {
      setValue('image_description', markdown);
    },
    [setValue]
  );

  return (
    <Stack>
      <FormTitle title={t('largeImageLinksTitle')} subTitle={t('largeImageLinksSubTitle')} />
      <CustomDropzone
        onChange={(file) => handleChangeImage(file)}
        attachmentUrl={img_src}
        isLoading={isLoading}
        dropzoneTextIdle={t('dropzoneIdleText')}
        dropzoneProps={DropZonePropsDefault}
        onClearImages={handleClearImageFile}
      />
      <TextInput
        label={t('title')}
        placeholder={t('addTitlePlaceholder')}
        name='image_title'
        control={control}
        maxLength={50}
      />
      <TextInput
        label={t('link')}
        placeholder={t('enterLinkUrlPlaceholder')}
        name='image_link'
        control={control}
        rules={{
          validate: (value) => {
            if (!value) return true;
            return URL_PATTERN.test(value) ? true : t('wrong_url_format');
          },
        }}
      />
      <div>
        <Text c='silverFox.9' fw={600} fz={rem(14)}>
          {t('description')}
        </Text>
        <BlockNoteMarkdown
          className={classes.editor}
          initialMarkdown={initialText}
          language={tolgee.getLanguage()}
          onChange={onBlockNoteChange}
        />
      </div>
    </Stack>
  );
};

export default LargeImageLinkForm;
