import { Stack, Group, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { TextInput, Radio } from 'react-hook-form-mantine';
import { useContentSettingsContext } from '@/pages/ContentSetting/ContentSettingsContext';
import { URL_PATTERN } from '@/constants';
import { createStyles } from '@mantine/emotion';
import FormTitle from '../../FormTitle';

const useStyles = createStyles(() => ({
  textInput: {
    '.mantine-InputWrapper-label': {
      fontSize: '14px',
      paddingBottom: '4px',
      fontWeight: '600',
    },
  },
  radioGroup: {
    '.mantine-Radio-label': {
      fontSize: '14px',
      paddingBottom: '4px',
      fontWeight: '600',
    },
  },
  radioGroupLabel: {
    fontSize: rem(14),
    paddingBottom: '4px',
    fontWeight: '600',
  },
}));

const CallActionButtonForm = () => {
  const { classes } = useStyles();
  const { t } = useTranslate('contentSetting');
  const {
    drawerForm: { control, watch },
  } = useContentSettingsContext();

  const buttonBehavior = watch('button_behavior');

  return (
    <Stack>
      <FormTitle title={t('callToActionButtonTitle')} subTitle={t('callToActionButtonSubTitle')} />
      <TextInput
        className={classes.textInput}
        name='button_label'
        control={control}
        rules={{ required: t('required') }}
        label={t('buttonLabel')}
        placeholder={t('buttonLabelPlaceholder')}
      />
      <Radio.Group control={control} name='button_behavior'>
        <Group>
          <Radio.Item
            value='moveToUrl'
            label={t('moveToUrl')}
            classNames={{ label: classes.radioGroupLabel }}
          />
          <Radio.Item
            value='chatWithBot'
            label={t('chatWithBot')}
            classNames={{ label: classes.radioGroupLabel }}
          />
        </Group>
      </Radio.Group>
      {buttonBehavior === 'moveToUrl' && (
        <TextInput
          className={classes.textInput}
          name='button_link'
          control={control}
          label={t('link')}
          placeholder={t('enterLinkUrlPlaceholder')}
          rules={{
            required: t('required'),
            validate: (value) => (URL_PATTERN.test(value) ? true : t('wrong_url_format')),
          }}
        />
      )}
    </Stack>
  );
};

export default CallActionButtonForm;
