import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { CustomGroupButton } from '@/components';
import { useContentSettingsContext } from '@/pages/ContentSetting/ContentSettingsContext';

const useStyles = createStyles((theme) => ({
  container: {
    gap: rem(10),
    flexWrap: 'nowrap',
    padding: `${rem(10)} 0`,
  },
}));

type WidgetButtonGroupProps = {
  onCloseDrawer: () => void;
  widgetType: string;
  widgetId?: string;
};

const WidgetButtonGroup = ({ onCloseDrawer, widgetType, widgetId }: WidgetButtonGroupProps) => {
  const { classes } = useStyles();
  const { t } = useTranslate('contentSetting');
  const { drawerForm, onSaveWidget } = useContentSettingsContext();

  const handleSaveWidget = (values) => {
    onSaveWidget({ ...values, type: widgetType, id: widgetId });
    onCloseDrawer();
  };
  const handleCancelWidget = () => {
    onCloseDrawer();
  };

  return (
    <CustomGroupButton
      rejectTitle={t('cancel')}
      approveTitle={t('save')}
      onReject={handleCancelWidget}
      onApprove={drawerForm.handleSubmit(handleSaveWidget)}
      size='md'
      containerCustomClass={classes.container}
      grow={true}
      widgetType={widgetType}
    />
  );
};

export default WidgetButtonGroup;
