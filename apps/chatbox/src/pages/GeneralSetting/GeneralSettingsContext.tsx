import type React from 'react';
import { createContext, useContext, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { BOT_TRIGGER_TYPES } from '@/constants';
import { useSettingsContext } from '@/contexts/SettingsContext';

type FormValues = {
  image: string | File;
  botName: string;
  botDescription: string;
  autoOpen: boolean;
  botTrigger: BOT_TRIGGER_TYPES;
  botDelayDisplayTime: number;
  enableChatwindow: boolean;
};

const useGeneralSettings = () => {
  const { currentSettings } = useSettingsContext();

  const form = useForm({
    defaultValues: {
      image: '',
      botName: '',
      botDescription: '',
      autoOpen: false,
      botTrigger: BOT_TRIGGER_TYPES.OPEN_PAGE,
      botDelayDisplayTime: 0,
      enableChatwindow: true,
    } as FormValues,
  });

  useEffect(() => {
    const _settings = currentSettings?.generalSettings;
    form.reset({
      image: currentSettings?.image || '',
      botName: currentSettings?.generalSettings?.botName || '',
      botDescription: currentSettings?.generalSettings?.botDescription || '',
      autoOpen: _settings?.autoOpen,
      botTrigger: (_settings?.botTrigger as BOT_TRIGGER_TYPES) || BOT_TRIGGER_TYPES.OPEN_PAGE,
      botDelayDisplayTime: _settings?.botDelayDisplayTime || 0,
      enableChatwindow: _settings?.enableChatwindow,
    });
  }, [currentSettings]);

  return {
    form,
  };
};

export type GeneralSettingsContextType = ReturnType<typeof useGeneralSettings>;

const context = createContext<GeneralSettingsContextType | null>(null);

export const GeneralSettingsContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useGeneralSettings();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useGeneralSettingsContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useGeneralSettingsContext must be used inside GeneralSettingsContextProvider');
  }

  return value;
};
