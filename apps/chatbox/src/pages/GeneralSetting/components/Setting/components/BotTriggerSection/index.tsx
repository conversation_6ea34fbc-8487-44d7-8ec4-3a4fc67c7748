import { <PERSON><PERSON><PERSON>, CustomLabel } from '@/components';
import { Stack, Text, rem, Tooltip, Copy<PERSON>utton, ActionIcon } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCheck, IconCopy } from '@tabler/icons-react';
import { Select } from 'react-hook-form-mantine';
import { useTranslate } from '@tolgee/react';
import { BOT_TRIGGER_TYPES } from '@/constants';
import { useGeneralSettingsContext } from '@/pages/GeneralSetting/GeneralSettingsContext';
import { useSettingsContext } from '@/contexts/SettingsContext';

const useStyles = createStyles((theme) => ({
  input: {
    width: '100%',
    minWidth: rem(310),
    '& .mantine-Select-input': {
      fontSize: '14px',
    },
  },
  radioGroup: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
  },
  infor: {
    fontSize: rem(12),
    color: theme.colors.silverFox[8],
  },
  description: {
    fontSize: rem(12),
    color: theme.colors.silverFox[9],
    lineHeight: rem(18),
    marginBottom: rem(6),
  },
  box: {
    position: 'relative',
    height: rem(50),
    padding: `${rem(16)} ${rem(12)}`,
    width: '100%',
    fontSize: rem(12),
    borderRadius: rem(4),
    backgroundColor: theme.colors.silverFox[9],
    color: theme.colors.silverFox[0],
  },
  actionIcon: {
    position: 'absolute',
    backgroundColor: theme.colors.silverFox[0],
    right: rem(10),
    top: rem(10),
    width: rem(32),
    height: rem(32),
    '&:hover': {
      backgroundColor: theme.colors.decaMono[1],
    },
  },
  scriptText: {
    whiteSpace: 'pre',
    color: theme.colors.decaMono[1],
    fontSize: '12px',
  },
  customOption: {
    '&[data-checked]': {
      backgroundColor: theme.colors.navy[6],
      color: theme.colors.silverFox[0],
      fontWeight: 600,
      fontSize: rem(14),
    },
  },
}));

type TriggerContentsProps = {
  triggerValue: string;
};

const TriggerContents = ({ triggerValue }: TriggerContentsProps) => {
  const { classes } = useStyles();
  const { t } = useTranslate('generalSetting');
  const { currentChatboxId } = useSettingsContext();

  const onClickScript = `onclick="window.__DECA_CLIENT__${currentChatboxId}.chat.toggleChatWindow()"`;
  const buttonSample = `
  <button onclick="window.__DECA_CLIENT__${currentChatboxId}.chat.toggleChatWindow()">
    {buttonName}
  </button>`;

  if (triggerValue === BOT_TRIGGER_TYPES.CLICK_A_BUTTON) {
    return (
      <div>
        <div
          className={classes.description}
          dangerouslySetInnerHTML={{ __html: t('userClickAButtonDescription') }}
        />
        <Text className={classes.description}>{t('exampleText')}:</Text>
        <Text className={classes.description}>
          {buttonSample.replace('{buttonName}', t('openChatText'))}
        </Text>
        <Text className={classes.description} mb={rem(20)}>
          {t('ifOnClickEventNotSetText')}
        </Text>
        <Text c='silverFox.9' fz={16} fw='500' lh={rem(12)} mb={rem(12)}>
          {t('onClickEventLabel')}
        </Text>
        <div className={classes.box}>
          <CopyButton value={onClickScript} timeout={500}>
            {({ copied, copy }) => (
              <Tooltip label={copied ? t('copied') : t('copy')} withArrow position='right'>
                <ActionIcon className={classes.actionIcon} onClick={copy}>
                  {copied ? (
                    <IconCheck size={20} color='teal' />
                  ) : (
                    <IconCopy size={20} color='gray' />
                  )}
                </ActionIcon>
              </Tooltip>
            )}
          </CopyButton>
          <Text className={classes.scriptText}>{onClickScript}</Text>
        </div>
      </div>
    );
  }

  return null;
};

export default function BotTrigger() {
  const { classes } = useStyles();
  const { t } = useTranslate('generalSetting');

  const {
    form: { control, watch },
  } = useGeneralSettingsContext();

  const value = watch('botTrigger');

  return (
    <CustomCard title={t('botTrigger')}>
      <Stack gap={8}>
        <CustomLabel text={t('appearanceTimingLabel')} />
        <Select
          withCheckIcon={false}
          classNames={{ option: classes.customOption }}
          allowDeselect={false}
          className={classes.input}
          name='botTrigger'
          control={control}
          data={[
            { value: BOT_TRIGGER_TYPES.OPEN_PAGE, label: t('triggerUserOpenPageOption') },
            { value: BOT_TRIGGER_TYPES.CLICK_A_BUTTON, label: t('triggerUserClickAButtonOption') },
          ]}
        />
      </Stack>
      <TriggerContents triggerValue={value} />
    </CustomCard>
  );
}
