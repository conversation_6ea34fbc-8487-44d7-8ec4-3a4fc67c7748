import { Box, Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { CustomCard, CustomOption } from '@/components';
import { useGeneralSettingsContext } from '@/pages/GeneralSetting/GeneralSettingsContext';
import { CustomImage } from '@resola-ai/ui';

const useStyles = createStyles((theme) => ({
  option: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(4),
    alignItems: 'end',
  },
  dot: {
    width: rem(6),
    height: rem(6),
    borderRadius: '100%',
    backgroundColor: theme.colors.decaNavy[3],
  },
  hidden: {
    visibility: 'hidden',
  },
  customImage: {
    height: rem('49'),
  },
}));

const Dot = () => {
  const { classes } = useStyles();
  return <Box className={classes.dot} />;
};

export default function AutoOpen() {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('generalSetting');

  const { form } = useGeneralSettingsContext();

  const value = form.watch('autoOpen');

  return (
    <CustomCard title={t('autoOpenCardTitle')}>
      <Flex gap={rem(16)} w={'100%'}>
        <CustomOption
          title={t('autoOpenOptionTitle')}
          active={value}
          onClick={() => form.setValue('autoOpen', true)}
        >
          <Box className={classes.option}>
            <CustomImage url='images/cb-window.png' />
            <Dot />
          </Box>
        </CustomOption>
        <CustomOption
          title={t('launcherOnly')}
          active={!value}
          onClick={() => form.setValue('autoOpen', false)}
        >
          <Box className={classes.option}>
            <CustomImage
              url='images/cb-window.png'
              className={cx(classes.hidden, classes.customImage)}
            />
            <Dot />
          </Box>
        </CustomOption>
      </Flex>
    </CustomCard>
  );
}
