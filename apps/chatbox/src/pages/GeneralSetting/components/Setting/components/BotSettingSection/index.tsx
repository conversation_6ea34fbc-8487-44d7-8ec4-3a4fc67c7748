import { Flex, Stack, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { TextInput, Textarea } from 'react-hook-form-mantine';
import { CustomCard, CustomLabel, UploadImage } from '@/components';
import { BOX_NAME_MAX_LENGTH, BOX_NAME_MIN_LENGTH } from '@/constants';
import { useGeneralSettingsContext } from '@/pages/GeneralSetting/GeneralSettingsContext';
import { checkBoxValidation } from '@/utils';

const useStyles = createStyles((theme) => ({
  input: {
    width: rem(238),
  },
  button: {
    fontSize: rem(16),
    fontWeight: 500,
    color: theme.colors.silverFox[9],
    backgroundColor: theme.colors.silverFox[3],
    '&:hover': {
      backgroundColor: theme.colors.silverFox[4],
    },
  },
}));

export default function BotSetting() {
  const { classes } = useStyles();
  const { t } = useTranslate(['generalSetting', 'home']);
  const {
    form: { setValue, watch, control },
  } = useGeneralSettingsContext();

  const handleChangeImage = async (file: File | null) => {
    file && setValue('image', file);
  };
  const img = watch('image');

  return (
    <CustomCard title={t('botSetting')}>
      <Stack gap={8}>
        <CustomLabel text={t('botImage')} />
        <Flex gap={8} align={'center'}>
          <UploadImage
            defaultValue={'images/bot_avatar.png'}
            staticUrl={img as string}
            rounded
            onChange={(file) => handleChangeImage(file)}
          />
        </Flex>
      </Stack>
      <Stack gap={8}>
        <CustomLabel text={t('botName')} />
        <TextInput
          className={classes.input}
          name='botName'
          control={control}
          maxLength={BOX_NAME_MAX_LENGTH}
          rules={{
            validate: (value) =>
              checkBoxValidation(
                value?.toString(),
                t('nameMinMaxError', {
                  ns: 'home',
                  min: BOX_NAME_MIN_LENGTH,
                  max: BOX_NAME_MAX_LENGTH,
                })
              ),
          }}
        />
      </Stack>
      <Stack gap={8}>
        <CustomLabel text={t('description')} />
        <Textarea
          className={classes.input}
          name='botDescription'
          control={control}
          minRows={2}
          maxRows={4}
          maxLength={BOX_NAME_MAX_LENGTH}
          rules={{
            validate: (value) =>
              checkBoxValidation(
                value?.toString(),
                t('descriptionMinMaxError', {
                  ns: 'home',
                  min: BOX_NAME_MIN_LENGTH,
                  max: BOX_NAME_MAX_LENGTH,
                })
              ),
          }}
        />
      </Stack>
    </CustomCard>
  );
}
