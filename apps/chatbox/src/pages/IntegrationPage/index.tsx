import type React from 'react';
import { Container, Grid, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { SavePageSettingControl } from '@/components';
import { useRouteParams } from '@/hooks';
import { WidgetItem } from './components';
import { IntegrationContextProvider, useIntegrationContext } from '@/contexts/IntegrationContext';
import { useSettingsContext } from '@/contexts/SettingsContext';

const useStyles = createStyles(() => ({
  container: {
    padding: rem(12),
    margin: 0,
  },
}));

const DEFAULT_WIDGETS = [
  {
    name: 'livechat',
    image: 'images/logo2.png',
    type: 'livechat',
    description: 'livechatDesc',
  },
  {
    name: 'chatbot',
    image: 'images/logo2.png',
    type: 'chatbot',
    description: 'chatbotDesc',
  },
];

const WrapperIntegrationPage: React.FC<any> = () => {
  useRouteParams();
  const { t } = useTranslate(['integration', 'common']);
  const { classes } = useStyles();
  const { onSaveIntegration } = useSettingsContext();
  const { form } = useIntegrationContext();

  return (
    <Container fluid p={0} m={0} w={'100%'}>
      <SavePageSettingControl
        title={t('pageTitle')}
        buttonText={t('publish', { ns: 'common' })}
        form={form}
        onSave={onSaveIntegration}
      />
      <Grid gutter={rem(12)} className={classes.container}>
        {DEFAULT_WIDGETS.map((widget, index) => (
          <Grid.Col span={{ sm: 6 }} key={`${widget.type}-${index}`}>
            <WidgetItem widget={widget} />
          </Grid.Col>
        ))}
      </Grid>
    </Container>
  );
};

const IntegrationPage = () => {
  return (
    <IntegrationContextProvider>
      <WrapperIntegrationPage />
    </IntegrationContextProvider>
  );
};

export default IntegrationPage;
