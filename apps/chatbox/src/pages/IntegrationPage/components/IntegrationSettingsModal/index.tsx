import { memo } from 'react';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { SERVICE_TYPES } from '@/constants';
import { Modal, rem, Box } from '@mantine/core';
import { CustomGroupButton } from '@/components';
import LiveChatSettings from '../LiveChatSettings';
import ChatbotSettings from '../ChatbotSettings';

const useStyles = createStyles((theme) => ({
  modalHeader: {
    borderBottom: `1px solid ${theme.colors.decaLight[2]}`,
  },
  containerCustomClass: {
    justifyContent: 'flex-end',
  },
  buttonGroup: {
    paddingTop: rem(16),
  },
}));

type IntegrationSettingsModalProps = {
  opened: boolean;
  close: () => void;
  type: SERVICE_TYPES;
};

const IntegrationSettingsModal = ({ opened, close, type }: IntegrationSettingsModalProps) => {
  const { classes } = useStyles();
  const { t } = useTranslate('integration');

  const modalTitle =
    type === SERVICE_TYPES.LIVECHAT ? t('livechatSettings.title') : t('chatbotSettings.title');

  return (
    <Modal
      title={modalTitle}
      opened={opened}
      onClose={close}
      centered
      padding={rem(24)}
      size={'lg'}
      classNames={{
        header: classes.modalHeader,
      }}
    >
      {type === SERVICE_TYPES.LIVECHAT ? <LiveChatSettings /> : <ChatbotSettings />}
      <Box className={classes.buttonGroup}>
        <CustomGroupButton
          rejectTitle={t('cancel')}
          approveTitle={t('save')}
          onReject={close}
          onApprove={() => {
            close();
          }}
          size={'sm'}
          containerCustomClass={classes.containerCustomClass}
        />
      </Box>
    </Modal>
  );
};

export default memo(IntegrationSettingsModal);
