import React, { useC<PERSON>back, useEffect, useMemo } from 'react';
import { IconCheck, IconLink, IconSettings } from '@tabler/icons-react';
import { ActionIcon, Flex, Select, Stack, Switch, Text, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { CustomCard, DialogConfirmation } from '@/components';
import { SERVICE_TYPES } from '@/constants';
import { useIntegrationContext } from '@/contexts/IntegrationContext';
import { useSettingsContext } from '@/contexts/SettingsContext';
import type { ChatBotInfor, Widget } from '@/types';
import { useDisclosure } from '@mantine/hooks';
import { CustomButton, CustomImage } from '@resola-ai/ui';
import IntegrationSettingsModal from '../IntegrationSettingsModal';

const useStyles = createStyles((theme) => ({
  container: {
    padding: rem(32),
    height: '100%',
    justifyContent: 'flex-start',
    '& .mantine-Switch-root': {
      '&[data-checked]': {
        '& .mantine-Switch-track': {
          backgroundColor: theme.colors.navy[6],
          borderColor: theme.colors.navy[6],
        },
      },
    },
  },
  fullWidth: {
    width: '100%',
  },
  connectedButton: {
    height: 'auto',
    width: '100%',
    padding: 0,
    maxWidth: rem(152),
    '& .mantine-Button-section': {
      marginInline: '2px',
    },
    '[data-position="left"]': {
      marginInlineEnd: '6px',
    },
  },
  title: {
    color: theme.colors.silverFox[9],
    fontSize: rem(22),
    fontWeight: 700,
  },
  description: {
    color: theme.colors.silverFox[9],
    fontSize: rem(14),
    fontWeight: 400,
    whiteSpace: 'pre-line',
  },
  bold5: {
    fontWeight: 500,
  },
  customImage: {
    height: rem('40'),
  },
  bgGreen: {
    backgroundColor: `${theme.colors.green[5]} !important`,
    '&:hover': {
      backgroundColor: `${theme.colors.green[4]} !important`,
    },
  },
  customOption: {
    '&[data-checked]': {
      backgroundColor: theme.colors.navy[6],
      color: theme.colors.silverFox[0],
      fontWeight: 600,
      fontSize: rem(14),
    },
  },
}));

interface WidgetItemProps {
  widget: Widget;
}

interface SettingsIconProps {
  disabled: boolean;
  type: SERVICE_TYPES;
}

const SettingsIcon = ({ disabled, type }: SettingsIconProps) => {
  const [opened, { open, close }] = useDisclosure(false);

  return (
    <>
      <ActionIcon variant='transparent' color={'decaGrey.6'} onClick={open} disabled={disabled}>
        <IconSettings />
      </ActionIcon>
      <IntegrationSettingsModal opened={opened} close={close} type={type} />
    </>
  );
};

export default function WidgetItem({ widget }: WidgetItemProps) {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('integration');
  const [opened, { open, close }] = useDisclosure(false);
  const [active, setActive] = React.useState(true);
  const [chatbot, setChatbot] = React.useState<ChatBotInfor | null>(null);
  const { currentChatboxId, currentChatbox } = useSettingsContext();
  const { listChatbots, connectBot, disconnectBots, loading, form } = useIntegrationContext();

  const livechatConnected = form.watch('livechatConnected');

  const handleActiveBtn = (event: React.ChangeEvent<HTMLInputElement>) => {
    !event.target.checked ? open() : setActive(event.target.checked);
  };
  const handleSelect = useCallback(
    (value: string | null) => {
      if (value === null) return;

      setChatbot(listChatbots?.find((chatbot) => chatbot.id === value) ?? null);
    },
    [listChatbots]
  );

  const isConnected: boolean = useMemo(() => {
    if (!currentChatbox?.botConnected || !chatbot?.id) return false;
    return chatbot.id === currentChatbox.botConnected;
  }, [chatbot?.id, currentChatbox?.botConnected]);

  const handleConnect = useCallback(() => {
    const oldBot = listChatbots?.find((bot) => bot.id === currentChatbox?.botConnected);
    oldBot
      ? connectBot({ botId: oldBot.id, chatBoxId: currentChatboxId, newBotId: chatbot?.id })
      : connectBot({ botId: chatbot?.id, chatBoxId: currentChatboxId });
  }, [listChatbots, connectBot, currentChatboxId, chatbot?.id, currentChatbox?.botConnected]);

  useEffect(() => {
    const bot = listChatbots?.find((bot) => bot.id === chatbot?.id);
    if (bot) {
      setChatbot(bot);
    }
  }, [chatbot, listChatbots]);

  useEffect(() => {
    const bot = listChatbots?.find((bot) => bot.id === currentChatbox?.botConnected);
    if (bot) {
      setChatbot(bot);
    }
  }, [currentChatbox?.botConnected, listChatbots]);
  return (
    <CustomCard className={classes.container}>
      <Flex justify={'space-between'} className={classes.fullWidth}>
        <CustomImage
          url={widget?.image as string}
          alt={widget.name}
          className={classes.customImage}
        />
        <Flex align={'center'} gap={rem(10)}>
          {widget.type === SERVICE_TYPES.LIVECHAT ? (
            <>
              <SettingsIcon disabled={!livechatConnected} type={SERVICE_TYPES.LIVECHAT} />
              <Switch
                name='livechatConnected'
                checked={livechatConnected}
                onChange={(e) => form.setValue('livechatConnected', e.currentTarget.checked)}
              />
            </>
          ) : (
            <>
              <SettingsIcon disabled={!active} type={SERVICE_TYPES.CHATBOT} />
              <Switch checked={active} onChange={handleActiveBtn} />
            </>
          )}
        </Flex>
      </Flex>
      <Title order={5} className={classes.title}>
        {t(widget.name || '')}
      </Title>
      <Text className={classes.description}>{t(widget.description || '')}</Text>
      {widget.type !== SERVICE_TYPES.LIVECHAT && (
        <Stack gap={rem(4)} className={classes.fullWidth}>
          <Text className={cx(classes.description, classes.bold5)}>{t('chatbotName')}</Text>
          <Flex gap={rem(10)} justify={'space-between'} className={classes.fullWidth}>
            <Select
              withCheckIcon={false}
              classNames={{ option: classes.customOption }}
              allowDeselect={false}
              onChange={(value, _) => handleSelect(value)}
              className={classes.fullWidth}
              disabled={!active}
              data={
                listChatbots?.map((chatbot) => ({ value: chatbot.id, label: chatbot.name })) ?? []
              }
              value={chatbot?.id ?? ''}
            />
            <CustomButton
              colorScheme='primary'
              className={cx(classes.connectedButton, isConnected && classes.bgGreen)}
              disabled={!active || !chatbot || loading}
              onClick={isConnected ? undefined : handleConnect}
              leftIcon={isConnected ? <IconCheck width={rem(20)} /> : <IconLink width={rem(20)} />}
            >
              {isConnected ? t('connected', { ns: 'common' }) : t('connect', { ns: 'common' })}
            </CustomButton>
          </Flex>
        </Stack>
      )}
      <DialogConfirmation
        opened={opened}
        close={close}
        onSave={() => {
          disconnectBots();
          setChatbot(null);
          setActive(false);
        }}
      />
    </CustomCard>
  );
}
