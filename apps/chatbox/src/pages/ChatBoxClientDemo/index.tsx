import {
  DECA_CLIENT_BUNDLE_URL,
  DECA_CLIENT_DOM_ID_PREFIX,
  DECA_CLIENT_EMBED_SCRIPT_TEMPLATE,
} from '@/constants/embedScript';
import { Accordion, Box, Button, Container, Grid, Input, Textarea, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import React, { useCallback } from 'react';
import { syncEngineDataWithContext } from '@resola-ai/widget-engine';

const useStyles = createStyles(() => ({
  previewBox: {},
  chatboxContainer: {},
}));

const DEMO_ID = 'chatbox-client-demo';
const DEMO_JSON_CONFIG_URL =
  'https://cdn.deca-dev.com/chatbox/01haxd218s50f6yy4jf2f92fzf/01HPQZ98PRQ3XG2E9H4ZT1Y3TA.json';
const ChatBoxClientDemo: React.FC<any> = () => {
  const { classes } = useStyles();
  const [jsonConfigUrl, setJsonConfigUrl] = React.useState(DEMO_JSON_CONFIG_URL);
  const [bundleUrl, setBundleUrl] = React.useState(DECA_CLIENT_BUNDLE_URL);

  const [customEmbedScript, setCustomEmbedScript] = React.useState('');

  const updateHandler = useCallback(() => {
    try {
      const latestEmbedScript = syncEngineDataWithContext(DECA_CLIENT_EMBED_SCRIPT_TEMPLATE, {
        data: {
          bundleUrl: bundleUrl,
          domId: DEMO_ID,
          configUrl: jsonConfigUrl,
        },
      });
      const script = latestEmbedScript.replace('<script>', '').replace('</script>', '');
      eval(script);
    } catch (error) {
      console.log(error);
    }
  }, [jsonConfigUrl, bundleUrl]);

  const updateEmbedScriptHandler = useCallback(() => {
    try {
      const script = customEmbedScript.replace('<script>', '').replace('</script>', '');
      const theDomId = script.match(new RegExp(`${DECA_CLIENT_DOM_ID_PREFIX}[A-Z0-9]{26}`, 'g'));
      if (theDomId) {
        const latestEmbedScript = script.replace(theDomId[0], DEMO_ID);
        eval(latestEmbedScript);
      } else {
        eval(script);
      }
    } catch (error) {
      console.log(error);
    }
  }, [customEmbedScript]);

  return (
    <Container fluid p={0} m={0} w={'100%'}>
      <Grid gutter={0}>
        <Grid.Col span={{ md: 12, lg: 6 }} p={30} mt={10}>
          <Title order={1}>Chatbox Client Demo</Title>
          <Accordion defaultValue={'normal'} mt={50}>
            <Accordion.Item value='advance'>
              <Accordion.Control>Advanced mode</Accordion.Control>
              <Accordion.Panel>
                <Input.Wrapper
                  mt={20}
                  label='Enter JSON config URL'
                  description='Enter the JSON config URL to load the chatbox'
                >
                  <Input
                    value={jsonConfigUrl}
                    onChange={(event) => setJsonConfigUrl(event.currentTarget.value)}
                    placeholder='Enter JSON config URL'
                    radius='md'
                    mt={20}
                  />
                </Input.Wrapper>
                <Input.Wrapper
                  mt={20}
                  label='Enter Bundle URL'
                  description='Enter the Bundle URL to load the chatbox'
                >
                  <Input
                    value={bundleUrl}
                    onChange={(event) => setBundleUrl(event.currentTarget.value)}
                    placeholder='Enter Bundle URL'
                    radius='md'
                    mt={20}
                  />
                </Input.Wrapper>
                <Button mt={20} onClick={updateHandler}>
                  Render Demo
                </Button>
              </Accordion.Panel>
            </Accordion.Item>
            <Accordion.Item value='normal'>
              <Accordion.Control>Normal mode</Accordion.Control>
              <Accordion.Panel>
                <Input.Wrapper
                  mt={20}
                  label='Custom Embed Script'
                  description='Enter the custom embed script to load the chatbox'
                >
                  <Textarea
                    value={customEmbedScript}
                    onChange={(event) => setCustomEmbedScript(event.currentTarget.value)}
                    placeholder='Enter Custom Embed Script'
                    radius='md'
                    mt={20}
                    autosize
                  />
                </Input.Wrapper>
                <Button mt={20} onClick={updateEmbedScriptHandler}>
                  Render Demo
                </Button>
              </Accordion.Panel>
            </Accordion.Item>
          </Accordion>
        </Grid.Col>
        <Grid.Col span={{ md: 12, lg: 6 }}>
          <Box className={classes.previewBox}>
            <div className={classes.chatboxContainer} id={DEMO_ID}></div>
          </Box>
        </Grid.Col>
      </Grid>
    </Container>
  );
};

export default ChatBoxClientDemo;
