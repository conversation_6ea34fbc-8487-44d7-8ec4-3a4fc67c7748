import { useMemo, useState } from 'react';
import { Box, Flex, Modal, Text, TextInput, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useSettingsContext } from '@/contexts/SettingsContext';
import { CustomButton } from '@resola-ai/ui';

const useStyles = createStyles(() => ({
  modal: {
    '.mantine-Modal-title': {
      fontWeight: 500,
      fontSize: rem(20),
    },
  },
  breakWord: {
    wordWrap: 'break-word',
    overflowWrap: 'break-word',
    whiteSpace: 'break-spaces',
  },
}));

const DeleteModal = () => {
  const { handleDelete, clickedChatbox, delOpened, delClose } = useSettingsContext();
  const { t } = useTranslate('home');
  const { classes } = useStyles();
  const [value, setValue] = useState('');

  const name = clickedChatbox?.title || clickedChatbox?.config?.name || '';

  const handleClose = () => {
    delClose();
    setValue('');
  };

  const handleDeleteBox = async () => {
    await handleDelete();
    handleClose();
  };

  const disabled = useMemo(() => {
    return !value || value.trim() !== name.trim();
  }, [value, name]);

  return (
    <Modal
      opened={delOpened}
      onClose={handleClose}
      title={t('deleteChatbox')}
      centered
      w={rem(440)}
      className={classes.modal}
    >
      <div
        className={classes.breakWord}
        dangerouslySetInnerHTML={{
          __html: t('deleteChatboxDesc', { name }),
        }}
      />
      <Box mb={rem(20)} mt={rem(10)}>
        <Text mb={rem(5)}>{t('reEnterName')}</Text>
        <TextInput
          placeholder={t('reEnterNamePlaceHolder')}
          value={value}
          onChange={(e) => setValue(e.target.value)}
        />
      </Box>
      <Flex gap={rem(10)} my={rem(20)} justify={'flex-end'}>
        <CustomButton colorScheme={'tertiary'} size={'sm'} onClick={handleClose}>
          {t('cancel')}
        </CustomButton>
        <CustomButton
          colorScheme={'negative'}
          size={'sm'}
          type='submit'
          disabled={disabled}
          onClick={handleDeleteBox}
        >
          {t('deleteForever')}
        </CustomButton>
      </Flex>
    </Modal>
  );
};

export default DeleteModal;
