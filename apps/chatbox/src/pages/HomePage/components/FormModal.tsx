import { useEffect, useState } from 'react';
import { Box, Flex, Modal, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useForm } from 'react-hook-form';
import { useTranslate } from '@tolgee/react';
import { TextInput, Textarea } from 'react-hook-form-mantine';
import { UploadImage } from '@/components';
import { useSettingsContext } from '@/contexts/SettingsContext';
import { BOX_NAME_MAX_LENGTH, BOX_NAME_MIN_LENGTH } from '@/constants';
import { checkBoxValidation } from '@/utils';
import { CustomButton } from '@resola-ai/ui';

const useStyles = createStyles((theme) => ({
  modal: {
    '.mantine-Modal-title': {
      fontWeight: 500,
      fontSize: rem(20),
    },
  },
}));

const FormModal = () => {
  const { handleSavingBox, clickedChatbox, formOpened, formClose } = useSettingsContext();
  const { t } = useTranslate('home');
  const { classes } = useStyles();
  const [file, setFile] = useState<File | null>(null);
  const [saving, setSaving] = useState(false);
  const form = useForm({
    defaultValues: {
      title: '',
      desc: '',
    },
  });

  useEffect(() => {
    form.reset({
      title: clickedChatbox?.title || '',
      desc: clickedChatbox?.desc || '',
    });
  }, [clickedChatbox]);

  const handleClose = () => {
    formClose();
    setFile(null);
    form.reset();
    setSaving(false);
  };

  const handleSave = async (data) => {
    setSaving(true);
    await handleSavingBox(data, file);
    handleClose();
  };

  return (
    <Modal
      opened={formOpened}
      onClose={handleClose}
      title={clickedChatbox ? t('editChatbox') : t('newChatbox')}
      centered
      w={rem(440)}
      className={classes.modal}
    >
      <form onSubmit={form.handleSubmit(handleSave)} style={{ padding: rem(10) }}>
        <Box mb={rem(20)} mt={rem(10)}>
          <Text mb={rem(20)}>{t('boxImage')}</Text>
          <UploadImage
            rounded
            defaultValue={'images/bot_avatar.png'}
            staticUrl={clickedChatbox?.config?.image}
            onChange={(file) => setFile(file)}
          />
        </Box>
        <Box mb={rem(20)} mt={rem(10)}>
          <TextInput
            label={t('name')}
            withAsterisk
            type='text'
            placeholder={t('namePlaceholder')}
            name='title'
            control={form.control}
            rules={{
              validate: (value) =>
                checkBoxValidation(
                  value,
                  t('nameMinMaxError', {
                    min: BOX_NAME_MIN_LENGTH,
                    max: BOX_NAME_MAX_LENGTH,
                  })
                ),
            }}
          />
        </Box>
        <Box>
          <Textarea
            label={t('description')}
            withAsterisk
            name='desc'
            control={form.control}
            placeholder={t('descPlaceholder')}
            rules={{
              validate: (value) =>
                checkBoxValidation(
                  value,
                  t('descriptionMinMaxError', {
                    min: BOX_NAME_MIN_LENGTH,
                    max: BOX_NAME_MAX_LENGTH,
                  })
                ),
            }}
          />
        </Box>
        <Flex gap={rem(10)} my={rem(20)} justify={'flex-end'}>
          <CustomButton colorScheme={'tertiary'} size={'sm'} onClick={handleClose}>
            {t('cancel')}
          </CustomButton>
          <CustomButton colorScheme={'primary'} size={'sm'} type='submit' disabled={saving}>
            {clickedChatbox ? t('save') : t('create')}
          </CustomButton>
        </Flex>
      </form>
    </Modal>
  );
};

export default FormModal;
