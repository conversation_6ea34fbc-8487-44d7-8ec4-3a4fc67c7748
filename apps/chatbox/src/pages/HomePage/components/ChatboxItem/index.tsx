import { Box, Divider, Flex, Menu, Stack, Text, Title, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconDotsVertical, IconPlugConnected } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { CustomBadge } from '@/components';
import { useSettingsContext } from '@/contexts/SettingsContext';
import type { ChatBox } from '@/types';
import { CustomImage } from '@resola-ai/ui';

const useStyles = createStyles((theme) => ({
  container: {
    height: '100%',
    padding: rem(12),
    borderRadius: rem(12),
    border: `1px solid ${theme.colors.silverFox[5]}`,
    backgroundColor: theme.colors.silverFox[1],
    cursor: 'pointer',
    color: theme.colors.silverFox[9],
    '&:hover': {
      border: `1px solid ${theme.colors.pervenche[5]}`,
      backgroundColor: theme.colors.pervenche[0],
    },
  },
  wrapper: {
    gap: rem(12),
  },
  image: {
    borderRadius: rem(8),
    height: rem(195),
    objectFit: 'cover',
  },
  content: {
    maxWidth: '90%',
  },
  title: {
    fontSize: rem(14),
    fontWeight: 700,
    wordWrap: 'break-word',
    overflowWrap: 'break-word',
  },
  description: {
    fontSize: rem(12),
    fontWeight: 400,
    wordWrap: 'break-word',
    overflowWrap: 'break-word',
    whiteSpace: 'pre-wrap',
  },
  imageContainer: {
    width: '100%',
    height: rem(195),
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.decaViolet[1],
    borderRadius: rem(8),
  },
  defaultImage: {
    width: rem(148),
    height: rem(148),
  },
}));

interface ChatboxItemProps {
  item?: ChatBox;
  onClick: () => void;
}

const DEFAULT_IMAGE = 'svg/default-bot.svg';

export default function ChatboxItem({ item, onClick }: ChatboxItemProps) {
  const { classes } = useStyles();
  const { t } = useTranslate('home');
  const { setClickChatbox, delOpen, formOpen } = useSettingsContext();
  const config = item?.config;

  const onClickEdit = (e) => {
    setClickChatbox(item);
    formOpen();
  };
  const onClickDelete = (e) => {
    setClickChatbox(item);
    delOpen();
  };

  return (
    <Box className={classes.container} onClick={onClick}>
      <Stack className={classes.wrapper}>
        {config?.image ? (
          <img className={classes.image} src={config?.image} alt={config?.image} />
        ) : (
          <Box className={classes.imageContainer}>
            <CustomImage url={DEFAULT_IMAGE} className={classes.defaultImage} />
          </Box>
        )}
        <Flex justify={'space-between'}>
          <Flex direction={'column'} className={classes.content}>
            <Title lineClamp={1} order={5} className={classes.title}>
              {item?.title || config?.name}
            </Title>
            <Text lineClamp={2} className={classes.description}>
              {item?.desc || config?.description}
            </Text>
          </Flex>

          <Menu position='bottom-start' trigger='click'>
            <Menu.Target>
              <IconDotsVertical onClick={(e) => e.stopPropagation()} />
            </Menu.Target>

            <Menu.Dropdown onClick={(e) => e.stopPropagation()}>
              <Menu.Item onClick={onClickEdit}>{t('editChatbox')}</Menu.Item>
              <Divider />
              <Menu.Item onClick={onClickDelete}>{t('deleteChatbox')}</Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Flex>
        {config?.widgets && (
          <Flex wrap={'wrap'} gap={rem(4)}>
            {config.widgets?.map((widget, index) => (
              <CustomBadge
                key={`${widget.type}-${index}`}
                leftSection={<IconPlugConnected size='1rem' />}
                variant={widget.variant ?? 'default'}
              >
                {widget.name}
              </CustomBadge>
            ))}
          </Flex>
        )}
      </Stack>
    </Box>
  );
}
