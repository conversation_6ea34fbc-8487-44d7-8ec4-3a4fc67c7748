import { AppConfig } from '@/configs';
import { useSettingsContext } from '@/contexts/SettingsContext';
import { ChatboxAPI } from '@/services/api';
import type { ChatBox } from '@/types';
import { Box, Grid, rem } from '@mantine/core';
import { useDebouncedState } from '@mantine/hooks';
import { useEffect, useMemo } from 'react';
import { useTranslate } from '@tolgee/react';
import { useNavigate } from 'react-router-dom';
import useSWR from 'swr';
import { NoData, NotFound, SearchInput } from '@resola-ai/ui';
import { usePathParams } from '@resola-ai/ui/hooks';
import ChatboxItem from '../ChatboxItem';

const BASE_PATH = AppConfig.BASE_PATH;
const DEBOUNCE = 500;

export default function ChatboxList() {
  const navigate = useNavigate();
  const pathParams = usePathParams();
  const { t } = useTranslate('home');

  const [searchValue, setSearchValue] = useDebouncedState('', DEBOUNCE);

  const {
    deletedChatbox,
    addedChatbox,
    updatedChatbox,
    setCurrentChatboxId,
    handleResetAddedChatbox,
    setChatboxList,
  } = useSettingsContext();

  const { data, mutate } = useSWR('/settings/all', async () => {
    return await ChatboxAPI.getAll();
  });

  useEffect(() => {
    setChatboxList(data?.data || []);
  }, [data]);

  useEffect(() => {
    if (addedChatbox || updatedChatbox || deletedChatbox) {
      mutate();
    }
  }, [addedChatbox, updatedChatbox, deletedChatbox, mutate]);

  useEffect(() => {
    if (addedChatbox?.id) {
      handleGoToChatboxDetail(addedChatbox);
      handleResetAddedChatbox();
    }
  }, [addedChatbox]);

  const gotoDetail = (chatboxId = 'create') => {
    const rawPath = `${BASE_PATH}${chatboxId}/content-setting/`;
    const fullPath = pathParams.createPathWithLngParam(rawPath);
    navigate(fullPath);
  };

  const handleGoToChatboxDetail = (chatbox: ChatBox) => {
    setCurrentChatboxId(chatbox.id as string);
    gotoDetail(chatbox.id);
  };

  const chatboxes = useMemo(() => {
    let chatboxes = data?.data;

    if (searchValue) {
      chatboxes = chatboxes?.filter((item: ChatBox) => {
        return item.title?.toLowerCase().includes(searchValue.toLowerCase());
      });
    }

    return chatboxes?.sort((a: ChatBox, b: ChatBox) => {
      return b.created.localeCompare(a.created);
    });
  }, [data, searchValue]);

  return (
    <>
      <Box mb={rem(24)}>
        <SearchInput placeholder={t('searchChatbox')} onChange={setSearchValue} />
      </Box>

      {chatboxes?.length === 0 ? (
        data?.data?.length === 0 ? (
          <NoData label={t('noChatboxMsg')} />
        ) : (
          <NotFound title={t('noResultFound')} description={t('noResultFoundDesc')} />
        )
      ) : null}

      <Grid pb={rem(24)}>
        {chatboxes?.map((item, index) => (
          <Grid.Col span={{ xs: 6, sm: 4, md: 3 }} key={`${item?.id}-${index}`}>
            <ChatboxItem item={item} onClick={() => handleGoToChatboxDetail(item)} />
          </Grid.Col>
        ))}
      </Grid>
    </>
  );
}
