import type React from 'react';
import { Container, Grid } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
// import styled from '@emotion/styled';
import { ChatBoxPreview, SavePageSettingControl } from '@/components';
import { useSettingsContext } from '@/contexts/SettingsContext';
import { useRouteParams } from '@/hooks';
import {
  StylingSettingsContextProvider,
  useStylingSettingsContext,
} from './StylingSettingsContext';
import { Setting } from './components';
import { ContentSettingsContextProvider } from '../ContentSetting/ContentSettingsContext';

// const Form = styled.form`
//   width: 100%;
// `;

const useStyles = createStyles((_) => ({
  formContainer: {
    width: '100%',
  },
}));

const StylingSetting: React.FC<any> = () => {
  useRouteParams();
  const { classes } = useStyles();
  const { t } = useTranslate(['stylingSetting', 'common']);
  const { onSaveStyling } = useSettingsContext();
  const { form } = useStylingSettingsContext();

  return (
    <Container fluid p={0} m={0} w={'100%'}>
      <SavePageSettingControl
        title={t('pageTitle')}
        buttonText={t('publish', { ns: 'common' })}
        form={form}
        onSave={onSaveStyling}
      />
      <Grid gutter={0}>
        <Grid.Col span={{ md: 12, lg: 6 }}>
          <form className={classes.formContainer}>
            <Setting />
          </form>
        </Grid.Col>
        <Grid.Col span={{ md: 12, lg: 6 }}>
          <ChatBoxPreview settings={form.watch()} />
        </Grid.Col>
      </Grid>
    </Container>
  );
};

const StylingSettingPage = () => {
  return (
    <StylingSettingsContextProvider>
      <ContentSettingsContextProvider>
        <StylingSetting />
      </ContentSettingsContextProvider>
    </StylingSettingsContextProvider>
  );
};

export default StylingSettingPage;
