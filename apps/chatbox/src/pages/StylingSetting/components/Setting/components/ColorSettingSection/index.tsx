import { CustomCard } from '@/components';
import { Tabs, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import CustomTheme from './CustomTheme';
import ChatboxTheme from './ChatboxTheme';

export default function ColorSettingSection() {
  const { t } = useTranslate('stylingSetting');

  return (
    <CustomCard title={t('colorSettingTitle')}>
      <Tabs
        defaultValue='chatboxTheme'
        styles={(theme) => ({
          root: {
            width: '100%',
          },
          tab: {
            padding: `${rem(10)} ${rem(12)}`,
            fontWeight: 500,
            color: theme.colors.silverFox[9],
            fontSize: rem(14),
            '&:hover': {
              backgroundColor: 'transparent',
            },
            '&[data-active]': {
              backgroundColor: 'transparent',
              borderColor: theme.colors.navy[4],
              color: theme.colors.navy[4],
              '&:hover': {
                borderColor: theme.colors.navy[4],
              },
            },
          },
        })}
      >
        <Tabs.List>
          <Tabs.Tab value='chatboxTheme'>{t('chatboxTheme')}</Tabs.Tab>
          <Tabs.Tab value='customTheme'>{t('customTheme')}</Tabs.Tab>
        </Tabs.List>
        <Tabs.Panel value='chatboxTheme' pt={12}>
          <ChatboxTheme />
        </Tabs.Panel>
        <Tabs.Panel value='customTheme' pt={12}>
          <CustomTheme />
        </Tabs.Panel>
      </Tabs>
    </CustomCard>
  );
}
