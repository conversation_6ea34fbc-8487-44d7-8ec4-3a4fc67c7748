import { Flex, Group, Stack } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { Radio, Switch } from 'react-hook-form-mantine';
import { ColorPicker, CustomCard, CustomLabel, UploadImage } from '@/components';
import { useStylingSettingsContext } from '../../../../StylingSettingsContext';

export default function StylingHomeSection() {
  const { t } = useTranslate('stylingSetting');
  const {
    form: { watch, setValue, control },
  } = useStylingSettingsContext();
  const values = watch('styling.home');

  const handleChangeImage = async (file: File | null) => {
    setValue('styling.home.image', file);
  };

  const brandValue = watch('brandColor');

  return (
    <CustomCard title={t('stylingHome')}>
      <Radio.Group name='styling.home.type' control={control}>
        <Group style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
          <Radio.Item value={'color'} label={<CustomLabel text={t('solid')} />} />

          {values?.type === 'color' && (
            <Stack gap={8}>
              <CustomLabel text={t('bgColor')} />
              <ColorPicker
                defaultValue={brandValue?.background}
                onChange={(value) => setValue('brandColor.background', value)}
              />
            </Stack>
          )}

          <Radio.Item value={'gradient'} label={<CustomLabel text={t('gradient')} />} />

          {values?.type === 'gradient' && (
            <Flex gap={16}>
              <Stack gap={8}>
                <CustomLabel text={t('topLeft')} />
                <ColorPicker
                  defaultValue={values?.gradientTl}
                  onChange={(value: string) => setValue('styling.home.gradientTl', value)}
                />
              </Stack>
              <Stack gap={8}>
                <CustomLabel text={t('bottomRight')} />
                <ColorPicker
                  defaultValue={values?.gradientBr}
                  onChange={(value: string) => setValue('styling.home.gradientBr', value)}
                />
              </Stack>
            </Flex>
          )}

          <Radio.Item value={'image'} label={<CustomLabel text={t('image')} />} />

          {values?.type === 'image' && (
            <UploadImage
              onChange={(file) => handleChangeImage(file)}
              staticUrl={values?.image as string}
              rounded
            />
          )}
        </Group>
      </Radio.Group>

      <Switch
        name='styling.home.fadeBg'
        control={control}
        label={<CustomLabel text={t('fadeBgToWhite')} />}
      />
    </CustomCard>
  );
}
