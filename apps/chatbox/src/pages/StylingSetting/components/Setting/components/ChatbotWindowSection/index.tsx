import { Box, Flex, Stack, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { Slider } from 'react-hook-form-mantine';
import {
  CHATBOX_SIZE_HEIGHT,
  CHATBOX_SIZE_HEIGHT_MAX,
  CHATBOX_SIZE_HEIGHT_MIN,
  CHATBOX_SIZE_STEP,
  CHATBOX_SIZE_WIDTH,
  CHATBOX_SIZE_WIDTH_MAX,
  CHATBOX_SIZE_WIDTH_MIN,
} from '@/configs';
import { CustomCard, CustomLabel, CustomOption } from '@/components';
import { CustomImage } from '@resola-ai/ui';
import { useStylingSettingsContext } from '../../../../StylingSettingsContext';
import { CHATBOX_SIZE } from '@/constants';

const useStyles = createStyles((theme) => ({
  option: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(4),
    alignItems: 'end',
  },
  dot: {
    width: rem(6),
    height: rem(6),
    borderRadius: '100%',
    backgroundColor: theme.colors.decaNavy[3],
  },
  customImage: {
    height: rem('49'),
  },
  sliderRoot: {
    '& .mantine-Slider-root': {
      width: '100%',
    },
    '& .mantine-Slider-label': {
      fontSize: '12px',
      padding: '4px',
    },
  },
}));

const Dot = () => {
  const { classes } = useStyles();
  return <Box className={classes.dot} />;
};

export default function ChatbotWindowSection() {
  const { t } = useTranslate('stylingSetting');
  const { classes } = useStyles();
  const {
    form: { watch, setValue, control },
  } = useStylingSettingsContext();
  const values = watch();

  return (
    <CustomCard title={t('chatboxWindow')}>
      <Flex gap={rem(16)} w={'100%'}>
        <CustomOption
          title={t('customSize')}
          active={values?.size === CHATBOX_SIZE.CUSTOM}
          onClick={() => setValue('size', CHATBOX_SIZE.CUSTOM)}
        >
          <Box className={classes.option}>
            <CustomImage url='images/cb-window.png' />
            <Dot />
          </Box>
        </CustomOption>
        <CustomOption
          title={t('normalSize')}
          active={values?.size === CHATBOX_SIZE.NORMAL}
          onClick={() => {
            setValue('size', CHATBOX_SIZE.NORMAL);
            setValue('width', CHATBOX_SIZE_WIDTH);
            setValue('height', CHATBOX_SIZE_HEIGHT);
          }}
        >
          <Box className={classes.option}>
            <CustomImage url='images/cb-window.png' className={classes.customImage} />
            <Dot />
          </Box>
        </CustomOption>
      </Flex>

      <Stack sx={{ width: '100%' }} gap={4}>
        <CustomLabel text={t('width (px)')} bold />
        <Slider
          className={classes.sliderRoot}
          step={CHATBOX_SIZE_STEP}
          min={CHATBOX_SIZE_WIDTH_MIN}
          max={CHATBOX_SIZE_WIDTH_MAX}
          disabled={values?.size === CHATBOX_SIZE.NORMAL}
          labelAlwaysOn
          control={control}
          name='width'
        />
      </Stack>

      <Stack sx={{ width: '100%' }} gap={4}>
        <CustomLabel text={t('height(px)')} bold />
        <Slider
          className={classes.sliderRoot}
          step={CHATBOX_SIZE_STEP}
          min={CHATBOX_SIZE_HEIGHT_MIN}
          max={CHATBOX_SIZE_HEIGHT_MAX}
          disabled={values?.size === CHATBOX_SIZE.NORMAL}
          labelAlwaysOn
          control={control}
          name='height'
        />
      </Stack>
    </CustomCard>
  );
}
