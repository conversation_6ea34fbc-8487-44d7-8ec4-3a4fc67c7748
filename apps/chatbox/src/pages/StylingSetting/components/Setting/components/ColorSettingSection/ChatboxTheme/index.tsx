import { Container, rem, Flex, useMantineTheme, type MantineTheme } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { useStylingSettingsContext } from '@/pages/StylingSetting/StylingSettingsContext';
import { darkColorTheme, lightColorTheme } from '@/constants';
import type { ThemeColorSetting } from '@/types';
import styled from '@emotion/styled';

const ThemePickerContainer = styled.div<{
  mainColor?: string;
  secondaryColor?: string;
  isActive?: boolean;
  mantineTheme: MantineTheme;
}>((props) => ({
  border: props.isActive ? `${rem(2)} solid ${props.mantineTheme.colors.navy[4]}` : 'none',
  transform: props.isActive ? 'scale(1.2)' : 'scale(1)',
  cursor: 'pointer',
  width: '32px',
  height: '32px',
  borderRadius: '50%',
  background: `Linear-gradient(-45deg, ${props.secondaryColor}, ${props.secondaryColor} 49%, ${props.secondaryColor} 49%, ${props.mainColor} 51%, ${props.mainColor} 51%)`,
  '&:hover': {
    transform: 'scale(1.2)',
    transition: 'transform 0.3s',
  },
}));

export default function ChatboxTheme() {
  const { t } = useTranslate('stylingSetting');
  const {
    form: { watch, setValue },
  } = useStylingSettingsContext();
  const mantineTheme = useMantineTheme();
  const themeColor = watch('themeColor');

  const handleSelectTheme = (theme: ThemeColorSetting) => {
    const { buttonBackgroundColor, buttonText, welcomeMessageColor, actionColor, backgroundColor } =
      theme.value;
    setValue('colorSettings.buttonBackground', buttonBackgroundColor);
    setValue('colorSettings.buttonText', buttonText);
    setValue('colorSettings.welcomeMessageColor', welcomeMessageColor);
    setValue('brandColor.action', actionColor);
    setValue('brandColor.background', backgroundColor);
    setValue('themeColor', theme.id);
  };

  return (
    <Container fluid p={0} m={0} w={'100%'}>
      <Flex gap={10} my={16}>
        {darkColorTheme.map((theme, index) => (
          <ThemePickerContainer
            key={theme.id}
            mainColor={theme.label.mainColor}
            secondaryColor={theme.label.secondaryColor}
            isActive={themeColor === theme.id}
            mantineTheme={mantineTheme}
            onClick={() => handleSelectTheme(theme)}
          />
        ))}
      </Flex>
      <Flex gap={10}>
        {lightColorTheme.map((theme, index) => (
          <ThemePickerContainer
            key={theme.id}
            mainColor={theme.label.mainColor}
            secondaryColor={theme.label.secondaryColor}
            isActive={themeColor === theme.id}
            mantineTheme={mantineTheme}
            onClick={() => handleSelectTheme(theme)}
          />
        ))}
      </Flex>
    </Container>
  );
}
