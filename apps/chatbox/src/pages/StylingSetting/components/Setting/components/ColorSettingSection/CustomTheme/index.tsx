import { ColorPicker, CustomLabel } from '@/components';
import { Stack, Container, rem, Switch } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { useStylingSettingsContext } from '@/pages/StylingSetting/StylingSettingsContext';

export default function CustomTheme() {
  const { t } = useTranslate('stylingSetting');
  const {
    form: { watch, setValue },
  } = useStylingSettingsContext();
  const brandValue = watch('brandColor');
  const colorSettingValue = watch('colorSettings');

  return (
    <Container fluid p={0} m={0} w={'100%'}>
      <Stack gap={8} my={rem(12)}>
        <CustomLabel text={t('buttonBackgroundColor')} />
        <ColorPicker
          defaultValue={colorSettingValue?.buttonBackground}
          onChange={(value) => setValue('colorSettings.buttonBackground', value)}
        />
      </Stack>
      <Stack gap={8} mb={rem(12)}>
        <CustomLabel text={t('buttonTextColor')} />
        <ColorPicker
          defaultValue={colorSettingValue?.buttonText}
          onChange={(value) => setValue('colorSettings.buttonText', value)}
        />
      </Stack>
      <Stack gap={8} mb={rem(12)}>
        <CustomLabel text={t('actionColor')} />
        <ColorPicker
          defaultValue={brandValue?.action}
          onChange={(value) => setValue('brandColor.action', value)}
        />
      </Stack>
      <Stack gap={8} mb={rem(12)}>
        <CustomLabel text={t('welcomeMessageColorLabel')} />
        <ColorPicker
          defaultValue={colorSettingValue?.welcomeMessageColor}
          onChange={(value) => setValue('colorSettings.welcomeMessageColor', value)}
        />
      </Stack>
      <Stack gap={8}>
        <Switch
          styles={{
            label: {
              fontSize: '12px',
            },
          }}
          label={t('actionColorAsHyperlinkLabel')}
          checked={brandValue?.useActionColorAsHyperlink}
          onChange={(event) =>
            setValue('brandColor.useActionColorAsHyperlink', event.currentTarget.checked)
          }
        />
      </Stack>
    </Container>
  );
}
