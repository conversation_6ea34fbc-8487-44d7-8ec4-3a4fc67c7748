import { Flex, Stack, rem, NumberInput, Grid } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { Select, Switch, Slider } from 'react-hook-form-mantine';
import { CustomCard, CustomLabel, UploadImage, CustomOption } from '@/components';
import { useStylingSettingsContext } from '../../../../StylingSettingsContext';
import {
  DEFAULT_LAUNCHER_IMAGE,
  LAUNCHER_SIZE_MAX,
  LAUNCHER_SIZE_STEP,
  LAUNCHER_SIZE_MIN,
  LAUNCHER_STYLE,
} from '@/constants';
import { CustomImage } from '@resola-ai/ui';

const useStyles = createStyles((theme) => ({
  launcherStyleContainer: {
    width: '100%',
  },
  input: {
    fontSize: rem(14),
  },
  customOption: {
    '&[data-checked]': {
      backgroundColor: theme.colors.navy[6],
      color: theme.colors.silverFox[0],
      fontWeight: 600,
      fontSize: rem(14),
    },
  },
  sliderRoot: {
    '& .mantine-Slider-root': {
      width: '100%',
    },
    '& .mantine-Slider-label': {
      fontSize: '12px',
      padding: '4px',
    },
  },
}));

const DEFAULT_SPACING = 20;

export default function LauncherSection() {
  const { classes } = useStyles();
  const { t } = useTranslate('stylingSetting');
  const {
    form: { watch, setValue, control },
  } = useStylingSettingsContext();

  const values = watch('launcher');

  const handleChangeImage = async (file: File | null) => {
    setValue('launcher.image', file);
  };

  return (
    <CustomCard title={t('launcher')}>
      <CustomLabel text={t('customLauncher')} />
      <CustomCard title={t('launcherStyle')} className={classes.launcherStyleContainer}>
        <Flex gap={rem(16)} w={'100%'}>
          <CustomOption
            title={t('bubble')}
            active={values?.shape === LAUNCHER_STYLE.BUBBLE}
            onClick={() => {
              setValue('launcher.shape', LAUNCHER_STYLE.BUBBLE);
            }}
          >
            <CustomImage url='images/bubble.png' />
          </CustomOption>
          <CustomOption
            title={t('custom')}
            active={values?.shape === LAUNCHER_STYLE.CUSTOM}
            onClick={() => {
              setValue('launcher.shape', LAUNCHER_STYLE.CUSTOM);
            }}
          >
            <CustomImage url='images/rectangle.png' />
          </CustomOption>
        </Flex>
      </CustomCard>
      <UploadImage
        circlePreview
        onChange={(file) => handleChangeImage(file)}
        defaultValue={values?.defaultImage || DEFAULT_LAUNCHER_IMAGE}
        staticUrl={values?.image as string}
        smallSize
        isLauncherSection
        useChatboxCDNUrl
      />
      <Stack sx={{ width: '100%' }} gap={8}>
        <CustomLabel text={t('launcherSize')} bold />
        <Slider
          className={classes.sliderRoot}
          defaultValue={LAUNCHER_SIZE_MIN}
          step={LAUNCHER_SIZE_STEP}
          min={LAUNCHER_SIZE_MIN}
          max={LAUNCHER_SIZE_MAX}
          labelAlwaysOn
          control={control}
          name='launcher.size'
        />
      </Stack>
      <Grid gutter={'lg'} w={'100%'}>
        <Grid.Col span={{ xl: 4, md: 12 }}>
          <CustomLabel text={t('launcherPos')} bold />
          <Select
            withCheckIcon={false}
            classNames={{
              input: classes.input,
              option: classes.customOption,
            }}
            allowDeselect={false}
            name='launcher.position'
            control={control}
            data={[
              { value: 'right', label: t('right') },
              { value: 'left', label: t('left') },
            ]}
          />
        </Grid.Col>
        <Grid.Col span={{ xl: 4, md: 12 }}>
          <CustomLabel text={t('sideSpacingPx')} bold />
          <NumberInput
            classNames={{ input: classes.input }}
            min={0}
            max={200}
            value={+(values?.sideSpacing ?? DEFAULT_SPACING)}
            onChange={(value) => setValue('launcher.sideSpacing', +value)}
            hideControls
          />
        </Grid.Col>
        <Grid.Col span={{ xl: 4, md: 12 }}>
          <CustomLabel text={t('bottomSpacingPx')} bold />
          <NumberInput
            classNames={{ input: classes.input }}
            min={0}
            max={100}
            value={+(values?.bottomSpacing ?? DEFAULT_SPACING)}
            onChange={(value) => setValue('launcher.bottomSpacing', +value)}
            hideControls
          />
        </Grid.Col>
      </Grid>
      <Switch
        label={<CustomLabel text={t('showingCloseButton')} />}
        name='launcher.showCloseButton'
        control={control}
      />
      <Switch
        label={<CustomLabel text={t('hideLauncherWhenOpenChatbox')} />}
        name='launcher.hideLauncherWhenOpenChatbox'
        control={control}
      />
    </CustomCard>
  );
}
