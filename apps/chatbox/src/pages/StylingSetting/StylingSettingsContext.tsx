import type React from 'react';
import { createContext, useContext, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { CONTENT_DISPLAY, STYLING_SETTINGS_DEFAULT, LAUNCHER_STYLE } from '@/constants';
import { useSettingsContext } from '@/contexts/SettingsContext';

const useStylingSettings = () => {
  const { currentSettings } = useSettingsContext();

  const form = useForm({
    defaultValues: { ...STYLING_SETTINGS_DEFAULT },
  });

  useEffect(() => {
    form.reset({
      ...currentSettings?.stylingSettings,
      launcher: {
        ...currentSettings?.stylingSettings?.launcher,
        hideLauncherWhenOpenChatbox:
          currentSettings?.stylingSettings?.launcher?.hideLauncherWhenOpenChatbox || false,
        shape: currentSettings?.stylingSettings?.launcher?.shape || LAUNCHER_STYLE.BUBBLE,
        size: currentSettings?.stylingSettings?.launcher?.size || 60,
      },
      contentDisplay: currentSettings?.stylingSettings?.contentDisplay || CONTENT_DISPLAY.TOP,
    });
  }, [currentSettings]);

  return {
    form,
  };
};

export type StylingSettingsContextType = ReturnType<typeof useStylingSettings>;

const context = createContext<StylingSettingsContextType | null>(null);

export const StylingSettingsContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useStylingSettings();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useStylingSettingsContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useStylingSettingsContext must be used inside StylingSettingsContextProvider');
  }

  return value;
};
