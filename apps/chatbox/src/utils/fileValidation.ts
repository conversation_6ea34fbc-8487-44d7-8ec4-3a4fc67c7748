import {
  ACCEPTED_FILE_TYPES,
  MAX_FILES,
  MAX_FILE_SIZE,
} from '../components/ChatBoxUI/constants/media';

export interface FileValidationResult {
  isValid: boolean;
  errorMessage?: string;
}

/**
 * Validates files against the upload rules:
 * - Only support file extensions from ACCEPTED_FILE_TYPES
 * - Maximum 5 files per upload
 * - Maximum file size is 10MB per file
 *
 * @param files - Array of files to validate
 * @param message - Default error message to use for validation failures
 * @param existingFileCount - Number of files already selected (optional)
 * @returns FileValidationResult indicating if validation passed
 */
export const validateFiles = (
  files: File[],
  message?: string,
  existingFileCount = 0
): FileValidationResult => {
  const maxFileSize = MAX_FILE_SIZE / 1024 / 1024; // Convert to MB
  const errorMessage =
    message ??
    `File size exceeds the ${maxFileSize}MB limit, is unsupported, or you have uploaded more than ${MAX_FILES} files. Please try again!`;

  // Check total file count (current batch + existing files)
  const totalFileCount = files.length + existingFileCount;
  if (totalFileCount > MAX_FILES) {
    return {
      isValid: false,
      errorMessage,
    };
  }

  // Check each file in the current batch
  for (const file of files) {
    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return {
        isValid: false,
        errorMessage,
      };
    }

    // Check file extension
    const fileName = file.name.toLowerCase();
    const isValidExtension = ACCEPTED_FILE_TYPES.some((ext) =>
      fileName.endsWith(ext.toLowerCase())
    );

    if (!isValidExtension) {
      return {
        isValid: false,
        errorMessage,
      };
    }
  }

  return { isValid: true };
};

/**
 * Gets the file extension from a filename
 * @param filename - The filename to extract extension from
 * @returns The file extension with dot (e.g., '.jpg')
 */
export const getFileExtension = (filename: string): string => {
  const lastDotIndex = filename.lastIndexOf('.');
  return lastDotIndex !== -1 ? filename.substring(lastDotIndex).toLowerCase() : '';
};
