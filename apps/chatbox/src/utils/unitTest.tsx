import type React from 'react';
import { MantineProvider } from '@mantine/core';
import { emotionTransform, MantineEmotionProvider } from '@mantine/emotion';
import { render, type RenderResult } from '@testing-library/react';
import { themeConfigurations } from '../constants/themeConfiguration';
import { FileValidationAlertProvider } from '../contexts/FileValidationAlertContext';

export const MantineWrapper = ({ children }: { children: React.ReactNode }) => {
  return (
    <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
      <MantineEmotionProvider>{children}</MantineEmotionProvider>
    </MantineProvider>
  );
};

export const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
      <MantineEmotionProvider>
        <FileValidationAlertProvider>{children}</FileValidationAlertProvider>
      </MantineEmotionProvider>
    </MantineProvider>
  );
};

export const renderWithMantine = (ui: React.ReactNode): RenderResult => {
  return render(<MantineWrapper>{ui}</MantineWrapper>);
};

export const renderWithAllProviders = (ui: React.ReactNode): RenderResult => {
  return render(<AllTheProviders>{ui}</AllTheProviders>);
};
