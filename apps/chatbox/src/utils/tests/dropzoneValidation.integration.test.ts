import { describe, it, expect } from 'vitest';
import { getAcceptedMimeTypes } from '@resola-ai/ui/components/RichMessageInput/helper';
import { ACCEPTED_FILE_TYPES } from '../../components/ChatBoxUI/constants/media';

describe('Dropzone Validation Integration', () => {
  describe('MIME type validation for Mantine Dropzone', () => {
    it('should generate correct MIME types that will reject DOCX files', () => {
      const mimeTypeMap = getAcceptedMimeTypes([...ACCEPTED_FILE_TYPES]);
      const mimeTypes = Object.keys(mimeTypeMap);

      // Simulate a DOCX file
      const docxMimeType =
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document';

      // This MIME type should NOT be in our accepted list
      expect(mimeTypes).not.toContain(docxMimeType);

      // Verify that wildcard application/* is NOT present to prevent accepting all application files
      expect(mimeTypes).not.toContain('application/*');
    });

    it('should generate correct MIME types that will accept our supported files', () => {
      const mimeTypeMap = getAcceptedMimeTypes([...ACCEPTED_FILE_TYPES]);
      const mimeTypes = Object.keys(mimeTypeMap);

      // Supported file MIME types that should be accepted
      const supportedMimeTypes = [
        'image/png', // .png
        'image/gif', // .gif
        'image/jpeg', // .jpg, .jpeg
        'image/webp', // .webp
        'application/pdf', // .pdf
        'video/mp4', // .mp4
        'video/webm', // .webm
        'video/x-msvideo', // .avi
        'video/x-matroska', // .mkv
      ];

      supportedMimeTypes.forEach((mimeType) => {
        expect(mimeTypes).toContain(mimeType);
      });
    });

    it('should not include unsupported office document MIME types', () => {
      const mimeTypeMap = getAcceptedMimeTypes([...ACCEPTED_FILE_TYPES]);
      const mimeTypes = Object.keys(mimeTypeMap);

      // These MIME types should NOT be in our accepted list
      const unsupportedOfficeMimeTypes = [
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
        'application/msword', // .doc
        'text/plain', // .txt
        'application/vnd.ms-excel', // .xls
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
        'text/csv', // .csv
      ];

      unsupportedOfficeMimeTypes.forEach((mimeType) => {
        expect(mimeTypes).not.toContain(mimeType);
      });
    });

    it('should generate MIME type object compatible with Mantine Dropzone accept prop', () => {
      const mimeTypeMap = getAcceptedMimeTypes([...ACCEPTED_FILE_TYPES]);

      // Verify the structure is correct for Mantine Dropzone
      // Should be an object with MIME types as keys and extension arrays as values
      expect(typeof mimeTypeMap).toBe('object');
      expect(Array.isArray(mimeTypeMap)).toBe(false);

      // Check that all values are arrays
      Object.values(mimeTypeMap).forEach((extensions) => {
        expect(Array.isArray(extensions)).toBe(true);
      });

      // Check that specific MIME types have their extensions
      expect(mimeTypeMap['image/png']).toEqual(['.png']);
      expect(mimeTypeMap['image/jpeg']).toEqual(['.jpg', '.jpeg']);
      expect(mimeTypeMap['application/pdf']).toEqual(['.pdf']);
    });

    it('should demonstrate that DOCX files would be rejected by Dropzone validation', () => {
      const mimeTypeMap = getAcceptedMimeTypes([...ACCEPTED_FILE_TYPES]);

      // Simulate Mantine Dropzone validation logic (no wildcards)
      const simulateFileAcceptance = (fileMimeType: string): boolean => {
        // Check if exact MIME type is in accepted list
        // Since we removed wildcards, only exact matches are accepted
        return Object.keys(mimeTypeMap).includes(fileMimeType);
      };

      // Test file acceptance simulation
      expect(simulateFileAcceptance('image/png')).toBe(true); // Should be accepted
      expect(simulateFileAcceptance('image/jpeg')).toBe(true); // Should be accepted
      expect(simulateFileAcceptance('application/pdf')).toBe(true); // Should be accepted
      expect(simulateFileAcceptance('video/mp4')).toBe(true); // Should be accepted

      // DOCX should be rejected because it's not in our specific MIME type list
      expect(
        simulateFileAcceptance(
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
      ).toBe(false);
      expect(simulateFileAcceptance('application/msword')).toBe(false); // DOC
      expect(simulateFileAcceptance('text/plain')).toBe(false); // TXT
    });
  });
});
