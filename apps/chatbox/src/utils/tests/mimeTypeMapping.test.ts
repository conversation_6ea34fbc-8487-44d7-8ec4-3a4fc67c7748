import { describe, it, expect } from 'vitest';
import { getAcceptedMimeTypes } from '@resola-ai/ui/components/RichMessageInput/helper';
import { ACCEPTED_FILE_TYPES } from '../../components/ChatBoxUI/constants/media';

describe('MIME Type Mapping for Dropzone Validation', () => {
  describe('getAcceptedMimeTypes', () => {
    it('should convert all ACCEPTED_FILE_TYPES to proper MIME types', () => {
      const mimeTypeMap = getAcceptedMimeTypes([...ACCEPTED_FILE_TYPES]);

      // Check that the function returns a non-empty object
      expect(Object.keys(mimeTypeMap).length).toBeGreaterThan(0);
    });

    it('should include all expected MIME types for our supported file extensions', () => {
      const mimeTypeMap = getAcceptedMimeTypes([...ACCEPTED_FILE_TYPES]);
      const mimeTypes = Object.keys(mimeTypeMap);

      // Check for image MIME types
      expect(mimeTypes).toContain('image/png');
      expect(mimeTypes).toContain('image/gif');
      expect(mimeTypes).toContain('image/jpeg');
      expect(mimeTypes).toContain('image/webp');

      // Check for document MIME types
      expect(mimeTypes).toContain('application/pdf');

      // Check for video MIME types
      expect(mimeTypes).toContain('video/mp4');
      expect(mimeTypes).toContain('video/webm');
      expect(mimeTypes).toContain('video/x-msvideo'); // .avi
      expect(mimeTypes).toContain('video/x-matroska'); // .mkv
    });

    it('should not include unsupported MIME types (docx, xlsx, etc.)', () => {
      const mimeTypeMap = getAcceptedMimeTypes([...ACCEPTED_FILE_TYPES]);
      const mimeTypes = Object.keys(mimeTypeMap);

      // These should NOT be included since they're not in our ACCEPTED_FILE_TYPES
      expect(mimeTypes).not.toContain(
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      ); // .docx
      expect(mimeTypes).not.toContain('application/msword'); // .doc
      expect(mimeTypes).not.toContain('text/plain'); // .txt
      expect(mimeTypes).not.toContain('application/vnd.ms-excel'); // .xls
      expect(mimeTypes).not.toContain(
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      ); // .xlsx
      expect(mimeTypes).not.toContain('text/csv'); // .csv
    });

    it('should handle individual file extensions correctly', () => {
      // Test individual extensions
      expect(getAcceptedMimeTypes(['.png'])).toHaveProperty('image/png');
      expect(getAcceptedMimeTypes(['.gif'])).toHaveProperty('image/gif');
      expect(getAcceptedMimeTypes(['.jpg'])).toHaveProperty('image/jpeg');
      expect(getAcceptedMimeTypes(['.webp'])).toHaveProperty('image/webp');
      expect(getAcceptedMimeTypes(['.pdf'])).toHaveProperty('application/pdf');
      expect(getAcceptedMimeTypes(['.mp4'])).toHaveProperty('video/mp4');
      expect(getAcceptedMimeTypes(['.webm'])).toHaveProperty('video/webm');
      expect(getAcceptedMimeTypes(['.avi'])).toHaveProperty('video/x-msvideo');
      expect(getAcceptedMimeTypes(['.mkv'])).toHaveProperty('video/x-matroska');
    });

    it('should reject unsupported extensions by not including them in MIME mapping', () => {
      // These extensions should not be mapped since they're not in ACCEPTED_FILE_TYPES
      const unsupportedExtensions = ['.docx', '.doc', '.txt', '.xls', '.xlsx', '.csv'];
      const mimeTypeMap = getAcceptedMimeTypes(unsupportedExtensions);

      // Should either be empty or contain only wildcard entries
      const specificMimeTypes = Object.keys(mimeTypeMap).filter((key) => !key.includes('*'));

      // These specific MIME types should be present since we're testing unsupported files
      expect(specificMimeTypes).toContain(
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      );
      expect(specificMimeTypes).toContain('application/msword');
      expect(specificMimeTypes).toContain('text/plain');
    });
  });
});
