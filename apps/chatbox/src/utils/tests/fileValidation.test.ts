import { describe, it, expect } from 'vitest';
import { validateFiles, getFileExtension } from '../fileValidation';
import {
  ACCEPTED_FILE_TYPES,
  MAX_FILES,
  MAX_FILE_SIZE,
} from '../../components/ChatBoxUI/constants/media';

// Test constants
const MOCK_VALID_FILE = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
const MOCK_LARGE_FILE = new File([new ArrayBuffer(MAX_FILE_SIZE + 1)], 'large.jpg', {
  type: 'image/jpeg',
});
const MOCK_INVALID_EXTENSION_FILE = new File(['test content'], 'test.txt', { type: 'text/plain' });
const MOCK_VALID_PDF_FILE = new File(['pdf content'], 'document.pdf', { type: 'application/pdf' });
const MOCK_VALID_MP4_FILE = new File(['video content'], 'video.mp4', { type: 'video/mp4' });

// Expected error message constant
const EXPECTED_ERROR_MESSAGE =
  'File size exceeds the 10MB limit, is unsupported, or you have uploaded more than 5 files. Please try again!';

describe('fileValidation', () => {
  describe('validateFiles', () => {
    describe('file count validation', () => {
      it('should pass validation for files within count limit', () => {
        const files = Array(MAX_FILES).fill(MOCK_VALID_FILE);
        const result = validateFiles(files);

        expect(result.isValid).toBe(true);
        expect(result.errorMessage).toBeUndefined();
      });

      it('should fail validation when file count exceeds maximum', () => {
        const files = Array(MAX_FILES + 1).fill(MOCK_VALID_FILE);
        const result = validateFiles(files);

        expect(result.isValid).toBe(false);
        expect(result.errorMessage).toBe(EXPECTED_ERROR_MESSAGE);
      });

      it('should pass validation for empty file array', () => {
        const result = validateFiles([]);

        expect(result.isValid).toBe(true);
        expect(result.errorMessage).toBeUndefined();
      });
    });

    describe('file size validation', () => {
      it('should pass validation for files within size limit', () => {
        const result = validateFiles([MOCK_VALID_FILE]);

        expect(result.isValid).toBe(true);
        expect(result.errorMessage).toBeUndefined();
      });

      it('should fail validation when file size exceeds maximum', () => {
        const result = validateFiles([MOCK_LARGE_FILE]);

        expect(result.isValid).toBe(false);
        expect(result.errorMessage).toBe(EXPECTED_ERROR_MESSAGE);
      });

      it('should fail validation when any file in array exceeds size limit', () => {
        const files = [MOCK_VALID_FILE, MOCK_LARGE_FILE];
        const result = validateFiles(files);

        expect(result.isValid).toBe(false);
        expect(result.errorMessage).toBe(EXPECTED_ERROR_MESSAGE);
      });

      it('should reject files larger than 10MB with correct error message', () => {
        // Create a mock file larger than 10MB (11MB)
        const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large-file.jpg', {
          type: 'image/jpeg',
        });

        const result = validateFiles([largeFile]);

        expect(result.isValid).toBe(false);
        expect(result.errorMessage).toBe(EXPECTED_ERROR_MESSAGE);
      });

      it('should accept files exactly at 10MB size limit', () => {
        // Create a mock file exactly 10MB
        const exactSizeFile = new File(['x'.repeat(10 * 1024 * 1024)], 'exact-size.jpg', {
          type: 'image/jpeg',
        });

        const result = validateFiles([exactSizeFile]);

        expect(result.isValid).toBe(true);
        expect(result.errorMessage).toBeUndefined();
      });

      it('should reject multiple files where one is too large', () => {
        const validFile = new File(['valid content'], 'valid.jpg', { type: 'image/jpeg' });
        const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.pdf', {
          type: 'application/pdf',
        });

        const result = validateFiles([validFile, largeFile]);

        expect(result.isValid).toBe(false);
        expect(result.errorMessage).toBe(EXPECTED_ERROR_MESSAGE);
      });
    });

    describe('file extension validation', () => {
      it('should pass validation for supported image extensions', () => {
        const jpgFile = new File(['content'], 'test.jpg', { type: 'image/jpeg' });
        const pngFile = new File(['content'], 'test.png', { type: 'image/png' });
        const webpFile = new File(['content'], 'test.webp', { type: 'image/webp' });

        expect(validateFiles([jpgFile]).isValid).toBe(true);
        expect(validateFiles([pngFile]).isValid).toBe(true);
        expect(validateFiles([webpFile]).isValid).toBe(true);
      });

      it('should pass validation for supported video extensions', () => {
        const mp4File = new File(['content'], 'test.mp4', { type: 'video/mp4' });
        const webmFile = new File(['content'], 'test.webm', { type: 'video/webm' });
        const aviFile = new File(['content'], 'test.avi', { type: 'video/x-msvideo' });
        const mkvFile = new File(['content'], 'test.mkv', { type: 'video/x-matroska' });

        expect(validateFiles([mp4File]).isValid).toBe(true);
        expect(validateFiles([webmFile]).isValid).toBe(true);
        expect(validateFiles([aviFile]).isValid).toBe(true);
        expect(validateFiles([mkvFile]).isValid).toBe(true);
      });

      it('should pass validation for supported document extensions', () => {
        const pdfFile = new File(['content'], 'test.pdf', { type: 'application/pdf' });

        expect(validateFiles([pdfFile]).isValid).toBe(true);
      });

      it('should fail validation for unsupported extensions', () => {
        const result = validateFiles([MOCK_INVALID_EXTENSION_FILE]);

        expect(result.isValid).toBe(false);
        expect(result.errorMessage).toBe(EXPECTED_ERROR_MESSAGE);
      });

      it('should handle case-insensitive extension matching', () => {
        const upperCaseFile = new File(['content'], 'test.JPG', { type: 'image/jpeg' });
        const mixedCaseFile = new File(['content'], 'test.Pdf', { type: 'application/pdf' });

        expect(validateFiles([upperCaseFile]).isValid).toBe(true);
        expect(validateFiles([mixedCaseFile]).isValid).toBe(true);
      });

      it('should fail validation when any file has unsupported extension', () => {
        const files = [MOCK_VALID_FILE, MOCK_INVALID_EXTENSION_FILE];
        const result = validateFiles(files);

        expect(result.isValid).toBe(false);
        expect(result.errorMessage).toBe(EXPECTED_ERROR_MESSAGE);
      });
    });

    describe('comprehensive validation scenarios', () => {
      it('should pass validation for mixed valid file types', () => {
        const files = [MOCK_VALID_FILE, MOCK_VALID_PDF_FILE, MOCK_VALID_MP4_FILE];
        const result = validateFiles(files);

        expect(result.isValid).toBe(true);
        expect(result.errorMessage).toBeUndefined();
      });

      it('should validate all accepted file types from constants', () => {
        // Test each accepted file type
        ACCEPTED_FILE_TYPES.forEach((extension) => {
          const fileName = `test${extension}`;
          const file = new File(['content'], fileName, { type: 'application/octet-stream' });
          const result = validateFiles([file]);

          expect(result.isValid).toBe(true);
        });
      });

      it('should return first validation error encountered', () => {
        // Test that file count error is returned first
        const tooManyFiles = Array(MAX_FILES + 1).fill(MOCK_LARGE_FILE);
        const result = validateFiles(tooManyFiles);

        expect(result.isValid).toBe(false);
        expect(result.errorMessage).toBe(EXPECTED_ERROR_MESSAGE);
      });
    });

    describe('validateFiles with existing files', () => {
      it('allows exactly MAX_FILES (5) total files', () => {
        // Test 1: 4 existing + 1 new = 5 total (should pass)
        const newFiles = [new File(['content'], 'test5.png', { type: 'image/png' })];
        const existingFileCount = 4;
        const result1 = validateFiles(newFiles, undefined, existingFileCount);
        expect(result1.isValid).toBe(true);

        // Test 2: 0 existing + 5 new = 5 total (should pass)
        const fiveFiles = Array.from(
          { length: 5 },
          (_, i) => new File(['content'], `test${i + 1}.png`, { type: 'image/png' })
        );
        const result2 = validateFiles(fiveFiles, undefined, 0);
        expect(result2.isValid).toBe(true);

        // Test 3: 3 existing + 2 new = 5 total (should pass)
        const twoFiles = Array.from(
          { length: 2 },
          (_, i) => new File(['content'], `test${i + 4}.png`, { type: 'image/png' })
        );
        const result3 = validateFiles(twoFiles, undefined, 3);
        expect(result3.isValid).toBe(true);

        // Test 4: 5 existing + 1 new = 6 total (should fail)
        const oneFile = [new File(['content'], 'test6.png', { type: 'image/png' })];
        const result4 = validateFiles(oneFile, undefined, 5);
        expect(result4.isValid).toBe(false);
      });

      it('should reject when total files (new + existing) exceeds MAX_FILES', () => {
        const newFiles = [
          new File(['content'], 'test4.png', { type: 'image/png' }),
          new File(['content'], 'test5.png', { type: 'image/png' }),
        ];
        const existingFileCount = 4; // Already have 4 files selected

        const result = validateFiles(newFiles, undefined, existingFileCount);

        expect(result.isValid).toBe(false);
        expect(result.errorMessage).toContain('more than 5 files');
      });

      it('should accept when total files (new + existing) equals MAX_FILES', () => {
        const newFiles = [new File(['content'], 'test5.png', { type: 'image/png' })];
        const existingFileCount = 4; // Already have 4 files selected

        const result = validateFiles(newFiles, undefined, existingFileCount);

        expect(result.isValid).toBe(true);
      });

      it('should accept when total files (new + existing) is less than MAX_FILES', () => {
        const newFiles = [
          new File(['content'], 'test3.png', { type: 'image/png' }),
          new File(['content'], 'test4.png', { type: 'image/png' }),
        ];
        const existingFileCount = 2; // Already have 2 files selected

        const result = validateFiles(newFiles, undefined, existingFileCount);

        expect(result.isValid).toBe(true);
      });

      it('should work correctly with no existing files (backward compatibility)', () => {
        const newFiles = [
          new File(['content'], 'test1.png', { type: 'image/png' }),
          new File(['content'], 'test2.png', { type: 'image/png' }),
          new File(['content'], 'test3.png', { type: 'image/png' }),
          new File(['content'], 'test4.png', { type: 'image/png' }),
          new File(['content'], 'test5.png', { type: 'image/png' }),
        ];

        const result = validateFiles(newFiles); // No existingFileCount parameter

        expect(result.isValid).toBe(true);
      });

      it('should reject when new files alone exceed MAX_FILES, even with no existing files', () => {
        const newFiles = [
          new File(['content'], 'test1.png', { type: 'image/png' }),
          new File(['content'], 'test2.png', { type: 'image/png' }),
          new File(['content'], 'test3.png', { type: 'image/png' }),
          new File(['content'], 'test4.png', { type: 'image/png' }),
          new File(['content'], 'test5.png', { type: 'image/png' }),
          new File(['content'], 'test6.png', { type: 'image/png' }),
        ];

        const result = validateFiles(newFiles); // No existingFileCount parameter

        expect(result.isValid).toBe(false);
        expect(result.errorMessage).toContain('more than 5 files');
      });
    });
  });

  describe('getFileExtension', () => {
    it('should extract file extension with dot', () => {
      expect(getFileExtension('test.jpg')).toBe('.jpg');
      expect(getFileExtension('document.pdf')).toBe('.pdf');
      expect(getFileExtension('video.mp4')).toBe('.mp4');
    });

    it('should handle multiple dots in filename', () => {
      expect(getFileExtension('my.file.name.jpg')).toBe('.jpg');
      expect(getFileExtension('archive.tar.gz')).toBe('.gz');
    });

    it('should return lowercase extension', () => {
      expect(getFileExtension('TEST.JPG')).toBe('.jpg');
      expect(getFileExtension('Document.PDF')).toBe('.pdf');
    });

    it('should return empty string for files without extension', () => {
      expect(getFileExtension('filename')).toBe('');
      expect(getFileExtension('')).toBe('');
    });

    it('should handle edge cases', () => {
      expect(getFileExtension('.')).toBe('.');
      expect(getFileExtension('.hidden')).toBe('.hidden');
      expect(getFileExtension('file.')).toBe('.');
    });
  });
});
