import type { ICredential } from '@resola-ai/ui';

export const mockCredentials: ICredential[] = [
  {
    id: 'cred_20250414093227',
    workspaceId: '67e61a91a17deff680e63275',
    name: 'OpenAI API Key',
    description: 'Production OpenAI API key for GPT-4 access',
    typeName: 'api_key',
    provider: 'openai',
    settings: {},
    metadata: {
      environment: 'production',
      team: 'ai-research',
      usageLimit: 10000,
    },
    createdAt: new Date('2025-04-14T09:32:27.611Z'),
    updatedAt: new Date('2025-04-14T09:32:27.611Z'),
  },
  {
    id: 'cred_20250414093111',
    workspaceId: '67e61a91a17deff680e63275',
    name: 'Google API Key',
    description: 'Production Google API key for GPT-4 access',
    typeName: 'api_key',
    provider: 'google',
    settings: {},
    metadata: {
      environment: 'production',
      team: 'ai-research',
      usageLimit: 10000,
    },
    createdAt: new Date('2025-04-15T09:32:27.611Z'),
    updatedAt: new Date('2025-04-15T09:32:27.611Z'),
  },
  {
    id: 'cred_20250414093000',
    workspaceId: '67e61a91a17deff680e63275',
    name: 'Slack API Key',
    description: 'Production Slack API key for GPT-4 access',
    typeName: 'api_key',
    provider: 'slack',
    settings: {},
    metadata: {
      environment: 'production',
      team: 'ai-research',
      usageLimit: 10000,
    },
    createdAt: new Date('2025-04-16T09:32:27.611Z'),
    updatedAt: new Date('2025-04-16T09:32:27.611Z'),
  },
  {
    id: 'cred_20250414093234',
    workspaceId: '67e61a91a17deff680e63275',
    name: 'LINE OAuth2',
    description: 'Production LINE OAuth2 for GPT-4 access',
    typeName: 'oauth2',
    provider: 'line',
    settings: {},
    metadata: {
      environment: 'production',
      team: 'ai-research',
      usageLimit: 10000,
    },
    createdAt: new Date('2025-04-17T09:32:27.611Z'),
    updatedAt: new Date('2025-04-17T09:32:27.611Z'),
  },
  {
    id: 'cred_202504140141',
    workspaceId: '67e61a91a17deff680e63275',
    name: 'Slack OAuth2',
    description: 'Production Slack OAuth2 for GPT-4 access',
    typeName: 'oauth2',
    provider: 'slack',
    settings: {},
    metadata: {
      environment: 'production',
      team: 'ai-research',
      usageLimit: 10000,
    },
    createdAt: new Date('2025-04-12T09:32:27.611Z'),
    updatedAt: new Date('2025-04-12T09:32:27.611Z'),
  },
  {
    id: 'cred_202504140142z',
    workspaceId: '67e61a91a17deff680e63275',
    name: 'Slack Access Token',
    description: 'Production Slack Access Token',
    typeName: 'bearer_token',
    provider: 'slack',
    settings: {},
    metadata: {
      environment: 'production',
      team: 'ai-research',
      usageLimit: 10000,
    },
    createdAt: new Date('2025-04-13T09:32:27.611Z'),
    updatedAt: new Date('2025-04-13T09:32:27.611Z'),
  },
  {
    id: 'cred_2025041401433z',
    workspaceId: '67e61a91a17deff680e63275',
    name: 'Google Access Token',
    description: 'Production Google Access Token',
    typeName: 'bearer_token',
    provider: 'google',
    settings: {},
    metadata: {
      environment: 'production',
      team: 'ai-research',
      usageLimit: 10000,
    },
    createdAt: new Date('2025-04-14T09:32:27.611Z'),
    updatedAt: new Date('2025-04-14T09:32:27.611Z'),
  },
];
