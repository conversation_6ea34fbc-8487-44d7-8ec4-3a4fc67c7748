import { useCallback, useEffect, useMemo, useState } from 'react';
import ApiService from '../services/api';
import { IResource } from '../models/resource';
import { useUploadAssetContext } from '../modules/uploadAssetContext';
import { v4 as createId } from 'uuid';
import {
    DraftResource,
    useDraftResources,
} from '../modules/TextEditor/Editor/hooks/useDraftResource';
import { delay } from '../utils/common';
import { sendCustomEvent } from '@resola-ai/utils';

type ResourceEntry = {
    file: File;
    id: string;
};

type SavedResourceEntry = {
    resource: IResource;
    id: string;
};

const getMessageTypeFromFile = (file: File): string => {
    const mimeType = file.type.toLowerCase();

    // Check for image types
    if (mimeType.startsWith('image/')) {
        return 'image';
    }

    // Check for video types
    if (mimeType.startsWith('video/')) {
        return 'video';
    }

    // Check for document types
    if (
        mimeType === 'application/pdf' ||
        mimeType.includes('document') ||
        mimeType.includes('text/') ||
        mimeType.includes('application/vnd.openxmlformats') ||
        mimeType.includes('application/vnd.ms-')
    ) {
        return 'document';
    }

    // Default to image for unknown types
    return 'image';
};

const createResources = (files: File[], converId: string) => {
    const resources: DraftResource[] = files.map((file) => ({
        id: createId(),
        name: file.name,
        ref: converId,
        type: getMessageTypeFromFile(file),
        url: URL.createObjectURL(file),
        statusCreated: '',
    }));
    return resources;
};

const isSameResources = (curr: DraftResource[], saved: DraftResource[]) => {
    if (curr.length !== saved.length) return false;
    const currIdStr = curr
        .map((item) => item.id)
        .sort((a, b) => a.localeCompare(b))
        .join('~');
    const savedIdStr = saved
        .map((item) => item.id)
        .sort((a, b) => a.localeCompare(b))
        .join('~');
    return currIdStr === savedIdStr;
};

const sendEventToSyncLayout = () => sendCustomEvent('deca-livechat-sync-chat-message-height', {});

// Helper function to update a single resource
const updateSingleResource = (
    item: DraftResource,
    found: SavedResourceEntry,
    revokeUrlList: string[],
) => {
    // Revoke the old blob URL if it exists
    if (item.url.startsWith('blob:http')) {
        revokeUrlList.push(item.url);
    }

    // Append filename as query parameter to the URL
    const url = new URL(found.resource.url);
    url.searchParams.set('fileName', found.resource.name);

    return {
        ...item,
        id: found.id,
        url: url.toString(),
        ref: found.resource.ref,
        statusCreated: found.resource.statusCreated,
    };
};

// Helper function to handle server deletion
const handleServerDeletion = (removedResources: DraftResource[]) => {
    const validResources = removedResources.filter((item) => item.ref && item.statusCreated);

    if (validResources.length > 0) {
        Promise.all(
            validResources.map((item) => ApiService.deleteAsset(item.ref, item.statusCreated)),
        ).catch((error) => {
            console.error('Error deleting assets:', error);
        });
    }
};

// Helper function to update draft resources
const updateDraftResources = (
    newResources: DraftResource[],
    currentConversationId: string,
    addDraftResources: (conversationId: string, resources: DraftResource[]) => void,
    clearDraftResources: (conversationId: string) => void,
) => {
    if (newResources.length) {
        addDraftResources(currentConversationId, newResources);
    } else {
        clearDraftResources(currentConversationId);
    }
};

// Create a new hook for attachment-related functionality
const useAttachmentUpload = (currentConversationId: string) => {
    const { setRejectErrors, LIMIT_SIZE, ALLOW_MIME_TYPES } = useUploadAssetContext();
    const { getDraftResources, addDraftResources, clearDraftResources } = useDraftResources();

    const [uploadResourceLoading, setUploadResourceLoading] = useState(false);
    const [resources, setResources] = useState<DraftResource[]>([]);

    const updateResources = useCallback(
        (resourceEntries: SavedResourceEntry[]) => {
            if (!resourceEntries || resourceEntries.length === 0) return;

            const revokeUrlList: string[] = [];

            setResources((pre) => {
                const newResources = pre.map((item) => {
                    const found = resourceEntries.find((rscEntry) => rscEntry.id === item.id);
                    if (found) {
                        return updateSingleResource(item, found, revokeUrlList);
                    }
                    return item;
                });

                // Update draft resources
                addDraftResources(currentConversationId, newResources);
                return newResources;
            });

            // Revoke object URLs after state update
            revokeUrlList.forEach((item) => URL.revokeObjectURL(item));
        },
        [addDraftResources, currentConversationId],
    );

    const uploadImageFile = useCallback(
        async (
            inputConversationId: string,
            file: File,
            resourceId: string,
        ): Promise<{ resource: IResource; id: string }> => {
            try {
                const response = await ApiService.uploadAsset(
                    inputConversationId,
                    'conversation',
                    file,
                );
                return {
                    resource: response,
                    id: resourceId,
                };
            } catch (error) {
                console.log(error);
                return undefined;
            }
        },
        [],
    );

    const uploadImagesHandler = useCallback(
        async (currentConversationId: string, resourceEntries: ResourceEntry[]) => {
            setUploadResourceLoading(true);
            const promises = resourceEntries.map((entry) => {
                return uploadImageFile(currentConversationId, entry.file, entry.id);
            });
            const results: SavedResourceEntry[] = (await Promise.all(promises)).filter(Boolean);
            updateResources(results);
            await delay(100);
            setUploadResourceLoading(false);
        },
        [updateResources, uploadImageFile],
    );

    const removeResources = useCallback(
        (ids: string[]) => {
            if (!ids || ids.length === 0) return;

            setResources((pre) => {
                const removedResources = pre.filter((item) => ids.includes(item.id));
                const newResources = pre.filter((preItem) => !ids.includes(preItem.id));

                // Handle server deletion
                handleServerDeletion(removedResources);

                // Update draft resources
                updateDraftResources(
                    newResources,
                    currentConversationId,
                    addDraftResources,
                    clearDraftResources,
                );

                return newResources;
            });
            sendEventToSyncLayout();
        },
        [addDraftResources, clearDraftResources, currentConversationId],
    );

    const validateFile = useCallback(
        (e: File) => {
            if (e === null) {
                setRejectErrors((prev) => ({
                    ...prev,
                    ['file-not-support']: true,
                }));
                return false;
            }
            // if the file is not support type in ALLOW_MIME_TYPES, then set the reject error and do not set the attachment file
            if (!ALLOW_MIME_TYPES.includes(e.type)) {
                setRejectErrors((prev) => ({
                    ...prev,
                    ['file-not-support']: true,
                }));
                return false;
            }
            // if the size is bigger than the LIMIT_SIZE, then set the reject error and do not set the attachment file
            if (e.size > LIMIT_SIZE) {
                setRejectErrors((prev) => ({
                    ...prev,
                    ['file-too-large']: true,
                }));
                return false;
            }
            return true;
        },
        [ALLOW_MIME_TYPES, LIMIT_SIZE, setRejectErrors],
    );

    const handleFileUpload = useCallback(
        (tempResources: DraftResource[], files: File[]) => {
            setTimeout(() => {
                sendCustomEvent('deca-livechat-sync-chat-message-height', {});
                const fileEntries: ResourceEntry[] = tempResources.map((item, index) => ({
                    id: item.id,
                    file: files[index],
                }));
                uploadImagesHandler(currentConversationId, fileEntries);
            }, 100);
        },
        [currentConversationId, uploadImagesHandler],
    );

    const addAttachedFiles = useCallback(
        (files: File[]) => {
            if (files.length === 0) {
                return;
            }
            setRejectErrors((prev) => ({}));
            const checks = files.map((file) => validateFile(file));
            if (checks.includes(false)) {
                console.log('some files are not valid', checks);
                return;
            }
            const tempResources = createResources(files, currentConversationId);
            setResources((pre) => [...pre, ...tempResources]);
            handleFileUpload(tempResources, files);
        },
        [currentConversationId, setRejectErrors, validateFile, handleFileUpload],
    );

    const resetResourcesHandler = useCallback(() => {
        setResources([]);
        clearDraftResources(currentConversationId);
        sendEventToSyncLayout();
    }, [clearDraftResources, currentConversationId]);

    const loadSavedResources = useCallback(() => {
        if (!currentConversationId) return;

        const savedResources = getDraftResources(currentConversationId);
        if (savedResources?.length) {
            setResources((pre) => {
                // If current resources belong to a different conversation, replace them
                if (pre.length > 0 && pre[0].ref !== currentConversationId) {
                    return savedResources;
                }
                // If resources are different, update them
                if (!isSameResources(pre, savedResources)) {
                    return savedResources;
                }
                // Otherwise keep current resources
                return pre;
            });
        } else {
            setResources([]);
        }
        setTimeout(() => sendEventToSyncLayout(), 100);
    }, [currentConversationId, getDraftResources]);

    useEffect(() => {
        loadSavedResources();
    }, [loadSavedResources]);

    return useMemo(
        () => ({
            resources,
            uploadResourceLoading,
            removeResources,
            addAttachedFiles,
            resetResourcesHandler,
            setUploadResourceLoading,
        }),
        [
            resources,
            uploadResourceLoading,
            removeResources,
            addAttachedFiles,
            resetResourcesHandler,
        ],
    );
};

export default useAttachmentUpload;
