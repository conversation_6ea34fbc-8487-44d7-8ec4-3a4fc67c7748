import { describe, expect, it, vi } from 'vitest';
import { screen } from '@testing-library/react';

import { renderWithMantine, mockLibraries } from '@/utils/test';
import BaseLayout from './index';

// Mock the dependencies
vi.mock('@/services', () => ({
  kbService: {
    instance: {},
    init: vi.fn(),
  },
}));

// Import the mocked usePathName
const { usePathName } = vi.hoisted(() => ({ usePathName: vi.fn(() => '/ai-widget') }));

vi.mock('@resola-ai/ui/hooks', () => ({
  usePathName,
}));

// Mock LayoutStructure component
vi.mock('@resola-ai/ui/components', () => ({
  LayoutStructure: ({ children }) => <div data-testid='layout-structure'>{children}</div>,
  HeaderContainer: () => <div data-testid='header-container'>Header</div>,
}));

describe('BaseLayout', () => {
  mockLibraries();

  it('renders correctly with default props', () => {
    renderWithMantine(<BaseLayout>Content</BaseLayout>);

    // Check if the main components are rendered
    expect(screen.getByTestId('layout-structure')).toBeInTheDocument();
    expect(screen.getByTestId('header-container')).toBeInTheDocument();
    expect(screen.getByText('Content')).toBeInTheDocument();
  });
  it('applies the correct burger class for home page', () => {
    vi.mocked(usePathName).mockReturnValue('/ai-widget');

    renderWithMantine(<BaseLayout>Content</BaseLayout>);

    // We can't directly test the CSS classes, but we can verify the component renders
    expect(screen.getByTestId('layout-structure')).toBeInTheDocument();
  });

  it('applies the correct burger class for detail page', () => {
    vi.mocked(usePathName).mockReturnValue('/ai-widget/detail');

    renderWithMantine(<BaseLayout>Content</BaseLayout>);

    // We can't directly test the CSS classes, but we can verify the component renders
    expect(screen.getByTestId('layout-structure')).toBeInTheDocument();
  });

  it('renders with navigationMobile prop', () => {
    const navigationMobile = <div data-testid='nav-mobile'>Navigation</div>;

    renderWithMantine(<BaseLayout navigationMobile={navigationMobile}>Content</BaseLayout>);

    // Check if the navigation is passed to HeaderContainer
    expect(screen.getByTestId('layout-structure')).toBeInTheDocument();
    expect(screen.getByTestId('header-container')).toBeInTheDocument();
  });
});
