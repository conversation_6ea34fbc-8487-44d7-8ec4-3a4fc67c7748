import { <PERSON><PERSON><PERSON><PERSON>, Stack } from '@mantine/core';
import { useCallback, useEffect, useMemo } from 'react';
import AppSelector from '../../AppSelector';
import { StepperActions, Stepper, Step } from './Stepper';
import { ObjectType, Schema, SchemaFormCallback, SetupFormInput } from '../types';
import { FormActions, FormCredential, FormTriggerEvent } from '../../FormChema';
import { useForm } from 'react-hook-form';
import { useCallbackRef } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import { ICredential, ICredentialPayload } from '../../Credential';

export interface SetupFormProps {
  schema: Schema;
  steps?: Step[];
  activeStep?: number;
  completedStep?: number;
  objectType: ObjectType;
  onOpenAppCatalog?: () => void;
  onStepChange?: (step: number) => void;
  credentials: ICredential[];
  isCredentialsLoading: boolean;
  createCredential?: (credential: ICredentialPayload) => Promise<ICredential | null>;
  form: SetupFormInput;
  onFormChange?: SchemaFormCallback;
}

const SetupForm = (props: SetupFormProps) => {
  const {
    schema,
    activeStep = 0,
    completedStep = -1,
    steps = [],
    objectType,
    credentials,
    isCredentialsLoading,
    createCredential = async () => null,
    onOpenAppCatalog = () => {},
    onStepChange = () => {},
    onFormChange = () => {},
    form = {},
  } = props;

  const { t } = useTranslate();
  const formChangeCallback = useCallbackRef(onFormChange);

  const selectedApp = {
    name: schema?.displayName,
    icon: schema?.icon,
  };

  const hasCredential = Object.keys(schema?.credentials || {}).length > 0;

  const methods = useForm<SetupFormInput>();
  const { control, watch, formState } = methods;
  const { isValid } = formState;
  const formValues = watch();

  const submitButtonText = useMemo(() => {
    if (hasCredential && !formValues.credential) {
      return t('form.chooseCredential');
    }
    if (!formValues[objectType]) {
      return objectType === 'action' ? t('form.chooseAction') : t('form.chooseTrigger');
    }
    return t('button.continue');
  }, [formValues, objectType, hasCredential, t]);

  const handleNext = useCallback(() => {
    onStepChange?.(activeStep + 1);
  }, [activeStep, onStepChange]);

  const updateCompletedStep = useCallback(() => {
    if (!isValid || activeStep <= completedStep) return;
    const formValuesTmp = watch();
    formChangeCallback({ ...formValuesTmp, completedFormStep: activeStep }, 'completedFormStep');
    Object.keys(formValuesTmp).forEach(key => {
      formChangeCallback({ ...formValuesTmp, completedFormStep: activeStep }, key);
    });
  }, [activeStep, completedStep, formChangeCallback, isValid, watch]);

  useEffect(() => {
    updateCompletedStep();
  }, [updateCompletedStep]);

  useEffect(() => {
    const subscription = watch((formValues, { name }) => {
      if (!name) return;
      formChangeCallback(formValues as any, name);
    });

    return () => subscription.unsubscribe();
  }, [watch, formChangeCallback]);

  return (
    <>
      <ScrollArea sx={{ flex: 1 }}>
        <Stack gap='lg'>
          <Stepper
            steps={steps}
            activeStep={activeStep}
            completedStep={completedStep}
            currentStepValid={isValid}
            onStepChange={onStepChange}
          />

          <AppSelector selectedApp={selectedApp} onChangeClick={onOpenAppCatalog} required />

          {hasCredential && (
            <FormCredential
              control={control}
              name={'credential'}
              schema={schema}
              credentials={credentials}
              isLoading={isCredentialsLoading}
              createCredential={createCredential}
              defaultValue={form['settings']?.['credential']}
              rules={{ required: true }}
            />
          )}

          {objectType === 'action' ? (
            <FormActions
              control={control}
              name={objectType}
              schema={schema}
              defaultValue={form[objectType]}
              rules={{ required: true }}
            />
          ) : (
            <FormTriggerEvent
              control={control}
              name={objectType}
              schema={schema}
              defaultValue={form[objectType]}
              rules={{ required: true }}
            />
          )}
        </Stack>
      </ScrollArea>

      <StepperActions
        activeStep={activeStep}
        showBackBtn={false}
        nextBtnLabel={submitButtonText}
        disabledNextBtn={!isValid}
        onNext={handleNext}
      />
    </>
  );
};

export default SetupForm;
