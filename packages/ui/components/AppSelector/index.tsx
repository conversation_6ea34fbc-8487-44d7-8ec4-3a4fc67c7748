import React from 'react';
import { Box, Text, Group, Stack } from '@mantine/core';
import { DecaButton } from '../DecaButton';
import { CatalogNodeIcon } from '../Catalog';

interface AppSelectorProps {
  selectedApp?: {
    displayName: string;
    name: string;
  };
  onChangeClick: (e?: React.MouseEvent) => void;
  required?: boolean;
}

/**
 * AppSelector component used to display and select an application
 */
export const AppSelector: React.FC<AppSelectorProps> = ({
  selectedApp,
  onChangeClick,
  required = false,
}) => {
  return (
    <Stack gap='sm'>
      <Text component='label' size='md' fw={500} mb={5} display='block'>
        App{' '}
        {required && (
          <Text component='span' c='red'>
            *
          </Text>
        )}
      </Text>
      <Box
        p='xs'
        sx={theme => ({
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          border: `1px solid ${theme.colors.gray[3]}`,
          borderRadius: theme.radius.sm,
          backgroundColor: theme.white,
        })}>
        <Group>
          {selectedApp && (
            <>
              <CatalogNodeIcon name={selectedApp.name} />
              <Text>{selectedApp.displayName}</Text>
            </>
          )}
        </Group>
        <DecaButton variant='neutral' size='xs' onClick={onChangeClick}>
          Change
        </DecaButton>
      </Box>
    </Stack>
  );
};

export default AppSelector;
