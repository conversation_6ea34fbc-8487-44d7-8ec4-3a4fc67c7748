import React, { useState, useRef } from 'react';
import { Box, Group, Text } from '@mantine/core';
import { useCatalogStyles } from '../CatalogStyles';
import { useTranslate } from '@tolgee/react';
import { NodeItem, WorkflowItem, ICategory } from '../types';
import { useCatalogSchema } from '../useCatalogSchema';
import { CatalogTabs } from './CatalogTabs';
import { SearchBar } from './SearchBar';
import { SelectedItem } from './SelectedItem';
import { CategoryColumn } from './CategoryColumn';

interface CatalogProps {
  schema: Record<string, any>;
  disabledNodes?: string[];
  onSelect?: (item: WorkflowItem) => void;
}

export const Catalog: React.FC<CatalogProps> = ({ schema, onSelect, disabledNodes }) => {
  const { classes } = useCatalogStyles();
  const { t } = useTranslate('common');
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItem, setSelectedItem] = useState<WorkflowItem | null>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  const { nodes, categories, searchFields, searchPlaceholder } = useCatalogSchema(
    schema,
    disabledNodes
  );
  const catalogConfig = schema.properties?.catalog?.properties || {};

  const searchInFields = (item: any, query: string, fields: string[]) => {
    const lowerQuery = query.toLowerCase();
    return fields.some(field => {
      const value = item[field];
      return value && typeof value === 'string' && value.toLowerCase().includes(lowerQuery);
    });
  };

  const filteredNodes = nodes.filter(node => {
    const matchesSearch = !searchQuery || searchInFields(node, searchQuery, searchFields);
    const matchesTab = activeTab === 'all' || node.category === activeTab;
    return matchesSearch && matchesTab;
  });

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const addItemToWorkflow = (
    name: string,
    displayName: string,
    icon: string | React.ReactNode,
    id?: string
  ) => {
    const newId = `${name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`;
    const catalogItem = {
      id: id || newId,
      name: name,
      displayName: displayName,
      icon: icon,
    };
    setSelectedItem(catalogItem);
    onSelect?.(catalogItem);
  };

  const handleNodeClick = (node: NodeItem) => {
    if (node.disabled) {
      return;
    }

    addItemToWorkflow(node.name, node.displayName, node.icon, node.id);
  };

  const handleNodeDragStart = (e: React.DragEvent, node: NodeItem) => {
    e.dataTransfer.setData(
      'text/plain',
      JSON.stringify({
        name: node.name,
        displayName: node.displayName,
        icon: typeof node.icon === 'string' ? node.icon : 'svg',
      })
    );
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (dropZoneRef.current) {
      dropZoneRef.current.classList.add('active-drop');
    }
  };

  const handleDragLeave = () => {
    if (dropZoneRef.current) {
      dropZoneRef.current.classList.remove('active-drop');
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    handleDragLeave();

    try {
      const data = JSON.parse(e.dataTransfer.getData('text/plain'));
      const newId = `${data.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`;
      setSelectedItem({
        id: newId,
        name: data.name,
        displayName: data.displayName,
        icon:
          data.icon === 'svg'
            ? nodes.find(n => n.name === data.name)?.icon || data.name[0]
            : data.icon,
      });
    } catch (error) {
      console.error('Error adding item to workflow:', error);
    }
  };

  const handleRemoveWorkflowItem = () => {
    setSelectedItem(null);
  };

  return (
    <Box className={classes.root} data-testid='catalog'>
      {catalogConfig.enableSearch?.default && (
        <SearchBar
          placeholder={searchPlaceholder}
          value={searchQuery}
          onChange={handleSearchChange}
        />
      )}
      {catalogConfig.showCategories?.default && (
        <CatalogTabs categories={categories} activeTab={activeTab} onTabClick={handleTabClick} />
      )}
      <Group className={classes.catalogContent}>
        {(() => {
          // Get enabled categories except 'all'
          const enabledCategories: ICategory[] = Object.entries(categories)
            .filter(
              ([_, categoryData]) => categoryData.defaultEnabled && categoryData.key !== 'all'
            )
            .map(([_, categoryData]) => categoryData);

          // If we're showing 'all' tab, split into two columns
          if (activeTab === 'all') {
            const midIndex = Math.ceil(enabledCategories.length / 2);
            const leftCategories = enabledCategories.slice(0, midIndex);
            const rightCategories = enabledCategories.slice(midIndex);

            return (
              <>
                <CategoryColumn
                  categories={leftCategories}
                  nodes={filteredNodes}
                  selectedItemName={selectedItem?.name || null}
                  onNodeDragStart={handleNodeDragStart}
                  onNodeClick={handleNodeClick}
                />
                <CategoryColumn
                  categories={rightCategories}
                  nodes={filteredNodes}
                  selectedItemName={selectedItem?.name || null}
                  onNodeDragStart={handleNodeDragStart}
                  onNodeClick={handleNodeClick}
                />
              </>
            );
          }

          // For other tabs, show all categories in a single column
          return (
            <CategoryColumn
              categories={enabledCategories}
              nodes={filteredNodes}
              selectedItemName={selectedItem?.name || null}
              onNodeDragStart={handleNodeDragStart}
              onNodeClick={handleNodeClick}
            />
          );
        })()}
      </Group>
      {catalogConfig.showSelectedItem?.default && (
        <Box p='md'>
          <Text fw={500} mb='xs'>
            {t('catalog.selectedAppTool')}
          </Text>
          <Box
            className={classes.dropZone}
            id='workflow-drop-zone'
            ref={dropZoneRef}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}>
            <SelectedItem item={selectedItem} onRemove={handleRemoveWorkflowItem} />
          </Box>
        </Box>
      )}
    </Box>
  );
};
