import { useCatalogStyles } from '../CatalogStyles';
import { CustomIconProps } from '../../Icons';
import iconMapper from '../iconMapper';

interface CategoryIconProps extends CustomIconProps {
  name: string;
  size?: number;
  className?: string;
}

const CategoryIcon: React.FC<CategoryIconProps> = ({ name, size = 20, className, ...rest }) => {
  const { classes, cx } = useCatalogStyles();
  const icon = iconMapper[name as keyof typeof iconMapper];
  const IconComponent = icon?.component;
  const withPrimaryColor = !!icon?.withPrimaryColor;

  if (!IconComponent) {
    return null;
  }

  return (
    <IconComponent
      width={size}
      height={size}
      className={cx(
        {
          [classes.svgIconPrimary]: withPrimaryColor,
        },
        className
      )}
      {...rest}
    />
  );
};

export default CategoryIcon;
