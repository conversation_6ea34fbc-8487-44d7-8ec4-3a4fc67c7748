import React from 'react';
import { Stack, Text, Group, Box } from '@mantine/core';
import { useCatalogStyles } from '../CatalogStyles';
import { NodeItem, ICategory } from '../types';
import CatalogNodeIcon from './CatalogNodeIcon';

interface CategoryColumnProps {
  categories: ICategory[];
  nodes: NodeItem[];
  selectedItemName: string | null;
  onNodeDragStart: (e: React.DragEvent, node: NodeItem) => void;
  onNodeClick: (node: NodeItem) => void;
}

export const CategoryColumn: React.FC<CategoryColumnProps> = ({
  categories,
  nodes,
  selectedItemName,
  onNodeDragStart,
  onNodeClick,
}) => {
  const { classes, cx } = useCatalogStyles();

  const NodeItem: React.FC<{
    node: NodeItem;
    isSelected: boolean;
    disabled: boolean;
  }> = ({ node, isSelected, disabled }) => {
    return (
      <Group
        className={cx(classes.item, { added: isSelected, disabled })}
        draggable
        onDragStart={e => onNodeDragStart(e, node)}
        onClick={() => onNodeClick(node)}>
        <Box className={cx(classes.icon, 'purple')}>
          <CatalogNodeIcon name={node.name} />
        </Box>
        <Text className={classes.name}>{node.displayName}</Text>
      </Group>
    );
  };

  return (
    <Stack className={classes.catalogColumn}>
      {categories.map(category => {
        let categoryNodes: NodeItem[] = [];
        if (category.key === 'all') {
          categoryNodes = nodes;
        } else {
          categoryNodes = nodes.filter(node => node.category === category.key);
        }

        if (categoryNodes.length === 0) return null;

        return (
          <React.Fragment key={category.key}>
            <Text className={classes.catalogHeading}>
              {category.headingName || category.displayName}
            </Text>
            {categoryNodes.map((node, index) => (
              <NodeItem
                key={`${category}-${index}`}
                node={node}
                isSelected={selectedItemName === node.name}
                disabled={!!node.disabled}
              />
            ))}
          </React.Fragment>
        );
      })}
    </Stack>
  );
};
