import React from 'react';
import { Group, Box, Text, Button } from '@mantine/core';
import { useCatalogStyles } from '../CatalogStyles';
import { WorkflowItem } from '../types';

interface SelectedItemProps {
  item: WorkflowItem | null;
  onRemove: () => void;
}

export const SelectedItem: React.FC<SelectedItemProps> = ({ item, onRemove }) => {
  const { classes } = useCatalogStyles();

  if (!item) return null;

  return (
    <Group className={classes.workflowTag} gap='xs'>
      <Box className={classes.tagIcon}>
        {/* {typeof item.icon === 'string' ? (
          item.icon.startsWith('http') ? (
            <Box component='img' src={item.icon} alt={item.displayName} w={14} h={14} />
          ) : (
            <Text className={classes.xIcon}>{item.icon}</Text>
          )
        ) : (
          item.icon
        )} */}
      </Box>
      <Text>{item.displayName}</Text>
      <Button
        variant='subtle'
        p={0}
        className={classes.removeButton}
        aria-label={`Remove ${item.displayName}`}
        onClick={onRemove}>
        ✕
      </Button>
    </Group>
  );
};
