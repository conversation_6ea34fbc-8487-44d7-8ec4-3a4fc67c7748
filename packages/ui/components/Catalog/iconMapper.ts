import {
  CustomIconProps,
  IconCode,
  IconFilter,
  IconFormatter,
  IconFunction,
  IconGmail,
  IconGoogleCalendar,
  IconGoogleDrive,
  IconGoogleSheets,
  IconHttp,
  IconLooping,
  IconNewTrigger,
  IconOpenai,
  IconPath,
  IconSchedule,
  IconSlack,
  IconStorage,
  IconWait,
  IconZoom,
  IconHubspot,
  DecaIconPages,
  DecaIconTables,
  DecaIconKB,
  DecaIconCRM,
  DecaIconAIWidget,
  DecaIconLivechat,
  DecaIconChatbot,
  IconWebhook,
  IconGoogleDocs,
  IconGoogleSlides,
  IconAnthropic,
  IconAwsBedrock,
  IconGoogleAiStudio,
  IconGoogleMeet,
  IconGoogleVertexAi,
  IconLine,
  IconMicrosoftTeams,
  IconNotion,
  IconOutlook,
  IconPerplexity,
  IconSalesforce,
  IconZendesk,
} from '../Icons';

export type IconMapper = {
  component: React.ComponentType<CustomIconProps>;
  withPrimaryColor?: boolean;
};

const iconMapper: Record<string, IconMapper> = {
  // Build-in tools
  code: {
    component: IconCode,
    withPrimaryColor: true,
  },
  filter: {
    component: IconFilter,
    withPrimaryColor: true,
  },
  formatter: {
    component: IconFormatter,
    withPrimaryColor: true,
  },
  function: {
    component: IconFunction,
    withPrimaryColor: true,
  },
  http: {
    component: IconHttp,
    withPrimaryColor: true,
  },
  'new-trigger': {
    component: IconNewTrigger,
    withPrimaryColor: true,
  },
  path: {
    component: IconPath,
    withPrimaryColor: true,
  },
  storage: {
    component: IconStorage,
    withPrimaryColor: true,
  },
  wait: {
    component: IconWait,
    withPrimaryColor: true,
  },
  loop: {
    component: IconLooping,
    withPrimaryColor: true,
  },
  schedule: {
    component: IconSchedule,
    withPrimaryColor: true,
  },
  // External tools
  gmail: {
    component: IconGmail,
  },
  openai: {
    component: IconOpenai,
  },
  'zoom-meetings': {
    component: IconZoom,
  },
  'google-calendar': {
    component: IconGoogleCalendar,
  },
  'google-drive': {
    component: IconGoogleDrive,
  },
  hubspot: {
    component: IconHubspot,
  },
  slack: {
    component: IconSlack,
  },
  'google-slides': {
    component: IconGoogleSlides,
  },
  'google-sheets': {
    component: IconGoogleSheets,
  },
  'google-docs': {
    component: IconGoogleDocs,
  },
  webhook: {
    component: IconWebhook,
  },
  anthropic: {
    component: IconAnthropic,
  },
  'aws-bedrock': {
    component: IconAwsBedrock,
  },
  'google-ai-studio': {
    component: IconGoogleAiStudio,
  },
  'google-meet': {
    component: IconGoogleMeet,
  },
  'google-vertex-ai': {
    component: IconGoogleVertexAi,
  },
  line: {
    component: IconLine,
  },
  'microsoft-teams': {
    component: IconMicrosoftTeams,
  },
  notion: {
    component: IconNotion,
  },
  outlook: {
    component: IconOutlook,
  },
  perplexity: {
    component: IconPerplexity,
  },
  salesforce: {
    component: IconSalesforce,
  },
  zendesk: {
    component: IconZendesk,
  },
  // Deca services
  'deca-kb': {
    component: DecaIconKB,
  },
  'deca-ai-widgets': {
    component: DecaIconAIWidget,
  },
  chatbot: {
    component: DecaIconChatbot,
  },
  'deca-crm': {
    component: DecaIconCRM,
  },
  'deca-tables': {
    component: DecaIconTables,
  },
  'deca-livechat': {
    component: DecaIconLivechat,
  },
  pages: {
    component: DecaIconPages,
  },
  // Other
  'virtual-node-sub-path': {
    component: IconPath,
    withPrimaryColor: true,
  },
};

export default iconMapper;
