import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

export const useCatalogStyles = createStyles(theme => ({
  root: {
    backgroundColor: theme.white,
    borderRadius: rem(8),
    overflow: 'hidden',
    width: '100%',
    maxWidth: rem(450),
    margin: '0 auto',
    minWidth: rem(280),
  },
  searchBar: {
    padding: `${rem(8)} ${rem(0)}`,
  },
  searchContainer: {
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
  },
  searchIcon: {
    position: 'absolute',
    left: rem(10),
    color: theme.colors.gray[6],
  },
  searchInput: {
    width: '100%',
    overflow: 'hidden',
    borderRadius: rem(4),
    fontSize: theme.fontSizes.sm,
  },
  catalogTabs: {
    paddingBottom: rem(8),
    backgroundColor: theme.white,
    borderBottom: `${rem(1)} solid ${theme.colors.gray[2]}`,
    display: 'flex',
    marginBottom: rem(8),
  },
  tab: {
    padding: `${rem(2)} ${rem(8)}`,
    display: 'flex',
    cursor: 'pointer',
    border: `${rem(1)} solid ${theme.colors.decaBlue[6]}`,
    backgroundColor: theme.white,
    color: theme.colors.decaBlue[6],
    borderRadius: rem(4),
    transition: 'all 0.2s ease',

    '&:hover': {
      backgroundColor: theme.colors.gray[0],
    },

    '&.active': {
      backgroundColor: theme.colors.decaBlue[6],
      borderColor: theme.colors.decaBlue[6],
      color: theme.white,
    },
  },
  tabText: {
    fontSize: theme.fontSizes.sm,
  },
  tabIcon: {
    marginRight: rem(5),
    width: rem(14),
    height: rem(14),
    '& svg': {
      width: rem(14),
      height: rem(14),
      fill: theme.colors.decaBlue[6],
    },
  },
  catalogContent: {
    display: 'flex',
    height: 'auto',
    maxHeight: 'calc(100vh - 250px)',
    overflowY: 'auto',
    gap: rem(0),
    alignItems: 'flex-start',
  },
  catalogColumn: {
    flex: 1,
    padding: rem(16),
    gap: rem(0),
    overflowY: 'auto',
    borderRight: `${rem(1)} solid ${theme.colors.gray[2]}`,
    '&:last-child': {
      borderRight: 'none',
      paddingRight: rem(0),
    },
    '&:first-of-type': {
      paddingLeft: rem(0),
    },
  },
  catalogHeading: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray[6],
    marginBottom: rem(12),
    fontWeight: 500,
  },
  item: {
    display: 'flex',
    alignItems: 'center',
    padding: `${rem(4)} ${rem(8)}`,
    marginBottom: rem(4),
    borderRadius: rem(4),
    gap: rem(8),
    cursor: 'pointer',
    transition: 'all 0.2s ease',
    '&:hover': {
      backgroundColor: theme.colors.gray[0],
    },
    '&.active': {
      backgroundColor: theme.colors.gray[1],
    },
    '&.added': {
      backgroundColor: theme.colors.blue[0],
      borderLeft: `${rem(3)} solid ${theme.colors.blue[6]}`,
    },
    '&.disabled': {
      opacity: 0.5,
      cursor: 'not-allowed',
    },
  },
  name: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.decaGray[9],
    fontWeight: 500,
  },
  icon: {
    width: rem(20),
    height: rem(20),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: rem(6),
    '&.blue': {
      backgroundColor: theme.colors.blue[0],
      color: theme.colors.blue[6],
    },
    '&.circular-icon': {
      borderRadius: '50%',
      width: rem(28),
      height: rem(28),
      backgroundColor: theme.colors.blue[0],
      boxShadow: theme.shadows.sm,
      transition: 'all 0.2s ease',
      '&:hover': {
        transform: 'scale(1.1)',
        boxShadow: theme.shadows.md,
      },
    },
  },
  xIcon: {
    fontWeight: 'bold',
    fontSize: theme.fontSizes.xs,
  },
  mt4: {
    marginTop: rem(24),
  },
  visuallyHidden: {
    position: 'absolute',
    width: rem(1),
    height: rem(1),
    margin: rem(-1),
    padding: 0,
    overflow: 'hidden',
    clip: 'rect(0, 0, 0, 0)',
    border: 0,
  },
  dropZone: {
    minHeight: '60px',
    border: '2px dashed var(--mantine-color-gray-4)',
    borderRadius: 'var(--mantine-radius-md)',
    padding: 'var(--mantine-spacing-sm)',
    '&.active-drop': {
      borderColor: 'var(--mantine-color-blue-5)',
      backgroundColor: 'var(--mantine-color-blue-0)',
    },
  },
  workflowTag: {
    display: 'inline-flex',
    alignItems: 'center',
    backgroundColor: 'var(--mantine-color-gray-1)',
    borderRadius: 'var(--mantine-radius-sm)',
    padding: '4px 8px',
  },
  tagIcon: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  removeButton: {
    color: 'var(--mantine-color-gray-6)',
    '&:hover': {
      color: 'var(--mantine-color-red-6)',
      backgroundColor: 'transparent',
    },
  },
  svgIconPrimary: {
    color: theme.colors.decaNavy[4],
    path: {
      fill: 'currentcolor',
    },
  },
}));
