import { useCallback, useEffect, useMemo, useState } from 'react';
import { with<PERSON><PERSON><PERSON>, withReactHookForm } from '../../hoc';
import { ChooseCredential } from './ChooseCredential';
import get from 'lodash/get';
import { CredentialModal } from '../../Credential/CredentialModal';
import { useDisclosure } from '@mantine/hooks';
import { ICredentialPayload, CardStatus, ICredential } from '../../Credential/type';

interface SchemaCredentialProps {
  schema: Record<string, any>;
  value?: string;
  credentials: ICredential[];
  isLoading: boolean;
  createCredential: (credential: ICredentialPayload) => Promise<ICredential | null>;
  onChange: (value: string) => void;
}

export const SchemaCredential = (props: SchemaCredentialProps) => {
  const { schema, credentials = [], isLoading, createCredential, value, onChange } = props;
  const [modalOpened, { open: openModal, close: closeModal }] = useDisclosure(false);
  const credentialType = get(schema, 'credentialTypes', []);

  const credentialOptions = useMemo(() => {
    if (credentialType.length > 0) {
      return credentials
        .filter(cred => credentialType.includes(cred.typeName))
        .map(cred => ({
          value: cred.id,
          label: cred.name,
          description: cred.description,
          type: cred.typeName,
          provider: cred.provider,
        }));
    }
    return credentials.map(cred => ({
      value: cred.id,
      label: cred.name,
      description: cred.description,
      type: cred.typeName,
      provider: cred.provider,
    }));
  }, [credentials, credentialType]);

  const [activeValue, setActiveValue] = useState<string>('');

  const handleChange = useCallback(
    (value: string) => {
      setActiveValue(value);
      onChange?.(value);
    },
    [onChange]
  );

  const handleSubmit = useCallback(
    async (values: ICredentialPayload, setStatus?: (status: CardStatus) => void) => {
      try {
        const credential = await createCredential(values);
        if (!credential) {
          throw new Error('Failed to create credential');
        }
        setStatus?.({
          status: 'success',
          message: 'Credential created successfully',
        });
        closeModal();
        onChange?.(credential.id);
      } catch (error) {
        setStatus?.({
          status: 'error',
          message: error instanceof Error ? error.message : 'An unknown error occurred',
        });
      }
    },
    [closeModal, createCredential, onChange]
  );

  useEffect(() => {
    setActiveValue(value || '');
  }, [value]);

  return (
    <>
      <ChooseCredential
        value={activeValue}
        onChange={handleChange}
        options={credentialOptions}
        isLoading={isLoading}
        onConnectNewCredential={openModal}
      />
      <CredentialModal
        opened={modalOpened}
        schema={schema}
        onClose={closeModal}
        onSubmit={handleSubmit}
      />
    </>
  );
};

export const SchemaCredentialWithContainer = withContainer(SchemaCredential);
export const FormCredential = withReactHookForm(SchemaCredential);
