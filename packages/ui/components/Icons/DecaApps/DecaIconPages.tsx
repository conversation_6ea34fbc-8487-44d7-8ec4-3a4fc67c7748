import { CustomIconProps } from '..';

const DecaIconPages = (props: CustomIconProps) => {
  return (
    <svg
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <rect x='2' y='13' width='30.8576' height='23.9997' rx='1.88889' fill='#6DAFF8' />
      <path
        d='M2 14.8889C2 13.8457 2.84568 13 3.88889 13H30.9687C32.0119 13 32.8576 13.8457 32.8576 14.8889V16H2V14.8889Z'
        fill='#3539BC'
      />
      <ellipse cx='3.62' cy='14.5724' rx='0.588753' ry='0.572383' fill='white' />
      <ellipse cx='7.29578' cy='14.5724' rx='0.588753' ry='0.572383' fill='white' />
      <ellipse cx='5.38563' cy='14.5724' rx='0.588753' ry='0.572383' fill='#E4007F' />
      <path
        d='M24.6342 26.6107H10.4482C10.0665 26.6107 9.76965 26.4068 9.76965 26.1446V21.4103C9.76965 21.148 10.0665 20.9441 10.4482 20.9441H24.6342C25.0159 20.9441 25.3127 21.148 25.3127 21.4103V26.1591C25.2915 26.4068 24.9947 26.6107 24.6342 26.6107Z'
        fill='#BBD9FC'
      />
      <rect x='9.82422' y='28.5' width='15.4288' height='1.99997' rx='0.999987' fill='#BBD9FC' />
      <path
        d='M35.4996 8.05019L36.0961 9.76764C36.1114 9.80481 36.142 9.84198 36.1802 9.84942L37.9468 10.4293C38.0156 10.4516 38.0156 10.5483 37.9468 10.5706L36.1802 11.1505C36.142 11.1654 36.1037 11.1951 36.0961 11.2323L35.4996 12.9497C35.4767 13.0167 35.3772 13.0167 35.3543 12.9497L34.7578 11.2323C34.7425 11.1951 34.7119 11.158 34.6737 11.1505L32.9071 10.5706C32.8383 10.5483 32.8383 10.4516 32.9071 10.4293L34.6737 9.84942C34.7119 9.83455 34.7501 9.80481 34.7578 9.76764L35.3543 8.05019C35.3772 7.98327 35.4767 7.98327 35.4996 8.05019Z'
        fill='#E4007F'
      />
      <path
        d='M30.2146 3.08645L31.0784 5.34226L33.3987 6.18202C33.5172 6.22318 33.5172 6.38784 33.3987 6.42901L31.0784 7.26876L30.2146 9.52458C30.1722 9.63984 30.0029 9.63984 29.9605 9.52458L29.0968 7.26876L26.7764 6.42901C26.6579 6.38784 26.6579 6.22318 26.7764 6.18202L29.0968 5.34226L29.9605 3.08645C30.0029 2.97118 30.1722 2.97118 30.2146 3.08645Z'
        fill='#E4007F'
      />
    </svg>
  );
};

export default DecaIconPages;
