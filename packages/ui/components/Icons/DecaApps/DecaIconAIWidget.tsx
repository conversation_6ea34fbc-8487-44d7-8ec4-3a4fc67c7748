import { CustomIconProps } from '..';

const DecaIconAIWidget = (props: CustomIconProps) => {
  return (
    <svg
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        d='M8.46094 24.4462C8.46094 23.9847 8.64555 23.5385 8.97632 23.2078C9.30709 22.877 9.74555 22.6924 10.2148 22.6924H17.2071C17.6686 22.6924 18.1148 22.877 18.4455 23.2078C18.7763 23.5385 18.9609 23.977 18.9609 24.4462V31.4385C18.9609 31.9001 18.7763 32.3462 18.4455 32.677C18.1148 33.0078 17.6763 33.1924 17.2071 33.1924H10.2148C9.75324 33.1924 9.30709 33.0078 8.97632 32.677C8.64555 32.3462 8.46094 31.9078 8.46094 31.4385V24.4462Z'
        fill='#7EC1FC'
      />
      <path
        d='M20.9062 24.4462C20.9062 23.9847 21.0909 23.5385 21.4216 23.2078C21.7524 22.877 22.1909 22.6924 22.6601 22.6924H29.6524C30.1139 22.6924 30.5601 22.877 30.8909 23.2078C31.2216 23.5385 31.4063 23.977 31.4063 24.4462V31.4385C31.4063 31.9001 31.2216 32.3462 30.8909 32.677C30.5601 33.0078 30.1216 33.1924 29.6524 33.1924H22.6601C22.1986 33.1924 21.7524 33.0078 21.4216 32.677C21.0909 32.3462 20.9062 31.9078 20.9062 31.4385V24.4462Z'
        fill='#3539BC'
      />
      <path
        d='M8.46094 11.9316C8.46094 11.47 8.64555 11.0239 8.97632 10.6931C9.30709 10.3623 9.74555 10.1777 10.2148 10.1777H17.2071C17.6686 10.1777 18.1148 10.3623 18.4455 10.6931C18.7763 11.0239 18.9609 11.4623 18.9609 11.9316V18.9239C18.9609 19.3854 18.7763 19.8316 18.4455 20.1624C18.1148 20.4931 17.6763 20.6777 17.2071 20.6777H10.2148C9.75324 20.6777 9.30709 20.4931 8.97632 20.1624C8.64555 19.8316 8.46094 19.3931 8.46094 18.9239V11.9316Z'
        fill='#3539BC'
      />
      <path
        d='M26.3921 9.26133L27.7921 13.369C27.8074 13.4075 27.8382 13.4383 27.8767 13.4536L31.9844 14.8536C32.1844 14.9229 32.1844 15.1998 31.9844 15.269L27.8767 16.669C27.8382 16.6844 27.8074 16.7152 27.7921 16.7536L26.3921 20.8613C26.3228 21.0613 26.0459 21.0613 25.9767 20.8613L24.5767 16.7536C24.5613 16.7152 24.5305 16.6844 24.4921 16.669L20.3844 15.269C20.1844 15.1998 20.1844 14.9229 20.3844 14.8536L24.4921 13.4536C24.5305 13.4383 24.5613 13.4075 24.5767 13.369L25.9767 9.26133C26.0459 9.06133 26.3228 9.06133 26.3921 9.26133Z'
        fill='#E03381'
      />
      <path
        d='M32.7453 4.47707L33.4145 6.68477C33.4299 6.73092 33.4607 6.76169 33.5069 6.77707L35.7146 7.4463C35.8376 7.48477 35.8376 7.654 35.7146 7.68477L33.5069 8.354C33.4607 8.36938 33.4299 8.40015 33.4145 8.4463L32.7453 10.654C32.7069 10.7771 32.5376 10.7771 32.5069 10.654L31.8376 8.4463C31.8223 8.40015 31.7915 8.36938 31.7453 8.354L29.5376 7.68477C29.4145 7.6463 29.4145 7.47707 29.5376 7.4463L31.7453 6.77707C31.7915 6.76169 31.8223 6.73092 31.8376 6.68477L32.5069 4.47707C32.5453 4.354 32.7146 4.354 32.7453 4.47707Z'
        fill='#E03381'
      />
    </svg>
  );
};

export default DecaIconAIWidget;
