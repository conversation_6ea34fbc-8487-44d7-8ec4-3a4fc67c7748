import { CustomIconProps } from '..';

const DecaIconTables = (props: CustomIconProps) => {
  return (
    <svg
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        d='M6.80078 15.4C6.80078 12.9699 8.77073 11 11.2008 11H28.8008C31.2308 11 33.2008 12.9699 33.2008 15.4V28.6C33.2008 31.0301 31.2308 33 28.8008 33H11.2008C8.77073 33 6.80078 31.0301 6.80078 28.6V15.4Z'
        fill='#3539BC'
      />
      <path
        d='M6.80078 15.4C6.80078 12.9699 8.77073 11 11.2008 11H28.8008C31.2308 11 33.2008 12.9699 33.2008 15.4V17.6H6.80078V15.4Z'
        fill='#7EC1FC'
      />
      <path
        d='M6.80078 17.5996H33.2008'
        stroke='white'
        strokeWidth='0.846154'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M6.80078 25.2998H33.2008'
        stroke='white'
        strokeWidth='0.846154'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M12.3008 17.5996L12.3008 32.9996'
        stroke='white'
        strokeWidth='0.846154'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M20 17.5996L20 32.9996'
        stroke='white'
        strokeWidth='0.846154'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M27.6992 17.5996L27.6992 32.9996'
        stroke='white'
        strokeWidth='0.846154'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M37.5706 8.05019L38.1506 9.76766C38.1654 9.80483 38.1952 9.84201 38.2323 9.84944L39.9498 10.4294C40.0167 10.4517 40.0167 10.5483 39.9498 10.5706L38.2323 11.1506C38.1952 11.1654 38.158 11.1952 38.1506 11.2323L37.5706 12.9498C37.5483 13.0167 37.4517 13.0167 37.4294 12.9498L36.8495 11.2323C36.8346 11.1952 36.8048 11.158 36.7677 11.1506L35.0502 10.5706C34.9833 10.5483 34.9833 10.4517 35.0502 10.4294L36.7677 9.84944C36.8048 9.83457 36.842 9.80483 36.8495 9.76766L37.4294 8.05019C37.4517 7.98327 37.5483 7.98327 37.5706 8.05019Z'
        fill='#E03381'
      />
      <path
        d='M31.6308 1.09153L32.5199 3.48007L34.9085 4.36924C35.0305 4.41283 35.0305 4.58717 34.9085 4.63076L32.5199 5.51993L31.6308 7.90847C31.5872 8.03051 31.4128 8.03051 31.3692 7.90847L30.4801 5.51993L28.0915 4.63076C27.9695 4.58717 27.9695 4.41283 28.0915 4.36924L30.4801 3.48007L31.3692 1.09153C31.4128 0.969489 31.5872 0.969489 31.6308 1.09153Z'
        fill='#E03381'
      />
    </svg>
  );
};

export default DecaIconTables;
