import { CustomIconProps } from '..';

const DecaIconKB = (props: CustomIconProps) => {
  return (
    <svg
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        d='M3 15.2577V29.6782C3 30.1402 3.49664 30.4659 3.98562 30.3295L6.82787 29.557C9.80765 28.7466 12.9861 28.7239 15.9811 29.4964L20.0153 30.5416C20.1528 30.5795 20.3056 30.5795 20.4431 30.5416L23.9807 29.5873C27.0369 28.7617 30.2993 28.7693 33.3555 29.6024L36.0144 30.3295C36.5034 30.4659 37 30.1326 37 29.6782V15.2501C37 14.9547 36.7861 14.6896 36.4652 14.5987L33.3784 13.7202C30.4827 12.9022 27.3883 12.8719 24.4697 13.6369L20.4355 14.6896C20.298 14.7275 20.1452 14.7275 20.0076 14.6896L15.9964 13.6217C13.1618 12.8643 10.1515 12.8643 7.31686 13.6141L3.55011 14.6063C3.22921 14.6896 3 14.9623 3 15.2652V15.2577Z'
        fill='#3539BC'
      />
      <path
        d='M4.9228 11.8033V27.3371C4.9228 27.8294 5.3583 28.1854 5.79381 28.0415L8.30752 27.2084C10.9511 26.3374 13.7704 26.3147 16.4217 27.1478L19.9974 28.2687C20.1196 28.3066 20.2572 28.3066 20.3794 28.2687L23.5196 27.2387C26.232 26.345 29.1201 26.3526 31.8325 27.2538L34.1857 28.0415C34.6212 28.1854 35.0567 27.8294 35.0567 27.3371V11.7957C35.0567 11.4776 34.8657 11.1898 34.583 11.0913L31.8477 10.1522C29.2806 9.26605 26.5376 9.23576 23.9552 10.0613L20.3794 11.1974C20.2572 11.2352 20.1196 11.2352 19.9974 11.1974L16.437 10.0462C13.9233 9.23576 11.2567 9.22818 8.74302 10.031L5.40415 11.0989C5.11381 11.1898 4.91516 11.4776 4.91516 11.8033H4.9228Z'
        fill='#6DAFF8'
      />
      <path
        d='M19.9922 12.123V27.6266'
        stroke='white'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M7.81549 14.9854C7.81549 14.9854 12.7054 11.5999 17.2514 14.9551'
        stroke='white'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M7.81549 18.9541C7.81549 18.9541 12.7054 15.5686 17.2514 18.9238'
        stroke='white'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M7.81549 22.9219C7.81549 22.9219 12.7054 19.5364 17.2514 22.8916'
        stroke='white'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M32.0473 14.9992C32.0473 14.9992 27.6999 11.697 23.5664 15.1052'
        stroke='white'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M31.9921 18.9611C31.9921 18.9611 27.6447 15.659 23.5112 19.0672'
        stroke='white'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M31.9252 22.923C31.9252 22.923 27.5778 19.6209 23.4443 23.0291'
        stroke='white'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M11.6447 2.10225L12.3782 4.29108C12.3934 4.32894 12.424 4.35924 12.4622 4.37439L14.6703 5.10147C14.8078 5.14691 14.8078 5.33626 14.6703 5.3817L12.4622 6.10879C12.424 6.12394 12.3934 6.15423 12.3782 6.1921L11.6447 8.38093C11.5988 8.51726 11.4078 8.51726 11.362 8.38093L10.6285 6.1921C10.6132 6.15423 10.5827 6.12394 10.5445 6.10879L8.33636 5.3817C8.19883 5.33626 8.19883 5.14691 8.33636 5.10147L10.5445 4.37439C10.5827 4.35924 10.6132 4.32894 10.6285 4.29108L11.362 2.10225C11.4078 1.96592 11.5988 1.96592 11.6447 2.10225Z'
        fill='#E4007F'
      />
      <path
        d='M33.2017 31.5722L33.9504 33.7761C33.9657 33.814 33.9963 33.8519 34.0345 33.8594L36.2578 34.6017C36.3877 34.6471 36.3877 34.8289 36.2578 34.8743L34.0345 35.6166C33.9963 35.6317 33.9581 35.662 33.9504 35.6999L33.2017 37.9039C33.1558 38.0326 32.9724 38.0326 32.9266 37.9039L32.1778 35.6999C32.1626 35.662 32.132 35.6241 32.0938 35.6166L29.8704 34.8743C29.7405 34.8289 29.7405 34.6471 29.8704 34.6017L32.0938 33.8594C32.132 33.8443 32.1702 33.814 32.1778 33.7761L32.9266 31.5722C32.9724 31.4434 33.1558 31.4434 33.2017 31.5722Z'
        fill='#E4007F'
      />
    </svg>
  );
};

export default DecaIconKB;
