import { CustomIconProps } from '..';

const DecaIconCRM = (props: CustomIconProps) => {
  return (
    <svg
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        d='M29.6745 19.1787C29.4866 19.0662 28.8329 18.8012 28.7512 18.7048C28.6532 18.5844 28.5225 17.8134 28.4408 17.5725C27.4521 14.609 24.1429 12.9868 21.1034 13.822C16.1845 8.61791 7.49896 11.7018 6.95969 18.5603C4.06722 19.6123 2 22.3429 2 25.5472C2 29.6671 5.39906 33 9.58251 33C9.79495 33 9.99923 32.9839 10.2035 32.9679L10.3587 33H26.3735C33.6292 32.4459 35.9579 22.9613 29.6664 19.1787H29.6745Z'
        fill='#3539BC'
      />
      <path
        d='M35.329 14.2884L35.9663 16.1436C35.9826 16.1837 36.0153 16.2239 36.0562 16.2319L37.9436 16.8583C38.0172 16.8824 38.0172 16.9868 37.9436 17.0109L36.0562 17.6374C36.0153 17.6534 35.9745 17.6855 35.9663 17.7257L35.329 19.5809C35.3045 19.6531 35.1982 19.6531 35.1737 19.5809L34.5364 17.7257C34.5201 17.6855 34.4874 17.6454 34.4465 17.6374L32.5591 17.0109C32.4855 16.9868 32.4855 16.8824 32.5591 16.8583L34.4465 16.2319C34.4874 16.2159 34.5282 16.1837 34.5364 16.1436L35.1737 14.2884C35.1982 14.2162 35.3045 14.2162 35.329 14.2884Z'
        fill='#E4007F'
      />
      <path
        d='M15.2498 25.3145C15.2498 25.3145 15.8136 25.3145 15.8136 24.7604C15.8136 24.2063 15.2498 22.5438 12.9947 22.5438C10.7396 22.5438 10.1758 24.2063 10.1758 24.7604C10.1758 25.3145 10.7396 25.3145 10.7396 25.3145H15.258H15.2498ZM12.9947 21.9817C12.5453 21.9817 12.1122 21.805 11.7936 21.4918C11.4749 21.1786 11.2952 20.7529 11.2952 20.3112C11.2952 19.8695 11.4749 19.4439 11.7936 19.1307C12.1122 18.8175 12.5453 18.6408 12.9947 18.6408C13.4441 18.6408 13.8771 18.8175 14.1958 19.1307C14.5145 19.4439 14.6942 19.8695 14.6942 20.3112C14.6942 20.7529 14.5145 21.1786 14.1958 21.4918C13.8771 21.805 13.4441 21.9817 12.9947 21.9817Z'
        fill='white'
      />
      <path
        d='M18.25 24.2703H24.9337'
        stroke='#6DAFF8'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M18.25 20.1344H24.9337'
        stroke='#E4007F'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M10.1719 28.4139H24.9365'
        stroke='#6DAFF8'
        strokeWidth='0.769231'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M30.157 7.08433L30.9905 9.28482L33.2293 10.104C33.3437 10.1441 33.3437 10.3048 33.2293 10.3449L30.9905 11.1641L30.157 13.3646C30.1162 13.477 29.9528 13.477 29.9119 13.3646L29.0785 11.1641L26.8397 10.3449C26.7253 10.3048 26.7253 10.1441 26.8397 10.104L29.0785 9.28482L29.9119 7.08433C29.9528 6.97189 30.1162 6.97189 30.157 7.08433Z'
        fill='#E4007F'
      />
    </svg>
  );
};

export default DecaIconCRM;
