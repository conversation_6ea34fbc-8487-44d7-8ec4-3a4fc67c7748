import { CustomIconProps } from '..';

const DecaIconLivechat = (props: CustomIconProps) => {
  return (
    <svg
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        d='M9.03137 24.0929C9.03137 30.1222 14.568 35 21.3975 35C22.9918 35 24.5782 34.7361 26.0774 34.1924L31.146 35C31.8757 35 32.2089 34.8561 32.2089 34.1124L31.6219 30.2181C32.9942 28.4749 33.7398 26.3239 33.7557 24.0929C33.7557 18.0636 28.227 13.1858 21.3895 13.1858C14.56 13.1858 9.02344 18.0636 9.02344 24.0929H9.03137Z'
        fill='#3539BC'
      />
      <path
        d='M25.1444 24.2935C25.9987 24.2935 26.6912 23.5954 26.6912 22.7342C26.6912 21.873 25.9987 21.1749 25.1444 21.1749C24.2902 21.1749 23.5977 21.873 23.5977 22.7342C23.5977 23.5954 24.2902 24.2935 25.1444 24.2935Z'
        fill='white'
      />
      <path
        d='M17.5663 24.2935C18.4205 24.2935 19.113 23.5954 19.113 22.7342C19.113 21.873 18.4205 21.1749 17.5663 21.1749C16.712 21.1749 16.0195 21.873 16.0195 22.7342C16.0195 23.5954 16.712 24.2935 17.5663 24.2935Z'
        fill='white'
      />
      <path
        d='M23.1908 28.3706C22.6752 28.6425 22.0961 28.7944 21.4774 28.7944C20.7953 28.7944 20.1528 28.6105 19.6055 28.2827'
        stroke='white'
        strokeWidth='1.15385'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M7.17172 24.0923C7.17172 22.3971 7.54453 20.7898 8.21875 19.3105C7.81422 19.0786 7.52866 18.6548 7.52866 18.151C7.52866 17.4073 8.1315 16.7916 8.87712 16.7916C9.13887 16.7916 9.36097 16.8875 9.5672 17.0155C11.923 13.8409 15.8177 11.6739 20.2834 11.362C18.3401 9.69076 15.6908 8.65923 12.7638 8.65923C6.81478 8.65923 2 12.9053 2 18.159C2.01586 20.1021 2.65837 21.9733 3.85611 23.4926L3.34846 26.8831C3.34846 27.5308 3.64194 27.6587 4.27651 27.6587L7.60005 27.131C7.33829 26.1634 7.17965 25.1558 7.17965 24.1083L7.17172 24.0923Z'
        fill='#6DAFF8'
      />
      <path
        d='M29.7982 5.10195L30.6311 7.63682C30.6469 7.69279 30.6945 7.73278 30.7421 7.74877L33.2566 8.58839C33.3914 8.63637 33.3914 8.82829 33.2566 8.87626L30.7421 9.71589C30.6866 9.73188 30.6469 9.77986 30.6311 9.82784L29.7982 12.3627C29.7506 12.4986 29.5602 12.4986 29.5126 12.3627L28.6798 9.82784C28.6639 9.77186 28.6163 9.73188 28.5687 9.71589L26.0543 8.87626C25.9194 8.82829 25.9194 8.63637 26.0543 8.58839L28.5687 7.74877C28.6243 7.73278 28.6639 7.6848 28.6798 7.63682L29.5126 5.10195C29.5602 4.96602 29.7506 4.96602 29.7982 5.10195Z'
        fill='#E4007F'
      />
      <path
        d='M35.401 13.4269L36.0594 15.2341C36.0594 15.2341 36.107 15.306 36.1467 15.322L37.9393 15.9857C38.0186 16.0177 38.0186 16.1217 37.9393 16.1537L36.1467 16.8174C36.1467 16.8174 36.0753 16.8653 36.0594 16.9053L35.401 18.7125C35.3693 18.7925 35.2662 18.7925 35.2345 18.7125L34.5761 16.9053C34.5761 16.9053 34.5285 16.8334 34.4889 16.8174L32.6962 16.1537C32.6169 16.1217 32.6169 16.0177 32.6962 15.9857L34.4889 15.322C34.4889 15.322 34.5602 15.2741 34.5761 15.2341L35.2345 13.4269C35.2662 13.3469 35.3693 13.3469 35.401 13.4269Z'
        fill='#E4007F'
      />
    </svg>
  );
};

export default DecaIconLivechat;
