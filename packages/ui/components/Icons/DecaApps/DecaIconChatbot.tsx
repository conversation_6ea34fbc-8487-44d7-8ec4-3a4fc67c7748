import { CustomIconProps } from '..';

const DecaIconChatbot = (props: CustomIconProps) => {
  return (
    <svg
      width='40'
      height='40'
      viewBox='0 0 40 40'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path d='M20.9305 7.68449H19.3438V10.7762H20.9305V7.68449Z' fill='#E4007F' />
      <path
        d='M20.1372 8.91679C21.2982 8.91679 22.2393 7.94746 22.2393 6.75174C22.2393 5.55602 21.2982 4.5867 20.1372 4.5867C18.9763 4.5867 18.0352 5.55602 18.0352 6.75174C18.0352 7.94746 18.9763 8.91679 20.1372 8.91679Z'
        fill='#E4007F'
      />
      <path
        d='M29.8251 10.2974H10.4646C8.92868 10.2974 7.68359 11.5798 7.68359 13.1617V27.7273C7.68359 29.3092 8.92868 30.5915 10.4646 30.5915H29.8251C31.361 30.5915 32.6061 29.3092 32.6061 27.7273V13.1617C32.6061 11.5798 31.361 10.2974 29.8251 10.2974Z'
        fill='#3539BC'
      />
      <path
        d='M29.6546 34.921H23.1602V33.6826H29.6546C32.6237 33.6826 35.0366 31.1974 35.0366 28.1394V20.6755H36.2389V28.1394C36.2389 31.8798 33.2862 34.921 29.6546 34.921Z'
        fill='#6DAFF8'
      />
      <path
        d='M16.9297 34.3488C16.9297 35.2587 17.6495 36 18.5328 36H21.7391C22.6225 36 23.3423 35.2587 23.3423 34.3488C23.3423 33.439 22.6225 32.6977 21.7391 32.6977H18.5328C17.6495 32.6977 16.9297 33.439 16.9297 34.3488Z'
        fill='#6DAFF8'
      />
      <path
        d='M5.57437 23.7345C5.57437 24.1557 5.19813 24.4843 4.79734 24.3916C3.19418 24.0294 2 22.5551 2 20.7944C2 19.0338 3.19418 17.5679 4.79734 17.1973C5.19813 17.1046 5.57437 17.4331 5.57437 17.8543V23.7261V23.7345Z'
        fill='#6DAFF8'
      />
      <path
        d='M34.1641 23.7345C34.1641 24.1557 34.5403 24.4843 34.9411 24.3916C36.5443 24.0294 37.7384 22.5551 37.7384 20.7944C37.7384 19.0338 36.5443 17.5679 34.9411 17.1973C34.5403 17.1046 34.1641 17.4331 34.1641 17.8543V23.7261V23.7345Z'
        fill='#6DAFF8'
      />
      <path
        d='M14.608 21.9144C15.6019 21.9144 16.4075 21.0846 16.4075 20.0611C16.4075 19.0375 15.6019 18.2077 14.608 18.2077C13.6142 18.2077 12.8086 19.0375 12.8086 20.0611C12.8086 21.0846 13.6142 21.9144 14.608 21.9144Z'
        fill='white'
      />
      <path
        d='M25.4323 21.9144C26.4261 21.9144 27.2317 21.0846 27.2317 20.0611C27.2317 19.0375 26.4261 18.2077 25.4323 18.2077C24.4385 18.2077 23.6328 19.0375 23.6328 20.0611C23.6328 21.0846 24.4385 21.9144 25.4323 21.9144Z'
        fill='white'
      />
      <path
        d='M22.2234 23.7833C21.6836 24.0697 21.0701 24.2382 20.424 24.2382C19.7042 24.2382 19.0335 24.036 18.4609 23.6906'
        stroke='white'
        strokeWidth='1.15385'
        strokeLinecap='round'
        strokeLinejoin='round'
      />
      <path
        d='M34.7972 4.139L35.5661 6.40514C35.5824 6.45568 35.6151 6.48938 35.6642 6.50623L37.8645 7.29811C38.0444 7.36551 38.0444 7.61824 37.8645 7.68563L35.6642 8.47752C35.6151 8.49436 35.5824 8.52806 35.5661 8.57861L34.7972 10.8447C34.7318 11.0301 34.4864 11.0301 34.421 10.8447L33.6521 8.57861C33.6357 8.52806 33.603 8.49436 33.554 8.47752L31.3537 7.68563C31.1738 7.61824 31.1738 7.36551 31.3537 7.29811L33.554 6.50623C33.603 6.48938 33.6357 6.45568 33.6521 6.40514L34.421 4.139C34.4864 3.95367 34.7318 3.95367 34.7972 4.139Z'
        fill='#E4007F'
      />
    </svg>
  );
};

export default DecaIconChatbot;
