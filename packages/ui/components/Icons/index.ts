import { CSSProperties } from 'react';

import IconPrivacy from './IconPrivacy';
import IconMediumResize from './IconMediumResize';
import IconShortResize from './IconShortResize';
import IconStroke from './IconStroke';
import IconFileTypePdf from './IconFileTypePdf';
import IconFileTypeJpg from './IconFileTypeJpg';
import IconFileTypePng from './IconFileTypePng';
import IconFileTypePpt from './IconFileTypePptx';
import IconGif from './IconGif';
import IconFileTypeXls from './IconFileTypeXls';
import IconClipboardTypography from './IconClipboardTypography';
import IconFolderEmpty from './IconFolderEmpty';
import DecaIconMd from './DecaIconMd';
import DecaIconPdf from './DecaIconPdf';
import DecaIconDocx from './DecaIconDocx';
import DecaIconTxt from './DecaIconTxt';
import DecaIconCsv from './DecaIconCsv';
import DecaIconDoc from './DecaIconDoc';
import IconLine from './IconLine';
import IconNoResultFound from './IconNoResultFound';
import IconNumberInput from './IconNumberInput';
import IconErrorImage from './IconErrorImage';
import IconErrorVideo from './IconErrorVideo';
import IconErrorDoc from './IconErrorDoc';
import DecaIconDebounceDot from './DecaIconDebounceDot';

export interface CustomIconProps {
  className?: string;
  style?: CSSProperties;
  fill?: string;
  width?: string | number;
  height?: string | number;
  onClick?: () => void;
}

export {
  IconPrivacy,
  IconMediumResize,
  IconShortResize,
  IconStroke,
  IconFileTypePdf,
  IconFileTypeJpg,
  IconFileTypePng,
  IconFileTypePpt,
  IconGif,
  IconFileTypeXls,
  IconClipboardTypography,
  IconFolderEmpty,
  DecaIconPdf,
  DecaIconMd,
  DecaIconDoc,
  DecaIconDocx,
  DecaIconCsv,
  DecaIconTxt,
  IconLine,
  IconNoResultFound,
  IconNumberInput,
  IconErrorImage,
  IconErrorVideo,
  IconErrorDoc,
  DecaIconDebounceDot,
};

export * from './BuildInTools';
export * from './ExternalTools';
export * from './DecaApps';
