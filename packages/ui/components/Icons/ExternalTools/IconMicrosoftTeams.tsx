import { CustomIconProps } from '..';

const IconMicrosoftTeams = (props: CustomIconProps) => (
  <svg
    width='24'
    height='24'
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}>
    <path
      d='M20.166 8.61149C21.5573 8.61149 22.6891 7.47653 22.6891 6.08189C22.5506 2.72597 17.7811 2.72693 17.6429 6.08189C17.6429 7.47653 18.775 8.61149 20.166 8.61149Z'
      fill='#464EB8'
    />
    <path
      d='M21.8299 9.29042H17.3263H17.3177C17.3165 9.29042 17.317 9.29042 17.3155 9.29042C17.046 9.29042 13.5223 9.29042 12.3864 9.29042V8.40722C12.5746 8.44058 12.7716 8.4629 12.9785 8.47154C13.0008 8.47178 13.0231 8.4701 13.0454 8.46986C13.1314 8.46842 13.2166 8.46434 13.3006 8.45714C13.3294 8.4545 13.3579 8.4521 13.3862 8.44874C13.483 8.43794 13.5785 8.42378 13.6726 8.4053C13.6783 8.4041 13.6846 8.40338 13.6903 8.40242C14.1362 8.31146 14.5567 8.13362 14.9304 7.88162C15.276 7.64858 15.5753 7.3529 15.8167 7.01402C15.8791 6.93218 15.936 6.84674 15.989 6.75938C15.9941 6.75074 15.9996 6.74258 16.0046 6.7337C16.0454 6.66482 16.0814 6.59354 16.1165 6.52154C16.1318 6.49034 16.1477 6.45914 16.1621 6.42746C16.199 6.34466 16.2331 6.26042 16.2631 6.17474C16.2658 6.16754 16.2684 6.16058 16.2708 6.15362C16.6133 5.1437 16.4566 3.95618 15.8686 3.06818C15.713 2.84042 15.5285 2.63474 15.3242 2.45042C15.282 2.4137 15.2417 2.37506 15.1973 2.34074C15.0259 2.19962 14.8409 2.07458 14.6443 1.96826C14.5459 1.91498 14.4449 1.86626 14.3412 1.82258C14.237 1.7765 14.1283 1.73858 14.0179 1.70426C13.9546 1.68482 13.8888 1.67042 13.824 1.65458C13.7666 1.64066 13.71 1.62554 13.6519 1.61474C13.6039 1.60562 13.5547 1.59986 13.5062 1.59266C13.4218 1.58066 13.3373 1.56962 13.2518 1.56362C13.2226 1.56146 13.193 1.56074 13.1635 1.5593C13.0766 1.55498 12.9895 1.55522 12.9024 1.55762C12.4032 1.58666 11.9602 1.69154 11.5721 1.85522C11.4929 1.89098 11.4127 1.92578 11.3381 1.96802C10.56 2.39186 9.9648 3.1133 9.69 3.95834C9.49944 4.5893 9.48312 5.27066 9.64512 5.9093C9.6468 5.9153 9.64824 5.92154 9.64992 5.92754C9.6576 5.95514 9.66552 5.98274 9.6732 6.01034C9.68256 6.04322 9.6936 6.07586 9.70392 6.1085C9.71328 6.1349 9.72312 6.16106 9.73296 6.18746H2.10576C1.42272 6.18746 0.866882 6.7433 0.866882 7.42634V16.4676C0.866882 17.1509 1.42272 17.7065 2.10576 17.7065H6.93504C6.94128 17.7348 6.9492 17.7622 6.95592 17.7903C7.62072 20.406 9.94704 22.2559 12.696 22.3227C14.9767 22.2672 16.9358 20.9496 17.9167 19.0606C17.9314 19.0659 17.9465 19.0697 17.9611 19.0747C20.4048 19.999 23.1816 18.0598 23.1322 15.4433V10.5893C23.1324 9.87314 22.548 9.29042 21.8299 9.29042Z'
      fill='#464EB8'
    />
    <path
      d='M18.5866 10.6157C18.6031 9.90265 18.0377 9.30817 17.3263 9.29065C17.3235 9.29065 12.1827 9.29065 12.1827 9.29065C11.9933 9.29065 11.8397 9.44449 11.8397 9.63457V16.6174C11.8397 16.9394 11.5783 17.2013 11.257 17.2013H7.24777C7.14481 17.2013 7.04713 17.2478 6.98209 17.3278C6.91705 17.4079 6.89113 17.513 6.91201 17.6143C7.48009 20.389 9.85153 22.3752 12.696 22.4446C16.0215 22.363 18.6639 19.5852 18.5866 16.2605V10.6157Z'
      fill='#7B83EB'
    />
    <path
      d='M12.9785 8.47153C13.0008 8.47177 13.0231 8.47009 13.0455 8.46985C13.1314 8.46865 13.2166 8.46433 13.3008 8.45713C13.3296 8.45449 13.3582 8.45209 13.3865 8.44873C13.483 8.43793 13.5787 8.42377 13.6728 8.40529C13.6786 8.40409 13.6848 8.40337 13.6906 8.40217C14.1365 8.31121 14.557 8.13337 14.9307 7.88137C15.2765 7.64833 15.5755 7.35265 15.8172 7.01377C15.8796 6.93193 15.9365 6.84649 15.9895 6.75913C15.9946 6.75049 16.0001 6.74233 16.0051 6.73345C16.0459 6.66457 16.0819 6.59305 16.117 6.52129C16.1323 6.49009 16.1482 6.45913 16.1626 6.42721C16.1995 6.34441 16.2339 6.26017 16.2636 6.17449C16.2663 6.16728 16.2689 6.16033 16.2713 6.15337C16.6138 5.14345 16.4571 3.95569 15.8691 3.06793C15.7135 2.84017 15.529 2.63449 15.3247 2.45017C15.2825 2.41345 15.2422 2.37481 15.1978 2.34049C15.0264 2.19937 14.8414 2.07433 14.6448 1.96801C14.5464 1.91473 14.4454 1.86601 14.3417 1.82233C14.2375 1.77625 14.1288 1.73833 14.0184 1.70401C13.9551 1.68457 13.8893 1.67041 13.8245 1.65433C13.7671 1.64041 13.7105 1.62529 13.6524 1.61449C13.6044 1.60537 13.5555 1.59961 13.5067 1.59241C13.4223 1.58041 13.3378 1.56937 13.2523 1.56337C13.2231 1.56121 13.1935 1.56049 13.1643 1.55905C13.0774 1.55521 12.9903 1.55545 12.9031 1.55785C12.4039 1.58689 11.9609 1.69153 11.5728 1.85545C11.4936 1.89121 11.4135 1.92601 11.3388 1.96825C10.5607 2.39233 9.9653 3.11377 9.69074 3.95881C9.50018 4.58977 9.48386 5.27113 9.64586 5.90977C9.64754 5.91577 9.64898 5.92201 9.65066 5.92801C9.65834 5.95561 9.66626 5.98321 9.67394 6.01081C9.6833 6.04369 9.69434 6.07633 9.70466 6.10897C9.71906 6.15001 9.73418 6.19057 9.74954 6.23137H9.74306C10.1691 7.42585 11.2474 8.39833 12.9785 8.47153Z'
      fill='#7B83EB'
    />
    <path
      d='M11.1475 6.18793H2.10576C1.42272 6.18793 0.866882 6.74377 0.866882 7.42681V16.4681C0.866882 17.1514 1.42272 17.707 2.10576 17.707H11.1475C11.8306 17.707 12.3862 17.1511 12.3862 16.4681V7.42681C12.3864 6.74377 11.8308 6.18793 11.1475 6.18793Z'
      fill='#464EB8'
    />
    <path
      d='M8.90615 8.70502H4.27895C4.09391 8.70502 3.94415 8.85502 3.94415 9.04006V9.88342C3.94415 10.0685 4.09391 10.2185 4.27895 10.2185H5.77175V14.9165C5.77175 15.1015 5.92151 15.2515 6.10655 15.2515H7.09871C7.28375 15.2515 7.43351 15.1015 7.43351 14.9165V10.2185H8.90615C9.09119 10.2185 9.24095 10.0685 9.24095 9.88342V9.04006C9.24095 8.85502 9.09119 8.70502 8.90615 8.70502Z'
      fill='white'
    />
  </svg>
);

export default IconMicrosoftTeams;
