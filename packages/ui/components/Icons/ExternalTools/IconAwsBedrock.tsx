import { CustomIconProps } from '..';

const IconAwsBedrock = (props: CustomIconProps) => {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        d='M13.05 15.513H16.13C16.344 15.513 16.519 15.69 16.519 15.907V17.727C16.89 17.8185 17.2194 18.032 17.4545 18.3332C17.6895 18.6344 17.8165 19.0059 17.815 19.388C17.815 20.331 17.06 21.096 16.13 21.096C15.199 21.096 14.444 20.331 14.444 19.388C14.444 18.581 14.998 17.904 15.741 17.726V16.301H13.051V20.964C13.0513 21.0317 13.0341 21.0984 13.0012 21.1576C12.9683 21.2168 12.9207 21.2665 12.863 21.302L10.173 22.943C10.112 22.9803 10.0418 22.9999 9.97022 22.9996C9.89868 22.9992 9.82866 22.9789 9.768 22.941L4.842 19.855C4.78516 19.8193 4.73835 19.7697 4.70598 19.711C4.67361 19.6522 4.65675 19.5861 4.657 19.519V16.3L2.196 14.87C2.14003 14.8376 2.09289 14.7919 2.05872 14.7369C2.02454 14.682 2.00438 14.6195 2 14.555V14.528V9.40599C2 9.26599 2.073 9.13599 2.192 9.06599L4.657 7.60399V4.44799C4.657 4.31899 4.719 4.19899 4.822 4.12599L4.843 4.11199L9.77 1.05799C9.83107 1.01997 9.90156 0.999817 9.9735 0.999817C10.0454 0.999817 10.1159 1.01997 10.177 1.05799L12.867 2.73299C12.9238 2.76867 12.9707 2.81824 13.003 2.87703C13.0354 2.93582 13.0522 3.00189 13.052 3.06899V7.59999H16.908V5.68299C16.5369 5.59147 16.2073 5.37783 15.9722 5.07639C15.7372 4.77495 15.6103 4.40324 15.612 4.02099C15.612 3.07799 16.367 2.31299 17.297 2.31299C18.228 2.31299 18.982 3.07799 18.982 4.02099C18.982 4.82799 18.429 5.50499 17.686 5.68299V7.99399C17.6864 8.04543 17.6766 8.09643 17.6573 8.14408C17.6379 8.19174 17.6094 8.23511 17.5732 8.27171C17.5371 8.30831 17.4941 8.33742 17.4467 8.35737C17.3993 8.37733 17.3484 8.38773 17.297 8.38799H13.052V10.194H19.676C19.7611 9.82218 19.9694 9.49009 20.2672 9.2517C20.5649 9.01332 20.9346 8.88267 21.316 8.88099C22.246 8.88099 23.001 9.64499 23.001 10.588C23.001 11.531 22.247 12.296 21.316 12.296C20.9344 12.2943 20.5647 12.1635 20.2669 11.9249C19.9692 11.6863 19.7609 11.354 19.676 10.982H13.05V12.919H18.003L18.918 14.099C19.1728 13.95 19.4628 13.8716 19.758 13.872C20.689 13.872 21.443 14.636 21.443 15.579C21.443 16.522 20.689 17.287 19.758 17.287C18.828 17.287 18.073 16.522 18.073 15.579C18.073 15.233 18.175 14.911 18.349 14.642L17.625 13.707H13.05V15.513ZM9.973 1.85599L7.93 3.12199V6.08999H7.152V3.60399L5.435 4.66899V7.61399L7.545 8.97399L9.712 7.60999V5.33399H10.49V7.82999C10.49 7.96599 10.42 8.09299 10.306 8.16499L7.963 9.63799V11.719L9.385 12.728L8.939 13.374L7.533 12.376L6.003 13.381L5.58 12.721L7.185 11.666V9.67599L5.038 8.28999L2.778 9.62999V11.306L4.75 10.117L5.148 10.794L2.778 12.223V14.3L4.944 15.558L7.214 14.19L7.611 14.867L5.435 16.178V19.3L7.311 20.475L9.676 19.049L10.074 19.727L8.057 20.943L9.975 22.144L12.273 20.741V14.961L7.515 17.854L7.115 17.179L12.273 14.043V3.28899L9.973 1.85599ZM16.13 18.47C16.0099 18.4706 15.8912 18.495 15.7805 18.5416C15.6699 18.5882 15.5695 18.6562 15.4852 18.7416C15.4008 18.8271 15.3342 18.9284 15.289 19.0396C15.2438 19.1509 15.2211 19.2699 15.222 19.39C15.222 19.897 15.628 20.308 16.13 20.308C16.2499 20.3072 16.3685 20.2828 16.4789 20.2362C16.5894 20.1896 16.6896 20.1217 16.7738 20.0363C16.8581 19.951 16.9246 19.8499 16.9698 19.7388C17.015 19.6278 17.0378 19.5089 17.037 19.389C17.0379 19.269 17.0152 19.15 16.9701 19.0388C16.925 18.9276 16.8584 18.8264 16.7742 18.741C16.69 18.6556 16.5897 18.5876 16.4792 18.5409C16.3686 18.4942 16.25 18.4708 16.13 18.47ZM19.76 14.66C19.6399 14.6606 19.5212 14.685 19.4105 14.7316C19.2999 14.7782 19.1995 14.8462 19.1152 14.9316C19.0308 15.0171 18.9642 15.1184 18.919 15.2296C18.8738 15.3409 18.8511 15.4599 18.852 15.58C18.852 16.088 19.258 16.5 19.759 16.5C19.8791 16.4993 19.9978 16.475 20.1085 16.4284C20.2191 16.3818 20.3195 16.3138 20.4038 16.2283C20.4882 16.1429 20.5548 16.0416 20.6 15.9304C20.6452 15.8191 20.6679 15.7001 20.667 15.58C20.6679 15.4599 20.6452 15.3409 20.6 15.2296C20.5548 15.1184 20.4882 15.0171 20.4038 14.9316C20.3195 14.8462 20.2191 14.7782 20.1085 14.7316C19.9978 14.685 19.8801 14.6606 19.76 14.66ZM21.315 9.66999C21.1949 9.67065 21.0762 9.69498 20.9655 9.74159C20.8549 9.78821 20.7545 9.85619 20.6702 9.94164C20.5858 10.0271 20.5192 10.1284 20.474 10.2396C20.4288 10.3509 20.4061 10.4699 20.407 10.59C20.407 11.097 20.814 11.508 21.315 11.508C21.4349 11.5072 21.5535 11.4828 21.6639 11.4362C21.7744 11.3896 21.8746 11.3217 21.9588 11.2363C22.0431 11.151 22.1097 11.0499 22.1548 10.9388C22.2 10.8278 22.2228 10.7089 22.222 10.589C22.2229 10.469 22.2002 10.35 22.1551 10.2388C22.11 10.1276 22.0434 10.0264 21.9592 9.941C21.875 9.85555 21.7747 9.78755 21.6642 9.74088C21.5536 9.69421 21.435 9.66978 21.315 9.66899V9.66999ZM17.296 3.09999C17.176 3.10078 17.0574 3.12521 16.9468 3.17188C16.8363 3.21855 16.736 3.28655 16.6518 3.372C16.5676 3.45744 16.501 3.55865 16.4559 3.66984C16.4108 3.78103 16.3881 3.90001 16.389 4.01999C16.389 4.52799 16.795 4.93999 17.296 4.93999C17.4161 4.93934 17.5348 4.91501 17.6455 4.8684C17.7561 4.82178 17.8565 4.7538 17.9408 4.66834C18.0252 4.58289 18.0918 4.48163 18.137 4.37038C18.1822 4.25913 18.2049 4.14006 18.204 4.01999C18.2049 3.89993 18.1822 3.78086 18.137 3.66961C18.0918 3.55836 18.0252 3.4571 17.9408 3.37164C17.8565 3.28619 17.7561 3.21821 17.6455 3.17159C17.5348 3.12498 17.4161 3.10065 17.296 3.09999Z'
        fill='url(#paint0_linear_5352_62872)'
      />
      <defs>
        <linearGradient
          id='paint0_linear_5352_62872'
          x1='1682.08'
          y1='440.995'
          x2='363.519'
          y2='1699.7'
          gradientUnits='userSpaceOnUse'>
          <stop stopColor='#6350FB' />
          <stop offset='0.5' stopColor='#3D8FFF' />
          <stop offset='1' stopColor='#9AD8F8' />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default IconAwsBedrock;
