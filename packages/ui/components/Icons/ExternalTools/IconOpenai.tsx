import { CustomIconProps } from '..';

const Icon<PERSON><PERSON>ai = (props: CustomIconProps) => {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        d='M22.2819 9.82107C22.5502 9.01295 22.6431 8.15687 22.5541 7.31001C22.4652 6.46315 22.1966 5.64502 21.7662 4.91027C21.1282 3.7997 20.1539 2.92043 18.984 2.39923C17.814 1.87804 16.5088 1.74184 15.2564 2.01028C14.5449 1.21884 13.6377 0.628547 12.6259 0.298687C11.614 -0.0311717 10.5332 -0.088985 9.49198 0.131054C8.45074 0.351093 7.48572 0.841237 6.69386 1.55226C5.902 2.26328 5.31116 3.17015 4.9807 4.18178C4.14636 4.35286 3.35816 4.70006 2.66875 5.20017C1.97935 5.70027 1.40463 6.34177 0.982998 7.08178C0.338032 8.1905 0.0623786 9.47561 0.195888 10.7513C0.329398 12.027 0.865153 13.2272 1.7257 14.1784C1.45636 14.9861 1.36262 15.842 1.45075 16.6889C1.53887 17.5358 1.80683 18.3541 2.2367 19.0891C2.87554 20.2001 3.85062 21.0795 5.02141 21.6007C6.1922 22.1219 7.49824 22.2579 8.7513 21.9892C9.31656 22.6257 10.0111 23.1344 10.7886 23.4811C11.5661 23.8279 12.4086 24.0048 13.2599 24C14.5435 24.0012 15.7943 23.5944 16.8318 22.8385C17.8692 22.0825 18.6396 21.0165 19.0317 19.7942C19.8659 19.6228 20.654 19.2755 21.3434 18.7754C22.0328 18.2753 22.6075 17.6339 23.0294 16.8941C23.6667 15.787 23.9377 14.5067 23.8034 13.2363C23.6691 11.966 23.1366 10.7705 22.2819 9.82107ZM13.2599 22.4292C12.2086 22.4308 11.1903 22.0624 10.3835 21.3884L10.5254 21.308L15.3037 18.5498C15.4226 18.48 15.5214 18.3805 15.5902 18.2611C15.6591 18.1416 15.6957 18.0063 15.6964 17.8685V11.1316L17.7164 12.3002C17.7264 12.3052 17.735 12.3126 17.7416 12.3216C17.7482 12.3307 17.7526 12.3411 17.7544 12.3522V17.9348C17.7519 19.126 17.2775 20.2677 16.4352 21.11C15.5928 21.9523 14.4511 22.4267 13.2599 22.4292ZM3.5992 18.3038C3.07197 17.3934 2.88267 16.3263 3.0646 15.2901L3.2066 15.3753L7.9896 18.1335C8.10795 18.2029 8.24268 18.2395 8.3799 18.2395C8.51712 18.2395 8.65185 18.2029 8.7702 18.1335L14.613 14.765V17.0974C14.6124 17.1095 14.6092 17.1213 14.6034 17.1319C14.5977 17.1426 14.5896 17.1518 14.5798 17.1589L9.74 19.9502C8.70712 20.5452 7.48034 20.706 6.32901 20.3973C5.17767 20.0886 4.19588 19.3357 3.5992 18.3038ZM2.3408 7.89558C2.87168 6.97934 3.70963 6.2805 4.7063 5.92278V11.6C4.7045 11.7371 4.73954 11.8723 4.80777 11.9912C4.876 12.1102 4.97492 12.2087 5.0942 12.2765L10.9086 15.6308L8.8885 16.7993C8.87757 16.8051 8.86538 16.8081 8.853 16.8081C8.84062 16.8081 8.82843 16.8051 8.8175 16.7993L3.9872 14.0128C2.95626 13.4152 2.20415 12.4334 1.89556 11.2825C1.58698 10.1315 1.74708 8.90514 2.3408 7.87198V7.89558ZM18.9371 11.7514L13.1038 8.36397L15.1192 7.19998C15.1301 7.19417 15.1423 7.19113 15.1547 7.19113C15.1671 7.19113 15.1793 7.19417 15.1902 7.19998L20.0205 9.99127C20.759 10.4174 21.3611 11.0449 21.7565 11.8004C22.1518 12.5559 22.324 13.4083 22.2531 14.258C22.1822 15.1077 21.871 15.9197 21.3559 16.5992C20.8408 17.2787 20.143 17.7977 19.344 18.0955V12.4183C19.3398 12.2814 19.3001 12.1479 19.2287 12.0309C19.1574 11.914 19.0569 11.8177 18.9371 11.7514ZM20.9478 8.72827L20.8058 8.64307L16.0323 5.86128C15.9132 5.7914 15.7777 5.75456 15.6396 5.75456C15.5015 5.75456 15.366 5.7914 15.2469 5.86128L9.409 9.22967V6.89738C9.40775 6.88552 9.40974 6.87354 9.41474 6.86272C9.41973 6.85189 9.42756 6.84262 9.4374 6.83588L14.2677 4.04928C15.008 3.62279 15.8545 3.41591 16.708 3.45283C17.5616 3.48975 18.387 3.76895 19.0877 4.25776C19.7885 4.74657 20.3355 5.42479 20.6649 6.21311C20.9943 7.00142 21.0925 7.86723 20.9479 8.70927L20.9478 8.72827ZM8.3065 12.863L6.2865 11.6992C6.27639 11.6931 6.26775 11.6849 6.26118 11.6751C6.25461 11.6653 6.25028 11.6541 6.2485 11.6425V6.07418C6.24961 5.21995 6.49388 4.38369 6.95275 3.66317C7.41162 2.94266 8.06612 2.36766 8.83974 2.00541C9.61335 1.64317 10.4741 1.50864 11.3214 1.61755C12.1686 1.72647 12.9674 2.07434 13.6242 2.62048L13.4822 2.70098L8.704 5.45898C8.58507 5.52872 8.48634 5.62821 8.41749 5.74766C8.34864 5.8671 8.31204 6.00241 8.3113 6.14028L8.3065 12.863ZM9.4041 10.4976L12.0061 8.99777L14.613 10.4976V13.497L12.0156 14.9967L9.4089 13.497L9.4041 10.4976Z'
        fill='black'
      />
    </svg>
  );
};

export default IconOpenai;
