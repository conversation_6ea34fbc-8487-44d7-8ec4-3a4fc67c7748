import { CustomIconProps } from '..';

const IconSalesforce = (props: CustomIconProps) => (
  <svg
    width='24'
    height='24'
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}>
    <path
      d='M9.98936 5.40486C10.7626 4.59917 11.8391 4.09949 13.0297 4.09949C14.6124 4.09949 15.9933 4.98203 16.7286 6.29217C17.3676 6.00665 18.0749 5.84783 18.8192 5.84783C21.6737 5.84783 23.988 8.18225 23.988 11.0618C23.988 13.9416 21.6737 16.276 18.8192 16.276C18.4708 16.276 18.1303 16.2412 17.801 16.1746C17.1535 17.3297 15.9195 18.1101 14.5031 18.1101C13.9102 18.1101 13.3494 17.9731 12.8501 17.7296C12.1937 19.2737 10.6642 20.3564 8.88175 20.3564C7.02549 20.3564 5.44349 19.1818 4.83623 17.5346C4.57086 17.5909 4.29592 17.6203 4.01381 17.6203C1.80371 17.6203 0.012001 15.8102 0.012001 13.5768C0.012001 12.0802 0.817007 10.7735 2.01307 10.0744C1.76683 9.50776 1.62987 8.8824 1.62987 8.22494C1.62987 5.65657 3.71496 3.57455 6.28675 3.57455C7.79669 3.57455 9.13859 4.29247 9.98936 5.40486Z'
      fill='#00A1E0'
    />
    <path
      d='M3.4847 12.2771C3.46967 12.3164 3.49016 12.3246 3.49494 12.3314C3.54003 12.3642 3.58579 12.3878 3.6319 12.4141C3.87644 12.5439 4.10732 12.5818 4.34879 12.5818C4.8406 12.5818 5.14594 12.3202 5.14594 11.8991V11.8909C5.14594 11.5015 4.80133 11.3601 4.47789 11.258L4.43588 11.2443C4.19202 11.1651 3.98164 11.0968 3.98164 10.9363V10.9277C3.98164 10.7904 4.10459 10.6893 4.29517 10.6893C4.50692 10.6893 4.75829 10.7597 4.92018 10.8492C4.92018 10.8492 4.96766 10.8799 4.98508 10.8338C4.99464 10.8092 5.07661 10.5886 5.08515 10.5647C5.09437 10.5387 5.07797 10.5196 5.06124 10.5093C4.87647 10.397 4.621 10.3201 4.35664 10.3201L4.30746 10.3205C3.85732 10.3205 3.5431 10.5923 3.5431 10.982V10.9902C3.5431 11.4011 3.88976 11.5343 4.21456 11.6272L4.26682 11.6432C4.50351 11.716 4.7074 11.7785 4.7074 11.9452V11.9534C4.7074 12.1057 4.57489 12.2191 4.36108 12.2191C4.27809 12.2191 4.0134 12.2174 3.72753 12.0367C3.69304 12.0165 3.67288 12.0019 3.64624 11.9858C3.63224 11.9769 3.59706 11.9616 3.58169 12.008L3.4847 12.2771Z'
      fill='white'
    />
    <path
      d='M10.6847 12.2771C10.6696 12.3164 10.6901 12.3246 10.6949 12.3314C10.74 12.3642 10.7858 12.3878 10.8319 12.4141C11.0764 12.5439 11.3073 12.5818 11.5488 12.5818C12.0406 12.5818 12.3459 12.3202 12.3459 11.8991V11.8909C12.3459 11.5015 12.0013 11.3601 11.6779 11.258L11.6359 11.2443C11.392 11.1651 11.1816 11.0968 11.1816 10.9363V10.9277C11.1816 10.7904 11.3046 10.6893 11.4951 10.6893C11.7069 10.6893 11.9583 10.7597 12.1202 10.8492C12.1202 10.8492 12.1676 10.8799 12.185 10.8338C12.1946 10.8092 12.2766 10.5886 12.2851 10.5647C12.2943 10.5387 12.2779 10.5196 12.2612 10.5093C12.0764 10.397 11.821 10.3201 11.5566 10.3201L11.5074 10.3205C11.0573 10.3205 10.7431 10.5923 10.7431 10.982V10.9902C10.7431 11.4011 11.0897 11.5343 11.4145 11.6272L11.4668 11.6432C11.7035 11.716 11.9077 11.7785 11.9077 11.9452V11.9534C11.9077 12.1057 11.7749 12.2191 11.5611 12.2191C11.4781 12.2191 11.2134 12.2174 10.9275 12.0367C10.893 12.0165 10.8725 12.0025 10.8466 11.9858C10.8377 11.98 10.796 11.9639 10.7817 12.008L10.6847 12.2771Z'
      fill='white'
    />
    <path
      d='M15.5999 11.4523C15.5999 11.6904 15.5555 11.8779 15.4681 12.0104C15.3816 12.1415 15.2508 12.2054 15.0685 12.2054C14.8857 12.2054 14.7556 12.1419 14.6706 12.0104C14.5845 11.8782 14.5408 11.6904 14.5408 11.4523C14.5408 11.2146 14.5845 11.0274 14.6706 10.8963C14.7556 10.7665 14.8857 10.7033 15.0685 10.7033C15.2508 10.7033 15.3816 10.7665 15.4684 10.8963C15.5555 11.0274 15.5999 11.2146 15.5999 11.4523ZM16.0104 11.011C15.9701 10.8748 15.9073 10.7545 15.8236 10.6545C15.7399 10.5541 15.634 10.4735 15.5084 10.4147C15.383 10.3563 15.2348 10.3266 15.0685 10.3266C14.9018 10.3266 14.7536 10.3563 14.6282 10.4147C14.5025 10.4735 14.3966 10.5541 14.3126 10.6545C14.2293 10.7549 14.1664 10.8751 14.1258 11.011C14.0858 11.1466 14.0657 11.2949 14.0657 11.4523C14.0657 11.6098 14.0858 11.7583 14.1258 11.8936C14.1664 12.0295 14.229 12.1497 14.313 12.2501C14.3966 12.3505 14.5029 12.4308 14.6282 12.4878C14.7539 12.5449 14.9018 12.5739 15.0685 12.5739C15.2348 12.5739 15.3827 12.5449 15.5084 12.4878C15.6337 12.4308 15.7399 12.3505 15.8236 12.2501C15.9073 12.1501 15.9701 12.0298 16.0104 11.8936C16.0507 11.758 16.0709 11.6094 16.0709 11.4523C16.0709 11.2952 16.0507 11.1466 16.0104 11.011Z'
      fill='white'
    />
    <path
      d='M19.3816 12.1418C19.368 12.1018 19.3294 12.1169 19.3294 12.1169C19.2696 12.1397 19.2061 12.1609 19.1385 12.1715C19.0698 12.1821 18.9943 12.1876 18.9134 12.1876C18.7146 12.1876 18.5568 12.1285 18.4438 12.0117C18.3304 11.8949 18.2669 11.706 18.2675 11.4505C18.2682 11.2179 18.3242 11.0431 18.425 10.9099C18.5251 10.7773 18.6774 10.7094 18.8806 10.7094C19.05 10.7094 19.1791 10.7288 19.3144 10.7715C19.3144 10.7715 19.3468 10.7855 19.3622 10.7432C19.398 10.6435 19.4247 10.5721 19.4629 10.4624C19.4739 10.4314 19.4472 10.418 19.4377 10.4143C19.3844 10.3935 19.2587 10.3596 19.1637 10.3453C19.0749 10.3316 18.9711 10.3245 18.8557 10.3245C18.6832 10.3245 18.5295 10.3538 18.398 10.4126C18.2669 10.471 18.1555 10.5516 18.0674 10.652C17.9793 10.7524 17.9123 10.8726 17.8676 11.0086C17.8232 11.1442 17.8007 11.2931 17.8007 11.4505C17.8007 11.791 17.8925 12.0663 18.0739 12.2678C18.2556 12.47 18.5285 12.5728 18.8844 12.5728C19.0947 12.5728 19.3106 12.5301 19.4657 12.469C19.4657 12.469 19.4954 12.4546 19.4824 12.4201L19.3816 12.1418Z'
      fill='white'
    />
    <path
      d='M20.0999 11.2242C20.1194 11.0921 20.1559 10.9821 20.2123 10.8964C20.2973 10.7662 20.4271 10.6949 20.6095 10.6949C20.7919 10.6949 20.9124 10.7666 20.9989 10.8964C21.0562 10.9821 21.0812 11.0968 21.0911 11.2242H20.0999ZM21.4821 10.9336C21.4473 10.8021 21.3609 10.6692 21.3042 10.6084C21.2147 10.5121 21.1273 10.4448 21.0405 10.4073C20.9271 10.3588 20.7912 10.3267 20.6423 10.3267C20.4688 10.3267 20.3113 10.3557 20.1836 10.4158C20.0555 10.4759 19.9479 10.5579 19.8636 10.66C19.7792 10.7618 19.7157 10.883 19.6754 11.0207C19.6348 11.1576 19.6143 11.3069 19.6143 11.4643C19.6143 11.6245 19.6354 11.7738 19.6774 11.908C19.7198 12.0432 19.7874 12.1624 19.8789 12.2611C19.9701 12.3605 20.0876 12.4384 20.2283 12.4927C20.368 12.5467 20.5378 12.5747 20.7328 12.5743C21.1341 12.573 21.3455 12.4835 21.4326 12.4353C21.448 12.4268 21.4627 12.4118 21.4442 12.3687L21.3534 12.1143C21.3397 12.0764 21.3011 12.0904 21.3011 12.0904C21.2017 12.1273 21.0603 12.1935 20.7307 12.1928C20.5152 12.1925 20.3554 12.129 20.2553 12.0296C20.1525 11.9278 20.1023 11.7782 20.0934 11.5671L21.4832 11.5685C21.4832 11.5685 21.5197 11.5678 21.5235 11.5323C21.5248 11.5173 21.5713 11.2468 21.4821 10.9336Z'
      fill='white'
    />
    <path
      d='M8.97028 11.2242C8.99009 11.0921 9.02629 10.9821 9.08265 10.8964C9.16769 10.7662 9.29747 10.6949 9.47985 10.6949C9.66224 10.6949 9.7828 10.7666 9.86955 10.8964C9.92659 10.9821 9.95152 11.0968 9.96142 11.2242H8.97028ZM10.3521 10.9336C10.3173 10.8021 10.2312 10.6692 10.1745 10.6084C10.0851 10.5121 9.99763 10.4448 9.91088 10.4073C9.79749 10.3588 9.66155 10.3267 9.51264 10.3267C9.33948 10.3267 9.18169 10.3557 9.05396 10.4158C8.92588 10.4759 8.81829 10.5579 8.73393 10.66C8.64957 10.7618 8.58605 10.883 8.54575 11.0207C8.50545 11.1576 8.48461 11.3069 8.48461 11.4643C8.48461 11.6245 8.50579 11.7738 8.5478 11.908C8.59015 12.0432 8.65777 12.1624 8.7493 12.2611C8.84049 12.3605 8.95798 12.4384 9.0987 12.4927C9.23839 12.5467 9.40813 12.5747 9.60315 12.5743C10.0045 12.573 10.2159 12.4835 10.303 12.4353C10.3183 12.4268 10.333 12.4118 10.3146 12.3687L10.2241 12.1143C10.2101 12.0764 10.1715 12.0904 10.1715 12.0904C10.0721 12.1273 9.93103 12.1935 9.60076 12.1928C9.38559 12.1925 9.22575 12.129 9.12568 12.0296C9.02288 11.9278 8.97267 11.7782 8.96379 11.5671L10.3535 11.5685C10.3535 11.5685 10.3901 11.5678 10.3938 11.5323C10.3952 11.5173 10.4416 11.2468 10.3521 10.9336Z'
      fill='white'
    />
    <path
      d='M5.96642 12.1342C5.91211 12.0908 5.9046 12.0799 5.88616 12.0519C5.85883 12.0092 5.84483 11.9484 5.84483 11.8712C5.84483 11.7489 5.88513 11.6612 5.96881 11.6021C5.96778 11.6024 6.08835 11.4979 6.37182 11.5017C6.57094 11.5044 6.74888 11.5338 6.74888 11.5338V12.1656H6.74922C6.74922 12.1656 6.57265 12.2035 6.37387 12.2155C6.09108 12.2326 5.96539 12.1339 5.96642 12.1342ZM6.51937 11.1577C6.46301 11.1536 6.38993 11.1512 6.30249 11.1512C6.18329 11.1512 6.0682 11.1663 5.96027 11.1953C5.85166 11.2243 5.75398 11.2698 5.66996 11.3299C5.5856 11.3903 5.51764 11.4675 5.46845 11.559C5.41927 11.6506 5.39434 11.7585 5.39434 11.8794C5.39434 12.0024 5.41552 12.1093 5.45787 12.1967C5.50022 12.2845 5.56135 12.3576 5.63922 12.4139C5.71641 12.4703 5.8117 12.5116 5.92236 12.5365C6.03131 12.5615 6.15495 12.5741 6.2902 12.5741C6.43262 12.5741 6.5747 12.5625 6.71234 12.5389C6.84861 12.5157 7.01596 12.4819 7.06241 12.4713C7.10852 12.4604 7.15975 12.4464 7.15975 12.4464C7.19425 12.4378 7.19152 12.4009 7.19152 12.4009L7.19083 11.1301C7.19083 10.8514 7.11638 10.6447 6.96986 10.5167C6.82402 10.3889 6.60919 10.3244 6.33152 10.3244C6.22735 10.3244 6.05966 10.3387 5.95924 10.3589C5.95924 10.3589 5.65562 10.4176 5.53061 10.5153C5.53061 10.5153 5.50329 10.5324 5.51832 10.5706L5.61668 10.835C5.62898 10.8691 5.66211 10.8575 5.66211 10.8575C5.66211 10.8575 5.67269 10.8534 5.68499 10.8463C5.95241 10.7008 6.29054 10.7052 6.29054 10.7052C6.44081 10.7052 6.55625 10.7353 6.63412 10.795C6.70995 10.8531 6.74854 10.9409 6.74854 11.126V11.1847C6.629 11.1676 6.51937 11.1577 6.51937 11.1577Z'
      fill='white'
    />
    <path
      d='M17.728 10.4417C17.7386 10.4102 17.7164 10.3952 17.7072 10.3918C17.6836 10.3826 17.5655 10.3576 17.4743 10.3518C17.2998 10.3413 17.2028 10.3706 17.116 10.4096C17.0299 10.4485 16.9343 10.5113 16.881 10.5827V10.4137C16.881 10.3901 16.8643 10.3713 16.8411 10.3713H16.4848C16.4616 10.3713 16.4449 10.3901 16.4449 10.4137V12.4865C16.4449 12.5097 16.464 12.5288 16.4872 12.5288H16.8523C16.8756 12.5288 16.8944 12.5097 16.8944 12.4865V11.4509C16.8944 11.3119 16.9097 11.1732 16.9405 11.0862C16.9705 11.0001 17.0115 10.9311 17.062 10.8816C17.1129 10.8324 17.1707 10.7979 17.2338 10.7784C17.2984 10.7586 17.3698 10.7521 17.4203 10.7521C17.4931 10.7521 17.573 10.7709 17.573 10.7709C17.5996 10.774 17.6147 10.7576 17.6235 10.7333C17.6474 10.6698 17.7151 10.4796 17.728 10.4417Z'
      fill='white'
    />
    <path
      d='M14.301 9.48096C14.2566 9.4673 14.2163 9.45808 14.1637 9.44818C14.1104 9.43861 14.0469 9.43383 13.9748 9.43383C13.7234 9.43383 13.5253 9.50487 13.3863 9.6449C13.248 9.78425 13.1541 9.99635 13.107 10.2754L13.0899 10.3693H12.7743C12.7743 10.3693 12.736 10.3679 12.7279 10.4096L12.6763 10.6989C12.6725 10.7262 12.6845 10.7436 12.7214 10.7436H13.0284L12.7169 12.4827C12.6927 12.6228 12.6647 12.7379 12.6336 12.8253C12.6032 12.9114 12.5735 12.9759 12.5366 13.0231C12.5011 13.0681 12.4676 13.1016 12.4095 13.1211C12.3617 13.1371 12.3064 13.1446 12.2459 13.1446C12.2125 13.1446 12.1677 13.1392 12.1346 13.1324C12.1018 13.1259 12.0844 13.1187 12.0595 13.1081C12.0595 13.1081 12.0236 13.0944 12.0093 13.1303C11.998 13.16 11.916 13.3851 11.9061 13.4128C11.8965 13.4404 11.9102 13.4619 11.9276 13.4684C11.9686 13.4828 11.999 13.4923 12.0547 13.5057C12.1319 13.5238 12.1971 13.5248 12.2582 13.5248C12.386 13.5248 12.5028 13.5067 12.5994 13.4718C12.6964 13.4367 12.7811 13.3755 12.8563 13.2929C12.9372 13.2034 12.9881 13.1098 13.0366 12.9817C13.0848 12.8554 13.1261 12.6983 13.1589 12.5152L13.4721 10.7436H13.9297C13.9297 10.7436 13.9683 10.745 13.9762 10.703L14.0281 10.414C14.0315 10.3864 14.0199 10.3693 13.9827 10.3693H13.5383C13.5407 10.3594 13.5609 10.203 13.6118 10.0558C13.6336 9.99327 13.6746 9.94238 13.7091 9.90755C13.7432 9.87339 13.7825 9.84914 13.8256 9.83514C13.8696 9.8208 13.9198 9.81396 13.9748 9.81396C14.0165 9.81396 14.0578 9.81875 14.0889 9.82524C14.1319 9.83446 14.1487 9.83924 14.1599 9.84265C14.2053 9.85632 14.2115 9.843 14.2204 9.82114L14.3266 9.52946C14.3375 9.49804 14.3105 9.48472 14.301 9.48096Z'
      fill='white'
    />
    <path
      d='M8.09266 12.4866C8.09266 12.5098 8.07592 12.5286 8.0527 12.5286H7.68418C7.66096 12.5286 7.64456 12.5098 7.64456 12.4866V9.52063C7.64456 9.49741 7.66096 9.47863 7.68418 9.47863H8.0527C8.07592 9.47863 8.09266 9.49741 8.09266 9.52063V12.4866Z'
      fill='white'
    />
  </svg>
);

export default IconSalesforce;
