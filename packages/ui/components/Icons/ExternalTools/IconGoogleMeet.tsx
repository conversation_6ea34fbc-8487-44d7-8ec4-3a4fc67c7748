import { CustomIconProps } from '..';

const IconGoogleMeet = (props: CustomIconProps) => (
  <svg
    width='24'
    height='24'
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}>
    <path
      d='M13.5771 11.9833L15.9168 14.6577L19.0633 16.6682L19.6105 12.0002L19.0633 7.4375L15.8566 9.20361L13.5771 11.9833Z'
      fill='#00832D'
    />
    <path
      d='M6.10352e-05 16.2337V20.2116C6.10352e-05 21.1199 0.737244 21.8572 1.64571 21.8572H5.62354L6.44724 18.8517L5.62354 16.2337L2.89449 15.41L6.10352e-05 16.2337Z'
      fill='#0066DA'
    />
    <path
      d='M5.62348 2.10938L0 7.73286L2.8946 8.55464L5.62348 7.73286L6.43221 5.15054L5.62348 2.10938Z'
      fill='#E94235'
    />
    <path d='M6.10352e-05 16.2356H5.62354V7.73279H6.10352e-05V16.2356Z' fill='#2684FC' />
    <path
      d='M22.6555 4.49039L19.0632 7.43756V16.6681L22.6705 19.6268C23.2104 20.0497 24.0004 19.6642 24.0004 18.9778V5.12609C24.0004 4.43208 23.1916 4.04843 22.6555 4.49039Z'
      fill='#00AC47'
    />
    <path
      d='M13.5771 11.9833V16.2337H5.62354V21.8572H17.4176C18.3261 21.8572 19.0633 21.1198 19.0633 20.2115V16.6682L13.5771 11.9833Z'
      fill='#00AC47'
    />
    <path
      d='M17.4176 2.10938H5.62354V7.73286H13.5771V11.9833L19.0633 7.43746V3.75503C19.0633 2.84656 18.3261 2.10938 17.4176 2.10938Z'
      fill='#FFBA00'
    />
  </svg>
);

export default IconGoogleMeet;
