import { CustomIconProps } from '..';

const IconNotion = (props: CustomIconProps) => (
  <svg
    width='24'
    height='24'
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}>
    <path
      d='M5.21251 4.98696C5.88435 5.53275 6.13638 5.4911 7.39791 5.40694L19.2914 4.6928C19.5435 4.6928 19.3338 4.44115 19.2498 4.39932L17.2744 2.97139C16.896 2.67756 16.3917 2.34105 15.4253 2.42523L3.90894 3.2652C3.48895 3.30684 3.40505 3.51683 3.57233 3.68516L5.21251 4.98696ZM5.92657 7.75868V20.2727C5.92657 20.9451 6.26265 21.1968 7.0191 21.1552L20.0899 20.3989C20.8467 20.3573 20.931 19.8946 20.931 19.3483V6.91836C20.931 6.37291 20.7213 6.07875 20.2579 6.12073L6.59876 6.91836C6.0947 6.96072 5.92657 7.21286 5.92657 7.75868ZM18.8301 8.42996C18.9139 8.8083 18.8301 9.18628 18.451 9.2288L17.8213 9.35427V18.5928C17.2744 18.8867 16.7702 19.0547 16.3501 19.0547C15.6774 19.0547 15.5089 18.8447 15.005 18.2151L10.8855 11.748V18.0051L12.1891 18.2992C12.1891 18.2992 12.1891 19.0547 11.1374 19.0547L8.23807 19.2229C8.15385 19.0547 8.23807 18.6352 8.53215 18.5511L9.28875 18.3415V10.0684L8.23825 9.98425C8.15401 9.60592 8.36383 9.06046 8.95265 9.01811L12.063 8.80846L16.3501 15.3597V9.56425L15.257 9.4388C15.1731 8.9763 15.5089 8.64046 15.9292 8.59881L18.8301 8.42996ZM2.94199 2.1314L14.9209 1.24927C16.392 1.1231 16.7704 1.20762 17.695 1.87924L21.5187 4.5668C22.1498 5.02896 22.36 5.15478 22.36 5.65859V20.3989C22.36 21.3226 22.0235 21.869 20.8469 21.9525L6.93588 22.7927C6.05267 22.8347 5.63231 22.7088 5.16979 22.1205L2.35387 18.4671C1.84929 17.7946 1.63947 17.2914 1.63947 16.7027V3.60067C1.63947 2.84521 1.97607 2.21507 2.94199 2.1314Z'
      fill='black'
    />
  </svg>
);

export default IconNotion;
