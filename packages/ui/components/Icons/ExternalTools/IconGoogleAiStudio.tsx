import { CustomIconProps } from '..';

const IconGoogleAiStudio = (props: CustomIconProps) => {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <g clipPath='url(#clip0_5352_62793)'>
        <path
          d='M15.028 14.16C14.993 14.544 15.117 14.933 15.381 15.217C15.646 15.501 16.02 15.651 16.359 15.785C16.729 15.93 17.051 16.048 17.397 16.253C17.727 16.448 18.019 16.69 18.263 16.989C18.7471 17.5814 18.9935 18.3328 18.9543 19.0969C18.9151 19.8609 18.5932 20.5831 18.051 21.123C17.5127 21.6596 16.7911 21.9723 16.0315 21.9983C15.2719 22.0243 14.5307 21.7615 13.957 21.263C13.6518 20.9862 13.3839 20.6709 13.16 20.325C12.822 18.111 13.478 15.792 15.028 14.16Z'
          fill='#85A8FF'
        />
        <path
          d='M16.457 13.024C16.829 12.807 17.177 12.654 17.582 12.511C17.972 12.375 18.369 12.262 18.77 12.161C19.17 12.059 19.573 11.973 19.977 11.886C20.38 11.8 20.781 11.716 21.171 11.568C23.081 10.835 24.238 8.86799 23.959 6.82199C23.679 4.77699 22.039 3.19999 20.003 3.01699C19.078 2.93499 18.185 3.15499 17.359 3.54099C16.535 3.92499 15.781 4.47099 15.125 5.09599C13.8 6.35599 12.82 7.97399 12.223 9.69599C11.6257 11.4387 11.3929 13.2856 11.539 15.122C11.609 16.032 11.773 16.949 12.035 17.825C12.291 18.684 12.662 19.571 13.161 20.323C12.944 18.9107 13.133 17.4659 13.706 16.157C14.278 14.857 15.227 13.742 16.457 13.024Z'
          fill='#0057CC'
        />
        <path
          d='M7.96199 14.27C7.83316 14.1218 7.69307 13.9837 7.54299 13.857C7.73099 14.182 7.74999 14.552 7.67099 14.907C7.58615 15.2585 7.42424 15.5867 7.19699 15.868C6.96852 16.1468 6.70424 16.3943 6.41099 16.604C6.15799 16.794 5.78799 17.035 5.55899 17.262C5.23123 17.5881 5.0207 18.0137 4.96027 18.4721C4.89985 18.9305 4.99293 19.396 5.22499 19.796C5.45389 20.1948 5.81037 20.5051 6.23702 20.6767C6.66367 20.8483 7.13567 20.8713 7.57699 20.742C8.07699 20.594 8.50199 20.262 8.83899 19.882C9.17899 19.497 9.45599 19.04 9.67499 18.578C9.6204 16.9869 9.0149 15.4641 7.96199 14.27Z'
          fill='#85A8FF'
        />
        <path
          d='M3.68698 5.396C5.07398 5.395 6.41998 6.173 7.39498 7.046C8.42998 7.973 9.23098 9.154 9.75898 10.438C10.289 11.728 10.545 13.124 10.535 14.518C10.525 15.872 10.261 17.34 9.67498 18.578C9.62052 16.9863 9.01503 15.4628 7.96198 14.268C7.78306 14.0596 7.57958 13.8735 7.35598 13.714C7.1301 13.5591 6.88461 13.4351 6.62598 13.345C6.11998 13.164 5.54798 13.079 5.02198 13.021C4.47898 12.959 3.93398 12.923 3.39198 12.842C2.83598 12.759 2.33698 12.636 1.84098 12.345C1.13661 11.9304 0.587591 11.2963 0.278016 10.5399C-0.0315602 9.7835 -0.0846157 8.94645 0.126977 8.157C0.333267 7.36894 0.794131 6.67111 1.43792 6.17199C2.08172 5.67287 2.87237 5.39942 3.68698 5.396Z'
          fill='#0057CC'
        />
      </g>
      <defs>
        <clipPath id='clip0_5352_62793'>
          <rect width='24' height='24' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconGoogleAiStudio;
