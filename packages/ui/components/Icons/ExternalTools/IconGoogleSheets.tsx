import { CustomIconProps } from '..';

const IconGoogleSheets = (props: CustomIconProps) => (
  <svg
    width='24'
    height='24'
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}>
    <mask
      id='mask0_6020_65285'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M14.1874 0H4.67811C3.75515 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.75515 24 4.67811 24H19.2217C20.1447 24 20.8998 23.2636 20.8998 22.3636V6.54545L14.1874 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask0_6020_65285)'>
      <path
        d='M14.1874 0H4.67811C3.75515 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.75515 24 4.67811 24H19.2217C20.1447 24 20.8998 23.2636 20.8998 22.3636V6.54545L16.9842 3.81818L14.1874 0Z'
        fill='#0F9D58'
      />
    </g>
    <mask
      id='mask1_6020_65285'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M14.1874 0H4.67811C3.75515 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.75515 24 4.67811 24H19.2217C20.1447 24 20.8998 23.2636 20.8998 22.3636V6.54545L14.1874 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask1_6020_65285)'>
      <path
        d='M7.47491 11.7272V19.6363H16.4248V11.7272H7.47491ZM11.3905 18.5454H8.59365V17.1818H11.3905V18.5454ZM11.3905 16.3636H8.59365V15H11.3905V16.3636ZM11.3905 14.1818H8.59365V12.8181H11.3905V14.1818ZM15.3061 18.5454H12.5092V17.1818H15.3061V18.5454ZM15.3061 16.3636H12.5092V15H15.3061V16.3636ZM15.3061 14.1818H12.5092V12.8181H15.3061V14.1818Z'
        fill='#F1F1F1'
      />
    </g>
    <mask
      id='mask2_6020_65285'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M14.1874 0H4.67811C3.75515 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.75515 24 4.67811 24H19.2217C20.1447 24 20.8998 23.2636 20.8998 22.3636V6.54545L14.1874 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask2_6020_65285)'>
      <path
        d='M14.6783 6.06677L20.8999 12.1322V6.54541L14.6783 6.06677Z'
        fill='url(#paint0_linear_6020_65285)'
      />
    </g>
    <mask
      id='mask3_6020_65285'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M14.1874 0H4.67811C3.75515 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.75515 24 4.67811 24H19.2217C20.1447 24 20.8998 23.2636 20.8998 22.3636V6.54545L14.1874 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask3_6020_65285)'>
      <path
        d='M14.1874 0V4.90909C14.1874 5.81318 14.9384 6.54545 15.8655 6.54545H20.8999L14.1874 0Z'
        fill='#87CEAC'
      />
    </g>
    <mask
      id='mask4_6020_65285'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M14.1874 0H4.67811C3.75515 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.75515 24 4.67811 24H19.2217C20.1447 24 20.8998 23.2636 20.8998 22.3636V6.54545L14.1874 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask4_6020_65285)'>
      <path
        d='M4.67811 0C3.75515 0 3 0.736364 3 1.63636V1.77273C3 0.872727 3.75515 0.136364 4.67811 0.136364H14.1874V0H4.67811Z'
        fill='white'
        fillOpacity='0.2'
      />
    </g>
    <mask
      id='mask5_6020_65285'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M14.1874 0H4.67811C3.75515 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.75515 24 4.67811 24H19.2217C20.1447 24 20.8998 23.2636 20.8998 22.3636V6.54545L14.1874 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask5_6020_65285)'>
      <path
        d='M19.2217 23.8636H4.67811C3.75515 23.8636 3 23.1272 3 22.2272V22.3636C3 23.2636 3.75515 24 4.67811 24H19.2217C20.1447 24 20.8998 23.2636 20.8998 22.3636V22.2272C20.8998 23.1272 20.1447 23.8636 19.2217 23.8636Z'
        fill='#263238'
        fillOpacity='0.2'
      />
    </g>
    <mask
      id='mask6_6020_65285'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M14.1874 0H4.67811C3.75515 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.75515 24 4.67811 24H19.2217C20.1447 24 20.8998 23.2636 20.8998 22.3636V6.54545L14.1874 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask6_6020_65285)'>
      <path
        d='M15.8655 6.54548C14.9384 6.54548 14.1874 5.81321 14.1874 4.90912V5.04548C14.1874 5.94957 14.9384 6.68185 15.8655 6.68185H20.8999V6.54548H15.8655Z'
        fill='#263238'
        fillOpacity='0.1'
      />
    </g>
    <path
      d='M14.1874 0H4.67811C3.75515 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.75515 24 4.67811 24H19.2217C20.1447 24 20.8998 23.2636 20.8998 22.3636V6.54545L14.1874 0Z'
      fill='url(#paint1_radial_6020_65285)'
    />
    <defs>
      <linearGradient
        id='paint0_linear_6020_65285'
        x1='325.791'
        y1='58.1454'
        x2='325.791'
        y2='612.697'
        gradientUnits='userSpaceOnUse'>
        <stop stopColor='#263238' stopOpacity='0.2' />
        <stop offset='1' stopColor='#263238' stopOpacity='0.02' />
      </linearGradient>
      <radialGradient
        id='paint1_radial_6020_65285'
        cx='0'
        cy='0'
        r='1'
        gradientUnits='userSpaceOnUse'
        gradientTransform='translate(59.7075 47.4346) scale(2886.32 2814.52)'>
        <stop stopColor='white' stopOpacity='0.1' />
        <stop offset='1' stopColor='white' stopOpacity='0' />
      </radialGradient>
    </defs>
  </svg>
);

export default IconGoogleSheets;
