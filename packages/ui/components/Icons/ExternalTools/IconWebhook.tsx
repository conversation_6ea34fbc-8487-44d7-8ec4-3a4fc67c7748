import { CustomIconProps } from '..';

const IconWebhook = (props: CustomIconProps) => (
  <svg
    width='24'
    height='24'
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}>
    <g clipPath='url(#clip0_5352_62799)'>
      <path
        d='M17.4583 18.0416C16.2666 18.0416 15.2916 17.0666 15.2916 15.875C15.2916 14.6833 16.2666 13.7083 17.4583 13.7083C18.65 13.7083 19.625 14.6833 19.625 15.875C19.625 17.0666 18.65 18.0416 17.4583 18.0416Z'
        fill='#37474F'
      />
      <path
        d='M17.4583 21.2917C15.8333 21.2917 14.2625 20.5333 13.2334 19.2875L14.9125 17.9333C15.5083 18.6917 16.4833 19.1792 17.4583 19.1792C19.2458 19.1792 20.7083 17.7167 20.7083 15.9292C20.7083 14.1417 19.2458 12.6792 17.4583 12.6792C16.9167 12.6792 16.375 12.8417 15.8875 13.0583L14.9667 13.6L11.1208 6.66668L13.0167 5.63751L15.8875 10.7292C16.4292 10.5667 16.9708 10.4583 17.5125 10.4583C20.4917 10.4583 22.9292 12.8958 22.9292 15.875C22.9292 18.8542 20.4375 21.2917 17.4583 21.2917Z'
        fill='#37474F'
      />
      <path
        d='M6.08329 21.2917C3.10413 21.2917 0.666626 18.8542 0.666626 15.875C0.666626 13.3834 2.34579 11.2708 4.72913 10.6208L5.27079 12.7334C3.86246 13.1125 2.83329 14.4125 2.83329 15.875C2.83329 17.6625 4.29579 19.125 6.08329 19.125C7.87079 19.125 9.33329 17.6625 9.33329 15.875V14.7917H17.4583V16.9584H11.3916C10.9041 19.45 8.68329 21.2917 6.08329 21.2917Z'
        fill='#37474F'
      />
      <path
        d='M6.08329 18.0416C4.89163 18.0416 3.91663 17.0666 3.91663 15.875C3.91663 14.6833 4.89163 13.7083 6.08329 13.7083C7.27496 13.7083 8.24996 14.6833 8.24996 15.875C8.24996 17.0666 7.27496 18.0416 6.08329 18.0416Z'
        fill='#E91E63'
      />
      <path
        d='M12.0417 8.29165C10.85 8.29165 9.875 7.31665 9.875 6.12498C9.875 4.93331 10.85 3.95831 12.0417 3.95831C13.2333 3.95831 14.2083 4.93331 14.2083 6.12498C14.2083 7.31665 13.2333 8.29165 12.0417 8.29165Z'
        fill='#37474F'
      />
      <path
        d='M7.00414 16.4166L5.16248 15.3333L8.35831 10.0791C7.27498 9.04998 6.62498 7.64165 6.62498 6.12498C6.62498 3.14581 9.06248 0.708313 12.0416 0.708313C15.0208 0.708313 17.4583 3.14581 17.4583 6.12498C17.4583 6.61248 17.4041 7.04581 17.2958 7.47915L15.1833 6.93748C15.2375 6.66665 15.2916 6.39581 15.2916 6.12498C15.2916 4.33748 13.8291 2.87498 12.0416 2.87498C10.2541 2.87498 8.79164 4.33748 8.79164 6.12498C8.79164 7.26248 9.38748 8.29165 10.3625 8.88748L11.2833 9.42915L7.00414 16.4166Z'
        fill='#E91E63'
      />
    </g>
    <defs>
      <clipPath id='clip0_5352_62799'>
        <rect width='23' height='23' fill='white' transform='translate(0.5 0.5)' />
      </clipPath>
    </defs>
  </svg>
);

export default IconWebhook;
