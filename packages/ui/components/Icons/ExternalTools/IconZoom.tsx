import { CustomIconProps } from '..';

const IconZoom = (props: CustomIconProps) => {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <g clipPath='url(#clip0_5229_59188)'>
        <path
          d='M20.4 0H3.6C1.61177 0 0 1.61177 0 3.6V20.4C0 22.3882 1.61177 24 3.6 24H20.4C22.3882 24 24 22.3882 24 20.4V3.6C24 1.61177 22.3882 0 20.4 0Z'
          fill='#2D8CFF'
        />
        <path
          d='M20.0625 16.7344C20.4375 16.8281 20.7656 16.6406 20.9531 16.3594C21.0469 16.2188 21.0469 15.9844 21.0469 15.4688V8.39062C21.0469 7.875 21.0469 7.6875 20.9531 7.5C20.8125 7.125 20.4375 6.98438 20.0625 7.125C19.0781 7.78125 16.9219 9.70312 16.875 10.5C16.8375 10.6406 16.8375 10.875 16.8375 11.2031V12.9844C16.8375 13.3594 16.8375 13.5 16.875 13.6875C16.9219 14.0625 17.0625 14.3906 17.25 14.5781C17.8125 15 19.6875 16.6875 20.1094 16.6875L20.0625 16.7344ZM3 8.76562C3 8.0625 3 7.6875 3.14062 7.5C3.23438 7.3125 3.51562 7.125 3.65625 6.98438C3.84375 6.84375 4.17188 6.84375 4.92188 6.84375H10.9688C12.75 6.84375 13.6406 6.84375 14.3438 7.21875C14.8594 7.59375 15.4219 7.92188 15.75 8.625C16.125 9.32812 16.125 10.2188 16.125 12V15.1875C16.125 15.8906 16.125 16.2656 15.9844 16.4531C15.8906 16.6406 15.6094 16.8281 15.4688 16.9688C15.2812 17.1094 14.9531 17.1094 14.2031 17.1094H8.15625C6.375 17.1094 5.48438 17.1094 4.78125 16.7344C4.26562 16.3594 3.70312 16.0312 3.375 15.3281C3 14.625 3 13.7344 3 11.9531V8.76562Z'
          fill='white'
        />
      </g>
      <defs>
        <clipPath id='clip0_5229_59188'>
          <rect width='24' height='24' fill='white' />
        </clipPath>
      </defs>
    </svg>
  );
};

export default IconZoom;
