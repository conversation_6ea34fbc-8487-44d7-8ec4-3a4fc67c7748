import { CustomIconProps } from '..';

const IconOutlook = (props: CustomIconProps) => (
  <svg
    width='24'
    height='24'
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}>
    <rect x='7.5' y='2' width='15' height='21' rx='2' fill='#1066B5' />
    <rect x='7.5' y='2' width='15' height='21' rx='2' fill='url(#paint0_linear_5229_59192)' />
    <rect x='7.5' y='4.25' width='7.5' height='7.5' fill='#32A9E7' />
    <rect x='7.5' y='11.75' width='7.5' height='7.5' fill='#167EB4' />
    <rect x='15' y='11.75' width='7.5' height='7.5' fill='#32A9E7' />
    <rect x='15' y='4.25' width='7.5' height='7.5' fill='#58D9FD' />
    <mask
      id='mask0_5229_59192'
      style={{ maskType: 'alpha' }}
      maskUnits='userSpaceOnUse'
      x='6'
      y='11'
      width='18'
      height='12'>
      <path
        d='M6 11H22C23.1046 11 24 11.8954 24 13V21C24 22.1046 23.1046 23 22 23H8C6.89543 23 6 22.1046 6 21V11Z'
        fill='url(#paint1_linear_5229_59192)'
      />
    </mask>
    <g mask='url(#mask0_5229_59192)'>
      <path d='M24 11V14H22.5V11H24Z' fill='#135298' />
      <path d='M24 23V12.5L5.25 23H24Z' fill='url(#paint2_linear_5229_59192)' />
      <path d='M6 23V12.5L24.75 23H6Z' fill='url(#paint3_linear_5229_59192)' />
    </g>
    <path
      d='M6 10.25C6 8.59315 7.34315 7.25 9 7.25H12C13.6569 7.25 15 8.59315 15 10.25V17.75C15 19.4069 13.6569 20.75 12 20.75H6V10.25Z'
      fill='black'
      fillOpacity='0.3'
    />
    <rect y='5.75' width='13.5' height='13.5' rx='2' fill='url(#paint4_linear_5229_59192)' />
    <path
      d='M10.5 12.5519V12.4273C10.5 10.2666 8.94541 8.75 6.76187 8.75C4.56646 8.75 3 10.277 3 12.4481V12.5727C3 14.7334 4.55459 16.25 6.75 16.25C8.93354 16.25 10.5 14.723 10.5 12.5519ZM8.7318 12.5727C8.7318 14.0062 7.92484 14.8684 6.76187 14.8684C5.59889 14.8684 4.78006 13.9855 4.78006 12.5519V12.4273C4.78006 10.9938 5.58703 10.1316 6.75 10.1316C7.90111 10.1316 8.7318 11.0145 8.7318 12.4481V12.5727Z'
      fill='white'
    />
    <defs>
      <linearGradient
        id='paint0_linear_5229_59192'
        x1='7.5'
        y1='12.5'
        x2='22.5'
        y2='12.5'
        gradientUnits='userSpaceOnUse'>
        <stop stopColor='#064484' />
        <stop offset='1' stopColor='#0F65B5' />
      </linearGradient>
      <linearGradient
        id='paint1_linear_5229_59192'
        x1='6'
        y1='20.5769'
        x2='24'
        y2='20.5769'
        gradientUnits='userSpaceOnUse'>
        <stop stopColor='#1B366F' />
        <stop offset='1' stopColor='#2657B0' />
      </linearGradient>
      <linearGradient
        id='paint2_linear_5229_59192'
        x1='24'
        y1='17.75'
        x2='6'
        y2='17.75'
        gradientUnits='userSpaceOnUse'>
        <stop stopColor='#44DCFD' />
        <stop offset='0.453125' stopColor='#259ED0' />
      </linearGradient>
      <linearGradient
        id='paint3_linear_5229_59192'
        x1='6'
        y1='17.75'
        x2='24'
        y2='17.75'
        gradientUnits='userSpaceOnUse'>
        <stop stopColor='#259ED0' />
        <stop offset='1' stopColor='#44DCFD' />
      </linearGradient>
      <linearGradient
        id='paint4_linear_5229_59192'
        x1='0'
        y1='12.5'
        x2='13.5'
        y2='12.5'
        gradientUnits='userSpaceOnUse'>
        <stop stopColor='#064484' />
        <stop offset='1' stopColor='#0F65B5' />
      </linearGradient>
    </defs>
  </svg>
);

export default IconOutlook;
