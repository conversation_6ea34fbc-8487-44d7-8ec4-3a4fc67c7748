import { CustomIconProps } from '..';

const IconGoogleDocs = (props: CustomIconProps) => (
  <svg
    width='24'
    height='24'
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}>
    <rect width='24' height='24' fill='white' />
    <mask
      id='mask0_6020_65224'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.8462 0H4.62692C3.73212 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73212 24 4.62692 24H18.7269C19.6217 24 20.3538 23.2636 20.3538 22.3636V6.54545L13.8462 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask0_6020_65224)'>
      <path
        d='M13.8462 0H4.62692C3.73212 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73212 24 4.62692 24H18.7269C19.6217 24 20.3538 23.2636 20.3538 22.3636V6.54545L16.5577 3.81818L13.8462 0Z'
        fill='#4285F4'
      />
    </g>
    <mask
      id='mask1_6020_65224'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.8462 0H4.62692C3.73212 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73212 24 4.62692 24H18.7269C19.6217 24 20.3538 23.2636 20.3538 22.3636V6.54545L13.8462 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask1_6020_65224)'>
      <path
        d='M14.322 6.06683L20.3538 12.1323V6.54547L14.322 6.06683Z'
        fill='url(#paint0_linear_6020_65224)'
      />
    </g>
    <mask
      id='mask2_6020_65224'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.8462 0H4.62692C3.73212 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73212 24 4.62692 24H18.7269C19.6217 24 20.3538 23.2636 20.3538 22.3636V6.54545L13.8462 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask2_6020_65224)'>
      <path
        d='M7.33844 17.4545H16.0154V16.3636H7.33844V17.4545ZM7.33844 19.6364H13.8461V18.5455H7.33844V19.6364ZM7.33844 12V13.0909H16.0154V12H7.33844ZM7.33844 15.2727H16.0154V14.1818H7.33844V15.2727Z'
        fill='#F1F1F1'
      />
    </g>
    <mask
      id='mask3_6020_65224'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.8462 0H4.62692C3.73212 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73212 24 4.62692 24H18.7269C19.6217 24 20.3538 23.2636 20.3538 22.3636V6.54545L13.8462 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask3_6020_65224)'>
      <path
        d='M13.8461 0V4.90909C13.8461 5.81318 14.5742 6.54545 15.4731 6.54545H20.3538L13.8461 0Z'
        fill='#A1C2FA'
      />
    </g>
    <mask
      id='mask4_6020_65224'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.8462 0H4.62692C3.73212 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73212 24 4.62692 24H18.7269C19.6217 24 20.3538 23.2636 20.3538 22.3636V6.54545L13.8462 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask4_6020_65224)'>
      <path
        d='M4.62692 0C3.73212 0 3 0.736364 3 1.63636V1.77273C3 0.872727 3.73212 0.136364 4.62692 0.136364H13.8462V0H4.62692Z'
        fill='white'
        fillOpacity='0.2'
      />
    </g>
    <mask
      id='mask5_6020_65224'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.8462 0H4.62692C3.73212 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73212 24 4.62692 24H18.7269C19.6217 24 20.3538 23.2636 20.3538 22.3636V6.54545L13.8462 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask5_6020_65224)'>
      <path
        d='M18.7269 23.8637H4.62692C3.73212 23.8637 3 23.1273 3 22.2273V22.3637C3 23.2637 3.73212 24 4.62692 24H18.7269C19.6217 24 20.3538 23.2637 20.3538 22.3637V22.2273C20.3538 23.1273 19.6217 23.8637 18.7269 23.8637Z'
        fill='#1A237E'
        fillOpacity='0.2'
      />
    </g>
    <mask
      id='mask6_6020_65224'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.8462 0H4.62692C3.73212 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73212 24 4.62692 24H18.7269C19.6217 24 20.3538 23.2636 20.3538 22.3636V6.54545L13.8462 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask6_6020_65224)'>
      <path
        d='M15.4731 6.54548C14.5742 6.54548 13.8461 5.81321 13.8461 4.90912V5.04548C13.8461 5.94957 14.5742 6.68185 15.4731 6.68185H20.3538V6.54548H15.4731Z'
        fill='#1A237E'
        fillOpacity='0.1'
      />
    </g>
    <path
      d='M13.8462 0H4.62692C3.73212 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73212 24 4.62692 24H18.7269C19.6217 24 20.3538 23.2636 20.3538 22.3636V6.54545L13.8462 0Z'
      fill='url(#paint1_radial_6020_65224)'
    />
    <defs>
      <linearGradient
        id='paint0_linear_6020_65224'
        x1='315.945'
        y1='58.1455'
        x2='315.945'
        y2='612.697'
        gradientUnits='userSpaceOnUse'>
        <stop stopColor='#1A237E' stopOpacity='0.2' />
        <stop offset='1' stopColor='#1A237E' stopOpacity='0.02' />
      </linearGradient>
      <radialGradient
        id='paint1_radial_6020_65224'
        cx='0'
        cy='0'
        r='1'
        gradientUnits='userSpaceOnUse'
        gradientTransform='translate(57.9778 47.1609) scale(2798.28 2798.28)'>
        <stop stopColor='white' stopOpacity='0.1' />
        <stop offset='1' stopColor='white' stopOpacity='0' />
      </radialGradient>
    </defs>
  </svg>
);

export default IconGoogleDocs;
