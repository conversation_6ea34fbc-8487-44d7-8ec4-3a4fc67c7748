import { CustomIconProps } from '..';

const IconHubspot = (props: CustomIconProps) => (
  <svg
    width='24'
    height='24'
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}>
    <g clipPath='url(#clip0_5229_59187)'>
      <path d='M24 0H0V24H24V0Z' fill='#FF5C35' />
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M15.1935 15.2195C14.1246 15.2195 13.2581 14.3459 13.2581 13.2684C13.2581 12.1907 14.1246 11.3171 15.1935 11.3171C16.2625 11.3171 17.129 12.1907 17.129 13.2684C17.129 14.3459 16.2625 15.2195 15.1935 15.2195ZM15.7632 9.53277V7.8137C16.2139 7.60176 16.5296 7.14784 16.5296 6.62104V6.58136C16.5296 5.85431 15.9322 5.25947 15.2019 5.25947H15.1623C14.4321 5.25947 13.8346 5.85431 13.8346 6.58136V6.62104C13.8346 7.14784 14.1503 7.60194 14.601 7.81388V9.53277C13.93 9.63605 13.3169 9.91159 12.8113 10.3171L8.07075 6.64552C8.10204 6.52592 8.12401 6.4028 8.1242 6.27337C8.12494 5.45009 7.45562 4.78163 6.62838 4.78052C5.80151 4.77959 5.12996 5.44619 5.12903 6.26966C5.1281 7.09314 5.79742 7.76159 6.62466 7.76252C6.89414 7.76289 7.14369 7.68668 7.36232 7.56282L12.0254 11.1747C11.6289 11.7707 11.3965 12.4845 11.3965 13.2531C11.3965 14.0577 11.6518 14.802 12.0831 15.4141L10.6651 16.8261C10.553 16.7925 10.4368 16.7692 10.3135 16.7692C9.63398 16.7692 9.08292 17.3176 9.08292 17.9943C9.08292 18.6711 9.63398 19.2195 10.3135 19.2195C10.9933 19.2195 11.5442 18.6711 11.5442 17.9943C11.5442 17.8719 11.5207 17.756 11.487 17.6444L12.8897 16.2477C13.5264 16.7315 14.3194 17.0223 15.1822 17.0223C17.273 17.0223 18.9677 15.3347 18.9677 13.2531C18.9677 11.3687 17.5773 9.81183 15.7632 9.53277Z'
        fill='white'
      />
    </g>
    <defs>
      <clipPath id='clip0_5229_59187'>
        <rect width='24' height='24' fill='white' />
      </clipPath>
    </defs>
  </svg>
);

export default IconHubspot;
