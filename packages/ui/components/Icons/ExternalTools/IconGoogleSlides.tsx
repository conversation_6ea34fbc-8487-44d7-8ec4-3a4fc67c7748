import { CustomIconProps } from '..';

const IconGoogleSlides = (props: CustomIconProps) => (
  <svg
    width='24'
    height='24'
    viewBox='0 0 24 24'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
    {...props}>
    <mask
      id='mask0_6020_65398'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.9091 0H4.63636C3.73636 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73636 24 4.63636 24H18.8182C19.7182 24 20.4545 23.2636 20.4545 22.3636V6.54545L13.9091 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask0_6020_65398)'>
      <path
        d='M13.9091 0H4.63636C3.73636 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73636 24 4.63636 24H18.8182C19.7182 24 20.4545 23.2636 20.4545 22.3636V6.54545L16.6364 3.81818L13.9091 0Z'
        fill='#F4B400'
      />
    </g>
    <mask
      id='mask1_6020_65398'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.9091 0H4.63636C3.73636 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73636 24 4.63636 24H18.8182C19.7182 24 20.4545 23.2636 20.4545 22.3636V6.54545L13.9091 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask1_6020_65398)'>
      <path
        d='M15.2727 10.9091H8.18183C7.73183 10.9091 7.36365 11.2773 7.36365 11.7273V18.8182C7.36365 19.2682 7.73183 19.6364 8.18183 19.6364H15.2727C15.7227 19.6364 16.0909 19.2682 16.0909 18.8182V11.7273C16.0909 11.2773 15.7227 10.9091 15.2727 10.9091ZM15 17.1818H8.45456V13.3637H15V17.1818Z'
        fill='#F1F1F1'
      />
    </g>
    <mask
      id='mask2_6020_65398'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.9091 0H4.63636C3.73636 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73636 24 4.63636 24H18.8182C19.7182 24 20.4545 23.2636 20.4545 22.3636V6.54545L13.9091 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask2_6020_65398)'>
      <path
        d='M14.3877 6.06683L20.4545 12.1323V6.54547L14.3877 6.06683Z'
        fill='url(#paint0_linear_6020_65398)'
      />
    </g>
    <mask
      id='mask3_6020_65398'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.9091 0H4.63636C3.73636 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73636 24 4.63636 24H18.8182C19.7182 24 20.4545 23.2636 20.4545 22.3636V6.54545L13.9091 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask3_6020_65398)'>
      <path
        d='M13.9091 0V4.90909C13.9091 5.81318 14.6413 6.54545 15.5454 6.54545H20.4545L13.9091 0Z'
        fill='#FADA80'
      />
    </g>
    <mask
      id='mask4_6020_65398'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.9091 0H4.63636C3.73636 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73636 24 4.63636 24H18.8182C19.7182 24 20.4545 23.2636 20.4545 22.3636V6.54545L13.9091 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask4_6020_65398)'>
      <path
        d='M13.9091 0V0.136364L20.3181 6.54545H20.4545L13.9091 0Z'
        fill='white'
        fillOpacity='0.1'
      />
    </g>
    <mask
      id='mask5_6020_65398'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.9091 0H4.63636C3.73636 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73636 24 4.63636 24H18.8182C19.7182 24 20.4545 23.2636 20.4545 22.3636V6.54545L13.9091 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask5_6020_65398)'>
      <path
        d='M4.63636 0C3.73636 0 3 0.736364 3 1.63636V1.77273C3 0.872727 3.73636 0.136364 4.63636 0.136364H13.9091V0H4.63636Z'
        fill='white'
        fillOpacity='0.2'
      />
    </g>
    <mask
      id='mask6_6020_65398'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.9091 0H4.63636C3.73636 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73636 24 4.63636 24H18.8182C19.7182 24 20.4545 23.2636 20.4545 22.3636V6.54545L13.9091 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask6_6020_65398)'>
      <path
        d='M18.8182 23.8637H4.63636C3.73636 23.8637 3 23.1273 3 22.2273V22.3637C3 23.2637 3.73636 24 4.63636 24H18.8182C19.7182 24 20.4545 23.2637 20.4545 22.3637V22.2273C20.4545 23.1273 19.7182 23.8637 18.8182 23.8637Z'
        fill='#BF360C'
        fillOpacity='0.2'
      />
    </g>
    <mask
      id='mask7_6020_65398'
      style={{ maskType: 'luminance' }}
      maskUnits='userSpaceOnUse'
      x='3'
      y='0'
      width='18'
      height='24'>
      <path
        d='M13.9091 0H4.63636C3.73636 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73636 24 4.63636 24H18.8182C19.7182 24 20.4545 23.2636 20.4545 22.3636V6.54545L13.9091 0Z'
        fill='white'
      />
    </mask>
    <g mask='url(#mask7_6020_65398)'>
      <path
        d='M15.5454 6.54548C14.6413 6.54548 13.9091 5.81321 13.9091 4.90912V5.04548C13.9091 5.94957 14.6413 6.68185 15.5454 6.68185H20.4545V6.54548H15.5454Z'
        fill='#BF360C'
        fillOpacity='0.1'
      />
    </g>
    <path
      d='M13.9091 0H4.63636C3.73636 0 3 0.736364 3 1.63636V22.3636C3 23.2636 3.73636 24 4.63636 24H18.8182C19.7182 24 20.4545 23.2636 20.4545 22.3636V6.54545L13.9091 0Z'
      fill='url(#paint1_radial_6020_65398)'
    />
    <defs>
      <linearGradient
        id='paint0_linear_6020_65398'
        x1='317.761'
        y1='58.1455'
        x2='317.761'
        y2='612.697'
        gradientUnits='userSpaceOnUse'>
        <stop stopColor='#BF360C' stopOpacity='0.2' />
        <stop offset='1' stopColor='#BF360C' stopOpacity='0.02' />
      </linearGradient>
      <radialGradient
        id='paint1_radial_6020_65398'
        cx='0'
        cy='0'
        r='1'
        gradientUnits='userSpaceOnUse'
        gradientTransform='translate(58.2969 47.4346) scale(2814.52 2814.52)'>
        <stop stopColor='white' stopOpacity='0.1' />
        <stop offset='1' stopColor='white' stopOpacity='0' />
      </radialGradient>
    </defs>
  </svg>
);

export default IconGoogleSlides;
