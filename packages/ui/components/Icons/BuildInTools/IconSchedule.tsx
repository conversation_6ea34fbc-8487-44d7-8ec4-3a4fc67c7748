import { CustomIconProps } from '..';

const IconSchedule = (props: CustomIconProps) => {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M4.11209 6.53954L12 11.7981L19.8879 6.53954C19.7213 6.21897 19.3862 6 19 6H5C4.61376 6 4.27867 6.21897 4.11209 6.53954ZM20 8.86852L12.5547 13.8321C12.2188 14.056 11.7812 14.056 11.4453 13.8321L4 8.86852V17C4 17.5523 4.44772 18 5 18H19C19.5523 18 20 17.5523 20 17V8.86852ZM2 7C2 5.34315 3.34315 4 5 4H19C20.6569 4 22 5.34315 22 7V17C22 18.6569 20.6569 20 19 20H5C3.34315 20 2 18.6569 2 17V7Z'
        fill={props.fill || '#5C5C5C'}
      />
    </svg>
  );
};

export default IconSchedule;
