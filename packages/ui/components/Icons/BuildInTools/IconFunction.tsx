import { CustomIconProps } from '..';

const IconFunction = (props: CustomIconProps) => {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M6 6C5.73478 6 5.48043 6.10536 5.29289 6.29289C5.10536 6.48043 5 6.73478 5 7V11C5 11.2652 4.89464 11.5196 4.70711 11.7071L4.41421 12L4.70711 12.2929C4.89464 12.4804 5 12.7348 5 13V17C5 17.2652 5.10536 17.5196 5.29289 17.7071C5.48043 17.8946 5.73478 18 6 18C6.55228 18 7 18.4477 7 19C7 19.5523 6.55228 20 6 20C5.20435 20 4.44129 19.6839 3.87868 19.1213C3.31607 18.5587 3 17.7956 3 17V13.4142L2.29289 12.7071C1.90237 12.3166 1.90237 11.6834 2.29289 11.2929L3 10.5858V7C3 6.20435 3.31607 5.44129 3.87868 4.87868C4.44129 4.31607 5.20435 4 6 4C6.55228 4 7 4.44772 7 5C7 5.55228 6.55228 6 6 6ZM17 5C17 4.44772 17.4477 4 18 4C18.7956 4 19.5587 4.31607 20.1213 4.87868C20.6839 5.44129 21 6.20435 21 7V10.5858L21.7071 11.2929C22.0976 11.6834 22.0976 12.3166 21.7071 12.7071L21 13.4142V17C21 17.7957 20.6839 18.5587 20.1213 19.1213C19.5587 19.6839 18.7957 20 18 20C17.4477 20 17 19.5523 17 19C17 18.4477 17.4477 18 18 18C18.2652 18 18.5196 17.8946 18.7071 17.7071C18.8946 17.5196 19 17.2652 19 17V13C19 12.7348 19.1054 12.4804 19.2929 12.2929L19.5858 12L19.2929 11.7071C19.1054 11.5196 19 11.2652 19 11V7C19 6.73478 18.8946 6.48043 18.7071 6.29289C18.5196 6.10536 18.2652 6 18 6C17.4477 6 17 5.55228 17 5ZM8 12C8 11.4477 8.44772 11 9 11H9.01C9.56228 11 10.01 11.4477 10.01 12C10.01 12.5523 9.56228 13 9.01 13H9C8.44772 13 8 12.5523 8 12ZM12.01 13H12C11.4477 13 11 12.5523 11 12C11 11.4477 11.4477 11 12 11H12.01C12.5623 11 13.01 11.4477 13.01 12C13.01 12.5523 12.5623 13 12.01 13ZM15.01 13H15C14.4477 13 14 12.5523 14 12C14 11.4477 14.4477 11 15 11H15.01C15.5623 11 16.01 11.4477 16.01 12C16.01 12.5523 15.5623 13 15.01 13Z'
        fill={props.fill || '#5C5C5C'}
      />
    </svg>
  );
};

export default IconFunction;
