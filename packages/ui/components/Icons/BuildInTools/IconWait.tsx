import { CustomIconProps } from '..';

const IconWait = (props: CustomIconProps) => {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.58579 2.58579C5.96086 2.21071 6.46957 2 7 2H17C17.5304 2 18.0391 2.21071 18.4142 2.58579C18.7893 2.96086 19 3.46957 19 4V6C19 7.85651 18.2625 9.63699 16.9497 10.9497C16.5428 11.3567 16.0908 11.7084 15.6056 12C16.0908 12.2916 16.5428 12.6433 16.9497 13.0503C18.2625 14.363 19 16.1435 19 18V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V18C5 16.1435 5.7375 14.363 7.05025 13.0503C7.45724 12.6433 7.90918 12.2916 8.39441 12C7.90918 11.7084 7.45724 11.3567 7.05025 10.9497C5.7375 9.63699 5 7.85652 5 6V4C5 3.46957 5.21071 2.96086 5.58579 2.58579ZM12 13C10.6739 13 9.40215 13.5268 8.46447 14.4645C8.01823 14.9107 7.66505 15.4326 7.41742 16H16.5826C16.3349 15.4326 15.9818 14.9107 15.5355 14.4645C14.5979 13.5268 13.3261 13 12 13ZM17 18H7V20H17V18ZM17 4H7L7 6H17V4ZM16.5826 8H7.41742C7.66505 8.5674 8.01823 9.0893 8.46447 9.53553C9.40215 10.4732 10.6739 11 12 11C13.3261 11 14.5979 10.4732 15.5355 9.53553C15.9818 9.0893 16.3349 8.5674 16.5826 8Z'
        fill={props.fill || '#5C5C5C'}
      />
    </svg>
  );
};

export default IconWait;
