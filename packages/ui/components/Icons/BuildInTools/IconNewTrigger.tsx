import { CustomIconProps } from '..';

const IconNewTrigger = (props: CustomIconProps) => {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M2 7C2 6.44772 2.44772 6 3 6H8C8.32631 6 8.6321 6.15921 8.81923 6.42654L12.0207 11H18.5858L17.2929 9.70711C16.9024 9.31658 16.9024 8.68342 17.2929 8.29289C17.6834 7.90237 18.3166 7.90237 18.7071 8.29289L21.7071 11.2929C22.0976 11.6834 22.0976 12.3166 21.7071 12.7071L18.7071 15.7071C18.3166 16.0976 17.6834 16.0976 17.2929 15.7071C16.9024 15.3166 16.9024 14.6834 17.2929 14.2929L18.5858 13H12.0161L8.81962 17.5729C8.63253 17.8406 8.32655 18 8 18H3C2.44772 18 2 17.5523 2 17C2 16.4477 2.44772 16 3 16H7.47892L10.2771 11.9968L7.47934 8H3C2.44772 8 2 7.55228 2 7Z'
        fill={props.fill || '#5C5C5C'}
      />
    </svg>
  );
};

export default IconNewTrigger;
