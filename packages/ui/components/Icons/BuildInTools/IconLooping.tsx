import { CustomIconProps } from '..';

const IconLooping = (props: CustomIconProps) => {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M5.0862 7.38046C5.99992 7.00207 7.00533 6.90314 7.97527 7.09619C8.93696 7.28759 9.82095 7.7575 10.5174 8.44718C11.0672 8.94796 11.5638 9.50223 12.0001 10.101C12.4365 9.50223 12.933 8.94796 13.4828 8.44718C14.1793 7.75749 15.0633 7.28759 16.025 7.09619C16.9949 6.90314 18.0003 7.00207 18.914 7.38046C19.8278 7.75885 20.6087 8.39972 21.1582 9.22199C21.7077 10.0443 22.001 11.011 22.001 12C22.001 12.989 21.7077 13.9557 21.1582 14.778C20.6087 15.6003 19.8278 16.2411 18.914 16.6195C18.0003 16.9979 16.9949 17.0969 16.025 16.9038C15.0633 16.7124 14.1793 16.2425 13.4828 15.5528C12.933 15.052 12.4365 14.4978 12.0001 13.8991C11.5638 14.4978 11.0673 15.052 10.5174 15.5528C9.82096 16.2425 8.93696 16.7124 7.97527 16.9038C7.00533 17.0969 5.99992 16.9979 5.0862 16.6195C4.17248 16.2411 3.3915 15.6003 2.84202 14.778C2.29255 13.9557 1.99927 12.989 1.99927 12C1.99927 11.011 2.29255 10.0443 2.84202 9.22199C3.3915 8.39972 4.17248 7.75885 5.0862 7.38046ZM10.8581 12C10.3993 11.2237 9.82581 10.5199 9.15635 9.91276C9.14426 9.9018 9.13244 9.89054 9.12091 9.879C8.70139 9.45935 8.16684 9.17354 7.58487 9.05771C7.0029 8.94188 6.39966 9.00124 5.85143 9.22828C5.3032 9.45531 4.83461 9.83983 4.50492 10.3332C4.17524 10.8266 3.99927 11.4066 3.99927 12C3.99927 12.5934 4.17524 13.1734 4.50492 13.6668C4.83461 14.1602 5.3032 14.5447 5.85143 14.7717C6.39966 14.9988 7.0029 15.0581 7.58487 14.9423C8.16684 14.8265 8.70139 14.5407 9.12091 14.121C9.13244 14.1095 9.14426 14.0982 9.15635 14.0872C9.82581 13.4801 10.3993 12.7763 10.8581 12ZM13.1421 12C13.601 12.7763 14.1744 13.4801 14.8439 14.0872C14.856 14.0982 14.8678 14.1095 14.8793 14.121C15.2989 14.5407 15.8334 14.8265 16.4154 14.9423C16.9973 15.0581 17.6006 14.9988 18.1488 14.7717C18.697 14.5447 19.1656 14.1602 19.4953 13.6668C19.825 13.1734 20.001 12.5934 20.001 12C20.001 11.4066 19.825 10.8266 19.4953 10.3332C19.1656 9.83983 18.697 9.45531 18.1488 9.22828C17.6006 9.00124 16.9973 8.94188 16.4154 9.05771C15.8334 9.17354 15.2989 9.45935 14.8793 9.879C14.8678 9.89054 14.856 9.9018 14.8439 9.91276C14.1744 10.5199 13.601 11.2237 13.1421 12Z'
        fill={props.fill || '#5C5C5C'}
      />
    </svg>
  );
};

export default IconLooping;
