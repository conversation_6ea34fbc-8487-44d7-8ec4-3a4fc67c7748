import { CustomIconProps } from '..';

const IconPath = (props: CustomIconProps) => {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M17.2929 3.29288C17.6834 2.90236 18.3166 2.90238 18.7071 3.29291L21.707 6.29291C22.0975 6.68343 22.0975 7.31657 21.707 7.70709L18.7071 10.7071C18.3166 11.0976 17.6834 11.0976 17.2929 10.7071C16.9024 10.3166 16.9024 9.68344 17.2929 9.29291L18.5857 8H13.521L10.7228 12.0031L13.5207 16H18.5858L17.2929 14.7071C16.9024 14.3166 16.9024 13.6834 17.2929 13.2929C17.6834 12.9024 18.3166 12.9024 18.7071 13.2929L21.7071 16.2929C22.0976 16.6834 22.0976 17.3166 21.7071 17.7071L18.7071 20.7071C18.3166 21.0976 17.6834 21.0976 17.2929 20.7071C16.9024 20.3166 16.9024 19.6834 17.2929 19.2929L18.5858 18H13C12.6737 18 12.3679 17.8408 12.1808 17.5735L8.97934 13H3C2.44772 13 2 12.5523 2 12C2 11.4477 2.44772 11 3 11H8.9838L12.1803 6.42709C12.3674 6.15944 12.6733 6 12.9999 6H18.5857L17.2929 4.70709C16.9024 4.31656 16.9024 3.6834 17.2929 3.29288Z'
        fill={props.fill || '#5C5C5C'}
      />
    </svg>
  );
};

export default IconPath;
