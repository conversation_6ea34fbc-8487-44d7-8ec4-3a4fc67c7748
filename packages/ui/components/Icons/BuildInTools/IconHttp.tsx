import { CustomIconProps } from '..';

const IconHttp = (props: CustomIconProps) => {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12.7265 2.026C12.6135 1.99972 12.4959 1.99299 12.3794 2.00708C12.2535 2.00238 12.127 2.00001 12 2.00001C11.873 2.00001 11.7465 2.00238 11.6207 2.00707C11.5041 1.99299 11.3866 1.99972 11.2736 2.02599C6.08984 2.39813 2 6.72145 2 12C2 17.2786 6.08984 21.6019 11.2736 21.974C11.3866 22.0003 11.5041 22.007 11.6207 21.993C11.7465 21.9976 11.873 22 12 22C12.127 22 12.2535 21.9976 12.3794 21.993C12.4959 22.007 12.6135 22.0003 12.7265 21.974C17.9102 21.6019 22 17.2786 22 12C22 6.72147 17.9102 2.39816 12.7265 2.026ZM9.62476 4.35852C7.68843 4.95974 6.06715 6.27669 5.07026 8.00001H8.37231C8.65799 6.74664 9.07752 5.52566 9.62476 4.35852ZM10.4303 8.00001C10.7798 6.64646 11.3064 5.33984 12 4.1151C12.6937 5.33984 13.2202 6.64646 13.5697 8.00001H10.4303ZM8.03369 10H4.25203C4.08751 10.6393 4 11.3094 4 12C4 12.6906 4.08751 13.3608 4.25204 14H8.03369C7.95967 13.338 7.92224 12.6703 7.92224 12C7.92224 11.3297 7.95967 10.662 8.03369 10ZM10.0477 14C9.96443 13.3389 9.92224 12.6709 9.92224 12C9.92224 11.3291 9.96443 10.6612 10.0477 10H13.9523C14.0356 10.6612 14.0778 11.3291 14.0778 12C14.0778 12.6709 14.0356 13.3389 13.9523 14H10.0477ZM8.37231 16H5.07026C6.06715 17.7233 7.68843 19.0403 9.62476 19.6415C9.07752 18.4744 8.65799 17.2534 8.37231 16ZM12 19.8849C11.3064 18.6602 10.7798 17.3536 10.4303 16H13.5697C13.2202 17.3536 12.6937 18.6602 12 19.8849ZM14.3753 19.6415C16.3116 19.0403 17.9329 17.7233 18.9297 16H15.6277C15.3421 17.2534 14.9225 18.4744 14.3753 19.6415ZM15.9663 14H19.748C19.9125 13.3608 20 12.6906 20 12C20 11.3094 19.9125 10.6393 19.748 10H15.9663C16.0404 10.662 16.0778 11.3297 16.0778 12C16.0778 12.6703 16.0404 13.338 15.9663 14ZM15.6277 8.00001H18.9297C17.9329 6.2767 16.3116 4.95976 14.3753 4.35854C14.9225 5.52566 15.3421 6.74664 15.6277 8.00001Z'
        fill={props.fill || '#5C5C5C'}
      />
    </svg>
  );
};

export default IconHttp;
