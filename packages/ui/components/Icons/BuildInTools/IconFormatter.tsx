import { CustomIconProps } from '..';

const IconFormatter = (props: CustomIconProps) => {
  return (
    <svg
      width='24'
      height='24'
      viewBox='0 0 24 24'
      fill='none'
      xmlns='http://www.w3.org/2000/svg'
      {...props}>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M12 5C10.6739 5 9.40215 5.52678 8.46447 6.46447C7.52678 7.40215 7 8.67392 7 10V12C7 12.5523 6.55228 13 6 13C5.44772 13 5 12.5523 5 12V10C5 8.14348 5.7375 6.36301 7.05025 5.05025C8.36301 3.7375 10.1435 3 12 3C13.8565 3 15.637 3.7375 16.9497 5.05025C18.1673 6.26779 18.89 7.88765 18.9884 9.59736L20.2929 8.29289C20.6834 7.90237 21.3166 7.90237 21.7071 8.29289C22.0976 8.68342 22.0976 9.31658 21.7071 9.70711L18.7071 12.7071C18.3166 13.0976 17.6834 13.0976 17.2929 12.7071L14.2929 9.70711C13.9024 9.31658 13.9024 8.68342 14.2929 8.29289C14.6834 7.90237 15.3166 7.90237 15.7071 8.29289L16.9812 9.56702C16.8798 8.3996 16.3706 7.29955 15.5355 6.46447C14.5979 5.52678 13.3261 5 12 5ZM6 17C5.44772 17 5 17.4477 5 18C5 18.5523 5.44772 19 6 19C6.55228 19 7 18.5523 7 18C7 17.4477 6.55228 17 6 17ZM3 18C3 16.3431 4.34315 15 6 15C7.65685 15 9 16.3431 9 18C9 19.6569 7.65685 21 6 21C4.34315 21 3 19.6569 3 18ZM15 18C15 16.3431 16.3431 15 18 15C19.6569 15 21 16.3431 21 18C21 19.6569 19.6569 21 18 21C16.3431 21 15 19.6569 15 18ZM18 17C17.4477 17 17 17.4477 17 18C17 18.5523 17.4477 19 18 19C18.5523 19 19 18.5523 19 18C19 17.4477 18.5523 17 18 17Z'
        fill={props.fill || '#5C5C5C'}
      />
    </svg>
  );
};

export default IconFormatter;
