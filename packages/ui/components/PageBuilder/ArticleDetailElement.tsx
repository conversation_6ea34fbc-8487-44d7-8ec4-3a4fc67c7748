import { Box, Divider, Flex, rem, Text } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import dayjs from 'dayjs';
import { ListType } from '../../types/pageBuilder';
import { withTolgee } from '../hoc/withTolgee';
import { BlockNoteViewer } from '@resola-ai/blocknote-editor';
import {
  generateResponsivePadding,
  generateResponsiveStyles,
  generateResponsiveMaxWidth,
} from '../../utils';

const ArticleDetailElement = (props: Record<string, any>) => {
  const { t } = useTranslate('page-builder');
  const {
    article,
    maxWidth,
    type,
    backgroundColor,
    padding,
    hasMaxWidth,
    textColor,
    dividerColor,
    contentBackgroundColor,
    borderColor
  } = props;

  if (!article) return null;

  return (
    <Flex
      styles={generateResponsiveStyles({ padding: generateResponsivePadding(padding) })}
      w={'100%'}
      bg={backgroundColor}>
      <Flex
        styles={generateResponsiveStyles({
          maxWidth: generateResponsiveMaxWidth(hasMaxWidth, maxWidth),
          borderRadius: type === ListType.BlankPage ? '0' : rem(20),
          background: type === ListType.BlankPage ? 'transparent' : contentBackgroundColor,
          border: type === ListType.BlankPage ? 'none' : `1px solid ${borderColor}`,
        })}
        mih={rem(360)}
        m={'0'}>
        <Flex p='lg' w={'100%'} h={'100%'}>
          <Flex direction='column' w={'100%'} gap={rem(20)}>
            <Flex>
              <Box>
                <Text fw={500} size={'xl'} c={textColor}>
                  {article.title}
                </Text>
              </Box>
            </Flex>
            <Flex gap={rem(20)} justify={'space-between'}>
              <Box c={textColor}>
                {t('lastUpdated')}:{' '}
                {article ? dayjs(article.updated_at).format(t('lastUpdatedDateFormat')) : ''}
              </Box>
            </Flex>
            <Divider color={dividerColor} />
            <Flex>
              <BlockNoteViewer initialHTML={article?.content_raw || ''} textColor={textColor} />
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default withTolgee(ArticleDetailElement);
