import { Accordion, Box, Flex, rem, Text, Drawer } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconArticle, IconMenu2 } from '@tabler/icons-react';
import { uniqueId } from 'lodash';
import { ArticleType, Category, CategoryArticle, CategoryTypes } from '../../types/pageBuilder';
import { useEffect, useState } from 'react';
import {
  isResponsiveProp,
  createResponsiveValue,
  generateResponsiveStyles,
  generateResponsivePadding,
} from '../../utils';
import { useDisclosure } from '@mantine/hooks';
import { withTolgee } from '../hoc';
import { useTranslate } from '@tolgee/react';

const useStyles = createStyles(theme => ({
  categoryTreeWrapper: {
    '@media (max-width: 1199px)': {
      '&:not(.preview-desktop)': {
        '.tree-view': {
          display: 'none !important',
        },
        '.overlay-view': {
          display: 'flex !important',
        },
      },
    },
    '@media (min-width: 1200px)': {
      '&:not(.preview-mobile), &:not(.preview-tablet)': {
        '.tree-view': {
          display: 'flex !important',
        },
        '.overlay-view': {
          display: 'none !important',
        },
      },
    },
    '&.preview-mobile, &.preview-tablet': {
      '.tree-view': {
        display: 'none !important',
      },
      '.overlay-view': {
        display: 'flex !important',
      },
    },
    '&.preview-desktop': {
      '.tree-view': {
        display: 'flex !important',
      },
      '.overlay-view': {
        display: 'none !important',
      },
    },
  },
  categoryTreeContainer: {
    '.mantine-Accordion-content': {
      paddingRight: '0px !important',
    },
    '.mantine-Accordion-control:hover': {
      backgroundColor: 'transparent !important',
    },
    'a.category-link': {
      cursor: 'pointer',
      color: `${theme.colors.decaGrey[6]} !important`,
      textDecoration: 'none !important',
      '&:hover': {
        color: `${theme.colors.decaBlue[6]} !important`,
      },
    },
  },
  menuButtonContainer: {
    cursor: 'pointer',
    color: `${theme.colors.decaGrey[6]} !important`,
  },
  subCategoryItem: {
    borderBottom: '0px !important',
    'a.subcategory-link': {
      cursor: 'pointer',
      color: `${theme.colors.decaGrey[6]} !important`,
      textDecoration: 'none !important',
      '&:hover': {
        color: `${theme.colors.decaBlue[6]} !important`,
      },
    },
  },
  articleItem: {
    color: `${theme.colors.decaGrey[6]} !important`,
    '&:hover': {
      color: `${theme.colors.decaBlue[6]} !important`,
    },
    a: {
      cursor: 'pointer',
      color: `${theme.colors.decaGrey[6]}`,
      textDecoration: 'none !important',
      '&:hover': {
        color: `${theme.colors.decaBlue[6]} !important`,
      },
    },
  },
}));

const ArticleItem = ({ label, url }: { label: string; url: string }) => {
  const { classes } = useStyles();
  return (
    <Flex
      my={rem(10)}
      align='center'
      gap={rem(10)}
      c={'decaGrey.8'}
      className={classes.articleItem}>
      <Box h={rem(20)} w={rem(20)}>
        <IconArticle size={20} color='currentColor' />
      </Box>
      <Text truncate>
        <a href={url} title={label}>
          {label}
        </a>
      </Text>
    </Flex>
  );
};

type CategoryTreeElementProps = {
  padding: Record<string, number>;
  width: Record<string, any>;
  categories: Category[] | CategoryArticle[];
  selectedCategory: string;
  selectedSubCategory: string;
  selectedArticle?: string;
  selectedElement?: string;
  articleDetailSlug?: string;
  categoryListSlug?: string;
  styles?: Record<string, any>;
  showRightDivider: boolean;
  dividerColor: string;
  previewMode?: string;
  containerId?: string;
};

const CategoryTreeElement = ({
  padding,
  width,
  categories,
  selectedCategory,
  selectedSubCategory,
  selectedArticle,
  selectedElement,
  articleDetailSlug,
  categoryListSlug,
  styles,
  showRightDivider,
  dividerColor,
  previewMode,
  containerId,
}: CategoryTreeElementProps) => {
  const { classes } = useStyles();
  const { t } = useTranslate('page-builder');
  const [opened, { open, close }] = useDisclosure();

  const [selectedCategoryId, setSelectedCategoryId] = useState<string[]>([]);
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState<string[]>([]);

  const getUrl = (item: Category | CategoryArticle, type?: string, parentId?: string) => {
    if (!categoryListSlug || !articleDetailSlug) {
      return '#';
    }
    if (type === CategoryTypes.Category) {
      return `${categoryListSlug}?element_id=${selectedElement}&faq_category_id=${item.id}`;
    } else if (type === CategoryTypes.SubCategory) {
      return `${categoryListSlug}?element_id=${selectedElement}&faq_category_id=${parentId}&faq_sub_category_id=${item.id}`;
    } else {
      return `${articleDetailSlug}?faq_article_id=${(item as CategoryArticle).value}&faq_base_id=${(item as CategoryArticle).parentId}&element_id=${selectedElement}`;
    }
  };

  useEffect(() => {
    if (selectedArticle) {
      categories?.forEach(category => {
        if (!selectedCategoryId?.length) {
          if (category.subType === 'article') {
            if (
              category.data.find(
                article => article?.value?.toLowerCase() === selectedArticle?.toLowerCase()
              )
            ) {
              setSelectedCategoryId([category.id]);
            }
          } else {
            category.data.forEach(subCategory => {
              if (!selectedSubCategoryId?.length) {
                if (
                  subCategory.data.find(
                    article => article?.value?.toLowerCase() === selectedArticle?.toLowerCase()
                  )
                ) {
                  setSelectedCategoryId([category.id]);
                  setSelectedSubCategoryId([subCategory.id]);
                }
              }
            });
          }
        }
      });
    } else {
      setSelectedCategoryId(selectedCategory ? [selectedCategory] : []);
      setSelectedSubCategoryId(selectedSubCategory ? [selectedSubCategory] : []);
    }
  }, [categories, selectedArticle, selectedCategory, selectedSubCategory]);

  const renderCategoryTree = (isInOverlay = false) => (
    <Flex
      align={'center'}
      h='100%'
      w='100%'
      styles={generateResponsiveStyles({
        width: isInOverlay
          ? '100%'
          : isResponsiveProp(width)
            ? createResponsiveValue(rem(width.mobile), rem(width.tablet), rem(width.desktop))
            : rem(width),
        minWidth: isInOverlay
          ? '100%'
          : isResponsiveProp(width)
            ? createResponsiveValue(rem(width.mobile), rem(width.tablet), rem(width.desktop))
            : rem(width),
        maxWidth: isInOverlay
          ? '100%'
          : isResponsiveProp(width)
            ? createResponsiveValue(rem(width.mobile), rem(width.tablet), rem(width.desktop))
            : rem(width),
        padding: isInOverlay ? '0px' : generateResponsivePadding(padding),
        borderRight: showRightDivider && !isInOverlay ? `1px solid ${dividerColor}` : 'none',
      })}
      className={classes.categoryTreeContainer}>
      <Accordion
        w='100%'
        multiple={true}
        value={selectedCategoryId}
        onChange={setSelectedCategoryId}
        styles={styles}>
        {categories?.map(category => (
          <Accordion.Item key={category.id} value={category.id || ''}>
            <Accordion.Control>
              <Text truncate>
                <a
                  href={getUrl(category, CategoryTypes.Category)}
                  className='category-link'
                  title={category.name}>
                  {category.name}
                </a>
              </Text>
            </Accordion.Control>
            {!!category.data?.length && (
              <Accordion.Panel pr={0}>
                {category.subType === ArticleType.Category ? (
                  <Accordion
                    w='100%'
                    pr={0}
                    styles={styles}
                    multiple={true}
                    value={selectedSubCategoryId}
                    onChange={setSelectedSubCategoryId}>
                    {(category.data as Category[])?.map((subCategory: Category) => (
                      <Accordion.Item
                        key={subCategory.id}
                        value={subCategory.id || ''}
                        className={classes.subCategoryItem}>
                        <Accordion.Control>
                          <Text truncate>
                            <a
                              href={getUrl(subCategory, CategoryTypes.SubCategory, category.id)}
                              className='subcategory-link'
                              title={subCategory.name}>
                              {subCategory.name}
                            </a>
                          </Text>
                        </Accordion.Control>
                        <Accordion.Panel pr={0}>
                          <Box pl={rem(10)}>
                            {(subCategory.data as CategoryArticle[])?.map(article => (
                              <ArticleItem
                                key={uniqueId('article-item-')}
                                label={article.label || ''}
                                url={getUrl(article)}
                              />
                            ))}
                          </Box>
                        </Accordion.Panel>
                      </Accordion.Item>
                    ))}
                  </Accordion>
                ) : (
                  <Box pl={rem(10)}>
                    {(category.data as CategoryArticle[])?.map(article => (
                      <ArticleItem
                        key={uniqueId('article-item-')}
                        label={article.label || ''}
                        url={getUrl(article)}
                      />
                    ))}
                  </Box>
                )}
              </Accordion.Panel>
            )}
          </Accordion.Item>
        ))}
      </Accordion>
    </Flex>
  );

  return (
    <Flex
      align='center'
      styles={generateResponsiveStyles({
        width: isResponsiveProp(width)
          ? createResponsiveValue('100%', '100%', rem(width.desktop))
          : rem(width),
        minWidth: isResponsiveProp(width)
          ? createResponsiveValue('100%', '100%', rem(width.desktop))
          : rem(width),
        maxWidth: isResponsiveProp(width)
          ? createResponsiveValue('100%', '100%', rem(width.desktop))
          : rem(width),
      })}
      className={`${classes.categoryTreeWrapper} ${previewMode ? `preview-${previewMode}` : ''}`}>
      <Flex align='center' w='100%' className='overlay-view'>
        <Flex
          align='center'
          w='100%'
          className={classes.menuButtonContainer}
          onClick={open}
          styles={generateResponsiveStyles({
            padding: generateResponsivePadding(padding),
            borderBottom: showRightDivider ? `1px solid ${dividerColor}` : 'none',
          })}>
          <IconMenu2 size={18} />
          <Text ml={5}>{t('menu')}</Text>
        </Flex>
        <Drawer
          opened={opened}
          onClose={close}
          position='left'
          size='100%'
          portalProps={previewMode && containerId ? { target: `#${containerId}` } : undefined}
          transitionProps={{ transition: undefined }}>
          {renderCategoryTree(true)}
        </Drawer>
      </Flex>
      <Flex align='center' w='100%' className='tree-view'>
        {renderCategoryTree()}
      </Flex>
    </Flex>
  );
};

export default withTolgee(CategoryTreeElement);
