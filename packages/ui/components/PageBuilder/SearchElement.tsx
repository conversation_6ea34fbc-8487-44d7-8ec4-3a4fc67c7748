import { ActionIcon, Loader, Text, TextInput, Flex } from '@mantine/core';
import { IconSearch } from '@tabler/icons-react';
import { useEffect, useMemo, useRef, useState } from 'react';
import debounce from 'lodash/debounce';
import { axiosService } from '@resola-ai/services-shared';
import styles from './SearchElement.module.css';
import { DecaButton } from '../DecaButton';
import { useTranslate } from '@tolgee/react';
import { withTolgee } from '../hoc/withTolgee';
import {
  BUILDER_CONTAINER_ELEMENT_ID,
  PREVIEW_BUILDER_CONTAINER_ELEMENT_ID,
} from '../../constants/page-builder';
import { createPortal } from 'react-dom';
import { useIMEComposition } from '../../hooks/useIMEComposition';
import { generateResponsiveStyles, createResponsiveValue, isResponsiveProp } from '../../utils/pageBuilder';

const PERSISTENT_ELEMENT_ID_KEY = 'persistentSearchElementId';

const RADIUS = {
  round: 12,
  classic: 'sm',
  pill: 9999,
  square: 0,
};

const HEIGHT = {
  s: '36px',
  m: '48px',
  l: '60px',
};

export const searchArticles = async (faqQuery?: string | null, articleIds?: string[]) => {
  try {
    const articleData = await axiosService.instance.post<any>('/integrations/kb/search', {
      query: faqQuery,
      article_ids: articleIds
    });
    return articleData?.data?.response;
  } catch (error) {
    console.error('Error fetching articles:', error);
    return {
      pagination: {},
      data: []
    };
  }
};

const getElementOffset = (element: HTMLElement) => {
  let offset = {
    left: 0,
    top: 0
  };

  let currentNode = element;
  while (
    currentNode &&
    currentNode.id !== BUILDER_CONTAINER_ELEMENT_ID &&
    currentNode.id !== PREVIEW_BUILDER_CONTAINER_ELEMENT_ID &&
    currentNode !== globalThis.document.body
  ) {
    offset.left = offset.left + currentNode.offsetLeft;
    offset.top = offset.top + currentNode.offsetTop;
    currentNode = currentNode.parentNode as HTMLElement;
  }

  return {
    offset,
    parent: currentNode
  };
}

const SearchElement = (props: Record<string, any>) => {
  const {
    placeholder = '',
    type = 'round',
    backgroundColor,
    borderColor,
    textColor,
    buttonColor,
    showSearchButton = true,
    searchResultPageSlug,
    articleDetailSlug,
    elementId,
    quickResultsPanel,
    selectedArticles,
    isKbEnabled,
    size,
    placeholderColor,
    width
  } = props;

  const [query, setQuery] = useState('');
  const [currentData, setCurrentData] = useState<{ id: string, title: string, base_id }[]>([]);
  const { t } = useTranslate('page-builder');
  const [isShowAll, setIsShowAll] = useState(false);
  const [hasClickedOutSide, setHasClickedOutSide] = useState(false);
  const quickResultRef = useRef<HTMLDivElement | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const { handleCompositionStart, handleCompositionEnd, shouldTriggerOnEnter } = useIMEComposition();

  const executeSearch = () => {
    if (!query.trim() || !searchResultPageSlug || !elementId) return;
    window.location.href = `${searchResultPageSlug}?faq_query=${encodeURIComponent(query)}&element_id=${updatedElementId}`;
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (shouldTriggerOnEnter(event)) {
      executeSearch();
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(event.target.value);
  };

  const handleInputFocus = () => {
    setHasClickedOutSide(false);
  };

  const quickResultPositionRef = useRef({
    left: 0,
    top: 0,
    width: 0
  });
  const wrapperParentRef = useRef<HTMLElement | null>(null);

  /* Goal: If isKbEnabled is true, always update with the latest elementId, if false, use the persistent elementId from localStorage
   */

  // Get the stored value from localStorage or initialize as null
  const getStoredElementId = () => {
    if (typeof window === 'undefined') return null; // Handle SSR
    return localStorage.getItem(PERSISTENT_ELEMENT_ID_KEY);
  };
  // Set the persistent element ID in localStorage
  const setStoredElementId = (id: string) => {
    if (typeof window === 'undefined') return; // Handle SSR
    localStorage.setItem(PERSISTENT_ELEMENT_ID_KEY, id);
  };
  // Add state to store the persistent elementId (initialized from localStorage)
  const [persistentElementId, setPersistentElementId] = useState<string | null | undefined>(null);
  const isInitializedRef = useRef(false);
  // Initialize from localStorage on first render
  useEffect(() => {
    if (!isInitializedRef.current) {
      const storedId = getStoredElementId();
      if (storedId) {
        setPersistentElementId(storedId);
      }
      isInitializedRef.current = true;
    }
  }, []);

  useEffect(() => {
    // If KB is enabled, always update with the latest elementId
    if (isKbEnabled && elementId) {
      setPersistentElementId(elementId);
      setStoredElementId(elementId);
      return;
    }
    // If we don't have a persisted ID yet and have an elementId, store it
    if (!persistentElementId && !getStoredElementId() && elementId) {
      setPersistentElementId(elementId);
      setStoredElementId(elementId);
    }
  }, [elementId, isKbEnabled, persistentElementId]);

  useEffect(() => {
    if (quickResultRef.current) {
      const { offset, parent } = getElementOffset(quickResultRef.current);
      quickResultPositionRef.current = {
        top: offset.top + quickResultRef.current.clientHeight + 5,
        left: offset.left,
        width: quickResultRef.current.clientWidth
      };
      wrapperParentRef.current = parent
    }
  }, []);

  // Use the persistent ID if available, otherwise use elementId
  const updatedElementId = !isKbEnabled && (persistentElementId || getStoredElementId())
    ? (persistentElementId || getStoredElementId())
    : elementId;

  const debounceQuickSearch = useMemo(() => debounce(async (query: string, articleIds: string[]) => {
    const { data } = await searchArticles(query, articleIds)
    setIsLoading(false)
    setCurrentData(data)
  }, 500), [])

  useEffect(() => {
    if (query && quickResultsPanel) {
      setIsShowAll(false)
      setIsLoading(true)
      setCurrentData([])
      debounceQuickSearch(query.trim(), selectedArticles?.map(({value}) => value) || [])
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query]);

  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (quickResultRef.current && !quickResultRef.current.contains(event.target as Node)) {
        setHasClickedOutSide(true);
      }
    }

    document.addEventListener('click', handleOutsideClick);
    return () => {
      document.removeEventListener('click', handleOutsideClick);
    }
  }, []);

  const displayingData = isShowAll ? currentData : currentData.slice(0, 5);

  const getHeight = () => {
    if (isResponsiveProp(size)  ) {
      return createResponsiveValue(HEIGHT[size.mobile], HEIGHT[size.tablet], HEIGHT[size.desktop]);
    }
    return HEIGHT[size];
  }
  
  return (
    <Flex className={styles.searchElement} ref={quickResultRef} styles={generateResponsiveStyles({
        width: width
    })}>
      <TextInput
        placeholder={placeholder || t('searchByKeyword')}
        radius={RADIUS[type]}
        value={query}
        w='100%'
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onCompositionStart={handleCompositionStart}
        onCompositionEnd={handleCompositionEnd}
        onFocus={handleInputFocus}
        styles={generateResponsiveStyles({
          input: {
            height: getHeight(),
            backgroundColor: backgroundColor,
            borderColor: borderColor,
            color: textColor,
            '::placeholder': {
              color: placeholderColor,
            },
          },
          section: {
            width: 'auto'
          }
        })}
        rightSection={
          showSearchButton && (
            <ActionIcon
              onClick={executeSearch}
              bg={buttonColor}
              styles={generateResponsiveStyles({
                root: {
                  borderTopLeftRadius: `0px`,
                  borderBottomLeftRadius: `0px`,
                  borderTopRightRadius: `${RADIUS[type]}px`,
                  borderBottomRightRadius: `${RADIUS[type]}px`,
                  pointerEvents: 'auto',
                  width: getHeight(),
                  height: getHeight(),
                },
              })}>
              <IconSearch size={14} />
            </ActionIcon>
          )
        }
      />
      {query && quickResultsPanel && !hasClickedOutSide && createPortal(<div className={styles.quickSearchResults} style={quickResultPositionRef.current}>
        <Text fw={700} py={8} px={12}>{(!isLoading && !currentData.length) ? t('noResults') : t('results')}</Text>
        {isLoading && <Text py={8} px={12}><Loader color='indigo' size={10} /> {t('quickSearchLoading')}... </Text>}
        {displayingData.map((item) => <a key={item.id} href={articleDetailSlug ? `${articleDetailSlug}?faq_base_id=${item.base_id}&element_id=${updatedElementId}&faq_article_id=${item.id}` : '#'}>{item.title}</a>)}
        {!isShowAll && currentData.length > 5 && <DecaButton size="sm" px={12} variant="secondary_text" onClick={executeSearch}>{t('checkMoreResult', { num: currentData.length - 5 })}</DecaButton>}
      </div>, wrapperParentRef.current!)}
    </Flex>
  );
};

export default withTolgee(SearchElement);
