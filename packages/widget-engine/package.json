{"name": "@resola-ai/widget-engine", "version": "1.0.0", "main": "./index.tsx", "types": "./index.tsx", "license": "MIT", "scripts": {"lint": "biome lint .", "format": "biome format --write .", "lint:eslint:fix": "biome lint --write .", "generate:component": "turbo gen react-component", "test:unit": "echo \"Note: no test specified\" && exit 0", "build": "echo \"Note: no build specified\" && exit 0", "release": "standard-version -t @resola-ai/widget-engine@", "release:minor": "standard-version -t @resola-ai/widget-engine@ --release-as minor", "release:patch": "standard-version -t @resola-ai/widget-engine@ --release-as patch", "release:major": "standard-version -t @resola-ai/widget-engine@ --release-as major", "coverage": "echo 'Do not need test coverage'"}, "devDependencies": {"@biomejs/biome": "^1.5.3", "@resola-ai/biome-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@turbo/gen": "^1.11.3", "@types/node": "^20.11.0", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@types/uuid": "^9.0.7", "handlebars": "4.7.8", "react": "^18.2.0", "@resola-ai/shared-cache": "workspace:*", "standard-version": "^9.5.0", "typescript": "5.6.3", "@vitest/coverage-v8": "^2.1.9", "@testing-library/jest-dom": "^6.2.0", "jest-environment-jsdom": "^29.7.0"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/utils": "1.4.2", "@hookform/resolvers": "^3.3.1", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/dropzone": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/form": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/modals": "7.17.7", "@mantine/notifications": "7.17.7", "@mantine/tiptap": "7.17.7", "@tabler/icons-react": "3.17.0", "axios": "^1.8.2", "fuse.js": "^7.0.0", "@resola-ai/blocknote-editor": "workspace:*", "@resola-ai/models": "workspace:*", "moment": "^2.30.1", "react-hook-form": "^7.54.2", "react-hook-form-mantine": "^3.1.3", "string-to-react-component": "3.1.0", "styled-components": "^6.1.8", "@resola-ai/ui": "workspace:*", "@resola-ai/utils": "workspace:*", "uuid": "^9.0.1", "zod": "^3.24.1"}, "lint-staged": {"*.{js,ts,tsx,jsx}": ["biome format --write", "biome lint --apply"]}}