import { Avatar, Box, Group, Text } from '@mantine/core';
import { MessageItem as MessageItemType } from './types';
import { displayHour, displayTime } from './helper';
import { BotAvatarIcon } from '@resola-ai/ui';
import ErrorBoundary from '@resola-ai/ui/components/ErrorBoundary';
import { BlockNoteViewer } from '@resola-ai/blocknote-editor';
import { createStyles } from '@mantine/emotion';

const useStyle = createStyles(() => ({
  avatar: {
    '& .mantine-Avatar-placeholder': {
      backgroundColor: '#ced4da',
    },
  },
}));

export function MessageItem({ fontSize = 12, ...item }: MessageItemType & { fontSize?: number }) {
  const { classes } = useStyle();
  return (
    <Box
      w={'100%'}
      pb={'5px'}
      pt={'10px'}
      pl={'1rem'}
      pr={'1rem'}
      mb={'2px'}
      bg={item?.backgroundColor || 'gray.0'}
    >
      {item?.type !== 'log' ? (
        <>
          <Group justify='left' gap={'5px'}>
            {item?.showAvatar ? (
              item?.picture === 'BOT' ? (
                <Avatar radius={'xl'} size={'md'} className={classes.avatar}>
                  <BotAvatarIcon />
                </Avatar>
              ) : (
                <Avatar alt="it's me" src={item?.picture || ''} radius={'xl'} size={'md'} />
              )
            ) : null}
            <Text fw={700} fz={fontSize - 1} c='dark.4'>
              {item?.userName || ''} {!item?.showAvatar ? ':' : null}
            </Text>
            <Text fw={400} c='dark.2' fz={fontSize - 1} ml={'10px'}>
              {item?.showAvatar ? displayHour(item.created) : displayTime(item.created)}
            </Text>
          </Group>
          {item?.kbCard && (
            <Box style={{ outline: '1px solid #d7d7d7', borderRadius: '8px' }} my={'sm'} py={10}>
              <ErrorBoundary
                fallback={
                  <Text fz={fontSize - 1} color='dark.3'>
                    ⚠️ Something went wrong
                  </Text>
                }
              >
                <BlockNoteViewer
                  isUsingInlineCSS
                  className='bn-html-message'
                  initialHTML={item.text}
                />
              </ErrorBoundary>
            </Box>
          )}
          {item?.htmlMessage && (
            <Text
              fw={400}
              fz={fontSize}
              mt={'5px'}
              c='dark.3'
              dangerouslySetInnerHTML={{ __html: item.text }}
              style={{ lineBreak: 'auto', whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}
            ></Text>
          )}
          {!item.kbCard && !item.htmlMessage && (
            <Text
              fw={400}
              fz={fontSize}
              mt={'5px'}
              c='dark.3'
              style={{ lineBreak: 'auto', whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}
            >
              {item.text}
            </Text>
          )}
        </>
      ) : (
        <Group justify={item?.showAvatar ? 'center' : 'left'}>
          <Text fz={fontSize - 1} c='dark.4' lineClamp={1} mb={'5px'}>
            <span>{item?.showAvatar ? displayHour(item.created) : displayTime(item.created)}</span>
            <span style={{ marginLeft: '10px' }} title={item?.text || ''}>
              {item?.text || ''}
            </span>
          </Text>
        </Group>
      )}
    </Box>
  );
}
