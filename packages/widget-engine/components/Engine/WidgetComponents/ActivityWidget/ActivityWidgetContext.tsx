import { ReactNode, createContext, useContext } from 'react';
import { useActivityWidget } from './useActivityWidget';

export type ActivityWidgetContextType = ReturnType<typeof useActivityWidget>;

export const ActivityWidgetContext = createContext<ActivityWidgetContextType | null>(null);

export const ActivityWidgetContextProvider = ({ children }: { children: ReactNode }) => {
  const data = useActivityWidget();
  return <ActivityWidgetContext.Provider value={data}>{children}</ActivityWidgetContext.Provider>;
};

export const useActivityWidgetContext = () => {
  const data = useContext(ActivityWidgetContext);
  if (!data) {
    throw new Error('useActivityWidgetContext should be used in ActivityWidgetContextProvider');
  }
  return data;
};
