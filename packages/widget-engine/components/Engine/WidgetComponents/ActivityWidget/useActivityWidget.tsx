import { useEngineContext } from '../../context';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDebouncedState, useDebouncedValue, useDisclosure } from '@mantine/hooks';
import { ActivityItem, ActivityPayload, SOURCE_ACTIVITY } from './types';
import { INIT_FLAG, END_FLAG } from '../../common/constant';
import { convertMessageToDisplayHtml } from './helper';
import { MemoItemData } from '../Memo/constants';

export function useActivityWidget() {
  const [opened, { open, close }] = useDisclosure();
  const [openedMemos, { close: closeMemos, open: openMemos }] = useDisclosure();
  const [memoList, setMemoList] = useState<MemoItemData[]>([]);
  const [nextFlag, setNextFlag] = useState(INIT_FLAG);
  const nextDataRef = useRef<Record<string, boolean>>({ [INIT_FLAG]: true });
  const [dataActivities, setDataActivities] = useState<ActivityItem[]>([]);
  const [isLoadingActives, setIsLoadingActives] = useDebouncedState(false, 100);
  const typeConversationRef = useRef<SOURCE_ACTIVITY | null>(null);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [currentConversationCreated, setCurrentConversationCreated] = useState<string>('');

  const {
    t,
    responsiveScreen,
    activityWidgetActions,
    operatorIdToFinalNameMap,
    currentConversationId: selectedConversationId,
  } = useEngineContext();

  const {
    endUserId,
    endUserName,
    endUserImage,
    ocsChannelId,
    currentUserId,
    integrationId,
    loadActivities,
    loadConservationMessages,
    loadConversationMessagesChatBot,
    useSWR = () => ({ data: [], isLoading: false, mutate: () => null }),
  } = activityWidgetActions;

  const {
    data: dataLoad,
    isLoading: isLoadingActivities,
    // mutate: reloadActivity,
  } = useSWR(
    endUserId && integrationId && selectedConversationId
      ? [endUserId, integrationId, selectedConversationId]
      : null,
    () => loadActivities(endUserId, integrationId, selectedConversationId, INIT_FLAG),
    {
      revalidateOnMount: true,
    }
  );

  const [isLoadingDebounce] = useDebouncedValue(isLoadingActivities || isLoadingActives, 500);

  const getNextPageActivity = useCallback(async () => {
    setIsLoadingActives(true);
    const dataActives = await loadActivities(
      endUserId,
      integrationId,
      selectedConversationId,
      nextFlag
    );
    setIsLoadingActives(false);
    let { data = [], next = '' } = (dataActives || {}) as ActivityPayload;
    if (next === '') next = END_FLAG;
    if (!nextDataRef.current?.[next]) {
      nextDataRef.current[next] = true;
      setDataActivities((pre) => [...pre, ...data]);
      setNextFlag(next);
    }
  }, [
    nextFlag,
    endUserId,
    integrationId,
    loadActivities,
    selectedConversationId,
    setIsLoadingActives,
  ]);

  const handleOpenModalMessageHistoryOfConversation = useCallback(
    (conversationId: string, type: SOURCE_ACTIVITY, created: string) => {
      typeConversationRef.current = type;
      currentConversationCreated !== created ? setCurrentConversationCreated(created) : null;
      currentConversationId !== conversationId && setCurrentConversationId(conversationId);
      setTimeout(() => open(), 200);
    },
    [currentConversationCreated, currentConversationId, open]
  );

  useEffect(() => {
    if (dataLoad) {
      let { data = [], next = '' } = dataLoad as ActivityPayload;
      if (next === '') next = END_FLAG;
      if (!nextDataRef.current?.[next]) {
        nextDataRef.current[next] = true;
        setDataActivities((pre) => [...pre, ...data]);
        setNextFlag(next);
      }
    }
  }, [dataLoad]);

  useEffect(() => {
    return () => {
      nextDataRef.current = { [INIT_FLAG]: true };
    };
  }, [integrationId, endUserId, selectedConversationId]);

  useEffect(() => {
    return () => {
      setDataActivities([]);
    };
  }, []);

  const convertedDataMessages = useMemo(() => {
    if (!dataActivities.length) return [];
    return dataActivities
      .filter((item) => item?.id !== selectedConversationId)
      ?.map((item) => {
        return {
          ...item,
          messages: item.messages
            .filter((message) => Boolean(message) && Object.keys(message).length > 0)
            .slice(0, 5)
            .map((message) => convertMessageToDisplayHtml(message)),
        };
      });
  }, [dataActivities, selectedConversationId]);

  return useMemo(
    () => ({
      t,
      modalMessageHistory: {
        visible: opened,
        close,
        open,
      },
      useSWR,
      memoList,
      endUserId,
      openMemos,
      closeMemos,
      openedMemos,
      endUserName,
      endUserImage,
      ocsChannelId,
      integrationId,
      currentUserId,
      responsiveScreen,
      currentConversationId,
      loadConservationMessages,
      operatorIdToFinalNameMap,
      currentConversationCreated,
      loadConversationMessagesChatBot,
      dataActivities: convertedDataMessages,
      isLoadingActivities: isLoadingDebounce,
      typeConversation: typeConversationRef.current,
      isNextAble: !!nextFlag && nextFlag !== END_FLAG,
      setMemoList,
      getNextPageActivity,
      handleOpenModalMessageHistoryOfConversation,
    }),
    [
      opened,
      useSWR,
      memoList,
      nextFlag,
      openMemos,
      endUserId,
      closeMemos,
      openedMemos,
      endUserImage,
      endUserName,
      ocsChannelId,
      integrationId,
      currentUserId,
      responsiveScreen,
      isLoadingDebounce,
      convertedDataMessages,
      currentConversationId,
      operatorIdToFinalNameMap,
      loadConservationMessages,
      currentConversationCreated,
      loadConversationMessagesChatBot,
      t,
      open,
      close,
      setMemoList,
      getNextPageActivity,
      handleOpenModalMessageHistoryOfConversation,
    ]
  );
}
