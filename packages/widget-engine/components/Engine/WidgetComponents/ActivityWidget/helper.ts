import moment from 'moment';
import { MessageItem } from './types';

export const displayTime = (timeString: string) => {
  return moment(timeString).format('YYYY/M/D HH:mm');
};

export const displayHour = (timeString: string) => {
  return moment(timeString).format('HH:mm');
};

// eslint-disable-next-line no-unused-vars
export function callbackMapMessageItem(
  item: MessageItem,
  currentUserId: string,
  endUserName: string,
  operatorIdToNameMaps: Record<string, string> = {},
  trans: (key: string, data?: any) => string
) {
  if (item?.userType === 'system') {
    if (item?.messageType === 'activity') {
      let activity = item?.text;
      let name =
        item?.metadata?.team?.name ||
        operatorIdToNameMaps?.[item?.metadata?.assignee?.id || ''] ||
        item?.metadata?.assignee?.name ||
        '';
      if (name === 'UNKNOWN') {
        name =
          operatorIdToNameMaps?.[item?.metadata?.oldAssignee?.id || ''] ||
          item?.metadata?.oldAssignee?.name ||
          '';
        activity = 'conversation.operator.assigned.unknown';
      }

      let text = trans(activity, {
        name: name,
      });

      if (activity === 'conversation.operator.assigned') {
        if (currentUserId === item?.metadata?.assignee?.id)
          text = trans('conversation.operator.assigned.you');
        else {
          name =
            operatorIdToNameMaps?.[item?.metadata?.assignee?.id || ''] ||
            item?.metadata?.assignee?.name ||
            '';
          text = trans(activity, { name: name });
        }
      }

      if (activity === 'conversation.inwrapup') {
        text = trans('activity.inwrapup', {
          name:
            operatorIdToNameMaps?.[item?.metadata?.assignee?.id || ''] ||
            item?.metadata?.assignee?.name ||
            '',
        });
      }

      if (activity === 'conversation.inwrapup.system') {
        text = trans('activity.inwrapup.system');
      }

      return {
        id: item.id,
        type: 'log',
        text: text,
        created: item?.created,
      } as MessageItem;
    } else {
      return { ...item, userName: trans('bot') };
    }
  } else {
    return {
      ...item,
      userName:
        item?.userType === 'bot'
          ? trans('bot')
          : item?.userType === 'operator' &&
              ['organization', 'team'].includes(item?.metadata?.sender?.type || '')
            ? item?.metadata?.sender?.name
            : item.userName || (item?.metadata?.user?.id || '').substring(0, 5) || endUserName,
    };
  }
}

export const checkIsHtmlMessage = (message: string) => {
  // if (!json) return '';
  // try {
  //   return JSON.parse(json);
  // } catch (e) {
  //   return json;
  // }
  if (!message) return false;
  return `${message}`.includes('bn-inline-');
};

// eslint-disable-next-line no-unused-vars
export function pipe<A, B>(fn: (a: A) => B) {
  function run(a: A) {
    return fn(a);
  }

  // eslint-disable-next-line no-unused-vars
  run.pipe = <C>(fn2: (b: B) => C) => pipe((a: A) => fn2(fn(a)));

  return run;
}

const urlRegex = /(((https?:\/\/)|(www\.))[^\s]+)/g;

export const hasUrl = (str: string) => urlRegex.test(str);

export const transformTextHyperLinks = (str: string = '') => {
  return str.replace(urlRegex, function (_, contents) {
    return `<a href="${contents.trim()}" role="link" target='_blank' referrerpolicy='no-referrer' rel='nofollow noreferrer noopener'>${contents.trim()}</a>`;
  });
};

export const convertMessageToDisplayHtml = (message: MessageItem) => {
  const isHtmlMessage = checkIsHtmlMessage(message?.text || '');
  if (isHtmlMessage && message?.userType === 'bot') {
    return {
      ...message,
      kbCard: isHtmlMessage,
      text: message.text || '',
    };
  }
  if (message?.userType === 'bot' && hasUrl(message.text)) {
    return {
      ...message,
      htmlMessage: true,
      text: transformTextHyperLinks(message.text),
    };
  }
  return message;
};

export const getBackgroundColorByType = (item: MessageItem) => {
  const defaultColor = 'gray.0';
  if (item?.type === 'log') {
    return defaultColor; // this uses default color
  }
  let bgColor: string = defaultColor;
  switch (item?.userType) {
    case 'operator':
      bgColor = '#F7F3FF';
      break;
    case 'enduser':
      bgColor = '#FFF7E5';
      break;
    default:
      break;
  }
  return bgColor;
};
